<template>
  <div class="app-container">
    <el-row class="top" type="flex" justify="space-between">
      <el-col :span="22">
        <i class="el-icon-location"></i>
        <span>当前位置：<router-link to="/product">产品管理</router-link> /</span>
        <span>产品详情</span>
      </el-col>
      <el-col :span="1">
        <el-button type="primary" size="small" icon="el-icon-back" @click="$router.push('/product')">返回</el-button>
      </el-col>
    </el-row>
    <el-card class="content">
      <div slot="header">
        <span>产品详情</span>
      </div>
      <div class="card_body">
        <el-descriptions :column="1">
          <el-descriptions-item label="产品编号">{{ details.productCode }}</el-descriptions-item>
          <el-descriptions-item label="产品名称">
            <span class="productName">{{ details.productName }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="产品类型">
            <!-- <el-tag size="small">{{ details.productType | formattingProductType }}</el-tag> -->
            <img v-if="details.productType == 1" src="@/assets/product/productType1.png" alt="" />
            <img v-if="details.productType == 2" src="@/assets/product/productType2.png" alt="" />
            <img v-if="details.productType == 3" src="@/assets/product/productType3.png" alt="" />
          </el-descriptions-item>
          <el-descriptions-item label="演示地址">
            <a style="color: #3464e0" :href="details.perform">{{ details.perform }}</a>
          </el-descriptions-item>
          <el-descriptions-item label="产品描述">
            <div style="width: 800px">{{ details.description }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="负责人">{{ details.userName }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ details.createTime }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ details.updateTime }}</el-descriptions-item>
          <el-descriptions-item label="人员列表">
            <div class="personList">
              <div v-for="item in details.userDtos" :key="item.userId">
                <div>
                  <span>{{ item.realName }}</span>
                  <!-- <img v-if="item.sex === 'F'" src="@/assets/meeting/wuman.png" alt="" />
                  <img v-else src="@/assets/meeting/man.png" alt="" /> -->
                </div>
                <span>{{ item.organizationName }}-{{ item.jobName }}</span>
              </div>
            </div>
          </el-descriptions-item>
        </el-descriptions>
        <div v-if="details.iterations && details.iterations.length >= 1" class="record">
          <div class="record_header">迭代记录</div>
          <div>
            <light-timeline v-if="details.iterations && details.iterations.length >= 1" :items="details.iterations" class="lightTimeline">
              <template v-slot:tag="{ item }">
                <div>
                  <img src="@/assets/product/version_icon.png" alt="" /> <span class="version">版本号:{{ item.version }}</span>
                </div>
                <div class="createTime">{{ item.time | formatDate('yyyy年MM月dd日 hh:mm') }}</div>
              </template>
              <template v-slot:symbol>
                <img src="@/assets/product/record_icon.png" alt="" />
              </template>
              <template v-slot:content="{ item }">
                <div class="record_item">
                  <span>{{ item.description }}</span>
                </div>
              </template>
            </light-timeline>
          </div>
        </div>
      </div>
    </el-card>
    <!-- <el-card v-if="details.productCode" header="详情信息">
        <el-descriptions :column="1">
          <el-descriptions-item label="产品编号">{{ details.productCode }}</el-descriptions-item>
          <el-descriptions-item label="产品名称">{{ details.productName }}</el-descriptions-item>
          <el-descriptions-item label="产品类型">
            <el-tag size="small">{{ details.productType | formattingProductType }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="演示地址">
            <a type="primary" :href="details.perform">{{ details.perform }}</a>
          </el-descriptions-item>
          <el-descriptions-item label="产品描述">{{ details.description }}</el-descriptions-item>
          <el-descriptions-item label="负责人">{{ details.userName }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ details.createTime }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ details.updateTime }}</el-descriptions-item>
        </el-descriptions>
        <el-row :gutter="10" style="border-top: 1px solid #ababab; padding-top: 15px">
          <el-col :span="6"><span style="font-size: 20px; font-weight: 600">人员列表</span></el-col>
        </el-row>
        <el-table :data="details.userDtos ? details.userDtos.slice((pagination.page - 1) * pagination.size, (pagination.page - 1) * pagination.size + pagination.size) : details.userDtos" style="width: 100%; margin-top: 15px" border>
          <el-table-column label="姓名" align="center" prop="realName" />
          <el-table-column label="部门" align="center" prop="organizationName" />
          <el-table-column label="岗位" align="center" prop="jobName" />
        </el-table>
        <el-pagination :page-size.sync="pagination.size" :current-page.sync="pagination.page" :total="details.userDtos ? details.userDtos.length : pagination.total" :page-sizes="[5, 10, 15, 20]" layout=" prev,pager,next,sizes,jumper" style="text-align: center; margin-top: 15px" @size-change="handleSizeChange" @current-change="handleCurrentChange"> </el-pagination>
      </el-card>
      <el-card header="迭代记录">

      </el-card> -->
    <el-row :gutter="10" type="flex" justify="center" style="margin-top: 35px">
      <el-col :span="1.5">
        <el-button type="primary" @click="$router.push('/product')">关 闭</el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { productDetails } from '@/api/product'

export default {
  name: 'ProductDetails',
  data() {
    return {
      details: {},
      // 添加人员的假数据分页
      pagination: {
        size: 5,
        page: 1,
        total: 0
      },
      activities: [
        {
          content: '活动按期开始',
          timestamp: '2018-04-15'
        },
        {
          content: '通过审核',
          timestamp: '2018-04-13'
        },
        {
          content: '创建成功',
          timestamp: '2018-04-11'
        }
      ]
    }
  },
  created() {
    this.getProductDetails()
  },
  methods: {
    async getProductDetails() {
      const { data } = await productDetails({ productId: this.$route.params.productId })
      this.details = data
      console.log(data)
    },
    // 添加人员的假分页
    handleSizeChange(val) {
      this.pagination.size = val
    },
    handleCurrentChange(val) {
      this.pagination.page = val
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  padding: 0;
  background-color: #e8eaed;
  min-height: 100%;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  .top {
    top: 0px;
    width: 100%;
    background-color: #e8eaed;
    padding: 24px 24px 16px 0;
    z-index: 999;
    .el-col {
      i {
        font-size: 14px;
        color: #657081;
      }
      span {
        &:first-of-type {
          margin: 0 5px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #657081;
        }
        &:last-of-type {
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
      }
    }
  }
}
.content {
  width: 1754px;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  box-shadow: none;
  ::v-deep {
    .el-card__header {
      padding-left: 56px;
      padding-top: 24px;
      font-size: 16px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #0b1a44;
    }
    .card_body {
      padding-left: 72px;
      padding-right: 79px;
      display: flex;
      justify-content: space-between;
      .el-descriptions {
        width: 1000px;
        .el-descriptions-item {
          padding-bottom: 28px;
        }
        .el-descriptions-item__label {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #868b9f;
        }
        .el-descriptions-item__content {
          color: #0b1a44;
        }
        .productName {
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
        .personList {
          display: flex;
          flex-wrap: wrap;
          width: 870px;
          & > div {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            position: relative;
            width: 148px;
            height: 54px;
            margin-right: 18px;
            margin-bottom: 18px;
            padding: 8px 14px;
            background: #f5f5f5;
            border-radius: 4px 4px 4px 4px;
            box-sizing: border-box;
            & > div {
              display: flex;
              align-items: center;
              span {
                padding: 0;
                font-size: 14px;
                font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                font-weight: 400;
                color: #0b1a44;
              }
              img {
                margin-left: 5px;
                width: initial;
                height: initial;
              }
            }
            span {
              font-size: 12px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              color: #a3a8bb;
              text-overflow: ellipsis; /*文字隐藏后添加省略号*/
              white-space: nowrap; /*强制不换行*/
            }
            p {
              font-size: 12px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              color: #868b9f;
            }
          }
        }
      }
      .record {
        width: 528px;
        height: 697px;
        padding-bottom: 15px;
        background: #f5f5f5;
        border-radius: 8px;
        overflow: auto;
        /* 设置滚动条的样式 */
        &::-webkit-scrollbar {
          width: 3px;
        }
        &::-webkit-scrollbar-track {
          box-shadow: 0 0 10px #000;
        }
        &::-webkit-scrollbar-thumb {
          background-color: #cdcfd3;
          border-radius: 30px;
        }
        .record_header {
          width: 100%;
          padding: 24px;
          padding-top: 12px;
          height: 42px;
          background: #d5dff1;
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
          border-radius: 8px 8px 0 0;
        }
        .lightTimeline {
          .version,
          .createTime {
            font-size: 14px;
            font-family: Microsoft YaHei-Bold, Microsoft YaHei;
            font-weight: bold;
            color: #0b1a44;
          }
          .record_item {
            margin-left: 55px;
            padding: 11px 21px;
            width: 285px;
            min-height: 126px;
            background: #fff;
            border-radius: 8px 8px 8px 8px;
            span {
              font-size: 12px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #0b1a44;
            }
          }
        }
      }
    }
  }
}
::v-deep {
  .line-container {
    margin-left: 25px;
    margin-top: 25px;
    &::after {
      border-right: 2px dashed #b1bac7;
      left: 176px;
      top: 3px;
      width: 2px;
      background: none;
    }
    .line-item {
      padding: 0;
      padding-left: 45px;
      margin-top: 15px;
      min-height: 100px;
      max-width: 630px;
      .item-tag {
        top: 0;
        width: 160px;
        text-align: start;
        line-height: 20px;
      }
      .item-symbol {
        left: 75px;
        background: none;
      }
    }
  }
}
</style>
