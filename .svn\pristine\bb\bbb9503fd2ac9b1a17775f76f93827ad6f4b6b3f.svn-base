import request from '@/utils/request'

export function getOrganizationList(params) {
  return request({
    url: '/system/organization/organizationList',
    method: 'get',
    params
  })
}
export function getOrganizationTree() {
  return request({
    url: '/system/organization/organizationTree',
    method: 'get'
  })
}
export function delOrganization(id) {
  return request({
    url: '/system/organization/organizationRemove',
    method: 'DELETE',
    params: {
      id: id
    }
  })
}
export function addOrganization(data) {
  return request({
    url: '/system/organization/organizationSave',
    method: 'post',
    data
  })
}
export function editOrganization(data) {
  return request({
    url: '/system/organization/organizationUpdate',
    method: 'post',
    data
  })
}
/** 组织列表（考试计划筛选学生使用） */
export function studentOrgForPlan() {
  return request({
    url: '/system/organization/studentOrgForPlan',
    method: 'GET'
  })
}
