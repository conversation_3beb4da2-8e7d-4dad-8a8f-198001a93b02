// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}


// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-button--primary {
  background-color: #3464e0;
  border: 1px solid #3464e0;

  &:active {
    background: #3464e0 !important;
    border: 1px solid #3464e0 !important;
  }
}
.el-button--primary:focus, .el-button--primary:hover{
  background-color: #3464e0;
  border: 1px solid #3464e0;
  color: #FFF;

}

.el-button--warning {
  background-color: #FF9F1E;
  border: 1px solid #FF9F1E;

  &:active {
    background: #FF9F1E !important;
    border: 1px solid #FF9F1E !important;
  }
}
.el-button--warning:focus, .el-button--warning:hover{
  background-color: #FF9F1E;
  border: 1px solid #FF9F1E;
  color: #FFF;

}

.el-button--danger {
  background-color: #FF0000;
  border: 1px solid #FF0000;

  &:active {
    background: #FF0000 !important;
    border: 1px solid #FF0000 !important;
  }

}
.el-button--danger:focus, .el-button--danger:hover{
  background: #FF0000;
  border-color: #FF0000;
  color: #FFF;
}


.el-button--success {
  background-color: #1BC75A;
  border: 1px solid #1BC75A;

  &:active {
    background: #1BC75A !important;
    border: 1px solid #1BC75A !important;
  }
}
.el-button--success:focus, .el-button--success:hover{
  background: #1BC75A;
  border-color: #1BC75A;
  color: #FFF;
}


// 朴素样式
.el-button--primary.is-plain {
  border-color: #3464e0;
  background: transparent;
  color: #3464e0;
  &:active {
    background: transparent !important;
  }
}
.el-button--primary.is-plain:focus,
.el-button--primary.is-plain:hover {
  color: #355fce !important;
  border-color: #355fce !important;
  background-color: transparent;
}
.el-button--primary:focus,
.el-button--primary:hover {
  background-color: #355fce;
}


.el-button--info.is-plain {
  border-color: #b1bac7;
  background: transparent;
  color: #A3A8BB;
  &:active {
    background: transparent !important;
  }
}
.el-button--info.is-plain:focus,
.el-button--info.is-plain:hover {
  color: #A3A8BB !important;
  border-color: #b1bac7 !important;
  background-color: transparent;
}
.el-button--info:focus,
.el-button--info:hover {
  background-color: transparent;
}

//分页样式
.el-pagination.is-background .el-pager li:not(.disabled).active{
  background-color: #3464e0 !important;

}
.el-pagination.is-background .el-pager li:not(.disabled):hover {
  color: #3464e0 !important;
}
.el-select .el-input.is-focus .el-input__inner {
  border-color: #3464e0;
}
.el-pagination.is-background .el-pager li:not(.disabled).active:hover{
  color: #fff !important;

}
.el-select-dropdown__item .selected {
  color: #3464e0;
}
.el-input.is-active .el-input__inner,
.el-input__inner:focus {
  border-color: #3464e0;
}
// 分页样式
.el-pagination.is-background .el-pager li:not(.disabled).active {
  background-color: #3464e0 !important;
}
.el-pagination.is-background .el-pager li:not(.disabled):hover {
  color: #3464e0 !important;
}
.el-select .el-input.is-focus .el-input__inner {
  border-color: #3464e0;
}
.el-select-dropdown__item .selected {
  color: #3464e0;
}
.el-input.is-active .el-input__inner,
.el-input__inner:focus {
  border-color: #3464e0;
}