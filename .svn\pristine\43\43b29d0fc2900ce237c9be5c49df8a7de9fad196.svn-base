<template>
  <div class="workTrack_addDialog">
    <el-dialog
      v-loading.fullscreen.lock="loading"
      element-loading-text="数据保存中,请稍后"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.7)"
      :title="dialogTitle"
      :visible="showDialog"
      width="1000px"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-form ref="form" :model="formInfo" :rules="rules" inline label-width="120px">
        <el-form-item label="问题部门:" prop="problemOrgId">
          <el-select v-model="formInfo.problemOrgId" placeholder="请选择问题部门" @focus="getAllOrganization">
            <el-option v-for="item in allOrganization" :key="item.organizationId" :label="item.organizationName" :value="item.organizationId"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="问题名称:" prop="name">
          <el-input v-model="formInfo.name" placeholder="请输入问题名称" maxlength="40"></el-input>
        </el-form-item>
        <el-form-item label="问题描述:" prop="description">
          <el-input v-model="formInfo.description" placeholder="请输入问题描述" maxlength="40"></el-input>
        </el-form-item>
        <el-form-item label="问题状态:" prop="state">
          <el-radio-group v-model="formInfo.state">
            <el-radio :label="1" border size="small"> 未解决</el-radio>
            <el-radio :label="2" border size="small"> 已解决</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="反馈时间:" prop="feedbackTime">
          <el-date-picker v-model="formInfo.feedbackTime" type="date" value-format="yyyy-MM-dd" placeholder="选择反馈时间"> </el-date-picker>
        </el-form-item>
        <el-form-item label="解决人:" prop="resolveId">
          <el-select v-model="formInfo.resolveId" placeholder="请选择解决人" @focus="getAllManager">
            <el-option v-for="item in allManager" :key="item.userId" :label="item.realName" :value="item.userId"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="协助部门:">
          <el-select v-model="formInfo.helpOrgIds" multiple placeholder="请选择协助部门" @focus="getAllOrganization">
            <el-option v-for="item in assistOrganization" :key="item.organizationId" :label="item.organizationName" :value="item.organizationId"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="计划完成时间:" prop="planTime">
          <el-date-picker v-model="formInfo.planTime" type="date" value-format="yyyy-MM-dd" placeholder="选择计划完成时间"> </el-date-picker>
        </el-form-item>
        <el-form-item label="实际完成时间:"> <el-date-picker v-model="formInfo.realityTime" type="date" value-format="yyyy-MM-dd" placeholder="选择实际完成时间"> </el-date-picker> </el-form-item>
        <el-form-item label="备注:">
          <el-input v-model="formInfo.remark" placeholder="请输入备注" type="textarea" maxlength="50"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="save">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getManager } from '@/api/systemUser'
import { allOrganization } from '@/api/organization'
import { workTrackAdd, workTrackUpdate } from '@/api/workTrack'
import _ from 'lodash'
export default {
  name: 'WorkTrackAddDialog',
  props: {
    showDialog: {
      type: Boolean,
      require: true
    }
  },
  data() {
    return {
      formInfo: {
        name: null, // 问题名称
        description: null, // 问题描述
        problemOrgId: null, // 问题部门id
        feedbackTime: null, // 反馈时间
        state: null, // 问题状态 1 未解决 2 已解决
        resolveId: null, // 解决人id
        helpOrgIds: [], // 协助部门(多个部门用逗号隔开)
        planTime: null, // 计划完成时间
        realityTime: null, // 实际完成时间
        remark: null // 备注
      },
      allManager: [], // 所有的部门经理
      allOrganization: [], // 所有的组织机构
      rules: {
        problemOrgId: [{ required: true, message: '请选择问题部门', type: 'number', trigger: 'change' }],
        feedbackTime: [{ required: true, message: '请选择反馈时间', trigger: 'change' }],
        description: [{ required: true, message: '请输入问题描述', trigger: 'blur' }],
        name: [{ required: true, message: '请输入问题名称', trigger: 'blur' }],
        resolveId: [{ required: true, message: '请选择解决人', trigger: 'change' }],
        planTime: [{ required: true, message: '请选择计划完成时间', trigger: 'change' }],
        state: [{ required: true, type: 'number', message: '请选择问题状态', trigger: 'change' }]
      },
      loading: false
    }
  },
  computed: {
    dialogTitle() {
      return this.formInfo.trackId ? '编辑问题' : '新增问题'
    },
    assistOrganization() {
      return this.allOrganization.filter((item) => item.organizationId !== this.formInfo.problemOrgId)
    }
  },

  methods: {
    async getAllManager() {
      const { data } = await getManager()
      this.allManager = data
    },
    async getAllOrganization() {
      const { data } = await allOrganization()
      this.allOrganization = data
    },
    showDetails(infoData) {
      this.getAllManager()
      this.getAllOrganization()
      this.formInfo = infoData
      if (infoData.helpOrgIds) {
        this.formInfo.helpOrgIds = infoData.helpOrgIds.split(',').map((item) => parseFloat(item))
      }
    },
    close() {
      this.$emit('update:showDialog', false)
      this.$refs['form'].resetFields()
      const info = {
        name: null, // 问题名称
        description: null, // 问题描述
        problemOrgId: null, // 问题部门id
        feedbackTime: null, // 反馈时间
        state: null, // 问题状态 1 未解决 2 已解决
        resolveId: null, // 解决人id
        helpOrgIds: [], // 协助部门(多个部门用逗号隔开)
        planTime: null, // 计划完成时间
        realityTime: null, // 实际完成时间
        remark: null // 备注
      }
      this.formInfo = _.cloneDeep(info)
    },
    save() {
      this.$refs['form'].validate((val) => {
        if (val) {
          const helpOrgIds = this.formInfo.helpOrgIds ? this.formInfo.helpOrgIds.join(',') : null
          this.loading = true
          if (this.formInfo.trackId) {
            workTrackUpdate({ ...this.formInfo, helpOrgIds })
              .then((res) => {
                this.$message.success('修改成功!')
                this.close()
                this.$emit('refreshList')
              })
              .finally(() => {
                this.loading = false
              })
          } else {
            workTrackAdd({ ...this.formInfo, helpOrgIds })
              .then((res) => {
                this.$message.success('保存成功!')
                this.close()
                this.$emit('refreshList')
              })
              .finally(() => {
                this.loading = false
              })
          }
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.workTrack_addDialog {
  ::v-deep {
    .el-form-item__content {
      width: 350px;
    }
    .el-input {
      width: 350px;
    }
    .el-textarea {
      width: 350px;
    }
  }
}
</style>
