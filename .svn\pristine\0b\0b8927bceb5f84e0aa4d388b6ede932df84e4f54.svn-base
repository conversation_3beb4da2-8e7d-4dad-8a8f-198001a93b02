<template>
  <div class="app-container">
    <el-card class="user-card">
      <div slot="header" class="card-header">
        <span class="card-title">销售简介</span>
      </div>
      <div v-if="userDetail" class="user-info">
        <div class="user-basic">
          <span class="user-name">{{ userDetail.realName }}</span>
          <el-tag :type="userDetail.status === 1 ? 'success' : 'danger'" class="status-tag">
            {{ userDetail.status === 1 ? '在职' : '离职' }}
          </el-tag>
        </div>
        <div class="detail-box">
          <div class="detail-content"><i class="detail-label">用户名:</i> {{ userDetail.username }}</div>
          <div class="detail-content"><i class="detail-label">创建时间:</i> {{ userDetail.createTime }}</div>
          <div class="detail-content"><i class="detail-label">更新时间:</i> {{ userDetail.regionUpdateTime }}</div>
          <div class="region-list">
            <i class="detail-label">负责区域:</i>
            <el-tag v-for="(region, index) in userDetail.regionNames" :key="index" type="" class="region-tag">
              {{ region }}
            </el-tag>
          </div>
        </div>
      </div>
    </el-card>
    <el-card style="margin-top: 20px">
      <div slot="header" class="card-header">
        <span class="card-title">负责的客户</span>
      </div>
      <el-table v-if="customerList.length > 0" :data="customerList" border style="width: 100%" @row-click="handleRowClick">
        <el-table-column align="center" type="index" label="序号" width="80"> </el-table-column>
        <el-table-column align="center" prop="customerName" label="客户名称" width="width"> </el-table-column>
        <el-table-column align="center" prop="source" label="客户来源" width="width">
          <template v-slot="{ row }">
            <span>{{ row.source | source }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="level" label="客户级别" width="width">
          <template v-slot="{ row }">
            <span>{{ row.level | level }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="level" label="客户类别" width="width">
          <template v-slot="{ row }">
            <span v-if="row.category">{{ row.category === 1 ? '代理商' : '终端客户' }}</span>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="regionName" label="所属区域" width="width"> </el-table-column>
        <el-table-column align="center" prop="createTime" label="创建时间" width="width"> </el-table-column>
        <el-table-column align="center" prop="updateTime" label="更新时间" width="width"> </el-table-column>
      </el-table>
      <el-pagination
        v-if="customerList.length > 0"
        layout="total,prev, pager, next"
        style="margin-top: 15px; text-align: right"
        :page-sizes="[5, 10, 15, 20]"
        background
        :total="total"
        :page-size.sync="queryInfo.pageSize"
        :current-page.sync="queryInfo.pageNum"
        @size-change="getCustomerList"
        @current-change="getCustomerList"
      />
      <el-empty v-else description="暂无负责的客户" />
    </el-card>
  </div>
</template>

<script>
import { transfer_userDetail } from '@/api/transfer'
import { secretCustomerCustomerList } from '@/api/clientele'
export default {
  name: '',
  data() {
    return {
      userDetail: null,
      queryInfo: {
        userId: this.$route.params.userId,
        pageNum: 1,
        pageSize: 10
      },
      customerList: []
    }
  },
  created() {
    this.getTransferUserDetail()
    this.getCustomerList()
  },
  methods: {
    async getTransferUserDetail() {
      const { data } = await transfer_userDetail({ userId: this.$route.params.userId })
      this.userDetail = data
    },
    async getCustomerList() {
      const { data } = await secretCustomerCustomerList(this.queryInfo)
      this.customerList = data.list
      this.total = data.total
    },
    handleRowClick(row) {
      this.$router.replace(`/transfer/details/${row.customerId}`)
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  padding: 20px;
}

.user-card {
  margin: 0 auto;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .user-info {
    padding: 20px;
    padding-top: 0;

    .user-basic {
      display: flex;
      align-items: center;
      margin-bottom: 20px;

      .user-name {
        font-size: 20px;
        font-weight: bold;
        color: #333;
        margin-right: 15px;
      }

      .status-tag {
        font-size: 14px;
      }
    }

    .detail-box {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin-top: 20px;

      .detail-label {
        width: 80px !important;
        font-size: 16px;
        font-style: normal;
        color: #666;
      }
      .detail-content {
        display: flex;
        align-items: center;
        font-size: 16px;
        color: #333;
      }

      .region-list {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        margin-top: 20px;

        .region-tag {
          font-size: 14px;
          border-radius: 4px;
          margin-right: 5px;
        }
      }
    }
  }
}
.card-header {
  .card-title {
    font-size: 20px;
    font-weight: bold;
    color: #333;
  }
}
::v-deep .el-table {
  .el-table__body {
    tr {
      cursor: pointer;

      &:hover {
        td {
          background-color: #f5f7fa;
        }
      }
    }
  }
}
</style>
