<template>
  <div class="">
    <el-dialog title="历史备份记录" :visible="showDialog" width="1300px" custom-class="historyRecordDialog" top="25vh" @close="close">
      <el-table :data="list" style="width: 100%" border header-cell-class-name="historyRecord_table_header">
        <el-table-column align="center" prop="name" label="方案名称" width="width"> </el-table-column>
        <el-table-column align="center" prop="backupName" label="备份名称" width="width"> </el-table-column>
        <el-table-column v-if="organizationId !== 61311410" align="center" prop="customerName" label="客户名称" width="width"> </el-table-column>
        <el-table-column align="center" prop="budget" label="客户预算(万元)" width="120"> </el-table-column>
        <el-table-column align="center" prop="totalMoney" label="方案总价(万元)" width="120"> </el-table-column>
        <el-table-column align="center" prop="remark" label="备注" width="width"> </el-table-column>
        <el-table-column align="center" prop="realName" label="创建人" width="width"> </el-table-column>
        <el-table-column align="center" prop="createTime" label="创建时间" width="150">
          <template v-slot="{ row }">
            <span>{{ row.createTime | formatDate }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="createTime" label="操作" width="width">
          <template v-slot="{ row }">
            <el-button type="primary" size="small" icon="el-icon-tickets" @click="productBackups(row)">产品备份</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-if="list.length > 0"
        style="margin-top: 32px; text-align: right"
        layout="total,  prev, pager, next"
        background
        :total="total"
        :page-size.sync="pageSize"
        :current-page.sync="pageNum"
        @size-change="getList"
        @current-change="getList"
      />
      <div slot="footer">
        <el-button type="primary" @click="close">关 闭</el-button>
      </div>
    </el-dialog>
    <el-dialog title="产品备份" :visible.sync="backupsDialog" width="width" top="20vh">
      <el-table :data="backupsList" style="width: 100%" border>
        <el-table-column prop="name" align="center" label="产品名称" width="width"> </el-table-column>
        <el-table-column prop="offer" align="center" label="单价(万元)" width="100"> </el-table-column>
        <el-table-column align="center" label="单位" width="100">
          <template v-slot="{ row }">
            <span>{{ row.unit | softwareUnit }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="number" align="center" label="需求数量" width="100"> </el-table-column>
        <el-table-column prop="totalOffer" align="center" label="总价(万元)" width="100">
          <template v-slot="{ row }">
            <span>{{ row.totalOffer === '-1' ? 0 : row.totalOffer }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="showContent" align="center" show-overflow-tooltip :label="showContentLabel" width="width"> </el-table-column>
        <el-table-column prop="remark" align="center" show-overflow-tooltip label="备注" width="width"> </el-table-column>
      </el-table>
      <div slot="footer">
        <el-button type="primary" @click="backupsDialog = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { tSalesPlanBackupList } from '@/api/tSalesPlan'
import { mapGetters } from 'vuex'

export default {
  name: '',
  props: {
    showDialog: {
      type: Boolean,
      required: true
    },
    id: {
      type: Number || String,
      required: true
    }
  },
  data() {
    return {
      pageNum: 1,
      pageSize: 10,
      total: 0,
      list: [],
      isShow: null,
      backupsDialog: false,
      backupsList: []
    }
  },
  computed: {
    showContentLabel() {
      return this.isShow === 1 ? '简介' : '参数'
    },
    ...mapGetters(['organizationId'])
  },
  methods: {
    async getList() {
      const { data } = await tSalesPlanBackupList({ pageNum: this.pageNum, pageSize: this.pageSize, salesPlanId: this.id })
      this.total = data.total
      this.list = data.list
      console.log(this.list)
    },
    productBackups(row) {
      this.isShow = row.isShow
      this.backupsList = row.productBackups
      this.backupsDialog = true
    },
    close() {
      this.$emit('update:showDialog', false)
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep {
  // .historyRecordDialog{
  //   height: 600px !important;
  // }
  .historyRecord_table_header {
    color: #1a1a1a;
  }
}
</style>
