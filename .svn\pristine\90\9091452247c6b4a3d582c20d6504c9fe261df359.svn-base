<template>
  <div class="app-container">
    <el-row :gutter="50" class="top" type="flex" justify="space-between" align="middle" style="margin-bottom: 16px">
      <el-col :span="6">
        <div class="top_left">
          <span class="meeting_icon">
            <img src="@/assets/training/training_icon.png" alt="" />
            培训管理
          </span>
        </div>
      </el-col>
    </el-row>
    <el-card style="border-radius: 8px">
      <div slot="header">
        <img src="@/assets/training/add.png" alt="" style="cursor: pointer" @click="$router.push('/addTraining/0')" />
        <el-radio-group v-model="queryInfo.type" style="padding-left: 40px" @change="geTrainList">
          <el-radio-button :label="1">
            <span>我接收的</span>
          </el-radio-button>
          <el-radio-button :label="2">
            <span>我发起的</span>
          </el-radio-button>
        </el-radio-group>
      </div>
      <div style="background: #f5f5f5; border-radius: 8px; padding: 25px 73px 32px 57px">
        <el-row :gutter="10">
          <el-col :span="24" class="searchForm">
            <el-form ref="form" label-width="90px" inline>
              <el-form-item label="培训状态:" label-width="72px" class="trainStatus">
                <el-radio-group v-model="queryInfo.trainStatus" @change="geTrainList">
                  <el-radio :label="null">全部</el-radio>
                  <el-radio :label="1">未开始</el-radio>
                  <el-radio :label="2">进行中</el-radio>
                  <el-radio :label="3">已结束</el-radio>
                  <el-radio :label="4">已终止</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="培训时间:" class="trainTime">
                <el-date-picker v-model="queryInfo.startTime" type="date" size="small" prefix-icon="" placeholder="选择开始时间" @change="geTrainList"> </el-date-picker>
                至
                <el-date-picker v-model="queryInfo.endTime" type="date" size="small" prefix-icon="" placeholder="选择结束时间" @change="geTrainList"> </el-date-picker>
              </el-form-item>
              <el-form-item class="trainName">
                <el-input v-model="queryInfo.name" size="small" placeholder="查询培训标题/主讲人" maxlength="50" clearable>
                  <el-button slot="append" type="primary" icon="el-icon-search" size="small" @click="geTrainList"></el-button>
                </el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <div class="showRegion">
          <div v-for="item in list" :key="item.trainId">
            <div class="showRegion_left">
              <img v-if="item.powerType === 1 || item.powerType === 2" src="@/assets/training/inside.png" alt="" />
              <img v-else src="@/assets/training/outside.png" alt="" />
            </div>
            <div class="showRegion_center">
              <div>
                <span>{{ item.trainName }}</span>
                <span v-if="item.trainStatus === 2" :class="[item.isSignUp === 1 ? 'apply1' : 'apply0', 'apply']">{{ item.isSignUp === 0 ? '未报名' : '已报名' }}</span>
              </div>
              <div>
                <!-- <span>距离培训开始时间：<em>{{ item.distanceTime }}小时</em></span> -->
                <span>培训时间：<em>{{ item.startTime }}</em></span>
                <span>主讲人：<em>{{ item.trainUserName }}</em></span>
                <span>培训地点：<em>{{ item.meetingRoomName }}</em></span>
                <span>培训范围：<em>{{ item.powerType | powerType }}</em></span>
                <span>培训状态：<em :style="{ color: item.trainStatus === 2 ? '#FF7E26' : item.trainStatus === 3 ? '#3464E0' : item.trainStatus === 4 ? '#9D302C' : '#0B1A44' }">{{ item.trainStatus | trainStatus }}</em></span>
                <span>评分：<em>{{ item.score || 0.0 }}</em></span>
              </div>
            </div>
            <div class="showRegion_right">
              <template v-if="queryInfo.type === 1">
                <!-- 报名 -->
                <div v-if="item.trainStatus === 1 && item.isSignUp === 0" class="applyImg" @click="comfirmJoin(item)"></div>
                <img v-if="item.trainStatus === 1 && item.isSignUp === 1" src="@/assets/training/apply2.png" alt="" />
                <!-- 签到 -->
                <div v-if="item.trainStatus === 2 && item.isSign === 0 && item.isSignUp === 1" class="signImg" @click="sign(item)"></div>
                <img v-if="item.trainStatus === 2 && item.isSign === 1 && item.isSignUp === 1" src="@/assets/training/sign3.png" alt="" />
                <!-- 评分 -->
                <el-rate v-if="item.trainStatus === 3 && item.isSignUp === 1" v-model="item.score" show-score void-icon-class="iconfont zf-xingxing" class="rate" @change="score(item)"></el-rate>
                <!-- 详情 -->
                <div v-if="item.trainStatus === 3" class="details" @click="$router.push(`/trainingDetails/${item.trainId}`)">
                  <i class="el-icon-right"></i>
                  <div>详情</div>
                </div>
              </template>
              <template v-if="queryInfo.type === 2">
                <div class="showRegion_right_typ2">
                  <!-- 修改 删除 -->
                  <img v-if="item.trainStatus === 1" src="@/assets/meeting/edit.png" alt="" @click="edit(item)" />
                  <img v-if="item.trainStatus === 1" src="@/assets/meeting/del.png" alt="" @click="del(item)" />
                  <!-- 签到 结束培训 -->
                  <span v-if="item.trainStatus === 2 && item.isSign === 0" class="sign" @click="sign(item)">签到</span>
                  <span v-if="item.trainStatus === 2 && item.isSign === 1" class="sign" style="background: #d8dbe1">已签到</span>
                  <span v-if="item.trainStatus === 2" class="sign" style="background: #eb6557; margin-left: 15px" @click="overMeeting(item)">结束培训</span>
                  <!-- 评分 -->
                  <el-rate v-if="item.trainStatus === 3" v-model="item.score" show-score void-icon-class="iconfont zf-xingxing" class="rate" @change="score(item)"></el-rate>
                  <!-- 详情 -->
                  <div v-if="item.trainStatus === 3" class="details" @click="$router.push(`/trainingDetails/${item.trainId}`)">
                    <i class="el-icon-right"></i>
                    <div>详情</div>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </div>
        <el-pagination v-if="list.length > 0" style="text-align: center; margin-top: 15px" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[5, 10, 15, 20]" background :total="total" :page-size.sync="queryInfo.pageSize" :current-page.sync="queryInfo.pageNum" @size-change="geTrainList" @current-change="geTrainList" />
        <el-empty v-if="list.length <= 0" :image="noDataImg">
          <template v-slot:description>
            <img src="@/assets/training/noData_bg.png" alt="" />
          </template>
          <el-button type="primary" size="small" @click="$router.push('/addTraining/0')">去添加</el-button>
        </el-empty>
      </div>
    </el-card>
  </div>
</template>

<script>
import { trainList, trainDetails, trainSign, trainGrade, trainSignUp, trainpdateStatus } from '@/api/training.js'
import { mapGetters } from 'vuex'
import noDataImg from '@/assets/training/noData_text.png'
export default {
  name: 'Meeting',

  data() {
    return {
      queryInfo: {
        name: null,
        trainStatus: null,
        startTime: null,
        endTime: null,
        userId: null,
        organizationId: null,
        type: 1,
        pageNum: 1,
        pageSize: 5
      },
      list: [],
      total: 0,
      showDialog: false,
      replenishSummaryDialog: false,
      fileList: [],
      header: {
        Authorization: null
      },
      trainId: null,
      meetingCheckDialog: false, // 培训查看dialog
      trainDetails: {},
      activeName: 'first',
      grade: 0, // 分数
      noDataImg
    }
  },
  computed: {
    ...mapGetters(['token', 'organizationId'])
  },
  created() {
    this.geTrainList()
  },

  methods: {
    async geTrainList() {
      this.queryInfo.organizationId = this.organizationId
      const { data } = await trainList(this.queryInfo)
      this.total = data.total
      this.list = data.list
      // this.list = []
    },
    reset() {
      const type = this.queryInfo.type
      this.queryInfo = {
        trainName: null,
        trainStatus: null,
        trainUserName: null,
        userId: null,
        startTime: null,
        endTime: null,
        type,
        pageNum: 1,
        pageSize: 10
      }
      this.geTrainList()
    },
    async edit(row) {
      const { data } = await trainDetails({ trainId: row.trainId, belongType: 2, isReserve: 1 })
      window.localStorage.setItem('trainingData', JSON.stringify(data))

      this.$router.push('/addTraining/1')
      console.log(data)
    },
    // 参加
    comfirmJoin(row) {
      this.$confirm('您确认要参加该培训吗, 是否继续?', '参加培训', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await trainSignUp({ trainId: row.trainId })
          this.$message.success('参加成功')
          this.geTrainList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
    },
    // 签到
    async sign(row) {
      await trainSign({ trainId: row.trainId })
      this.$message.success('签到成功')
      this.geTrainList()
    },
    // 结束培训
    overMeeting(row) {
      this.$confirm('您确认要结束该培训吗, 是否继续?', '结束培训', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await trainpdateStatus({ status: 2, trainId: row.trainId })
          this.$message.success('结束培训成功')
          this.geTrainList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
    },
    // 删除
    del(row) {
      this.$confirm('您确认要删除该培训吗, 是否继续?', '删除培训', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await trainpdateStatus({ status: 1, trainId: row.trainId })
          this.$message.success('删除培训成功')
          this.geTrainList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
    },
    // 评分
    async score(row) {
      await trainGrade({ trainId: row.trainId, grade: row.score })
      this.$message.success('评分成功')
      this.geTrainList()
    },
    // 查看
    async check(row) {
      const { data } = await trainDetails({ trainId: row.trainId, belongType: 2 })
      this.trainDetails = data
      console.log(data)
      this.meetingCheckDialog = true
    },
    // 查看中附件下载
    download(row) {
      window.open(row.fileUrl, '_blank')
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  background-color: #e8eaed;
  min-height: 100%;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
}
.top {
  .top_left {
    display: flex;
    align-items: center;
    .meeting_icon {
      display: flex;
      align-items: center;
      margin-right: 10px;
      font-size: 16px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #0b1a44;
      img {
        margin-right: 8px;
      }
    }
  }
}
.searchForm {
  .trainStatus {
    ::v-deep {
      .el-radio__input.is-checked .el-radio__inner {
        // background: #3464e0;
        background: #fff;
        width: 18px;
        height: 18px;
        border-color: #3464e0;
        // border: none;
        &::after {
          background-color: #3464e0;
          width: 8px;
          height: 8px;
        }
      }
      .el-radio__inner {
        width: 18px;
        height: 18px;
      }
      .el-radio__input.is-checked + .el-radio__label {
        color: #0b1a44;
      }
      .el-radio__label {
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #0b1a44;
      }
    }
  }
  .trainTime {
    width: 500px;
    color: #0b1a44;
    ::v-deep {
      .el-form-item__content {
        width: 400px;
      }
      .el-input {
        width: 150px;
        border-bottom: 1px solid #b8c0cc;
      }
      .el-input__prefix {
        display: none;
      }
      .el-input__inner {
        width: 100%;
        background: none;
        border: none;
        // border-bottom: 1px solid #b8c0cc;
        opacity: 1 !important;
        color: #0b1a44;
      }
    }
  }
  .trainName {
    ::v-deep {
      .el-form-item__content {
        line-height: initial;
      }
      .el-input {
        width: 340px;
        height: 35px;
        .el-input__prefix,
        .el-input__suffix {
          top: 1px !important;
        }
      }
      .el-input__inner {
        background-color: #f5f5f5;
        height: 35px;
        border-radius: 4px 0 0px 4px;
        margin-right: 10px;
        &::placeholder {
          font-size: 14px;
          font-weight: 400;
          color: #a3a8bb;
        }
      }
      .el-input__inner::placeholder {
        color: #a3a8bb;
      }
      .el-button {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        padding: 0;
      }
      .el-input-group__append .el-button,
      .el-input-group__append .el-select,
      .el-input-group__prepend .el-button,
      .el-input-group__prepend .el-select {
        margin: 0;
      }
      .el-input-group__append,
      .el-input-group__prepend {
        position: relative;
        background: #3464e0;
        height: 35px;
        width: 34px;
        padding: 0;

        border-radius: 0;
        border: none;
        .el-icon-search {
          color: #fff;
          font-size: 18px;
        }
      }
    }
  }
  ::v-deep {
    .el-form {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .el-form-item__label {
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      color: #868b9f;
      font-weight: 400;
    }
  }
}
// 列表展示区
.showRegion {
  & > div {
    display: flex;
    // justify-content: space-between;
    height: 140px;
    padding-right: 93px;
    margin-bottom: 20px;
    background: #fff;
    &:hover {
      background-color: #f9f9f9;
    }
    .showRegion_center {
      padding-left: 40px;
      padding-top: 31px;
      padding-bottom: 31px;
      div {
        &:first-of-type {
          display: flex;
          align-items: center;
          font-size: 16px;
          font-weight: bold;
          color: #0b1a44;
          .apply {
            display: inline-block;
            width: 58px;
            height: 20px;
            line-height: 19px;
            margin-left: 8px;
            border-radius: 12px 12px 12px 1px;
            font-size: 12px;
            font-weight: 400;
            color: #ffffff;
            text-align: center;
          }
          .apply0 {
            background: #b1bac7;
          }
          .apply1 {
            background: #3464e0;
          }
        }
        &:last-of-type {
          display: flex;
          flex-wrap: wrap;
          padding-top: 15px;
          span {
            margin-right: 25px;
            font-size: 14px;
            color: #868b9f;
            line-height: 25px;
            em {
              font-style: normal;
              font-size: 14px;
              color: #0b1a44;
            }
          }
        }
      }
    }
    .showRegion_right {
      position: relative;
      display: flex;
      align-items: center;
      margin-left: auto;
      .applyImg {
        width: 116px;
        height: 40px;
        background: #3464e0;
        border-radius: 4px 4px 4px 4px;
        background: url('../../assets/training/apply.png') no-repeat;
        cursor: pointer;
        &:hover {
          background: url('../../assets/training/apply1.png') no-repeat;
        }
      }
      .signImg {
        width: 86px;
        height: 32px;
        background: #ff7e26;
        border-radius: 4px 4px 4px 4px;
        background: url('../../assets/training/sign0.png') no-repeat;
        cursor: pointer;
        &:hover {
          background: url('../../assets/training/sign1.png') no-repeat;
        }
      }
      .rate {
        position: relative;
        height: initial;
        &::after {
          position: absolute;
          left: 25px;
          bottom: -25px;
          content: '请给本次培训评分吧！';
          font-size: 12px;
          color: #657081;
        }
        ::v-deep {
          .el-rate__icon {
            display: inline-block;
            width: 26px;
            height: 26px;
            background-size: cover;
            font-size: 26px;
          }
          .el-icon-star-on {
            background: url('../../assets/training/xingxing2.png') no-repeat;
            background-size: cover;
          }
          .el-rate__text {
            font-size: 14px;
            font-weight: bold;
            color: #ff7e26 !important;
          }
        }
      }
      .details {
        position: absolute;
        right: -50px;
        top: 54%;
        font-size: 14px;
        color: #a3a8bb;
        text-align: center;
        cursor: pointer;

        i {
          font-size: 16px;
        }
        &:hover {
          color: #3464e0;
        }
      }
      .showRegion_right_typ2 {
        display: flex;
        .sign {
          width: 80px;
          height: 28px;
          line-height: 28px;
          background: #ff7e26;
          border-radius: 4px 4px 4px 4px;
          font-size: 14px;
          font-weight: bold;
          color: #ffffff;
          text-align: center;
          cursor: pointer;
        }
      }
      img {
        cursor: pointer;
      }
    }
  }
}
::v-deep {
  .el-card__header {
    padding: 0;
    border: none;
  }
  .el-card__body {
    padding-left: 56px;
    padding-right: 56px;
  }
  .el-radio-button {
    .el-radio-button__inner {
      border: none;
      background: none;
      font-size: 18px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      span {
        position: relative;
        z-index: 999;
      }
    }
    .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      font-weight: bold;
      color: #0b1a44;
      -webkit-box-shadow: none;
      &::after {
        content: '';
        position: absolute;
        left: 50%;
        bottom: 10px;
        z-index: 1;
        width: 72px;
        height: 5px;
        background-color: #f3c057;
        transform: translateX(-50%);
      }
    }
  }
  .el-radio-button__inner:hover {
    color: #0b1a44;
  }
  .el-empty {
    .el-empty__image {
      width: 232px;
      height: 190px;
    }
    .el-button {
      width: 114px;
      height: 38px;
      background: #3464e0;
      border-radius: 4px;
      font-size: 14px;
      font-weight: bold;
      color: #ffffff;
    }
  }
}
</style>
