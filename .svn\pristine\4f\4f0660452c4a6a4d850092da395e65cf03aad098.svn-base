<template>
  <div class="Home">
    <div class="content">
      <el-row :gutter="120">
        <el-col v-if="keyList.includes('basicInfo')" :span="6">
          <span class="item" @click="checkedMode(1)">基础信息管理</span>
        </el-col>
        <el-col v-if="keyList.includes('project')" :span="6">
          <span class="item" @click="checkedMode(4)">项目管理</span>
        </el-col>
        <el-col :span="6">
          <span class="item">任务管理</span>
        </el-col>
        <el-col v-if="keyList.includes('process')" :span="6">
          <span class="item" @click="checkedMode(2)">流程管理</span>
        </el-col>
        <el-col :span="6">
          <span class="item">小微秘</span>
        </el-col>
        <el-col v-if="keyList.includes('meeting')" :span="6">
          <span class="item" @click="checkedMode(3)">会议管理</span>
        </el-col>
        <el-col v-if="keyList.includes('training')" :span="6">
          <span class="item" @click="checkedMode(5)">培训/知识库管理</span>
        </el-col>
        <el-col :span="6">
          <span class="item">制度管理</span>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: '',
  data() {
    return {}
  },
  computed: {
    ...mapGetters(['keyList'])
  },

  methods: {
    checkedMode(type) {
      if (type === 1) {
        this.$store.commit('checkedData/set_data_type', type)
        this.$nextTick(() => {
          this.$router.push('/basicInfo/home')
        })
      } else if (type === 2) {
        this.$store.commit('checkedData/set_data_type', type)
        this.$nextTick(() => {
          this.$router.push('/process/management')
        })
      } else if (type === 3) {
        this.$store.commit('checkedData/set_data_type', type)
        this.$nextTick(() => {
          this.$router.push('/meeting')
        })
      } else if (type === 4) {
        this.$store.commit('checkedData/set_data_type', type)
        this.$nextTick(() => {
          this.$router.push('/project')
        })
      } else if (type === 5) {
        this.$store.commit('checkedData/set_data_type', type)
        this.$nextTick(() => {
          this.$router.push('/training')
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.Home {
  width: 100%;
  height: 100%;
  background: url('../assets/Home/bg.png') no-repeat;
  background-size: 100% 100%;
  overflow: hidden;
  .content {
    max-width: 1600px;
    margin: 0 auto;
    margin-top: 300px;
    .item {
      display: inline-block;
      width: 100%;
      height: 100px;
      line-height: 100px;
      border: 2px solid #fff;
      background: rgba($color: #fff, $alpha: 0.7);
      text-align: center;
      font-size: 26px;
      cursor: pointer;
    }
  }
}
::v-deep {
  .el-row {
    .el-col {
      margin-bottom: 120px;
    }
  }
}
</style>
