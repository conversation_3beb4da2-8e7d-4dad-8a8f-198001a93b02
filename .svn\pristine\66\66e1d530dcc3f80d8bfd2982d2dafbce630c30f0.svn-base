<template>
  <div class="">
    <el-dialog :title="showTitle" :visible="showDialog" width="750px" :close-on-click-modal="false" @close="close">
      <el-form ref="form" :model="systemUser" label-width="120px" :rules="rules" inline>
        <el-form-item v-if="showTitle === '添加用户'" label="用户id:" prop="userId"><el-input v-model="systemUser.userId" maxlength="100" placeholder="请输入用户id" size="small" /></el-form-item>

        <el-form-item label="用户名:" prop="username"><el-input v-model="systemUser.username" maxlength="20" :disabled="showTitle === '修改用户'" placeholder="请输入用户名" size="small" /></el-form-item>
        <el-form-item v-if="showTitle === '添加用户'" label="密码:" prop="password"><el-input v-model="systemUser.password" maxlength="20" placeholder="请输入密码" type="password" size="small" /></el-form-item>

        <el-form-item label="真实姓名:"><el-input v-model="systemUser.realName" maxlength="20" placeholder="请输入真实姓名" size="small" /></el-form-item>

        <el-form-item label="请输入手机号:" prop="mobile"><el-input v-model="systemUser.mobile" maxlength="20" size="small" placeholder="请输入联系方式" /></el-form-item>
        <el-form-item label="部门:" prop="organizationId">
          <el-select ref="selecteltree" v-model="systemUser.organizationName" size="small" @focus="getMenuTreeOfParent">
            <el-option v-for="item in menu" :key="item.id" :label="item.organizationName" :value="item.id" style="display: none" />
            <el-tree style="padding-left: 15px" :data="menu" node-key="id" empty-text="暂无菜单" highlight-current :expand-on-click-node="false" :props="defaultProps" current-node-key="id" default-expand-all @node-click="handleNodeClick" /> </el-select></el-form-item>

        <el-form-item label="岗位名称:"><el-input v-model="systemUser.jobName" maxlength="11" placeholder="请输入岗位名称" size="small" /></el-form-item>
        <el-form-item label="直属上级id:"><el-input v-model="systemUser.higherUserId" maxlength="11" placeholder="请输入直属上级id" size="small" /></el-form-item>
        <el-form-item label="性别:">
          <el-radio-group v-model="systemUser.sex">
            <el-radio label="M">男</el-radio>
            <el-radio label="F">女</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="照片:" label-width="198px">
          <el-upload
            ref="uploadImg"
            class="avatar-uploader"
            action=""
            :file-list="fileList"
            :http-request="uploadImg"
            list-type="picture-card"
            :limit="1"
            :on-preview="handlePictureCardPreview"
            :on-change="handleChange"
            :on-remove="handleRemove"
            :class="{ disabled: fileComputed }"
          >
            <i class="el-icon-plus avatar-uploader-icon" />
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="confirmOnClick">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="ImgdialogVisible">
      <img width="100%" :src="systemUser.headurl" alt="" />
    </el-dialog>
  </div>
</template>

<script>
import { addSysUser, updateSysUser, uploadFile } from '@/api/systemUser'
import { getOrganizationTree } from '@/api/organization'
import { validQC } from '@/utils/validate'
import { checkTel } from '@/filters'
export default {
  name: 'SysUserDialog',
  props: {
    showDialog: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fileList: [],
      systemUser: {
        organizationId: null,
        realName: null,
        password: null,
        sex: 'M',
        mobile: null,
        headurl: null,
        username: null,
        organizationName: null,
        jobName: null,
        higherUserId: null
      },
      rules: {
        userId: [{ required: true, tiggle: 'blur', message: '请输入用户id ' }],
        username: [{ required: true, tiggle: 'blur', message: '请输入用户名 ' }],
        password: [{ required: true, tiggle: 'blur', message: '请输入密码' }],
        mobile: [
          { validator: validQC, trigger: 'blur' },
          { validator: checkTel, trigger: 'blur' }
        ],
        organizationId: [
          {
            required: true,
            tiggle: 'blur',
            message: '请输入组织',
            type: 'number'
          }
        ]
      },
      menu: [],
      defaultProps: {
        label: 'organizationName',
        children: 'children'
      },
      ImgdialogVisible: false,
      showTitle: '添加用户'
    }
  },
  computed: {
    // 计算当前上传的图片
    fileComputed() {
      return this.fileList.length === 1 // 设定一个计算属性 判断是否已经上传完了一张
    }
  },
  methods: {
    showData(row) {
      this.systemUser = { ...row }
      this.showTitle = '修改用户'
      if (row.headurl) {
        console.log(row.headurl)
        // this.systemUser.headurl = window.URL.createObjectURL(new Blob([row.headurl]))
        this.fileList.push({ url: this.systemUser.headurl })
      }
    },
    close() {
      this.systemUser = {
        organizationId: null,
        realName: null,
        password: null,
        sex: 'M',
        mobile: null,
        headurl: null,
        username: null
      }
      this.showTitle = '添加用户'

      this.$refs['form'].resetFields()
      this.$refs['uploadImg'].clearFiles()
      this.fileList = []
      this.menu = []
      this.$emit('update:showDialog', false)
    },
    // 点击确认后触发的事件
    async confirmOnClick() {
      if (this.showTitle === '修改用户') {
        // 有id代表修改
        const res = await updateSysUser(this.systemUser)
        console.log(res)
        this.$message.success('修改成功')
        this.$emit('addSuccess')

        this.close()
      } else {
        console.log(1231)
        this.$refs['form'].validate((val) => {
          if (val) {
            console.log(this.systemUser)
            addSysUser(this.systemUser)
              .then((res) => {
                console.log(res)
                this.$message.success('添加成功')
                this.$refs['form'].resetFields()
                this.$emit('addSuccess')
                this.close()
              })
              .catch((err) => {
                console.log(err)
              })
          }
        })
      }
    },
    handleNodeClick(node) {
      this.systemUser.organizationId = node.id
      this.systemUser.organizationName = node.organizationName
      this.$refs['selecteltree'].blur()
      this.$forceUpdate()
      console.log(node)
    },
    getMenuTreeOfParent() {
      getOrganizationTree().then((res) => {
        this.menu = res.data
        console.log(this.menu)
      })
    },
    uploadImg(optins) {
      const formData = new FormData()
      formData.append('file', optins.file)
      formData.append('url', this.systemUser.headurl)
      uploadFile(formData)
        .then((res) => {
          console.log(res)
          const url = res.data[0]
          this.systemUser.headurl = url
        })
        .catch((err) => {
          console.log(err)
        })
    },
    handleChange(file, fileList) {
      this.fileList = fileList.map((item) => item)
    },
    handlePictureCardPreview(file) {
      this.ImgdialogVisible = true
    },
    handleRemove(file, fileList) {
      this.systemUser.headurl = null
      this.fileList = fileList
    }
  }
}
</script>

<style scoped>
.disabled /deep/.el-upload--picture-card {
  display: none;
}
</style>
