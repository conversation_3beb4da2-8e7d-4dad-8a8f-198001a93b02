// 小微秘 日志管理
import request from '@/utils/request'
/** 添加日志管理 */
export function secretLog_saveLog(data) {
  return request({
    url: '/secret/log/saveLog',
    method: 'POST',
    data
  })
}
/** 修改日志管理 */
export function secretLog_updateLog(data) {
  return request({
    url: '/secret/log/updateLog',
    method: 'POST',
    data
  })
}

/** 日志管理列表 */
export function secretLog_logList(params) {
  return request({
    url: '/secret/log/logList',
    method: 'GET',
    params
  })
}

/** 日志管理统计 */
export function secretLog_logStatis(params) {
  return request({
    url: '/secret/log/logStatis',
    method: 'GET',
    params
  })
}

