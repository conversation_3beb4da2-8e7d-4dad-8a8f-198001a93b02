.webgl-content * {
  border: 0;
  margin: 0;
  padding: 0;
}
.webgl-content {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.webgl-content .logo,
.progress {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
.webgl-content .logo {
  background: url('progressLogo.Light.png') no-repeat center / contain;
  width: 154px;
  height: 130px;
}
.webgl-content .progress {
  height: 18px;
  width: 141px;
  margin-top: 90px;
}
.webgl-content .progress .empty {
  background: url('progressEmpty.Light.png') no-repeat right / cover;
  float: right;
  width: 100%;
  height: 100%;
  display: inline-block;
}
.webgl-content .progress .full {
  background: url('progressFull.Light.png') no-repeat left / cover;
  float: left;
  width: 0%;
  height: 100%;
  display: inline-block;
}

.webgl-content .logo.Dark {
  background-image: url('progressLogo.Dark.png');
}
.webgl-content .progress.Dark .empty {
  background-image: url('progressEmpty.Dark.png');
}
.webgl-content .progress.Dark .full {
  background-image: url('progressFull.Dark.png');
}

.webgl-content .footer {
  margin-top: 5px;
  height: 38px;
  line-height: 38px;
  font-family: Helvetica, Verdana, Arial, sans-serif;
  font-size: 18px;
}
.webgl-content .footer .webgl-logo,
.title,
.fullscreen {
  height: 100%;
  display: inline-block;
  background: transparent center no-repeat;
}
.webgl-content .footer .webgl-logo {
  background-image: url('webgl-logo.png');
  width: 204px;
  float: left;
}
.webgl-content .footer .title {
  margin-right: 10px;
  float: right;
}
.webgl-content .footer .fullscreen {
  background-image: url('fullscreen.png');
  width: 38px;
  float: right;
}

/* 新增的样式 */
html,
body {
  width: 100%;
  height: 100%;
  margin: 0;
}
.webgl_content {
  display: flex;
  justify-content: center;
  align-items: center;
}
/* .webgl_container {
  width: 98% !important; 
  height: 98% !important;
} */
.webgl_virtual {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: url('bg.png') no-repeat;
  background-size: cover;
  overflow: hidden;
}
.webgl_infobox {
  position: absolute;
  top: 284px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
}
.webgl_logo {
  width: 342px;
  height: 154px;
}

.webgl_light {
  position: absolute;
  left: 170px;
  top: 98px;
  width: 23px;
  height: 75px;
  visibility: hidden;
  animation: move linear 10s infinite 2s;
}
@keyframes move {
  0% {
    transform: translateX(0);
    visibility: visible;
  }
  88% {
    visibility: visible;
  }
  90% {
    transform: translateX(338px);
    visibility: hidden;
  }
}
.webgl_loading_bg {
  position: relative;
  width: 706px;
  height: 48px;
  margin-top: 46px;
  background: url('loading_bg.png') no-repeat;
  background-size: cover;
}

.progress_bar {
  position: absolute;
  left: 20px;
  top: 44%;
  transform: translateY(-50%);
  height: 12px;
  width: var(--width);
  background: linear-gradient(180deg, #48f2ff 0%, #003c8b 100%);
  border-radius: 9px;
  transition: all 0.3s;
}

.webgl_loading_text {
  width: 106px;
  height: 22px;
  margin: 40px auto 0;
  background: url('loading_text.png') no-repeat;
  background-size: cover;
}

.webgl_footfullscreen {
  visibility: hidden;
  /* position: absolute;
  right: 100px;
  top:0;
  width: 38px;
  height: 38px;
  background: url('fullscreen.png') no-repeat;
  background-size: cover; */
}
