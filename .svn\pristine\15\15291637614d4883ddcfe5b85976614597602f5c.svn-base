<template>
  <div class="app-container">
    <div class="content">
      <div class="searchForm">
        <el-form ref="form" :model="queryInfo" label-width="100px" inline>
          <el-form-item label="产品名称">
            <el-input v-model="queryInfo.name" placeholder="请输入产品名称" maxlength="40"></el-input>
          </el-form-item>
          <el-form-item label="上传日期">
            <el-date-picker v-model="date" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd" @change="dateChange">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="创建者">
            <el-input v-model="queryInfo.createUserName" placeholder="请输入创建者姓名"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="getList">查询</el-button>
            <el-button type="primary" plain @click="reset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div v-if="isOperate && isOperate" class="addProduct">
        <div @click="addProduct">
          <i class="el-icon-plus"></i>
          新建产品
        </div>
      </div>
      <div class="tableBox" :style="{ marginTop: !isOperate && !isOperate ? '30px' : '0' }">
        <div class="tbHeader">
          <span class="name" :class="{ lookPermission: !isOperate }">产品名称</span>
          <span class="type">产品所属部门</span>
          <span class="major">专业</span>
          <span class="mode">实验模式</span>
          <span class="date">发布时间</span>
          <span class="createUser">创建者</span>
          <span class="createBug">创建BUG</span>
          <span class="operate">操作</span>
        </div>
        <div class="tbBody">
          <el-collapse v-model="activeNames">
            <el-collapse-item v-for="collapse in collapseList" :key="collapse.name" :name="collapse.name" class="testItem">
              <template slot="title">
                <span :class="`title title${collapse.state}`">{{ collapse.name === 'test' ? '测试区' : collapse.name === 'finalize' ? '定稿区' : '发布区' }}</span>
                <span class="dataLength">{{ collapse.total }}个</span>
              </template>
              <div class="testTable">
                <div v-for="item in collapse.list" :key="item.productId" class="tableRow">
                  <span class="name" :class="{ lookPermission: !isOperate }" @click="productDetails(item, collapse.state)">{{ item.name }}</span>
                  <span class="type">
                    <span :class="{ type2: item.type == 2 }">
                      {{ item.type == 1 ? '产品部' : '虚拟仿真部' }}
                    </span>
                  </span>
                  <span class="major">{{ item.majorName }}</span>
                  <span class="mode">{{ item.mode == 1 ? '考核模式' : item.mode == 2 ? '学习模式' : '-' }}</span>
                  <span class="date">{{ item.releaseTime }}</span>
                  <span class="createUser">{{ item.createUserName }}</span>
                  <span class="createBug" @click="addBug(item)">
                    <i class="el-icon-circle-plus-outline"></i>
                    添加BUG
                  </span>
                  <span class="operate">
                    <span @click="lookDetails(item)"><i class="el-icon-view"></i> 产品动态</span>
                    <span v-if="collapse.state === 1 && isOperate" @click="remove(item)"> <i class="el-icon-delete"></i> 删除</span>
                    <span v-if="collapse.state === 1 && isOperate" @click="edit(collapse, item)"> <i class="el-icon-edit-outline"></i> 修改产品</span>
                    <span v-if="collapse.state !== 3 && isManager" @click="updateState(collapse, item)">
                      <i class="el-icon-rank"></i> {{ collapse.state === 1 ? '移动到定稿区' : collapse.state === 2 ? '移动到发布区' : '-' }}
                    </span>
                  </span>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </div>

    <!-- 删除弹窗 -->
    <el-dialog custom-class="removeDialog" :visible.sync="delDialog" width="413px" top="35vh">
      <template v-slot:title>
        <img src="@/assets/library/hint.png" alt="" class="hint" />
        <span class="hintText">提示</span>
      </template>
      <div class="delText">确定删除该产品吗？</div>
      <div class="operate">
        <span class="closeButton" @click="delDialog = false">取 消</span>
        <span class="confirmButton" @click="confirmDel">确 定</span>
      </div>
    </el-dialog>
    <AddDialog ref="AddDialogRef" :show-dialog.sync="showAddDialog" @success="getList" />
    <AddBug ref="AddBugRef" @success="getList" />
    <!--  产品动态 -->
    <Details ref="DetailsRef" />
    <!-- 产品详情 -->
    <ProductDetails ref="ProductDetailsRef" />
  </div>
</template>
<script>
import { releaseProductList, releaseProductUpdateState, releaseProductRemove } from '@/api/productRelease'
import addDialog from './components/addDialog.vue'
import addBug from './components/addBug.vue'
import details from './components/details.vue'
import productDetails from './components/productDetails.vue'
import { mapGetters } from 'vuex'
export default {
  name: 'ProductRelease',
  components: {
    AddDialog: addDialog,
    AddBug: addBug,
    Details: details,
    ProductDetails: productDetails
  },
  data() {
    return {
      queryInfo: {
        pageNum: 1,
        pageSize: 2000,
        name: null,
        state: null, // 类型 1 测试 2 定稿 3 发布 4 归档
        createUserName: null, // 创建人名称
        startTime: null, // 开始时间(yyyy-mm-dd)
        endTime: null
      },
      date: null,
      activeNames: ['test', 'finalize', 'issue'],
      collapseList: [
        {
          name: 'test',
          state: 1,
          list: [],
          total: 0
        },
        {
          name: 'finalize',
          state: 2,
          list: [],
          total: 0
        },
        {
          name: 'issue',
          state: 3,
          list: [],
          total: 0
        }
      ],
      showAddDialog: false,
      delDialog: false,
      delId: null
    }
  },
  computed: {
    ...mapGetters(['organizationId', 'jobName']),
    isOperate() {
      return this.organizationId === 56612504 || this.organizationId === 56648537
    },
    isManager() {
      return this.jobName === '部门经理'
    }
  },
  created() {
    this.getList()
  },

  methods: {
    getList() {
      // 56612504平台部  56648537虚拟仿真部
      const type = this.organizationId === 56612504 ? 1 : this.organizationId === 56648537 ? 2 : ''

      this.collapseList.forEach((item) => {
        releaseProductList({ ...this.queryInfo, state: item.state, type }).then((res) => {
          const { data } = res
          item.list = [...data.list]
          item.total = data.total
        })
      })
    },
    dateChange(val) {
      if (val) {
        this.queryInfo.startTime = val[0]
        this.queryInfo.endTime = val[1]
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getList()
    },
    reset() {
      this.queryInfo = {
        pageNum: 1,
        pageSize: 2000,
        name: null,
        state: null, // 类型 1 测试 2 定稿 3 发布 4 归档
        createUserName: null, // 创建人名称
        startTime: null, // 开始时间(yyyy-mm-dd)
        endTime: null
      }
      this.date = null
      this.getList()
    },
    addProduct() {
      this.showAddDialog = true
      this.$refs['AddDialogRef'].state = 1
    },
    edit(value, item) {
      this.showAddDialog = true
      this.$refs['AddDialogRef'].showDetalis(value.state, item)
    },
    remove(item) {
      this.delId = item.productId
      this.delDialog = true
    },
    async confirmDel() {
      await releaseProductRemove({ id: this.delId })
      this.$message.success('删除成功')
      this.delDialog = false
      this.getList()
    },
    // 更新产品状态
    async updateState(value, item) {
      await releaseProductUpdateState({ state: value.state + 1, productId: item.productId })
      this.$message.success('更改产品状态成功！')
      this.getList()
    },
    // 新增bug
    addBug(item) {
      this.$refs['AddBugRef'].open(item)
    },
    lookDetails(item) {
      this.$refs['DetailsRef'].open(item)
    },
    productDetails(item, state) {
      // if (item.type === 1) {
      // } else {
      // }
      this.$refs['ProductDetailsRef'].openDialog(item, state)
    }
  }
}
</script>
<style scoped lang="scss">
.app-container {
  width: 100%;
  height: 100%;
  background: #e8eaed;
  .content {
    width: 100%;
    height: 100%;
    background: #ffffff;
    border-radius: 6px 6px 6px 6px;
    overflow: hidden;
    .searchForm {
      display: flex;
      align-items: center;
      width: 100%;
      height: 60px;
      padding: 10px 30px;
      background: #ffffff;
      border-bottom: 1px solid #d9d9d9;
      ::v-deep {
        .el-form {
          .el-form-item {
            margin-bottom: 0;
            .el-form-item__label {
              font-family: PingFang SC, PingFang SC;
              font-weight: 500;
              font-size: 20px;
              color: #333333;
            }
            .el-form-item__content {
              .el-input {
                width: 279px;
                height: 40px;

                .el-input__inner {
                  width: 100%;
                  height: 40px;
                  background: #f2f3f3;
                  border-radius: 4px 4px 4px 4px;
                }
              }
              .el-date-editor {
                background-color: #f2f3f3;
                .el-range-input {
                  background-color: #f2f3f3;
                }
              }
            }
          }
        }
      }
    }
    .addProduct {
      padding-left: 14px;
      padding-top: 14px;

      & > div {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 129px;
        height: 48px;
        border-radius: 27px 27px 27px 27px;
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #333333;
        cursor: pointer;
        &:hover {
          background: #f2f3f3;
        }
        i {
          margin-right: 4px;
          font-size: 18px;
        }
      }
    }
    .tableBox {
      padding: 10px 30px;
      .tbHeader,
      .tableRow {
        display: flex;
        width: 100%;
        height: 64px;
        background: #ffffff;
        border-radius: 8px 8px 8px 8px;
        border: 1px solid #d9d9d9;
        line-height: 64px;
        & > span {
          height: 100%;
          border-right: 1px solid #d9d9d9;
          font-family: PingFang SC, PingFang SC;
          font-weight: 500;
          font-size: 18px;
          color: #666666;
          text-align: center;
          &:last-of-type {
            border-right: none;
          }
        }
        .name {
          width: 320px;
        }
        .lookPermission {
          width: 520px;
        }
        .type {
          width: 150px;
        }
        .mode,
        .major {
          width: 140px;
        }
        .date {
          width: 188px;
        }
        .createUser {
          width: 125px;
        }

        .createBug {
          width: 158px;
        }
        .operate {
          flex: 1;
        }
      }
      .tbBody {
        height: 600px;
        overflow: auto;
        &::-webkit-scrollbar {
          width: 5px;
        }
        &::-webkit-scrollbar-thumb {
          width: 5px;
          border-radius: 5px;
          background: #d9d9d9;
        }
        @media (min-height: 1000px) {
          height: 690px;
        }
        .testTable {
          width: 100%;
          .tableRow {
            width: 100%;
            height: 48px;
            line-height: 48px;
            border: none;
            border-top: 1px solid #d9d9d9;
            border-radius: 0;
            & > span {
              font-size: 16px;
              color: #333333;
            }
            .name {
              font-size: 18px;
              cursor: pointer;
              &:hover {
                color: #3464e0;
              }
            }
            .type {
              display: flex;
              align-items: center;
              justify-content: center;
              & > span {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 68px;
                height: 30px;
                background: #fee7cc;
                border-radius: 4px 4px 4px 4px;
                font-family: PingFang SC, PingFang SC;
                font-weight: 500;
                font-size: 16px;
                color: #333333;
              }
              .type2 {
                width: 100px;
                height: 30px;
                background: #fee5e3;
              }
            }
            .createBug {
              color: #666666;
              cursor: pointer;
              &:hover {
                color: #3464e0;
              }
            }
            .operate {
              color: #3464e0;
              & > span {
                margin-right: 20px;
                cursor: pointer;
              }
              & > span:last-of-type {
                margin-right: 0;
              }
            }
          }
        }
        ::v-deep {
          .el-collapse {
            border: none;
            .testItem {
              border: 1px solid #d9d9d9;
              border-radius: 8px;
              margin-top: 14px;
              overflow: hidden;
              .el-collapse-item__header {
                position: relative;
                display: flex;
                align-items: center;
                padding-left: 52px;
                border-bottom: none;

                .title {
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  width: 74px;
                  height: 32px;
                  background: #f6e5f9;
                  border-radius: 4px;

                  font-family: PingFang SC, PingFang SC;
                  font-weight: 500;
                  font-size: 18px;
                  color: #000000;
                }
                .title2 {
                  background: #e1eaff;
                }
                .title3 {
                  background: #d4f0ec;
                }
                .dataLength {
                  margin-left: 8px;
                  font-family: PingFang SC, PingFang SC;
                  font-weight: 500;
                  font-size: 18px;
                  color: #000000;
                }
                .el-collapse-item__arrow {
                  position: absolute;
                  left: 18px;
                  font-size: 24px;
                }
              }
              .el-collapse-item__wrap {
                border: none;
                .el-collapse-item__content {
                  padding-bottom: 0;
                }
              }
            }
          }
        }
      }
    }
  }
  ::v-deep {
    .removeDialog {
      height: 206px;
      border-radius: 8px !important;
      .el-dialog__header {
        display: flex;
        align-items: center;
        padding-top: 12px;
        padding-left: 24px;
        padding-right: 20px;
        padding-bottom: 8px;
        border-bottom: 1px solid #eeeeef;
        .hint {
          width: 30px;
          height: 30px;
        }
        .hintText {
          margin-left: 6px;
          font-size: 16px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #303a40;
        }
        .el-dialog__headerbtn {
          right: 20px;
          top: 20px;
          .el-dialog__close {
            font-weight: bold;
          }
        }
      }
      .delText {
        margin-top: 10px;
        margin-bottom: 30px;
        text-align: center;
        font-size: 16px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #303a40;
      }
      .operate {
        display: flex;
        justify-content: center;
        .closeButton {
          width: 114px;
          height: 38px;
          line-height: 36px;
          background: #ffffff;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #d8dbe1;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          color: #a3a8bb;
          text-align: center;
          cursor: pointer;
          &:hover {
            background: #f5f5f5;
          }
        }
        .confirmButton {
          margin-left: 32px;
          width: 114px;
          height: 38px;
          line-height: 36px;
          background: #3464e0;
          border-radius: 4px 4px 4px 4px;
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #ffffff;
          text-align: center;
          cursor: pointer;
          &:hover {
            background: #355fce;
          }
        }
      }
    }
  }
}
</style>
