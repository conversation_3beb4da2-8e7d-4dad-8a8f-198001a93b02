<template>
  <div class="app-container">
    <el-form ref="form" :model="queryInfo" inline label-width="80px">
      <el-form-item label="产品编号:">
        <el-input v-model="queryInfo.productCode" size="small" placeholder="请输入产品编号" maxlength="40"></el-input>
      </el-form-item>
      <el-form-item label="产品名称:">
        <el-input v-model="queryInfo.productName" size="small" placeholder="请输入产品名称" maxlength="40"></el-input>
      </el-form-item>
      <el-form-item label="创建人:">
        <el-input v-model="queryInfo.realName" size="small" placeholder="请输入创建人名称" maxlength="40"></el-input>
      </el-form-item>
      <el-form-item label="更新时间:">
        <el-date-picker
          v-model="dateTime"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          size="small"
          @change="dateTimeChange"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button size="small" type="success" @click="getList">查询</el-button>
        <el-button size="small" type="primary" plain @click="reset">重置</el-button>
        <el-button size="small" type="primary" @click="addDialog = true">新增</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" style="width: 100%" border>
      <el-table-column prop="productCode" label="产品编号" align="center" width="width"> </el-table-column>
      <el-table-column prop="productName" label="产品名称" align="center" width="width"> </el-table-column>
      <el-table-column prop="currentNumber" label="当前版本号" align="center" width="width"> </el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center" width="width"> </el-table-column>
      <el-table-column prop="updateTime" label="修改时间" align="center" width="width"> </el-table-column>
      <el-table-column prop="realName" label="创建人" align="center" width="width"> </el-table-column>
      <el-table-column prop="remark" label="备注" align="center" width="width"> </el-table-column>
      <el-table-column label="操作" align="center" width="340">
        <template v-slot="{ row }">
          <el-button size="small" type="warning" @click="edit(row)">修改</el-button>
          <el-button size="small" type="danger" @click="del(row)">删除</el-button>
          <el-button size="small" type="primary" @click="details(row)">发布版本</el-button>
          <el-button size="small" type="info" @click="record(row)">操作记录</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-if="list.length > 0"
      background
      style="text-align: center; margin-top: 15px"
      layout="total, prev, pager, next"
      :page-sizes="[5, 10, 15, 30]"
      :total="total"
      :page-size.sync="queryInfo.pageSize"
      :current-page.sync="queryInfo.pageNum"
      @size-change="getList"
      @current-change="getList"
    />
    <!-- 新增修改弹窗 -->
    <AddOrEdit ref="AddOrEditRef" :show-dialog.sync="addDialog" @refreshList="getList" />
    <!-- 详情弹窗 -->
    <DetailsDialog :id="versionNumberId" :show-dialog.sync="detailsDialog" />
    <!--操作记录弹窗 -->
    <RecordDialog ref="RecordDialog" />
  </div>
</template>
<script>
import { versionNumberList, versionNumberRemove } from '@/api/versionNumber'
import AddOrEdit from './components/AddOrEdit'
import DetailsDialog from './components/DetailsDialog.vue'
import RecordDialog from './components/RecordDialog.vue'
export default {
  name: 'VersionNumber',
  components: {
    AddOrEdit,
    DetailsDialog,
    RecordDialog
  },
  data() {
    return {
      queryInfo: {
        pageNum: 1,
        pageSize: 10,
        productName: null,
        productCode: null,
        realName: null,
        startTime: null,
        endTime: null
      },
      dateTime: null,
      list: [],
      total: 0,
      addDialog: false,
      detailsDialog: false,
      versionNumberId: null
    }
  },
  created() {
    this.getList()
  },
  methods: {
    async getList() {
      const { data } = await versionNumberList(this.queryInfo)
      this.list = data.list
      this.total = data.total
    },
    reset() {
      this.queryInfo = {
        pageNum: 1,
        pageSize: 10,
        productName: null,
        productCode: null,
        realName: null,
        startTime: null,
        endTime: null
      }
      this.dateTime = null
      this.getList()
    },
    dateTimeChange(val) {
      if (val) {
        this.queryInfo.startTime = val[0]
        this.queryInfo.endTime = val[1]
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getList()
    },
    edit(row) {
      this.$refs['AddOrEditRef'].showDetails(row)
      this.addDialog = true
    },
    del(row) {
      this.$confirm('确定要删除该数据吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await versionNumberRemove({ id: row.versionNumberId })
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    details(row) {
      this.versionNumberId = row.versionNumberId
      this.detailsDialog = true
    },
    record(row) {
      this.$refs['RecordDialog'].open(row.versionNumberId)
    }
  }
}
</script>
<style scoped lang="scss"></style>
