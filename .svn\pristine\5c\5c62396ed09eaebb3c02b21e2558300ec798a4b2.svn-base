<template>
  <div class="app-container">
    <div class="box">
      <img src="@/assets/tSalesPlan/header.png" alt="" class="header" />
      <div class="form">
        <div class="add" @click="add"></div>
        <el-form ref="form" label-width="100px" inline>
          <el-form-item label="方案名称:">
            <el-input v-model="queryInfo.name" placeholder="请输入方案名称" maxlength="40" @keydown.enter.native="getList"></el-input>
          </el-form-item>
          <el-form-item v-if="organizationId !== 61311410" label="客户名称:">
            <el-input v-model="queryInfo.customerName" placeholder="请输入客户名称" maxlength="40" @keydown.enter.native="getList"></el-input>
          </el-form-item>

          <el-form-item label="创建人:">
            <el-input v-model="queryInfo.realName" placeholder="请输入创建人" maxlength="40" @keydown.enter.native="getList"></el-input>
          </el-form-item>
          <el-form-item>
            <span class="searchButton search" @click="getList">查询</span>
            <span class="searchButton reset" @click="reset">重置</span>
            <span class="searchButton export" @click="exportList">导出</span>
          </el-form-item>
        </el-form>
      </div>
      <div class="table">
        <el-table :data="list" style="width: 100%" header-cell-class-name="tableHeader" cell-class-name="tableCell">
          <el-table-column prop="name" align="center" label="方案名称" width="width">
            <template v-slot="{ row }">
              <span style="color: #3464e0; cursor: pointer" @click="details(row)">{{ row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="organizationId !== 61311410" prop="customerName" align="center" label="客户名称" width="width"> </el-table-column>
          <el-table-column prop="budget" align="center" label="客户预算（万元）" width="width"> </el-table-column>
          <el-table-column prop="totalMoney" align="center" label="总价（万元）" width="width"> </el-table-column>
          <el-table-column prop="realName" align="center" label="创建人" width="width"> </el-table-column>
          <el-table-column prop="createTime" align="center" label="创建时间" width="width"> </el-table-column>
          <el-table-column prop="prop" align="center" label="操作" width="width">
            <template v-slot="{ row }">
              <div class="operateButtonBox">
                <el-tooltip effect="dark" content="修改" placement="top" :enterable="false">
                  <img class="operateButton" src="@/assets/meeting/edit.png" alt="" @click="edit(row)" />
                </el-tooltip>
                <el-tooltip effect="dark" content="删除" placement="top" :enterable="false">
                  <img class="operateButton" src="@/assets/meeting/del.png" alt="" @click="del(row)" />
                </el-tooltip>
                <el-tooltip effect="dark" content="历史备份记录" placement="top" :enterable="false">
                  <img class="operateButton" src="@/assets/performance/details_list.png" alt="" @click="openDetails(row)" />
                </el-tooltip>
                <el-tooltip effect="dark" content="分享" placement="top" :enterable="false">
                  <img class="operateButton" src="@/assets/tSalesPlan/share.png" alt="" @click="share(row)" />
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          v-if="list.length > 0"
          style="margin-top: 15px; text-align: right"
          layout="total,  prev, pager, next"
          background
          :total="total"
          :page-size.sync="queryInfo.pageSize"
          :current-page.sync="queryInfo.pageNum"
          @size-change="getList"
          @current-change="getList"
        />
      </div>
    </div>
    <!-- 删除弹窗 -->
    <el-dialog :visible.sync="delDialog" custom-class="delDialog" width="413px">
      <template v-slot:title>
        <img src="@/assets/library/hint.png" alt="" class="hint" />
        <span class="hintText">提示</span>
      </template>
      <div class="delText">确定删除该方案吗？</div>
      <div class="operate">
        <span class="closeButton" @click="delDialog = false">取 消</span>
        <span class="confirmButton" @click="confirmDel">确 定</span>
      </div>
    </el-dialog>
    <!-- 历史备份记录 -->
    <historyRecord :id="salesPlanId" ref="historyRecord" :show-dialog.sync="historyRecordDialog" />
    <!-- 分享 -->
    <share ref="share" :show-dialog.sync="shareDialog" />
  </div>
</template>

<script>
import { tSalesPlanList, tSalesPlanRemove, tSalesPlanListExport } from '@/api/tSalesPlan'
import historyRecord from '@/views/tSalesPlan/historyRecord'
import share from '@/views/tSalesPlan/share'
import { mapGetters } from 'vuex'

export default {
  name: '',
  components: {
    historyRecord,
    share
  },
  data() {
    return {
      queryInfo: {
        name: null, // 方案名称
        customerName: null, // 客户名称
        realName: null,
        pageNum: 1,
        pageSize: 10
      },
      list: [],
      total: 0,
      delDialog: false,
      salesPlanId: 0,
      historyRecordDialog: false,
      shareDialog: false
    }
  },
  computed: {
    ...mapGetters(['organizationId'])
  },
  created() {
    this.getList()
  },
  methods: {
    async getList() {
      const { data } = await tSalesPlanList(this.queryInfo)
      this.list = data.list
      this.total = data.total
    },
    add() {
      this.$router.push(`/tSalesPlan/add/0`)
    },
    edit(row) {
      this.$router.push(`/tSalesPlan/add/${row.salesPlanId}`)
    },
    del(row) {
      this.salesPlanId = row.salesPlanId
      this.delDialog = true
    },
    details(row) {
      this.$router.push(`/tSalesPlan/details/${row.salesPlanId}`)
    },
    async confirmDel() {
      await tSalesPlanRemove({ id: this.salesPlanId })
      this.$message.success('删除成功')
      this.delDialog = false
      this.getList()
    },
    reset() {
      this.queryInfo = {
        name: null, // 方案名称
        customerName: null, // 客户名称
        realName: null,
        pageNum: 1,
        pageSize: 10
      }
      this.getList()
    },
    async exportList() {
      const { data } = await tSalesPlanListExport(this.queryInfo)
      const headers = {
        方案名称: 'name',
        客户名称: 'customerName',
        '客户预算(万元)': 'budget',
        '总价(万元)': 'totalMoney',
        创建人: 'realName',
        创建时间: 'createTime'
      }
      const res = this.formatJson(headers, data)
      import('@/vendor/Export2Excel').then((excel) => {
        excel.export_json_to_excel({
          header: Object.keys(headers), // 表头 必填
          data: res, // 具体数据 必填
          filename: '销售方案列表' // 非必填
        })
      })
    },
    formatJson(headers, rows) {
      return rows.map((item) => {
        return Object.keys(headers).map((key) => {
          return item[headers[key]]
        })
      })
    },
    openDetails(row) {
      this.salesPlanId = row.salesPlanId
      this.historyRecordDialog = true
      this.$nextTick(() => {
        this.$refs['historyRecord'].getList()
      })
    },
    share(row) {
      this.$refs['share'].info = { ...row }
      this.shareDialog = true
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  padding: 0;
  padding-top: 24px;
  background: #e8eaed;
  height: 100%;
  .box {
    width: 1754px;
    height: 826px;
    padding-bottom: 15px;
    background: #f5f5f5;
    border-radius: 4px 4px 4px 4px;
    overflow: auto;
    .header {
      width: 100%;
      height: 82px;
    }
    .form {
      display: flex;
      align-items: center;
      width: 100%;
      height: 96px;
      background: #fff;
      padding-left: 40px;
      .add {
        margin-right: 15px;
        width: 116px;
        height: 42px;
        background: url('~@/assets/tSalesPlan/add.png') no-repeat;
        background-size: cover;
        cursor: pointer;
        &:hover {
          background: url('~@/assets/tSalesPlan/add_hover.png') no-repeat;
          background-size: cover;
        }
      }
    }
  }
  ::v-deep {
    .el-form {
      .el-form-item {
        margin-bottom: 0;
        .el-form-item__label {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
        }
        .el-form-item__content {
          .el-input {
            .el-input__inner {
              width: 284px;
              height: 36px;
              background: #ffffff;
              border-color: #d8dbe1;
              &::placeholder {
                font-size: 14px;
                font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                font-weight: 400;
                color: #868b9f;
              }
            }
          }
        }
        .searchButton {
          display: inline-block;
          margin-top: 6px;
          width: 68px;
          height: 28px;
          line-height: 26px;
          font-size: 14px;
          text-align: center;
          border-radius: 4px;
          cursor: pointer;
          border: 1px solid transparent;
        }
        .search {
          margin-left: 25px;
          background: #3464e0;
          border-color: #3464e0;
          color: #fff;
          &:hover {
            background: #355fce;
            border-color: #355fce;
          }
        }
        .reset {
          margin: 0 16px;
          background: #fff;
          border-color: #3464e0;
          color: #3464e0;
          &:hover {
            background: #fff;
            border-color: #355fce;
            color: #355fce;
          }
        }
        .export {
          border-color: #b1bac7;
          background: #fff;
          color: #a3a8bb;
          &:hover {
            background: #f5f5f5;
            color: #a3a8bb;
            border-color: #b1bac7;
          }
        }
      }
    }
  }
  // 表格样式
  ::v-deep {
    .table {
      padding-left: 40px;
      padding-right: 40px;
      .el-table {
        margin-top: 16px;
        &::before {
          display: none;
        }
      }
      .operateButtonBox {
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          cursor: pointer;
        }
      }
      .tableHeader {
        padding-bottom: 0;
        background: #f5f5f5 !important;
        font-size: 14px;
        font-weight: bold;
        color: #0b1a44;
      }
      .tableCell {
        padding: 0;
      }
      .el-table .el-table__body-wrapper .el-table__cell .cell {
        padding: 0;
        height: 56px;
        line-height: 56px;
        background: #fff;
        text-align: center;
      }
      .el-table .el-table__body-wrapper .el-table__row .el-table__cell:first-of-type {
        border-radius: 8px 0 0 8px;
        overflow: hidden;
      }
      .el-table .el-table__body-wrapper .el-table__row .el-table__cell:last-of-type {
        border-radius: 0px 8px 8px 0;
        overflow: hidden;
      }
      // 去掉表格身体背景样式
      .el-table,
      .el-table__expanded-cell {
        background: transparent;
      }
      .el-table .el-table__row {
        height: 56px;
        background: transparent;
      }
      .el-table__body {
        -webkit-border-vertical-spacing: 8px; // 垂直间距
      }
      .el-table td.el-table__cell,
      .el-table th.el-table__cell.is-leaf {
        border: none;
      }
      .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
        background: #eff1f3;
      }
      .el-table td.el-table__cell div {
        width: 100%;
        height: 100%;
      }
      .softwareName {
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #3464e0;
        cursor: pointer;
      }
    }
  }
  // 删除弹窗的样式
  ::v-deep {
    .delDialog {
      height: 206px;
      border-radius: 8px !important;
      .el-dialog__header {
        display: flex;
        align-items: center;
        padding-top: 12px;
        padding-left: 24px;
        padding-right: 20px;
        padding-bottom: 8px;
        border-bottom: 1px solid #eeeeef;
        .hint {
          width: 30px;
          height: 30px;
        }
        .hintText {
          margin-left: 6px;
          font-size: 16px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #303a40;
        }
        .el-dialog__headerbtn {
          right: 20px;
          top: 20px;
          .el-dialog__close {
            font-weight: bold;
          }
        }
      }
      .delText {
        margin-top: 10px;
        margin-bottom: 30px;
        text-align: center;
        font-size: 16px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #303a40;
      }
      .operate {
        display: flex;
        justify-content: center;
        .closeButton {
          width: 114px;
          height: 38px;
          line-height: 36px;
          background: #ffffff;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #d8dbe1;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          color: #a3a8bb;
          text-align: center;
          cursor: pointer;
          &:hover {
            background: #f5f5f5;
          }
        }
        .confirmButton {
          margin-left: 32px;
          width: 114px;
          height: 38px;
          line-height: 36px;
          background: #3464e0;
          border-radius: 4px 4px 4px 4px;
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #ffffff;
          text-align: center;
          cursor: pointer;
          &:hover {
            background: #355fce;
          }
        }
      }
    }
  }
}
</style>
