<template>
  <div class="">
    <div id="chart" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script>
import { map, shanDong } from '@/utils/map'
import { secretSelectWholeCountryMap, secretSelectCityMap } from '@/api/area.js'
import resize from './mixins/resize'

export default {
  name: '',
  mixins: [resize],
  data() {
    return {
      chart: null,
      showMap: true,
      list: [],
      cityList: []
    }
  },
  created() {
    this.getSelectWholeCountryMap()
  },
  mounted() {},
  methods: {
    async getSelectWholeCountryMap() {
      const { data } = await secretSelectWholeCountryMap()
      this.list = data
    },
    init() {
      this.$echarts.registerMap('map', map)
      this.chart = this.$echarts.init(document.getElementById('chart'))
      // 绘制图表
      this.setOpiton(this.list, 116.46, 35.92)
    },
    setOpiton(list, X, Y) {
      const that = this
      // 使用刚指定的配置项和数据显示图表。
      this.chart.setOption({
        visualMap: {
          min: 0, // 最小值
          max: 5, // 最大值
          text: ['5', '客户数量:0'],
          left: 'center',
          //   itemWidth: 140,
          //   itemHeight: 30,
          realtime: false,
          calculable: false,
          orient: 'horizontal',
          inRange: {
            color: ['#e6f7ff', '#1890FF', '#0050b3'] // 渐变颜色
          }
        },
        // tooltip: {
        //   formatter(params) {
        //     console.log(params)
        //   }
        // },
        series: [
          {
            type: 'map',
            map: 'map',
            center: [X, Y],
            // 地图的缩放等级  一次性放大的大小
            zoom: 1,
            // 是否开启鼠标缩放和平移漫游。默认不开启。
            roam: true,
            // coordinateSystem: 'geo',
            itemStyle: {
              normal: {
                // areaColor: '#269dfa'
              }
            },
            label: {
              normal: {
                // 文字选项
                show: false, // 是否显示文字
                textStyle: {
                  fontSize: 12, // 字体大小
                  color: '#fff' // 字体颜色
                }
              },
              emphasis: {
                show: true,
                textStyle: {
                  fontSize: 12, // 字体大小
                  color: '#fff' // 字体颜色
                }
              }
            },
            data: list
          }
        ]
      })
      this.chart.on('click', function (params) {
        if (params.name === '山东') {
          secretSelectCityMap({ provinceName: '山东' }).then((res) => {
            that.cityList = res.data
            that.$nextTick(() => {
              that.setChart()
            })
          })
        }
      })
    },
    setChart() {
      document.getElementById('chart').removeAttribute('_echarts_instance_')
      this.$echarts.registerMap('map', shanDong)
      this.setOpiton(this.cityList, 120.000923, 36.675807)
    }
  }
}
</script>

<style scoped lang="scss"></style>
