<template>
  <div class="app-container">
    <div class="content">
      <el-card v-if="details.productCode" header="详情信息">
        <el-descriptions :column="1">
          <el-descriptions-item label="产品编号">{{ details.productCode }}</el-descriptions-item>
          <el-descriptions-item label="产品名称">{{ details.productName }}</el-descriptions-item>
          <el-descriptions-item label="产品类型">
            <el-tag size="small">{{ details.productType | formattingProductType }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="演示地址">
            <a type="primary" :href="details.perform">{{ details.perform }}</a>
          </el-descriptions-item>
          <el-descriptions-item label="产品描述">{{ details.description }}</el-descriptions-item>
          <el-descriptions-item label="负责人">{{ details.userName }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ details.createTime }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ details.updateTime }}</el-descriptions-item>
        </el-descriptions>
        <el-row :gutter="10" style="border-top: 1px solid #ababab; padding-top: 15px">
          <el-col :span="6"><span style="font-size: 20px; font-weight: 600">人员列表</span></el-col>
        </el-row>
        <el-table :data="details.userDtos ? details.userDtos.slice((pagination.page - 1) * pagination.size, (pagination.page - 1) * pagination.size + pagination.size) : details.userDtos" style="width: 100%; margin-top: 15px" border>
          <el-table-column label="姓名" align="center" prop="realName" />
          <el-table-column label="部门" align="center" prop="organizationName" />
          <el-table-column label="岗位" align="center" prop="jobName" />
        </el-table>
        <el-pagination :page-size.sync="pagination.size" :current-page.sync="pagination.page" :total="details.userDtos ? details.userDtos.length : pagination.total" :page-sizes="[5, 10, 15, 20]" layout=" prev,pager,next,sizes,jumper" style="text-align: center; margin-top: 15px" @size-change="handleSizeChange" @current-change="handleCurrentChange"> </el-pagination>
      </el-card>
      <el-card header="迭代记录">
        <light-timeline v-if="details.iterations && details.iterations.length >= 1" :items="details.iterations" class="lightTimeline">
          <template v-slot:tag="{ item }">
            <div>版本号:{{ item.version }}</div>
            <div>{{ item.time | formatDate }}</div>
            <div>创建人:{{ item.realName }}</div>
          </template>
          <template v-slot:content="{ item }">
            <span>{{ item.description }}</span>
          </template>
        </light-timeline>
      </el-card>
    </div>
    <el-row :gutter="10" type="flex" justify="center" style="margin-top: 35px">
      <el-col :span="1.5">
        <el-button type="primary" @click="$router.push('/product')">关 闭</el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { productDetails } from '@/api/product'

export default {
  name: 'ProductDetails',
  data() {
    return {
      details: {},
      // 添加人员的假数据分页
      pagination: {
        size: 5,
        page: 1,
        total: 0
      },
      activities: [
        {
          content: '活动按期开始',
          timestamp: '2018-04-15'
        },
        {
          content: '通过审核',
          timestamp: '2018-04-13'
        },
        {
          content: '创建成功',
          timestamp: '2018-04-11'
        }
      ]
    }
  },
  created() {
    this.getProductDetails()
  },
  methods: {
    async getProductDetails() {
      const { data } = await productDetails({ productId: this.$route.params.productId })
      this.details = data
      console.log(data)
    },
    // 添加人员的假分页
    handleSizeChange(val) {
      this.pagination.size = val
    },
    handleCurrentChange(val) {
      this.pagination.page = val
    }
  }
}
</script>

<style scoped lang="scss">
.content {
  display: flex;
  justify-content: space-between;
  ::v-deep .el-card {
    width: 49.5%;
  }
}
::v-deep {
  .line-container {
    margin-left: 25px;
    margin-top: 25px;
    &::after {
      left: 115px;
      width: 2px;
    }
    .line-item {
      padding: 0;
      padding-left: 45px;
      margin-top: 15px;
      min-height: 100px;
      max-width: 630px;
      .item-tag {
        top: 0;
        width: 120px;
        text-align: start;
        line-height: 20px;
      }
      .item-symbol {
        left: 19px;
      }
    }
  }
}
</style>
