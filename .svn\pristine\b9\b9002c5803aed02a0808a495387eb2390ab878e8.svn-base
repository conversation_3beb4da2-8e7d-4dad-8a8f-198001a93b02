.clearfix:after {
  content: '\0020';
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.clearfix {
  zoom: 1;
}

.l {
  float: left;
}

.r {
  float: right;
}

.hide {
  display: none;
}

.inline {
  display: inline;
}

.inlinBlock {
  display: inline-block;
}

a.hidefocus {
  outline: 0;
}

button.hidefocus::-moz-focus-inner {
  border: 0;
}

a:focus {
  outline: 0;
  -moz-outline: 0;
}

input,
textarea {
  outline: 0;
}

h2 {
  font-size: 20px;
}

h3 {
  font-size: 16px;
  line-height: 32px;
}

h5 {
  font-size: 14px;
  line-height: 28px;
}

.hide-text {
  text-indent: 100%;
  white-space: nowrap;
  overflow: hidden;
}

.pointer {
  cursor: pointer;
}

.center {
  text-align: center !important;
}

.shadow {
  -webkit-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsistwo {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.boxShadow {
  border-radius: 5px;
  transition: box-shadow 0.3s;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.boxShadow:hover {
  border-radius: 5px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
}

.boxWhiteShadow {
  border-radius: 5px;
  transition: box-shadow 0.3s;
  box-shadow: 0 2px 12px 0 rgba(255, 255, 255, 0.3);
}

.boxWhiteShadow:hover {
  border-radius: 5px;
  box-shadow: 0 2px 12px 0 rgba(255, 255, 255, 0.5);
}

.beautifulScroll::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  background: #f5f5f5;
  border-radius: 5px;
}

.beautifulScroll::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 5px;
}

.beautifulScroll::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background: #999999;
}

.darkScorll::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  background: #cccccc;
  border-radius: 5px;
}

.darkScorll::-webkit-scrollbar-track {
  background: #cccccc;
  border-radius: 5px;
}

.darkScorll::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background: #999999;
}

.hiddenScroll::-webkit-scrollbar-thumb {
  display: none !important;
}

.hiddenScroll::-webkit-scrollbar {
  display: none !important;
}

.hiddenScroll::-webkit-scrollbar-track {
  display: none !important;
}

.scrollmainY {
  overflow-x: hidden;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
}

.unselect {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -webkit-user-select: none;
}

.emptybox {
  width: 100%;
  height: auto;
  box-sizing: border-box;
  text-align: center;
  line-height: 40px;
  font-size: 16px;
  color: #999999;
  position: relative;
  border-radius: 10px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  overflow: hidden;
}

.emptyitem {
  width: 100%;
  text-align: center;
  height: auto;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

.emptybox img {
  max-height: calc(100% - 50px);
  width: auto;
  height: auto;
}

.emptyitem_text {
  position: relative;
  top: -50px;
}

.content_all {
  width: auto;
  height: auto;
  min-width: 1000px;
  max-width: 1500px;
  margin: 0 auto;
  position: relative;
}

.minheight {
  min-height: 800px;
}

.warningColor {
  color: #fd8601;
}

.primaryColor {
  color: #448cf0;
}

.infoColor {
  color: #939393;
}

.successColor {
  color: #67c23a;
}

.errorColor {
  color: #f56c6c;
}

.mainColor {
  color: #2383f9;
}

/*=================0000=================*/

.coverItem {
  position: relative;
  overflow: hidden;
}

.coverItem img {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  height: auto;
  min-height: 100%;
}

.img_back {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: auto;
  min-height: 100%;
}

.hover_active .active {
  display: none;
}

.hover_active:hover .normal,
.hover_active.active .normal {
  display: none;
}

.hover_active:hover .active,
.hover_active.active .active {
  display: block;
}

.like_btn {
  cursor: pointer;
}

.like_btn:hover {
  opacity: 0.85;
}

.editor_box > p {
  margin: 0;
}

.editor_box img {
  max-width: 100%;
}

/*=================table=================*/

.normal_table {
  overflow: hidden;
}

.normal_table .el-table thead {
  color: #ffffff;
}

.normal_table .el-table th.el-table__cell {
  background: #05a68b;
  color: #ffffff;
}

.normal_table .el-table .sort-caret.ascending {
  border-bottom-color: #ffffff;
}

.normal_table .el-table .sort-caret.descending {
  border-top-color: #ffffff;
}

.normal_table .el-table .ascending .sort-caret.ascending {
  border-bottom-color: #e8ad28;
}

.normal_table .el-table .descending .sort-caret.descending {
  border-top-color: #e8ad28;
}

.green_table .el-table thead {
  color: #ffffff;
}

.green_table .el-table th.el-table__cell {
  background: #1d8486;
  color: #ffffff;
}

.green_table .el-table .sort-caret.ascending {
  border-bottom-color: #ffffff;
}

.green_table .el-table .sort-caret.descending {
  border-top-color: #ffffff;
}

.green_table .el-table .ascending .sort-caret.ascending {
  border-bottom-color: #e8ad28;
}

.green_table .el-table .descending .sort-caret.descending {
  border-top-color: #e8ad28;
}

.green_table th.el-table__cell {
  padding: 8px 0 !important;
}

.initial_table th.el-table__cell {
  padding: 8px 0 !important;
}

/*=================head-foot=================*/

.head_box {
  width: 100%;
  height: 75px;
  box-sizing: border-box;
  padding: 0 30px 0 25px;
  position: absolute;
  z-index: 1;
}

.head_logo1 {
  float: left;
  margin: 17px 12px 0 0;
  cursor: pointer;
}

.head_logo2 {
  float: left;
  margin: 29px 40px 0 0;
  cursor: pointer;
}

.head_logo3 {
  float: left;
  margin: 27.5px 12px 0 0;
  cursor: pointer;
}

.head_menus {
  height: 52px;
  width: 1080px;
  float: left;
  margin: 12px 0 0 0;
}

.head_menu {
  width: 125px;
  float: left;
  margin: 0 10px 0 0;
  height: 52px;
  line-height: 52px;
  box-sizing: border-box;
  padding: 0 10px 0 35px;
  text-align: center;
  cursor: pointer;
  font-size: 17px;
  font-family: Source Han Serif CN-Regular, Source Han Serif CN;
  font-weight: 400;
  color: #ffffff;
  letter-spacing: 1px;
  position: relative;
}

.head_menuback {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
}

.head_menuicon {
  position: absolute;
  left: 11px;
  top: 50%;
  height: auto;
  width: 22px;
  transform: translateY(-50%);
}

.head_menu span {
  position: relative;
  z-index: 1;
}

.head_menu:hover,
.head_menu.active {
  color: #055040;
}

.head_user {
  height: 34px;
  float: right;
  width: auto;
  margin: 21px 0 0 0;
  cursor: pointer;
  position: relative;
}

.head_iconlittle {
  float: left;
  height: 44px;
  width: 44px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 8px 0 0;
  position: relative;
}

.head_controltext {
  width: auto;
  float: left;
  margin: 0 12px 0 0;
  height: 34px;
  line-height: 34px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #ffffff;
}

.head_control2_list {
  position: absolute;
  top: 45px;
  right: 0;
  height: auto;
  width: 150px;
  border-radius: 5px;
  box-sizing: border-box;
  padding: 13px 0 8px 0;
  background: #ffffff;
  box-shadow: 0 3px 6px 1px rgba(29, 132, 134, 0.15);
}

.head_control2_list:before {
  content: ' ';
  position: absolute;
  right: 20px;
  top: -8px;
  border-top: none;
  border-bottom: 8px solid #ffffff;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
}

.head_control2_item {
  width: 100%;
  height: 36px;
  box-sizing: border-box;
  padding: 0 0 0 30px;
  line-height: 36px;
  margin: 0 0 5px 0;
  cursor: pointer;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  text-align: center;
  position: relative;
}

.head_control2_item img {
  position: absolute;
  left: 26px;
  top: 50%;
  transform: translateY(-50%);
}

.center_btns {
  text-align: center;
}

.center_btns button {
  margin: 0 20px;
}

.normal_dialog_back {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
  background: rgba(0, 0, 0, 0.6);
}

.normal_dialog {
  width: auto;
  height: auto;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border-radius: 3px;
  background: #ffffff;
  overflow: hidden;
}

.normal_dialog_title {
  height: 51px;
  width: 100%;
  box-sizing: border-box;
  padding: 0 24px;
  line-height: 50px;
  font-size: 16px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #303a40;
  position: relative;
  z-index: 1;
  text-align: center;
}

.green_dialog_title {
  background: #05a68b;
  color: #ffffff;
}

.normal_dialog_close {
  width: 16px;
  height: 16px;
  padding: 18px 35px 30px 20px;
  cursor: pointer;
  position: absolute;
  right: 0;
  top: 0;
  line-height: 16px;
  z-index: 1;
  font-size: 16px;
  text-align: center;
}

.normal_dialog_close img {
  width: 16px;
}

.normal_dialog_close:hover {
  opacity: 0.75;
}

.normal_dialog_body {
  width: 100%;
  height: calc(100% - 115px);
  overflow-y: auto;
  position: relative;
  z-index: 1;
}

.vue-office-docx {
  padding-top: 0;
  overflow-x: hidden;
}
.normal_dialog_foot {
  height: auto;
  width: 100%;
  box-sizing: border-box;
  padding: 12px 24px 20px;
  text-align: right;
  position: relative;
  z-index: 1;
}

.normal_dialog_foot.center {
  text-align: center;
}

.normal_dialog_foot img {
  margin: 0 0 0 24px;
}

.normal_dialog_foot1 {
  width: 130px;
  margin: 0 12px;
  height: 40px;
  line-height: 40px;
  background: #3d73f7;
  border-radius: 4px;
  display: inline-block;
  text-align: center;
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #ffffff;
}

.normal_dialog_foot1.loading {
  background: #a6d2d3;
  cursor: not-allowed;
}

.normal_dialog_foot2 {
  width: 130px;
  margin: 0 0 0 24px;
  height: 40px;
  line-height: 38px;
  border: 1px solid #de6c6c;
  border-radius: 20px;
  display: inline-block;
  text-align: center;
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #de6c6c;
}

.normal_dialog_foot3 {
  width: 130px;
  margin: 0 12px;
  height: 40px;
  line-height: 38px;
  border: 1px solid #6a757b;
  background: #ffffff;
  border-radius: 4px;
  display: inline-block;
  text-align: center;
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #6a757b;
}

.normal_dialog_foot.center .normal_dialog_foot1,
.normal_dialog_foot.center .normal_dialog_foot2,
.normal_dialog_foot.center .normal_dialog_foot3,
.paper_pages.center .normal_dialog_foot3 {
  display: inline-block;
  float: initial;
  margin: 0 12px;
}

.form_textarea {
  float: left;
  width: calc(100% - 120px);
}

.form_textbtn {
  width: 120px;
  float: right;
  box-sizing: border-box;
  padding: 0 0 0 10px;
}

.form_textalert {
  display: inline-block;
  width: calc(100% - 370px);
  margin: 0 0 0 20px;
  color: red;
}

.small_input .ql-editor {
  height: auto;
  min-height: 100px;
  max-height: 200px;
}

.small_input .el-form-item {
  margin: 0 0 20px 0;
  position: relative;
}

.small_inputbtn {
  box-sizing: border-box;
  padding: 20px;
}

.small_input .el-tabs__item {
  height: 36px !important;
  line-height: 36px !important;
}

.normal_fromtn {
  width: 115px;
  height: 32px;
  text-align: center;
  line-height: 32px;
  text-align: center;
  background: linear-gradient(to right, #1d8486, #3e9151);
  color: #ffffff;
  font-size: 15px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #ffffff;
  border-radius: 4px;
}

.info_fromtn {
  background: #cccccc;
}

/*=================public=================*/

.login_box {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.login_left {
  width: calc(100% - 735px);
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  overflow: hidden;
}

.login_leftback {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  min-height: 100%;
  min-width: 100%;
}

.login_leftlogo {
  position: absolute;
  left: 240px;
  top: 100px;
  z-index: 1;
}

.login_main {
  width: 735px;
  height: 100%;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 2;
  background: #ffffff;
}

.login_form {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  box-sizing: border-box;
  padding: 0 115px;
  width: 735px;
  z-index: 1;
  height: auto;
}

.login_formicon {
  height: 30px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 30px;
  color: #000000;
  line-height: 30px;
  letter-spacing: 3px;
  width: 100%;
  margin: 0 0 72px;
  text-align: center;
}

.login_formmain {
  width: 100%;
  height: auto;
  margin: 0 0 65px 0;
}

.login_formpre {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
}

.login_formmain .el-input__inner {
  height: 56px;
  line-height: 56px;
  box-sizing: border-box;
  padding-left: 0;
  border-radius: 0;
  border: none;
}

.login_formmain .el-form-item {
  margin: 0 0 30px 0;
}

.login_formbtn {
  width: 100%;
  height: 60px;
}

.login_formbtn button {
  width: 100%;
  height: 60px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 25px;
  color: #ffffff;
}

.login_foot {
  position: absolute;
  left: 50%;
  bottom: 55px;
  transform: translateX(-50%);
  z-index: 1;
}

.index_box {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.index_title {
  position: absolute;
  left: 50%;
  top: 70px;
  transform: translateX(-50%);
}

.index_foot {
  position: absolute;
  left: 50%;
  bottom: 40px;
  transform: translateX(-50%);
}

.index_main {
  width: 1410px;
  height: 700px;
  position: absolute;
  left: 50%;
  top: 160px;
  margin: 0 0 0 -705px;
  z-index: 1;
}

.index_info {
  height: 220px;
  width: 100%;
  margin: 0 0 25px 0;
}

.index_infouser {
  height: 100%;
  width: 1040px;
  float: left;
  position: relative;
}

.user_info {
  position: absolute;
  left: 42px;
  top: 40px;
  height: 56px;
  width: calc(100% - 455px);
}

.user_infoicon {
  width: 56px;
  height: 56px;
  float: left;
  border-radius: 50%;
  overflow: hidden;
  margin: 4px 12px 0 0;
  position: relative;
}

.user_infoiconP1 {
  float: left;
  height: 31px;
  font-size: 24px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #ffffff;
  line-height: 31px;
  margin: 0 0 15px 0;
  width: calc(100% - 68px);
}

.user_infoiconP2 {
  height: 21px;
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #adb9bb;
  line-height: 21px;
  float: left;
  width: calc(100% - 68px);
}

.user_infoiconP2 span.user_infoiconP2stand {
  margin: 0 12px;
}

.user_infoiconP3 {
  float: left;
  width: calc(100% - 90px);
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.class_time {
  position: absolute;
  left: 42px;
  bottom: 34px;
  height: 37px;
  font-size: 28px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #f2f3f3;
  line-height: 37px;
}

.class_time span {
  font-size: 18px;
  margin: 0 0 0 12px;
}

.user_control {
  position: absolute;
  right: 35px;
  top: 24px;
  height: 24px;
  width: auto;
  z-index: 1;
}

.user_control1 {
  float: right;
  height: 14px;
  margin: 5px 0 0 22px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #259a9d;
  line-height: 14px;
  cursor: pointer;
}

.user_control1 img {
  float: left;
  margin: 0 3px 0 0;
}

.user_control2 {
  width: auto;
  height: 24px;
  float: right;
  cursor: pointer;
  position: relative;
}

.user_iconlittle {
  float: left;
  height: 24px;
  width: 24px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 8px 0 0;
  position: relative;
}

.user_controltext {
  width: auto;
  float: left;
  margin: 0 12px 0 0;
  height: 24px;
  line-height: 24px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #259a9d;
}

.user_controlicon {
  float: left;
  width: 6px;
  height: 100%;
  position: relative;
}

.user_controlicon img {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

.user_control2_list {
  position: absolute;
  top: 35px;
  right: 0;
  height: auto;
  width: 150px;
  border-radius: 5px;
  box-sizing: border-box;
  padding: 13px 0 8px 0;
  background: #ffffff;
}

.user_control2_list:before {
  content: ' ';
  position: absolute;
  right: 20px;
  top: -8px;
  border-top: none;
  border-bottom: 8px solid #ffffff;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
}

.user_control2_item {
  width: 100%;
  height: 36px;
  box-sizing: border-box;
  padding: 0 0 0 30px;
  line-height: 36px;
  margin: 0 0 5px 0;
  cursor: pointer;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  text-align: center;
  position: relative;
}

.user_control2_item img {
  position: absolute;
  left: 26px;
  top: 50%;
  transform: translateY(-50%);
}

.user_control3 {
  width: auto;
  height: 20px;
  float: right;
  margin: 2px 20px 0 0;
  cursor: pointer;
  position: relative;
}

.user_messicon {
  width: 16px;
  height: 20px;
  float: left;
  margin: 0 8px 0 0;
  position: relative;
}

.user_control3_list {
  position: absolute;
  top: 35px;
  right: 0;
  height: auto;
  width: 335px;
  border-radius: 5px;
  box-sizing: border-box;
  padding: 20px 0 0 0;
  background: #ffffff;
}

.user_control3_list:before {
  content: ' ';
  position: absolute;
  right: 20px;
  top: -8px;
  border-top: none;
  border-bottom: 8px solid #ffffff;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
}

.user_control3_title {
  height: 22px;
  width: 100%;
  box-sizing: border-box;
  padding: 0 16px;
  margin: 0 0 15px 0;
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  line-height: 22px;
  letter-spacing: 1px;
}

.user_control3_title .r {
  color: #1d8486;
  letter-spacing: 0;
  cursor: pointer;
}

.user_list_all {
  height: auto;
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
}

.user_list_item {
  width: 100%;
  height: auto;
  box-sizing: border-box;
  padding: 12px 50px;
  position: relative;
}

.user_stis {
  width: 390px;
  height: 65px;
  position: absolute;
  right: 80px;
  top: 90px;
}

.user_stis_item {
  width: 130px;
  float: left;
  height: 65px;
}

.user_stis_itemP1 {
  width: 100%;
  height: 21px;
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #d5dfe1;
  line-height: 21px;
  text-align: center;
  margin: 0 0 12px 0;
}

.user_stis_itemP2 {
  height: 40px;
  font-size: 30px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #ffffff;
  line-height: 40px;
  text-align: center;
}

.user_stis_itemP2.active {
  color: #aeb131;
}

.index_start {
  width: 352px;
  height: 196px;
  float: right;
  margin: 12px 0 0 0;
  position: relative;
}

.index_startbtn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
}

.index_startbtn img {
  float: left;
}

.index_startbtn:hover .hover {
  display: block;
}

.index_startbtn:active .active {
  display: block;
}

.index_startbtn .hover,
.index_startbtn .active,
.index_startbtn:hover .active,
.index_startbtn:hover .normal,
.index_startbtn:active .normal,
.index_startbtn:active .hover {
  display: none;
}

.index_menus {
  width: 100%;
  height: auto;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.index_menu {
  width: 330px;
  height: 202px;
  cursor: pointer;
  position: relative;
  top: 0;
  transition: all 0.3s;
  margin: 0 0 23px 0;
}

.index_menu:hover {
  top: -4px;
}

.center_main {
  width: 1470px;
  margin: 75px auto 0;
  height: calc(100% - 105px);
  background: #ffffff;
  border-radius: 5px;
}

.center_menus {
  width: 330px;
  height: 100%;
  float: left;
  box-sizing: border-box;
  padding: 40px 0;
  overflow-y: auto;
}

.center_menu {
  height: 56px;
  width: 100%;
  text-align: center;
  position: relative;
  line-height: 56px;
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  cursor: pointer;
}

.center_menu img {
  position: absolute;
  left: 90px;
  top: 50%;
  transform: translateY(-50%);
}

.center_menu:hover,
.center_menu.active {
  background: #f2f3f3;
}

.center_box {
  width: calc(100% - 330px);
  float: right;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
  padding: 35px 40px 0 40px;
  position: relative;
}

.center_title {
  height: 30px;
  font-size: 24px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #1a1a1a;
  line-height: 35px;
  text-align: center;
  margin: 0 0 25px 0;
}

.center_form {
  width: 100%;
  height: auto;
  box-sizing: border-box;
  padding: 0 320px 0 0;
  height: calc(100% - 65px);
  position: relative;
}

.center_forminfo {
  width: 100%;
  height: 100%;
  overflow-y: auto;
}

.center_formitem {
  height: auto;
  width: 100%;
  margin: 0 0 30px 0;
}

.center_itemlabel {
  height: 20px;
  font-size: 15px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  line-height: 20px;
}

.center_iteminput {
  width: 100%;
  height: 51px;
  line-height: 50px;
  box-sizing: border-box;
  border-bottom: 1px solid #adb9bb;
  font-size: 15px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #303a40;
}

.center_itemradio {
  width: 100%;
  height: 50px;
  box-sizing: border-box;
  padding: 13px 0 0 0;
  box-sizing: border-box;
}

.center_radioitem {
  width: 55px;
  float: left;
  margin: 0 40px 0 0;
  height: 24px;
}

.center_radioite1 {
  width: 24px;
  height: 24px;
  float: left;
  position: relative;
}

.center_radioite1 img {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}

.center_radioite1 img.active {
  display: none;
}

.center_radioite1.active img.normal {
  display: block;
}

.center_radioite1.active img.active {
  display: block;
}

.center_radioite2 {
  float: right;
  height: 24px;
  position: relative;
}

.center_radioite2 img {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}

.center_icon {
  position: absolute;
  right: 65px;
  top: 120px;
  width: 140px;
  height: 170px;
}

.center_iconit {
  width: 106px;
  height: 106px;
  margin: 0 auto 24px;
  overflow: hidden;
  border-radius: 50%;
  overflow: hidden;
}

.center_iconit img {
  width: 100%;
  height: auto;
  min-height: 100%;
}

.center_iconbtn {
  width: 100%;
  height: 40px;
  cursor: pointer;
}

.center_iconbtn:hover {
  opacity: 0.8;
}

.all_cont {
  width: calc(100% - 30px);
  height: calc(100% - 105px);
  margin: 75px auto 0;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  overflow: hidden;
}

.all_contposition {
  position: relative;
}

.real_cont {
  width: 1466px;
  height: 100%;
  margin: 0 auto;
  box-sizing: border-box;
  padding: 28px 0 55px;
}

.real_cont_files {
  width: 100%;
}

.real_label {
  height: 40px;
  width: 100%;
  margin: 0 0 4px 0;
}

.real_label span.real_label_text {
  display: inline-block;
  height: 40px;
  line-height: 40px;
  font-size: 24px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
}

.real_label img.real_label_btn {
  position: relative;
  margin: 0 0 0 12px;
  top: -7px;
}

.real_input {
  width: 100%;
  height: 34px;
  margin: 0 0 24px 0;
}

.real_inputitem {
  width: 280px;
  height: 100%;
  display: flex;
  float: left;
}

.real_inputitem.large {
  width: 600px;
}

.real_inputitem.more {
  width: 500px;
}

.real_inputitem.small {
  width: 190px;
}

.real_inputitem.catch {
  width: 210px;
}

.real_inputitem.caps {
  width: 250px;
}

.real_inputitem.auto {
  width: auto;
}

.real_inputlabel {
  height: 34px;
  line-height: 34px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  white-space: nowrap;
}

.real_inputit {
  width: 100%;
  height: 34px;
  line-height: 34px;
}

.topics_input {
  float: right;
  width: auto;
}

.real_inputit .el-input__inner,
.real_inputit .el-cascader {
  height: 34px;
  line-height: 34px;
}

.real_inputit .el-input__icon {
  line-height: 34px;
}

.real_inputit .el-select,
.real_inputit .el-input,
.real_inputit .el-cascader {
  width: 100% !important;
}

.real_inputitem.large .real_inputit .el-input,
.real_inputitem.large .real_inputit .el-input__wrapper,
.real_inputitem.large .real_inputit .el-input__inner {
  background: #e7eaf0;
  border-radius: 17px;
}

.real_btn {
  width: 64px;
  height: 32px;
  margin: 1px 12px 0 0;
  float: left;
}

.normal_dialog .real_btn img {
  width: 62px;
}

.topic_btns {
  width: auto;
  height: 30px;
  box-sizing: border-box;
  border-radius: 15px;
  background: rgba(29, 132, 134, 0.2);
  margin: 0 0 12px 0;
  display: inline-block;
}

.topic_btn {
  width: 105px;
  height: 30px;
  box-sizing: border-box;
  padding: 7px 0;
  text-align: center;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #1d8486;
  line-height: 16px;
  float: left;
  cursor: pointer;
  position: relative;
}

.topic_btn span {
  display: inline-block;
  height: 16px;
  line-height: 16px;
  position: relative;
  top: -2px;
}

.topic_btn img {
  height: 16px;
  margin: 0 4px 0 0;
  display: inline-block;
}

.topic_btn:hover {
  opacity: 0.75;
}

.topic_btn:before {
  content: ' ';
  position: absolute;
  left: -0.5px;
  height: 16px;
  width: 1px;
  top: 50%;
  margin: -8px 0 0 0;
  background: #8ac3c4;
}

.topic_btn:first-child:before {
  display: none;
}

.topic_tables {
  width: 100%;
  height: calc(100% - 195px);
}

.topic_pages {
  width: 100%;
  text-align: center;
  padding: 30px 0 0 0;
  display: flex;
  justify-content: center;
}

.topic_pages.nopad {
  padding: 0;
}

.table_btn {
  box-sizing: border-box;
  padding: 0 10px;
  text-align: center;
  font-size: 15px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #1d8486;
  line-height: 20px;
  cursor: pointer;
}

.table_btn_news {
  white-space: nowrap;
  text-align: left;
  padding: 5px 0 0 0;
  height: 40px;
}

.table_btn_new {
  box-sizing: border-box;
  padding: 5px 10px;
  text-align: center;
  font-size: 15px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  line-height: 20px;
  cursor: pointer;
  border-radius: 8px;
  margin: 0 10px 0 0;
  float: left;
}

.table_line_center {
  display: inline-block;
  height: 25px;
  padding: 0 10px;
  background: rgba(35, 131, 249, 0.3);
  color: #2383f9;
  box-sizing: border-box;
  text-align: center;
  font-size: 15px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  line-height: 25px;
  cursor: pointer;
  border-radius: 8px;
  height: auto;
}

.table_btn_new1 {
  background: rgba(35, 131, 249, 0.3);
  color: #2383f9;
}

.table_btn_new2 {
  background: #ececed;
  color: #3d3d3d;
}

.table_btn_new3 {
  background: linear-gradient(270deg, rgba(146, 194, 253, 0.5) 0%, #ff8eb2 100%);
  color: #ffffff;
}

.table_btn_share {
  width: 90px;
  display: inline-block;
}

.topic_info {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 55px 0 0 0;
  position: relative;
}

.topic_info_title {
  height: 55px;
  width: 100%;
  border-bottom: 1px solid #cccccc;
  line-height: 55px;
  font-size: 18px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  box-sizing: border-box;
  padding: 0 0 0 35px;
  position: absolute;
  top: 0;
}

.topic_info_title:before {
  content: ' ';
  position: absolute;
  left: 22px;
  top: 50%;
  height: 16px;
  width: 5px;
  border-radius: 2.5px;
  margin: -8px 0 0 0;
  background: #1d8486;
}

.topic_info_body {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 20px 30px 0;
  overflow-y: auto;
}

.topic_info_type {
  height: 40px;
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #1d8486;
  line-height: 35px;
}

.topic_dialog_body {
  box-sizing: border-box;
  padding: 20px 70px;
  max-height: 650px;
}

.syllabus_dialog_body {
  box-sizing: border-box;
  padding: 20px 20px 10px;
  max-height: 320px;
}

.select_file_body {
  box-sizing: border-box;
  padding: 0 30px;
  position: initial;
}

.topic_edit {
  height: 785px;
  width: 1100px;
  max-height: calc(100% - 50px);
  max-width: 100%;
}

.topic_main {
  width: 100%;
  height: auto;
}

.topic_type_selects {
  width: 100%;
  height: 32px;
}

.topic_type_select {
  float: left;
  width: auto;
  height: 32px;
  background: #c2cfd1;
  width: 130px;
  margin: 0 24px 0 0;
  text-align: center;
  line-height: 32px;
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  border-radius: 5px;
}

.topic_type_select:hover,
.topic_type_select.active {
  background: linear-gradient(to right, #1d8486, #3e9151);
  color: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.14);
}

.topic_mainit {
  height: 32px;
}

.topic_questionType {
  width: auto;
  height: 20px;
  float: left;
  margin: 3px 12px 0 10px;
  line-height: 20px;
  box-sizing: border-box;
  padding: 0 0 0 24px;
  position: relative;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  cursor: pointer;
}

.topic_questionlabel {
  height: 20px;
  width: 20px;
  position: absolute;
  left: 0;
  top: 0;
  box-sizing: border-box;
  border: 1px solid #707070;
  border-radius: 50%;
  overflow: hidden;
}

.topic_questionlabel img {
  width: 100%;
  display: none;
}

.topic_questionType.active .topic_questionlabel {
  border: none;
}

.topic_questionType:hover .topic_questionlabel img,
.topic_questionType.active .topic_questionlabel img {
  display: block;
}

.topic_main .el-form-item__label {
  text-align: left;
}

.topic_upload {
  height: 515px;
  width: 735px;
  max-height: calc(100% - 100px);
  max-width: 100%;
  position: relative;
}

.upload_box {
  width: 100%;
  height: 150px;
  margin: 10px 0 25px 0;
  background: #ffffff;
  border-radius: 8px;
  border: 3px dashed #1d8486;
  cursor: pointer;
  position: relative;
}

.upload_box:hover,
.upload_box.active {
  border: 3px dashed #3e9151;
}

.upload_boxinfo {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: auto;
  height: auto;
  height: 62px;
}

.upload_boxinfo img {
  float: left;
  margin: 4px 45px 0 0;
}

.upload_boxtext {
  height: 100%;
  float: left;
}

.upload_boxtextP1 {
  height: 30px;
  font-size: 22px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #304243;
  line-height: 30px;
  margin: 0 0 12px 0;
}

.upload_boxtextP2 {
  height: 21px;
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #304243;
  line-height: 21px;
}

.upload_file {
  width: 100%;
  height: auto;
  background: #ffffff;
  box-shadow: 0px 0px 20px 1px rgba(0, 0, 0, 0.16);
  box-sizing: border-box;
  padding: 11px 20px 0;
}

.upload_fileitem {
  margin: 0 0 16px 0;
}

.upload_fileP1 {
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #304243;
  line-height: 19px;
  height: 19px;
  margin: 0 0 13px 0;
  width: 100%;
  float: left;
  box-sizing: border-box;
  padding: 0 35px 0 0;
  position: relative;
}

.upload_fileP1.active {
  padding: 0 90px 0 0;
}

.upload_fileP1del {
  width: 35px;
  position: absolute;
  line-height: 19px;
  height: 19px;
  right: 0;
  top: 0;
  color: #de6c6c;
  text-align: center;
  cursor: pointer;
}

.upload_fileP1open {
  width: 35px;
  position: absolute;
  line-height: 19px;
  height: 19px;
  right: 45px;
  top: 0;
  color: #1d8486;
  text-align: center;
  cursor: pointer;
}

.upload_fileP1suc {
  width: 35px;
  position: absolute;
  line-height: 19px;
  height: 19px;
  right: 0;
  top: 0;
  color: #67c23a;
  cursor: pointer;
}

.upload_fileP2 {
  width: 100%;
  float: left;
  height: 6px;
  border: 1px solid #c2cfd1;
  border-radius: 3px;
  margin: 0 0 13px 0;
  position: relative;
}

.upload_fileP3 {
  width: 100%;
  float: left;
  height: 430px;
  margin: 0 auto;
  overflow: hidden;
}

.upload_fileP3 video,
.upload_fileP3 iframe {
  width: 100%;
  height: 100%;
}

.upload_fileP3 video {
  background: #000000;
}

.upload_fileP2info {
  position: absolute;
  width: 0;
  height: 6px;
  border-radius: 3px;
  left: 0;
  top: 0;
  background: linear-gradient(to right, #1d8486, #3e9151);
}

.normal_dialog_foottip {
  float: left;
  margin: 10px 24px 0 0;
  height: 20px;
  font-size: 15px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #1d8486;
  line-height: 20px;
}

.topic_score {
  height: 410px;
  width: 615px;
  max-height: calc(100% - 100px);
  max-width: 100%;
  position: relative;
}

.topic_dialog_tip {
  font-size: 13px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #de6c6c;
  line-height: 20px;
  height: 20px;
  margin: 0 0 30px 0;
}

.resources_tabs {
  float: left;
  width: auto;
  height: 40px;
  margin: 0 0 0 25px;
}

.resources_tab {
  width: 130px;
  height: 40px;
  line-height: 35px;
  text-align: center;
  cursor: pointer;
  position: relative;
  font-size: 20px;
  font-family: Source Han Serif CN-Bold, Source Han Serif CN;
  font-weight: bold;
  color: #303a40;
  letter-spacing: 1px;
  float: left;
}

.resources_tab:before {
  content: ' ';
  position: absolute;
  left: 50%;
  bottom: 0;
  width: 18px;
  height: 4px;
  border-radius: 2px;
  transform: translateX(-50%);
  background: #1d8486;
  display: none;
}

.resources_tab:hover,
.resources_tab.active {
  color: #1d8486;
}

.resources_tab:hover:before,
.resources_tab.active:before {
  display: block;
}

.fils_main {
  width: 100%;
  height: calc(100% - 64px);
}

.fils_delete_main {
  width: 100%;
  height: calc(100% - 64px);
  overflow-y: auto;
}

.resources_btns {
  width: 100%;
  height: 40px;
  margin: 0 0 16px 0;
  box-sizing: border-box;
  padding: 0 0 0 136px;
  position: relative;
}

.resources_share_btns {
  padding: 0;
}

.resources_btn1 {
  position: absolute;
  left: -8px;
  top: -6px;
}

.fils_btns {
  margin: 6px 0 0 0;
  float: left;
  width: auto;
}

.fils_input {
  float: right;
  margin: 3px 0 0 0;
  width: 760px;
}

.fils_nav {
  height: 46px;
  width: 100%;
  box-sizing: border-box;
  border-top: 1px solid #d5dfe1;
  box-sizing: border-box;
  padding: 0 0 0 80px;
  position: relative;
  font-size: 14px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #303a40;
  line-height: 46px;
}

.fils_nav.nobor {
  border: none;
}

.fils_navarr {
  position: absolute;
  left: 0;
  top: 12px;
  height: 24px;
}

.fils_navarritem {
  float: left;
  margin: 0 12px 0 0;
  cursor: pointer;
  height: 24px;
}

.fils_navarritem img {
  height: 24px;
  float: left;
}

.fils_navarritem img.active {
  display: none;
}

.fils_navarritem.active img.normal {
  display: none;
}

.fils_navarritem.active img.active {
  display: block;
}

.fils_list {
  width: 100%;
  height: calc(100% - 162px);
  overflow: hidden;
}

.fils_name {
  max-width: calc(100% - 60px);
  width: auto;
  display: inline-block;
  height: 28px;
  margin: 0 0 0 30px;
  box-sizing: border-box;
  padding: 0 30px 0 32px;
  line-height: 28px;
  font-size: 15px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #1d8486;
  position: relative;
  float: left;
  text-align: left;
}

.fils_nametype {
  height: 20px;
  width: auto;
  position: absolute;
  left: 0;
  top: 4px;
}

.fils_nameedit {
  position: absolute;
  right: 0;
  top: 5px;
  height: 20px;
  display: none;
  cursor: pointer;
}

.fils_name:hover .fils_nameedit {
  display: block;
}

.name_class {
  padding: 10px 0 !important;
}

.name_class .el-input__inner {
  height: 26px;
  line-height: 26px;
}

.reource_delete {
  width: 485px;
  height: auto;
  max-height: calc(100% - 100px);
  max-width: 100%;
  position: relative;
}

.reource_show {
  width: 715px;
  height: auto;
  max-height: calc(100% - 100px);
  max-width: 100%;
  position: relative;
}

.reource_delete1 {
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #84861d;
  line-height: 28px;
  letter-spacing: 1px;
  margin: 15px 0 10px;
  text-align: center;
}

.reource_delete2 {
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  line-height: 28px;
  letter-spacing: 1px;
  text-align: center;
  margin: 0 0 10px;
}

.reource_delete3 {
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #2383f9;
  line-height: 28px;
  letter-spacing: 1px;
  text-align: center;
  margin: 0 0 10px;
}

.reource_link {
  height: 280px;
  width: 615px;
  max-height: calc(100% - 100px);
  max-width: 100%;
  position: relative;
}

.reource_linksmall {
  height: auto;
  width: 505px;
  max-width: 100%;
  position: relative;
}

.reource_move {
  height: 530px;
  width: 570px;
  max-height: calc(100% - 100px);
  max-width: 100%;
  position: relative;
}

.reource_move_body {
  box-sizing: border-box;
  padding: 30px;
}

.reource_move_tree {
  width: 100%;
  height: calc(100% - 30px);
  overflow-y: auto;
  box-sizing: border-box;
  border: 1px solid #adb9bb;
  border-radius: 5px;
}

.reource_move_tree .el-tree-node__content {
  height: 32px;
}

.custom_tree_node {
  width: 100%;
  height: auto;
  min-height: 32px;
  line-height: 32px;
}

.custom_tree_node img {
  position: relative;
  top: -2px;
  margin: 0 4px 0 0;
}

.reource_move_tree .el-tree-node.is-current > .el-tree-node__content {
  background-color: #e8eeee !important;
  color: #303a40;
}

.textbook_main {
  width: 100%;
  height: calc(100% - 126px);
  max-height: 555px;
  border-radius: 8px;
  background: #ffffff;
  box-shadow: 0 3px 6px 1px rgba(29, 132, 134, 0.11);
  box-sizing: border-box;
  padding: 24px 45px 0;
  overflow-y: auto;
}

.textbook_label {
  height: 21px;
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #6a757b;
  line-height: 21px;
  margin: 0 0 24px 0;
}

.textbook_list {
  width: 100%;
  height: auto;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
}

.textbook_item {
  width: calc(25% - 18px);
  height: 200px;
  margin: 0 9px 32px;
  background: #f2f3f3;
  border-radius: 5px;
  cursor: pointer;
  box-sizing: border-box;
  border: 1px solid #f2f3f3;
  padding: 33px 12px 0 150px;
  position: relative;
}

.textbook_item:hover {
  border: 1px solid #1d8486;
  box-shadow: 0 3px 6px 1px rgba(29, 132, 134, 0.15);
}

.textbook_itemicon {
  width: 130px;
  height: 144px;
  left: 16px;
  top: 24px;
  position: absolute;
}

.textbook_itemicon span {
  position: absolute;
  z-index: 1;
  top: 50%;
  left: 13%;
  width: 80%;
  text-align: center;
  font-size: 16px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #d5dfe1;
  line-height: 21px;
  word-break: break-all;
  transform: translateY(-50%);
}

.textbook_itemp1 {
  height: 22px;
  font-size: 17px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #303a40;
  line-height: 22px;
  margin: 0 0 13px 0;
}

.textbook_itemp2 {
  height: 18px;
  font-size: 14px;
  font-family: Microsoft YaHei-Light, Microsoft YaHei;
  font-weight: 300;
  color: #6a757b;
  line-height: 18px;
  text-indent: 5px;
  margin: 0 0 55px 0;
}

.textbook_itemp2 i {
  font-size: 16px;
}

.textbook_itemp3 {
  height: 17px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #6a757b;
  line-height: 17px;
  box-sizing: border-box;
  padding: 0 0 0 30px;
  position: relative;
}

.textbook_itemp3 img {
  position: absolute;
  left: 5px;
  top: 0px;
}

.textbook_top {
  height: 54px;
  width: 100%;
  margin: 0 auto;
  position: relative;
  border-bottom: 1px solid #e4e4e4;
  box-sizing: border-box;
  padding: 0 35px 0 60px;
  position: relative;
}

.textbook_top:before {
  content: ' ';
  position: absolute;
  left: 50px;
  top: 50%;
  height: 18px;
  width: 1px;
  transform: translateY(-50%);
  background: #e4e4e4;
}

.textbook_tophead {
  position: absolute;
  left: 50px;
  top: 0;
}

.textbook_toplabel {
  width: 275px;
  float: left;
  height: 60px;
  line-height: 60px;
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #6a757b;
  position: relative;
  box-sizing: border-box;
  padding: 0 0 0 5px;
}

.textbook_toplabel img {
  position: absolute;
  left: -27px;
  top: 50%;
  transform: translateY(-50%);
}

.textbook_desc {
  float: left;
  height: 54px;
  box-sizing: border-box;
  font-size: 17px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #000000;
  line-height: 54px;
}

.textbook_descitem {
  width: auto;
  margin: 0 40px 0 0;
  float: left;
  height: 26px;
  line-height: 26px;
  font-size: 16px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #303a40;
}

.textbook_descitem img {
  float: left;
}

.textbook_bottom {
  height: calc(100% - 54px);
  width: 100%;
  margin: 0 auto;
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
}

.textbook_bottom.classin {
  width: calc(100% - 125px);
  margin: 0 90px 0 35px;
}

.textbook_bottom.adding {
  width: 100%;
  height: 100%;
  margin: 0;
}

.paper_bottom {
  height: calc(100% - 110px);
  width: 100%;
  margin: 0 auto;
  position: relative;
}

.textbook_bottom_left {
  width: 390px;
  height: 100%;
  float: left;
  box-sizing: border-box;
  border-right: 1px solid #e4e4e4;
  padding: 10px 0 0 0;
}

.noradius {
  border-radius: 0;
}

.textbook_bottom_leftall {
  height: 100%;
}

.textbook_bottom_leftselect {
  border: 3px solid #f2f3f3;
  background: #f2f3f3;
}

.textbook_left_search {
  height: 40px;
  width: calc(100% - 20px);
  margin: 0 auto;
  line-height: 40px;
  font-size: 18px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #333333;
  text-align: center;
  position: relative;
}

.textbook_left_search .el-input__inner {
  border-radius: 0;
  border: none;
  background: transparent;
  padding: 0 10px;
  line-height: 32px;
  height: 32px;
}

.textbook_bottom_left .el-input__inner {
  color: #333333;
}

.class_left_search .el-input__inner {
  color: #333333;
}

.textbook_left_control {
  height: 40px;
  width: 40px;
  line-height: 42px;
  font-size: 18px;
  text-align: center;
  position: absolute;
  right: 0;
  top: 0;
  background: #eff3ff;
  border-radius: 4px 4px 4px 4px;
  cursor: pointer;
  box-sizing: border-box;
  padding: 0 5px 0 0;
}

.textbook_left_controlbottom {
  font-size: 10px;
  position: absolute;
  right: 4px;
  top: 50%;
  transform: translateY(-50%);
}

.textbook_left_controlit {
  float: left;
  height: 20px;
  text-align: left;
  margin: 0 12px 0 0;
  line-height: 20px;
  font-size: 12px;
  color: #9e9e9e;
  cursor: pointer;
}

.textbook_left_controlit img {
  float: left;
  margin: 0 5px 0 0;
}

.textbook_left_controlit:hover {
  color: #2383f9;
}

.textbook_left_list {
  width: 100%;
  height: calc(100% - 40px);
  overflow-y: auto;
  box-sizing: border-box;
  padding: 0 10px 0 5px;
  margin: 0 auto;
  background: #ffffff;
}

.textbook_left_list.classin {
  height: calc(100% - 110px);
  margin: 0 auto 15px;
}

.textbook_left_list.view {
  height: 100%;
  margin: 0 auto;
}

.textbook_chapter {
  width: 100%;
  height: auto;
  min-height: 40px;
  margin: 0 0 5px 0;
  position: relative;
  z-index: 1;
}

.textbook_chapterinfo {
  width: 100%;
  height: auto;
  min-height: 40px;
  box-sizing: border-box;
  line-height: 30px;
  padding: 5px 0 5px 30px;
  position: relative;
  font-size: 14px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #333333;
}

.textbook_chapterinfo.course_text {
  color: #303a40;
  padding: 5px 95px 5px 30px;
}

.textbook_chapterinfo:hover {
  color: #2383f9;
}

.textbook_chapterinfo.course_text:hover,
.textbook_chapterinfo.course_text.active {
  color: #2383f9;
}

.textbook_chapterinfo.select_text {
  padding: 5px 40px 5px 30px;
}

.textbook_chapterinfoall {
  padding: 5px 0 5px 30px;
}

.textbook_chapteropen {
  height: 30px;
  width: 30px;
  text-align: center;
  line-height: 30px;
  font-size: 18px;
  cursor: pointer;
  position: absolute;
  left: 0;
  bottom: 5px;
}

.textbook_chapteropen img {
  height: 18px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.textbook_sections {
  width: 100%;
  height: auto;
  min-height: 35px;
  box-sizing: border-box;
  padding: 0 0 0 20px;
  position: relative;
}

.textbook_sections:before {
  content: ' ';
  position: absolute;
  left: 29px;
  top: 50%;
  transform: translateY(-50%);
  height: calc(100% - 35px);
  width: 0;
  border-left: 1px dashed #cccccc;
  display: none;
}

.textbook_sections1 {
  position: absolute;
  top: 50%;
  left: 0;
  width: 9px;
  height: 0;
  border-top: 1px dashed #cccccc;
  z-index: 1;
  display: none;
}

.textbook_sectioninfo {
  width: 100%;
  height: auto;
  min-height: 35px;
  box-sizing: border-box;
  line-height: 35px;
  padding: 0 0 0 15px;
  position: relative;
  font-size: 15px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #4d4d4d;
}

.textbook_sectioninfo.course_text {
  color: #303a40;
  padding: 0 65px 0 15px;
}

.textbook_sectioninfo.select_text {
  padding: 0 40px 0 15px;
}

.textbook_sectioninfo:hover {
  color: #2383f9;
}

.textbook_sectioninfo.active {
  color: #2383f9;
  background: #f2f3ff;
}

.textbook_sectioninfo.course_text:hover,
.textbook_sectioninfo.course_text.active {
  /*background: rgba(29, 132, 134, 0.12);*/
  color: #2383f9;
}

.textbook_sectionicons {
  position: absolute;
  right: 0;
  top: 50%;
  margin: -15px 0 0 0;
  height: 30px;
  width: 60px;
}

.textbook_chaptericons {
  position: absolute;
  right: 0;
  top: 50%;
  margin: -15px 0 0 0;
  height: 30px;
  width: 90px;
}

.textbook_chaptericon {
  float: left;
  height: 30px;
  width: 30px;
  text-align: center;
  line-height: 30px;
  font-size: 18px;
  cursor: pointer;
}

.textbook_chaptericon:hover {
  color: #1482f0;
}

.textbook_left_info {
  width: 100%;
  height: auto;
  position: relative;
}

.textbook_left_info:before {
  content: ' ';
  position: absolute;
  top: 50%;
  left: 16px;
  transform: translateY(-50%);
  width: 0;
  height: calc(100% - 45px);
  border-left: 1px dashed #cccccc;
  display: none;
}

.textbook_left_info.none:before {
  display: none;
}

.textbook_bottom_right {
  width: calc(100% - 390px);
  height: 100%;
  background: #ffffff;
  float: right;
  position: relative;
}

.textbook_bottom_right.classin {
  box-sizing: border-box;
  padding: 0 0 70px 0;
}

.textbook_right_newmenu {
  height: 70px;
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  background: #d9e2ea;
}

.textbook_right_newmenuinfo {
  box-sizing: border-box;
  padding: 18px 0 0 0;
  text-align: center;
  width: calc(100% - 355px);
  height: 100%;
  box-sizing: border-box;
  float: left;
}

.textbook_right_newmenuitem {
  width: auto;
  margin: 0 auto;
  display: inline-block;
}

.textbook_right_info {
  width: calc(100% - 300px);
  height: 100%;
  box-sizing: border-box;
  float: left;
}

.textbook_right_infoall {
  width: 100%;
}

.textbook_right_page {
  height: 30px;
  width: 100%;
  background: #67654e;
  box-sizing: border-box;
  padding: 0 0 0 16px;
}

.textbook_right_file {
  background: #4e6868;
  color: #ffffff;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #ffffff;
  line-height: 30px;
}

.textbook_right_file img {
  float: left;
  margin: 5px 5px 0 10px;
}

.textbook_right_pageicon {
  float: left;
  margin: 2px 2px 0 0;
}

.textbook_right_text {
  float: left;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #dfd9bd;
  line-height: 30px;
  margin: 0 30px 0 0;
}

.textbook_right_jump {
  float: left;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #dfd9bd;
  line-height: 30px;
}

.textbook_right_jump .el-input--mini .el-input__inner {
  height: 24px;
  line-height: 24px;
}

.textbook_right_jump .el-input-number {
  width: 72px;
  margin: 0 5px;
}

.textbook_right_np {
  float: left;
  box-sizing: border-box;
  height: 30px;
  line-height: 30px;
  margin: 0 30px 0 30px;
}

.textbook_right_npicon {
  float: left;
  font-size: 14px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #666666;
}

.textbook_right_npicon:hover {
  color: #2383f9;
}

.textbook_right_npinner {
  width: 80px;
  margin: 0 10px;
  float: left;
  box-sizing: border-box;
  border: 1px solid #6d6d6d;
  border-radius: 40px;
  height: 30px;
  line-height: 28px;
  font-size: 14px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #333333;
  text-align: center;
}

.textbook_right_np .el-input--mini .el-input__inner {
  height: 30px;
  line-height: 30px;
}

.textbook_right_np .el-input-number {
  width: 50px;
  margin: 0 5px;
  float: left;
}

.textbook_right_tips {
  float: right;
  width: 115px;
  height: 30px;
  text-align: center;
  cursor: pointer;
  font-size: 15px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #dfd9bd;
  line-height: 30px;
}

.textbook_right_tips img {
  position: relative;
  top: 3px;
}

.textbook_right_tips:hover {
  background: #787554;
}

.textbook_right_html {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  background: #ffffff;
  border-right: 1px solid #e4e4e4;
}

.textbook_right_html.small {
  height: calc(100% - 30px);
}

.textbook_right_html.active {
  background: #d2e7d5fe;
}

.textbook_right_filecont {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  background: #ffffff;
  box-sizing: border-box;
  border: 2px solid #ffffff;
}

.textbook_right_filecont iframe,
.textbook_right_filecont video {
  width: calc(100% - 4px);
  height: calc(100% - 4px);
  overflow: hidden;
  border: none;
}

.textbook_right_filecont img {
  width: auto;
  max-width: calc(100% - 4px);
}

.textbook_right_filecont video {
  background: #333333;
}

.textbook_right_htmlprve {
  position: absolute;
  left: 25px;
  bottom: 88px;
}

.textbook_right_htmlnext {
  position: absolute;
  right: 28px;
  bottom: 88px;
}

.textbook_html {
  width: 100%;
  box-sizing: border-box;
  height: 100%;
  padding: 24px 40px 24px;
  overflow-y: auto;
  position: relative;
}

.textbook_html img {
  max-width: 100%;
}

.textbook_html p {
  font-size: 17px;
  font-family: Source Han Serif CN-Regular, Source Han Serif CN;
  font-weight: 400;
  color: #303a40;
  line-height: 30px;
}

.ream_html {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}

.ream_html p,
.ream_html div {
  word-break: break-all;
}

.ream_html img {
  max-width: 100%;
}

.textbook_right_mark {
  height: 100%;
  width: 300px;
  float: left;
  background: #f3f5f7;
  overflow: hidden;
}

.textbook_right_marklabel {
  width: 100%;
  height: 58px;
  background: #ffffff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.08);
  line-height: 58px;
  box-sizing: border-box;
  padding: 0 10px;
  font-size: 18px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #000000;
}

.textbook_right_marklabelr {
  font-size: 16px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #5d6373;
}

.textbook_right_usemenus {
  height: 100%;
  width: 300px;
  float: left;
}

.textbook_mark_list {
  position: relative;
  width: 100%;
  height: calc(100% - 58px);
  overflow-y: auto;
  box-sizing: border-box;
  padding: 10px;
}

.normal-info {
  font-style: normal;
}

.nite-writer-pen {
  background: rgba(255, 122, 6, 0.18);
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(255, 122, 6, 0.18);
  font-style: normal;
}

.noteNumCilcle {
  box-sizing: border-box;
  width: 18px;
  height: 18px;
  line-height: 16px;
  border-radius: 50%;
  border: 1px solid #cccccc;
  font-style: normal;
  display: inline-block;
  font-size: 12px !important;
  text-align: center;
  background-color: #ffffff;
  box-shadow: 0 0 10px #ffffff;
}

.note_item {
  box-sizing: border-box;
  padding: 10px;
  position: relative;
  height: auto;
  box-sizing: border-box;
  border: 1px solid transparent;
  width: 100%;
  background: #ffffff;
  margin: 0 0 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.note_itemlabel {
  width: 100%;
  height: 14px;
  position: relative;
  box-sizing: border-box;
  padding: 0 20px 0 0;
  margin: 0 0 12px 0;
  font-size: 14px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #333333;
  line-height: 14px;
}

.note_itemlabel i {
  position: relative;
  top: 2px;
}

.note_info {
  width: 100%;
  height: auto;
  font-size: 14px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #666666;
  line-height: 24px;
}

.note_tips {
  font-size: 12px;
  color: #3d73f7;
  cursor: pointer;
}

.note_close {
  position: absolute;
  right: 10px;
  top: 10px;
  width: 18px;
  height: 18px;
  z-index: 1;
  text-align: center;
  line-height: 18px;
  cursor: pointer;
}

.note_info_body {
  box-sizing: border-box;
  padding: 14px 30px 0;
}

.course_btns {
  margin: 6px 0 0 0;
  float: left;
  width: 105px;
}

.course_input {
  float: right;
  margin: 3px 0 0 0;
  width: auto;
}

.video_cont {
  width: 100%;
  height: calc(100% - 60px);
  overflow-y: auto;
}

.video_box {
  width: 100%;
  height: 630px;
  background: #191919;
  box-sizing: border-box;
  padding: 0 195px 20px;
  margin: 0 0 16px 0;
}

.video_box_title {
  width: 100%;
  height: 48px;
  line-height: 48px;
  font-size: 20px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #ffffff;
}

.video_box_body {
  width: 100%;
  height: calc(100% - 48px);
}

.video_box_left {
  float: left;
  width: calc(100% - 420px);
  height: 100%;
  border-radius: 10px;
  overflow: hidden;
  background: #2d2d2d;
}

.video_box_left video,
.video_box_left iframe {
  width: 100%;
  height: 100%;
}

.video_box_right {
  float: right;
  width: 395px;
  height: 100%;
  border-radius: 10px;
  overflow: hidden;
}

.video_right_top {
  width: 100%;
  height: 95px;
  background: #2d2d2d;
  margin: 0 0 5px 0;
}

.video_right_icon {
  float: left;
  margin: 15px 28px 0 20px;
  border-radius: 50%;
  overflow: hidden;
  width: 64px;
  height: 64px;
}

.video_right_icon img {
  width: 64px;
  height: auto;
  min-height: 100%;
}

.video_right_user {
  width: calc(100% - 120px);
  float: left;
  height: auto;
  margin: 20px 0 0 0;
}

.video_right_username {
  height: 21px;
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #ffffff;
  line-height: 21px;
  margin: 0 0 16px 0;
}

.video_right_state {
}

.video_right_statespan {
  font-size: 13px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #6a757b;
  line-height: 20px;
  margin: 0 0 0 5px;
}

.video_right_bottom {
  height: calc(100% - 100px);
  width: 100%;
  background: #333333;
}

.video_bottom_tabs {
  width: 100%;
  height: 36px;
  box-sizing: border-box;
  padding: 0 28px;
}

.video_bottom_tab {
  width: 60px;
  height: 36px;
  float: left;
  position: relative;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #ffffff;
  text-align: center;
  line-height: 36px;
}

.video_bottom_tab:before {
  content: ' ';
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: -1px;
  height: 2px;
  width: calc(100% - 10px);
  background: #1d8486;
}

.video_bottom_tab.active:before {
  display: block;
}

.video_bottom_list {
  width: 100%;
  height: calc(100% - 36px);
  overflow-y: auto;
  background: #2d2d2d;
}

.course_sectionBox {
  width: 100%;
  height: auto;
  transition: all 0.3s;
}

.course_chapterItem {
  width: 100%;
  height: 70px;
  line-height: 70px;
  font-size: 18px;
  font-weight: 400;
  color: #ffffff;
  box-sizing: border-box;
  padding: 0 45px 0 30px;
  position: relative;
  background: #2d2d2d;
  cursor: pointer;
}

.course_chapterItem i {
  position: absolute;
  right: 25px;
  top: 50%;
  transform: translateY(-50%);
}

.course_chapterItem:hover {
  color: #1d8486;
}

.course_sectionList {
  width: 100%;
  height: auto;
  background: #1f212a;
}

.course_fileBox {
  width: 100%;
  height: auto;
}

.course_sectionItem {
  width: 100%;
  height: 60px;
  box-sizing: border-box;
  padding: 0 35px;
  line-height: 60px;
  font-size: 18px;
  font-weight: 400;
  color: #ffffff;
  border-bottom: 1px solid #2f313b;
}

.course_fileList {
  width: 100%;
  height: auto;
}

.course_fileItem {
  width: 100%;
  height: 60px;
  box-sizing: border-box;
  padding: 0 100px 0 75px;
  position: relative;
  line-height: 60px;
  font-size: 16px;
  font-weight: 400;
  color: #ffffff;
  cursor: pointer;
  border-bottom: 1px solid #2f313b;
}

.course_fileIcon {
  position: absolute;
  left: 35px;
  top: 50%;
  transform: translateY(-50%);
  height: 20px;
}

.course_fileIcon img {
  height: 20px;
  float: left;
}

.course_fileItem.active,
.course_fileItem:hover {
  color: #1d8486;
}

.course_fileStatus {
  width: 70px;
  height: 60px;
  line-height: 60px;
  position: absolute;
  right: 25px;
  top: 0;
  font-size: 14px;
  font-weight: 400;
  color: #939393;
}

.course_fileStatus.active {
  color: #1d8486;
}

.course_fileStatus img {
  margin: 0 5px 0 0;
  position: relative;
  top: 1px;
}

.course_others {
  width: calc(100% - 390px);
  margin: 0 auto 60px;
  height: auto;
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0px 3px 6px 1px rgba(29, 132, 134, 0.11);
}

.course_tabs {
  width: 100%;
  height: 71px;
  box-sizing: border-box;
  padding: 0 16px;
  border-bottom: 1px solid #d5dfe1;
}

.course_tab {
  width: 135px;
  height: 70px;
  position: relative;
  text-align: center;
  line-height: 70px;
  cursor: pointer;
  float: left;
}

.course_tab:before {
  content: ' ';
  position: absolute;
  left: 25%;
  bottom: -1px;
  height: 2px;
  width: 50%;
  background: #1d8486;
  display: none;
}

.course_tab:hover:before,
.course_tab.active:before {
  display: block;
}

.course_bottomRight {
  width: 100%;
  height: auto;
  box-sizing: border-box;
  padding: 35px 75px;
  position: relative;
  z-index: 1;
}

.course_study_btns {
  padding: 0;
}

.study_input {
  float: left;
  width: auto;
}

.study_btns {
  float: left;
  margin: 2px 0 0 0;
}

.course_task_left {
  width: 280px;
  height: 500px;
  float: left;
  background: #f2f3f3;
  border-radius: 8px 8px 8px 8px;
  box-sizing: border-box;
  padding: 20px 0 0;
}

.class_task_left {
  height: 100%;
  background: no-repeat;
  padding: 0;
}

.course_task_leftlabel {
  height: 20px;
  font-size: 15px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  line-height: 20px;
  margin: 0 0 16px 0;
  box-sizing: border-box;
  padding: 0 16px;
}

.course_task_list {
  width: 100%;
  height: calc(100% - 80px);
  overflow-y: auto;
}

.course_task_item {
  width: 100%;
  height: 35px;
  cursor: pointer;
  line-height: 35px;
  box-sizing: border-box;
  padding: 0 15px 0 28px;
  font-size: 15px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
}

.course_task_item:hover,
.course_task_item.active {
  background: rgba(29, 132, 134, 0.12);
  color: rgba(29, 132, 134, 1);
}

.course_task_right {
  width: calc(100% - 315px);
  height: auto;
  float: right;
}

.teacher_list_icon {
  display: inline-block;
  width: 50px;
  height: 50px;
  overflow: hidden;
  border-radius: 50%;
}

.teacher_list_icon img {
  width: 100%;
  height: 100%;
}

.course_title {
  width: 100%;
  height: 42px;
  box-sizing: border-box;
  border-bottom: 1px solid #d5dfe1;
  padding: 0 10px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #303a40;
  line-height: 30px;
  margin: 0 0 35px;
}

.course_commBox {
  width: calc(100% - 30px);
  margin: 0 auto;
  height: auto;
}

.course_bottomRightAll .course_commBox {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
  height: calc(100% - 80px);
  overflow-y: auto;
}

.course_commItem {
  width: 100%;
  height: auto;
  box-sizing: border-box;
  padding: 0 0 0 80px;
  position: relative;
  margin: 0 0 40px 0;
}

.course_bottomRightAll .course_commItem {
  padding: 0 0 0 50px;
  margin: 0 0 20px 0;
}

.course_commIcon {
  width: 55px;
  height: 55px;
  border-radius: 50%;
  position: absolute;
  left: 0;
  top: 0;
  overflow: hidden;
}

.course_bottomRightAll .course_commIcon {
  width: 40px;
  height: 40px;
}

.course_commIcon img {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  height: auto;
  min-height: 100%;
}

.course_commUser {
  width: auto;
  height: 28px;
  font-size: 18px;
  font-weight: 400;
  line-height: 28px;
  color: #333333;
  letter-spacing: 1px;
  margin: 0 0 10px 0;
}

.course_commText {
  height: auto;
  font-size: 17px;
  font-weight: 400;
  line-height: 28px;
  color: #333333;
  letter-spacing: 1px;
  margin: 0 0 15px 0;
}

.course_bottomRightAll .course_commText {
  margin: 0 0 10px 0;
}

.course_commInfo {
  width: 100%;
  height: auto;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: #939393;
  letter-spacing: 1px;
  margin: 0 0 20px 0;
}

.course_commOprn {
  margin: 0 20px;
  cursor: pointer;
  height: 22px;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: #939393;
  letter-spacing: 1px;
}

.course_commOprn:hover {
  color: #1d8486;
}

.course_commLike {
  margin: 0 20px 0 0;
  cursor: pointer;
  height: 22px;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: #939393;
  letter-spacing: 1px;
}

.course_replyBox {
  width: 100%;
  height: auto;
  border-bottom: 1px solid #dbe6f0;
}

.course_replySpan {
  margin: 0 15px;
  word-break: break-all;
}

.course_replyHasMore {
  width: 100%;
  font-size: 16px;
  font-weight: 400;
  line-height: 22px;
  color: #333333;
  letter-spacing: 1px;
  margin: 0 0 30px 0;
}

.course_replyInput {
  width: 100%;
  height: auto;
  box-sizing: border-box;
  padding: 0 105px 0 80px;
  position: relative;
  margin: 0 0 40px 0;
}

.course_bottomRightAll .course_replyInput {
  padding: 0 105px 0 50px;
}

.course_replytextBox {
  width: 100%;
  height: 100px;
  line-height: 30px;
  box-sizing: border-box;
  padding: 5px 0 5px 15px;
  border: 1px solid #cccccc;
}

.course_replytextBox textarea {
  width: 100%;
  height: 100%;
  resize: none;
  border: none;
  outline: none;
  line-height: 32px;
  font-size: 20px;
  font-weight: 400;
  color: #333;
  letter-spacing: 1px;
  box-sizing: border-box;
  padding: 0 15px 0 0;
}

.course_replyBtn {
  height: 100px;
  width: 95px;
  position: absolute;
  right: 0;
  top: 0;
}

.course_replyBtn button {
  height: 100px;
  width: 95px;
  box-sizing: border-box;
  padding: 0 22px;
  text-align: center;
  font-size: 20px;
  font-weight: 400;
  line-height: 30px;
  color: #ffffff;
  word-break: break-all;
  white-space: normal;
}

.course_replycount {
  margin: 0 25px;
}

.textbook_add {
  width: calc(100% - 150px);
  height: calc(100% - 110px);
  overflow: hidden;
  margin: 0 auto;
}

.textbook_addLeft {
  width: 115px;
  height: 100%;
  float: left;
}

.textbook_addRight {
  float: right;
  width: calc(100% - 120px);
  height: 100%;
  border-radius: 8px;
  overflow-y: auto;
}

.textbook_addRightback {
  background: #ffffff;
}

.textbook_addtab {
  width: 100%;
  height: 52px;
  border-radius: 8px;
  line-height: 52px;
  font-size: 18px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  text-align: center;
  margin: 0 0 20px 0;
  cursor: pointer;
}

.textbook_addtab:hover,
.textbook_addtab.active {
  background: #ffffff;
  color: #1d8486;
}

.course_addform {
  width: 100%;
  height: auto;
  box-sizing: border-box;
  padding: 45px 35px 0;
}

.avataruploads {
  width: 100%;
  height: 290px;
  border-radius: 4px;
  border: 1px dashed #adb9bb;
  position: relative;
  cursor: pointer;
}

.avataruploads:hover {
  border: 1px dashed #05a68b;
}

.avataruploads img {
  width: 100%;
  height: 100%;
}

.avatarupcover {
  width: 205px;
  height: 155px;
  border-radius: 5px;
  background: #cccccc;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #ffffff;
  font-size: 50px;
  line-height: 155px;
}

.course_left_list {
  float: left;
  width: 390px;
  height: 100%;
  overflow: hidden;
  background: #ffffff;
  border-radius: 8px;
  box-sizing: border-box;
  padding: 16px 0;
}

.course_left_head {
  height: 28px;
  margin: 0 auto 16px;
  width: calc(100% - 44px);
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  line-height: 28px;
}

.textbook_right_form {
  width: calc(100% - 400px);
  float: right;
  height: 100%;
  overflow-y: auto;
  box-sizing: border-box;
  padding: 50px 0 15px 20px;
  background: #ffffff;
  overflow-y: auto;
  box-sizing: border-box;
}

.textbook_right_formall {
  padding: 0;
}

.tasks_chapterinfo {
  width: 100%;
  height: auto;
  min-height: 40px;
  box-sizing: border-box;
  line-height: 30px;
  padding: 5px 60px 5px 0;
  position: relative;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
}

.tasks_chapterinfo:hover,
.tasks_chapterinfo.active {
  background: rgba(29, 132, 134, 0.12);
}

.textbook_right_basic {
  width: 100%;
  height: 70px;
  box-sizing: border-box;
  padding: 15px 15px 0 0;
}

.textbook_right_basic .el-input__inner {
  border: none;
  border-radius: 0px;
  border-bottom: 1px solid #dcdfe6;
}

.textbook_right_basic .el-form-item__error {
  z-index: 1;
  background: #ffffff;
}

.textbook_right_from {
  height: 40px;
  width: 100%;
  background: rgba(29, 132, 134, 0.14);
  box-sizing: border-box;
  padding: 4px 0 0 24px;
  font-size: 15px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #1d8486;
  line-height: 32px;
}

.textbook_right_fromtn {
  width: 115px;
  height: 32px;
  text-align: center;
  line-height: 32px;
  text-align: center;
  background: linear-gradient(to right, #1d8486, #3e9151);
  color: #ffffff;
  font-size: 15px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #ffffff;
  border-radius: 4px;
  margin: 0 15px 0 0;
}

.textbook_right_from span {
  font-size: 15px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #1d8486;
}

.task_topics {
  height: calc(100% - 110px);
  width: 100%;
  overflow: hidden;
}

.task_topics_left {
  float: left;
  width: calc(100% - 245px);
  height: 100%;
  overflow-y: auto;
  box-sizing: border-box;
  padding: 16px 0 0 24px;
  position: relative;
}

.task_topics_right {
  width: 245px;
  height: 100%;
  overflow-y: auto;
  box-sizing: border-box;
  border-left: 1px solid #d5dfe1;
}

.task_topics_rightlabel {
  width: 100%;
  height: 47px;
  border-bottom: 1px solid #d5dfe1;
  line-height: 46px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #1d8486;
  box-sizing: border-box;
  padding: 0 23px;
}

.task_topics_rightlist {
  width: 100%;
  height: calc(100% - 48px);
  overflow-y: auto;
  box-sizing: border-box;
  padding: 0 0 0 23px;
}

.has_user {
  height: calc(100% - 120px);
}

.task_topics_name {
  height: 42px;
  line-height: 42px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
}

.task_topics_name span {
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #6a757b;
}

.task_select {
  width: 975px;
  height: auto;
}

.files_select {
  width: 1350px;
  height: auto;
}

.task_view {
  width: 1200px;
  height: 700px;
}

.task_selectnum {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #1d8486;
  height: 19px;
  line-height: 19px;
  margin: 0 0 12px;
}

.task_selectlist {
  width: 100%;
  height: auto;
}

.task_select_topics {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.course_teacheradd {
  width: 100%;
  height: auto;
  box-sizing: border-box;
  padding: 40px 140px;
}

.textbook_right_btn {
  float: right;
  width: 130px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background: linear-gradient(to right, #1d8486, #3e9151);
  color: #ffffff;
  font-size: 15px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #ffffff;
  border-radius: 4px;
}

.task_main {
  width: calc(100% - 390px);
  margin: 0 auto;
  height: calc(100% - 50px);
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.task_mainall {
  width: 100%;
  height: 100%;
}

.task_main_show {
  width: 100%;
  height: calc(100% - 50px);
}

.task_main_add {
  height: calc(100% - 115px);
}

.topic_mainleft {
  height: calc(100% - 85px);
}

.topic_mainbtns {
  float: left;
  width: calc(100% - 245px);
  height: 85px;
  box-sizing: border-box;
  border-top: 1px solid #d5dfe1;
  text-align: right;
  box-sizing: border-box;
  padding: 18px 0 0 0;
}

.topic_mainbtn {
  display: inline-block;
  width: 100px;
  height: 34px;
  line-height: 34px;
  text-align: center;
  background: linear-gradient(to right, #1d8486, #3e9151);
  color: #ffffff;
  font-size: 15px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #ffffff;
  border-radius: 4px;
  margin: 0 16px 0 0;
}

.task_main_title {
  width: calc(100% - 390px);
  height: 65px;
  margin: 0 auto;
  box-sizing: border-box;
  border-bottom: 1px solid #d5dfe1;
  background: #ffffff;
  border-radius: 8px;
}

.topic_select {
  width: 1200px;
  height: auto;
}

.topic_select_body {
  box-sizing: border-box;
  padding: 20px 30px 20px 40px;
  max-height: 650px;
}

.topic_views {
  width: 100%;
  height: 490px;
  overflow-y: auto;
  position: relative;
  background: #e4edee;
  box-shadow: 0px 3px 6px 1px rgba(0, 0, 0, 0.39);
  box-sizing: border-box;
  padding: 0 20px 0 0;
}

.topic_view {
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  padding: 20px 0 0 30px;
}

.task_select_view {
  padding: 0 !important;
}

.textbook_bottom_item {
  width: 100%;
  height: 100%;
}

.textbook_bottom_iframe {
  width: 100%;
  height: 100%;
  overflow: hidden;
  outline: none;
  border: none;
  float: left;
}

.paper_menus {
  position: absolute;
  left: 75px;
  top: 0;
  width: 115px;
  height: auto;
}

.task_topics_leftslelect {
  width: 100%;
  height: auto;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  text-align: center;
}

.task_topics_leftslelect img {
  display: block;
  margin: 0 auto;
}

.task_topics_leftslelect div {
  height: 21px;
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #adb9bb;
  line-height: 20px;
  margin: 20px 0 25px;
}

.paper_main {
  width: calc(100% - 390px);
  margin: 0 auto;
  height: 100%;
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;
}

.paper_main_num {
  width: 100%;
  height: 165px;
  box-sizing: border-box;
  position: relative;
  padding: 26px 48px 0;
}

.paper_main_info {
  position: relative;
  z-index: 1;
}

.paper_main_infoP1 {
  height: 24px;
  font-size: 18px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #303a40;
  line-height: 24px;
  margin: 0 0 20px 0;
}

.paper_main_infoP1 span {
  color: #1d8486;
}

.paper_main_infoP1 span.errorColor {
  color: #f56c6c;
  font-weight: initial;
}

.paper_main_infoP2 {
  height: 21px;
  font-size: 16px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #303a40;
  line-height: 21px;
  margin: 0 0 16px 0;
}

.paper_main_infoP2num {
  color: #84861d;
}

.paper_main_infoP2desc {
  font-size: 15px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #6a757b;
}

.stand {
  margin: 0 16px;
}

.paper_main_form {
  width: 100%;
  height: calc(100% - 165px);
  box-sizing: border-box;
  padding: 40px 0 0 0;
  overflow-y: auto;
}

.paper_main_title {
  width: calc(100% - 390px);
  height: 65px;
  margin: 0 auto;
  box-sizing: border-box;
  background: #ffffff;
  border-radius: 8px;
}

.textbook_right_num {
  width: 100%;
  height: auto;
  margin: 0 auto;
  padding: 25px 0 0 0;
}

.textbook_right_num .el-form-item,
.textbook_right_num .el-form-item__content {
  width: calc(100% - 80px);
}

.paper_pages {
  box-sizing: border-box;
  width: 100%;
  text-align: center;
  padding: 30px 0 0 0;
}

.copy_form {
  height: auto;
  width: 615px;
  max-height: calc(100% - 100px);
  max-width: 100%;
  position: relative;
}

.exam_add_btn {
  position: absolute;
  right: 30px;
  top: 0px;
  width: 120px;
  height: 40px;
  text-align: center;
  line-height: 40px;
  color: #ffffff;
  border-radius: 5px;
  background: linear-gradient(to right, #1d8486, #3e9151);
}

.exam_main {
  width: calc(100% - 390px);
  height: 100%;
  background: #ffffff;
  margin: 0 auto;
  border-radius: 8px;
  box-sizing: border-box;
  padding: 20px 0 0;
  overflow-y: auto;
}

.exam_main_class {
  width: 100%;
  height: auto;
}

.exam_class {
  float: left;
  width: auto;
  height: 36px;
  box-sizing: border-box;
  padding: 0 14px;
  border-radius: 4px;
  line-height: 36px;
  font-size: 15px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  margin: 0 12px 12px 0;
  background: #e8e3cf;
}

.exam_class_add {
  background: none;
  padding: 0;
}

.exam_class_add img {
  float: left;
  margin: 4px 0 0 0;
}

.exam_class_addnum {
  height: 20px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #1d8486;
  line-height: 20px;
}

.exam_select_from {
  height: 40px;
  width: 100%;
  background: rgba(29, 132, 134, 0.14);
  box-sizing: border-box;
  padding: 4px 0 0 24px;
  font-size: 15px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #1d8486;
  line-height: 32px;
}

.exam_right_basic {
  width: auto;
  height: 32px;
  box-sizing: border-box;
  display: inline-block;
}

.exam_right_basic .el-input__inner {
  border: none;
  border-radius: 0px;
  border-bottom: 1px solid #dcdfe6;
  line-height: 32px;
  height: 32px;
}

.exam_right_basic .el-form-item__content,
.exam_right_basic .el-form-item__label {
  line-height: 32px;
}

.exam_score_tabs {
  width: 100%;
  height: 55px;
  position: relative;
  box-sizing: border-box;
  border-bottom: 1px solid #dfd9bd;
}

.exam_score_tab {
  width: 105px;
  float: left;
  margin: 0 16px;
  height: 55px;
  cursor: pointer;
  line-height: 55px;
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  border-bottom: 1px solid transparent;
  text-align: center;
}

.exam_score_tab:hover,
.exam_score_tab.active {
  color: #84861d;
  border-bottom: 1px solid #84861d;
}

.exam_score_auto {
  box-sizing: border-box;
  width: 100%;
  height: auto;
  padding: 40px 30%;
}

.exam_score_hand {
  width: 100%;
  height: calc(100% - 270px);
  overflow: hidden;
  box-sizing: border-box;
  padding: 15px 0 0 24px;
}

.class_body {
  width: 100%;
  height: auto;
  max-height: 400px;
  box-sizing: border-box;
  padding: 25px;
  overflow-y: auto;
}

.class_item {
  width: calc(25% - 12px);
  height: 60px;
  box-sizing: border-box;
  padding: 10px;
  vertical-align: middle;
  text-align: center;
  cursor: pointer;
  font-size: 15px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  float: left;
  background: #ffffff;
  border-radius: 6px;
  margin: 0 6px 12px;
  border: 1px solid #adb9bb;
  position: relative;
}

.class_item i {
  position: absolute;
  right: 2px;
  bottom: 2px;
  font-size: 15px;
  color: #a6d0d1;
}

.class_item:hover,
.class_item.active {
  color: #ffffff;
  background: #1d8486;
  border: 1px solid #1d8486;
}

.exam_state {
  position: absolute;
  left: -8px;
  top: -8px;
}

.exam_studnet_main {
  width: calc(100% - 390px);
  margin: 0 auto;
  height: calc(100% - 50px);
  background: #ffffff;
  border-radius: 8px;
  position: relative;
}

.exam_studnet_top {
  height: 170px;
  width: 100%;
  box-sizing: border-box;
  border-bottom: 1px solid #d5dfe1;
  padding: 24px 430px 0 55px;
}

.exam_studnet_topP {
  height: 21px;
  line-height: 21px;
  font-size: 16px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  color: #303a40;
  float: left;
  margin: 0 0 20px 0;
}

.exam_studnet_topP span {
  font-weight: bold;
}

.exam_studnet_topP1 {
  width: 330px;
  margin: 0 15px 20px 0;
}

.exam_studnet_topP2 {
  width: calc(100% - 345px);
}

.exam_class_list {
  width: 100%;
  height: 36px;
}

.exam_class_label {
  width: 80px;
  float: left;
  height: 36px;
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
}

.exam_class_box {
  width: calc(100% - 100px);
  height: 36px;
  float: left;
  position: relative;
}

.exam_class_main {
  width: 100%;
  height: 36px;
  position: absolute;
  left: 0;
  top: 0;
  overflow: hidden;
}

.exam_class_item {
  width: auto;
  box-sizing: border-box;
  float: left;
  margin: 0 8px 8px 0;
  padding: 0 10px;
  line-height: 36px;
  height: 36px;
  background: #f2f3f3;
  border-radius: 4px 4px 4px 4px;
  font-size: 15px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
}

.exam_class_main:hover {
  height: auto;
  overflow: initial;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  box-sizing: border-box;
  padding: 10px 0 10px 10px;
  background: #ffffff;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1;
}

.exam_studnet_topinfos {
  width: 384px;
  height: auto;
  position: absolute;
  right: 36px;
  top: 18px;
}

.exam_studnet_topinfo {
  float: left;
  width: 180px;
  height: 50px;
  margin: 0 12px 12px 0;
  box-sizing: border-box;
  padding: 0 0 0 46px;
  line-height: 50px;
  text-align: left;
  background: rgba(29, 132, 134, 0.1);
  border-radius: 8px 8px 8px 8px;
  font-size: 15px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  color: #303a40;
  position: relative;
}

.exam_studnet_topinfo img {
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  margin: 2px 0 0 0;
}

.exam_studnet_topinfo span {
  font-weight: bold;
}

.exam_studnet_bottom {
  width: 100%;
  height: calc(100% - 170px);
  box-sizing: border-box;
  padding: 20px 60px 30px;
}

.exam_student_btns {
  padding: 0;
  width: auto;
  float: right;
}

.exam_studnet_bottomlabel {
  float: left;
  height: 34px;
  line-height: 34px;
  font-size: 16px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #303a40;
}

.exam_studnet_bottom_table {
  width: 100%;
  height: calc(100% - 120px);
}

.student_info {
  width: 100%;
  height: 72px;
  box-sizing: border-box;
  padding: 20px 23px 0;
}

.student_infoicon {
  float: left;
  width: 46px;
  height: 46px;
  margin: 0 7px 0 0;
  border-radius: 50%;
  overflow: hidden;
}

.student_infoicon img {
  width: 100%;
  height: auto;
  min-height: 100%;
}

.student_infoP1 {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  line-height: 19px;
  height: 19px;
  margin: 0 0 7px 0;
}

.state_img {
  margin: 0 5px 0 0;
  position: relative;
  top: -1px;
}

.classroom_detail_tabs {
  width: 544px;
  text-align: center;
  display: inline-block;
  height: 40px;
  position: absolute;
  left: 50%;
  bottom: 0;
  margin: 0 0 0 -272px;
  display: flex;
  justify-content: space-between;
}

.classroom_detail_tab {
  width: calc(25% - 16px);
  margin: 0 16px;
  height: 40px;
  line-height: 40px;
  font-size: 18px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  cursor: pointer;
  text-align: center;
  border-radius: 4px;
}

.classroom_detail_tab:hover,
.classroom_detail_tab.active {
  font-size: 18px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #ffffff;
  background: linear-gradient(to right, #1d8486, #3e9151);
}

.classroom_detail_bottom {
  height: calc(100% - 60px);
  width: 100%;
  background: #e7efea;
  box-sizing: border-box;
  padding: 20px 0 0 0;
}

.classroom_detail_bottom.new {
  padding: 0 0 20px 0;
}

.classroom_detail_cont {
  width: calc(100% - 390px);
  height: calc(100% - 30px);
  margin: 0 auto;
  position: relative;
  overflow-y: hidden;
  border-radius: 8px;
}

.classroom_detail_main {
  width: 100%;
  margin: 0 auto;
  height: auto;
  max-height: 100%;
  box-sizing: border-box;
  padding: 40px 300px;
  position: relative;
  z-index: 1;
  overflow-y: auto;
}

.classroom_detail_mainP1 {
  font-size: 16px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  color: #303a40;
  line-height: 0px;
  height: auto;
  line-height: 26px;
  margin: 0 0 10px 0;
}

.bolder {
  font-weight: bold;
}

.classroom_detail_class {
  display: inline-block;
  margin: 0 12px 12px 0;
  width: auto;
  padding: 0 14px;
  height: 36px;
  line-height: 36px;
  background: #ffffff;
  border-radius: 4px;
  line-height: 36px;
  font-size: 15px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
}

.class_preview {
  height: 820px;
  width: 1585px;
  max-height: calc(100% - 100px);
  max-width: 100%;
  position: relative;
}

.class_preview_body {
  width: 100%;
  height: calc(100% - 52px);
  overflow: hidden;
  box-sizing: border-box;
  padding: 0;
  position: relative;
}

.class_preview_body.select {
  height: calc(100% - 116px);
}

.claaa_view {
  width: 100%;
  height: 100%;
  margin: 0 auto 0;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  overflow: hidden;
}

.classroom_detail_task {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  padding: 24px 50px 0 0;
}

.class_task {
  height: 650px;
  width: 1200px;
  max-height: calc(100% - 100px);
  max-width: 100%;
  position: relative;
}

.class_add_select {
  width: 840px;
  height: auto;
}

.textbook_chapterselect {
  width: 16px;
  height: 16px;
  position: absolute;
  right: 5px;
  top: 50%;
  margin: -8px 0 0 0;
  cursor: pointer;
  border-radius: 3px 3px 3px 3px;
  border: 1px solid #6a757b;
  box-sizing: border-box;
}

.textbook_chapterselect img {
  position: absolute;
  left: -1px;
  top: -1px;
}

.textbook_chapterselect:hover {
  border: 1px solid #1d8486;
}

.textbook_chapterselect.active img {
  display: block;
}

.class_add_selectbtns {
  text-align: right;
  height: 28px;
  margin: 0 0 10px 0;
}

.class_add_selectbtn {
  width: auto;
  height: 28px;
  text-align: center;
  line-height: 28px;
  margin: 0 0 0 20px;
  display: inline-block;
}

.class_add_selectbtn img {
  float: left;
}

.interaction_box {
  width: 100%;
  margin: 0 auto;
  height: 100%;
  box-sizing: border-box;
  padding: 0 40px;
  overflow-y: auto;
}

.interaction_banner {
  width: 100%;
  height: 140px;
  position: relative;
  margin: 10px 0 18px 0;
  float: left;
}

.interaction_state {
  position: absolute;
  left: -6px;
  top: -6px;
}

.interaction_info {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  padding: 20px 0 0 75px;
}

.interaction_infoP {
  width: 450px;
  float: left;
  height: 100%;
}

.interaction_infoP1 {
  height: 26px;
  font-size: 20px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #ffffff;
  line-height: 26px;
  margin: 0 0 12px 0;
}

.interaction_infoP2 {
  height: 21px;
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #d5dfe1;
  line-height: 21px;
  margin: 0 0 16px 0;
}

.interaction_infoP3 {
  height: 22px;
  line-height: 22px;
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #f2f3f3;
}

.interaction_infoP3 img {
  float: left;
  margin: 0 4px 0 0;
}

.interaction_infoD {
  width: 280px;
  height: 64px;
  float: left;
  margin: 25px 0 0 0;
}

.interaction_infoD1 {
  width: 140px;
  text-align: center;
  float: left;
  position: relative;
}

.interaction_infoD1:last-child:before {
  content: ' ';
  position: absolute;
  left: 0;
  top: 50%;
  height: 46px;
  margin: -23px 0 0 0;
  width: 1px;
  background: #6a757b;
}

.interaction_infoD1P1 {
  height: 31px;
  font-size: 24px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #dfd9bd;
  line-height: 31px;
  margin: 0 0 12px 0;
}

.interaction_infoD1P2 {
  height: 21px;
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #adb9bb;
  line-height: 21px;
}

.interaction_left {
  width: calc(100% - 348px);
  float: left;
  height: auto;
}

.interaction_right {
  width: 330px;
  height: auto;
  float: right;
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}

.interaction_right_label {
  width: 100%;
  height: 50px;
  line-height: 50px;
  font-size: 16px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #303a40;
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  padding: 0 24px;
}

.interaction_right_item {
  height: 53px;
  box-sizing: border-box;
  border-bottom: 1px solid #d5e3b6;
  position: relative;
  z-index: 1;
  padding: 0 24px;
}

.interaction_right_itemall {
  padding: 0;
}

.interaction_right_itemnum {
  width: 35px;
  float: left;
  height: 52px;
  position: relative;
  font-size: 16px;
  color: #1d8486;
  line-height: 52px;
}

.interaction_right_itemnum img,
.interaction_right_itemnum span {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

.interaction_right_itemnum span {
  text-indent: 5px;
}

.interaction_right_itemicon {
  width: 28px;
  height: 28px;
  float: left;
  margin: 12px 16px 0 0;
  border-radius: 50%;
  overflow: hidden;
}

.interaction_right_itemicon img {
  width: 100%;
  height: auto;
  min-height: 100%;
}

.interaction_right_itemname {
  width: 50px;
  margin: 16px 15px 0 0;
  float: left;
  height: 20px;
  font-size: 15px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  line-height: 20px;
}

.interaction_right_itempre {
  width: 55px;
  height: 7px;
  float: left;
  margin: 23px 15px 0 0;
  background: rgba(132, 134, 29, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.interaction_right_itemprein {
  float: left;
  height: 7px;
  width: auto;
  background: rgba(132, 134, 29, 1);
}

.interaction_right_iteminfo {
  height: 52px;
  float: left;
  width: auto;
  line-height: 52px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
}

.interaction_left1 {
  width: calc(50% - 9px);
  height: 350px;
  float: left;
  margin: 0 18px 0 0;
  background: #ffffff;
  border-radius: 5px;
}

.interaction_left2 {
  margin: 0;
}

.interaction_leftlabel {
  width: 100%;
  height: 50px;
  line-height: 50px;
  padding: 0 20px;
  border-bottom: 1px solid #f2f3f3;
  font-size: 16px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #303a40;
}

.interaction_leftbox {
  width: 100%;
  height: calc(100% - 50px);
  overflow: hidden;
}

.interaction_left_one {
  width: 100%;
  height: auto;
}

.interaction_left_oneLabel {
  width: 100%;
  height: 50px;
  box-sizing: border-box;
  padding: 0 0 0 10px;
  line-height: 50px;
  font-size: 16px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #303a40;
}

.interaction_left_oneInfo {
  width: 100%;
  height: auto;
  margin: 0 0 18px 0;
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;
}

.interaction_left_oneInfo:last-child {
  margin: 0;
}

.interaction_one_label {
  width: 100%;
  height: 64px;
  position: relative;
}

.interaction_one_labelname {
  width: 314px;
  height: 46px;
  position: absolute;
  left: -10px;
  top: 13px;
  font-size: 15px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #ffffff;
  line-height: 32px;
  box-sizing: border-box;
  padding: 0 0 0 25px;
}

.interaction_one_labelname span {
  position: relative;
  z-index: 1;
}

.interaction_one_info {
  float: right;
  height: 64px;
  width: auto;
  box-sizing: border-box;
  padding: 0 110px 0 0;
  text-align: left;
  font-size: 16px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  color: #303a40;
  line-height: 64px;
}

.interaction_one_info span {
  font-weight: bold;
}

.interaction_one_info img {
  position: absolute;
  right: 32px;
  top: 50%;
  transform: translateY(-50%);
}

.interaction_one_info.active img {
  transform: translateY(-50%) rotateX(180deg);
}

.interaction_one_cont {
  width: 100%;
  height: 405px;
  overflow: hidden;
  box-sizing: border-box;
  border-top: 1px solid #d5dfe1;
}

.interaction_one_contleft {
  width: 380px;
  height: 100%;
  float: left;
  box-sizing: border-box;
  border-right: 1px solid #d5dfe1;
}

.interaction_one_contlefttop {
  height: 180px;
  width: 100%;
  box-sizing: border-box;
  padding: 24px 0 0 24px;
  border-bottom: 1px solid #d5dfe1;
}

.interaction_one_littlelabel {
  width: auto;
  height: 21px;
  font-size: 16px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #303a40;
  line-height: 21px;
  box-sizing: border-box;
  padding: 0 0 0 14px;
  position: relative;
}

.interaction_one_littlelabel:before {
  content: ' ';
  position: absolute;
  left: 0;
  top: 50%;
  height: 16px;
  width: 6px;
  border-radius: 3px;
  background: #1d8486;
  margin: -8.5px 0 0 0;
}

.interaction_one_littlelabel.yellow:before {
  background: #84861d;
}

.interaction_one_circle {
  float: left;
  width: 90px;
  height: 90px;
  margin: 25px 25px 0 14px;
}

.interaction_one_circleinfo {
  float: left;
  width: auto;
  margin: 45px 0 0 0;
  font-size: 16px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  color: #303a40;
  line-height: 21px;
}

.interaction_one_circleinfo span {
  font-weight: bold;
}

.interaction_one_contleftbot {
  width: 100%;
  height: calc(100% - 180px);
  overflow: auto;
  box-sizing: border-box;
  padding: 18px 20px 0 40px;
}

.interaction_one_contleftbotP1 {
  height: 31px;
  width: 100%;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #de6c6c;
  line-height: 19px;
}

.interaction_one_contleftbotP1 img {
  float: left;
  margin: 0 16px 0 0;
}

.interaction_one_contleftbotP1 span {
  font-weight: bold;
}

.interaction_one_contleftbotP2 {
  height: 42px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  line-height: 22px;
}

.interaction_one_contleftbotP3 {
  width: 100%;
  height: 82px;
  overflow: hidden;
  box-sizing: border-box;
  padding: 4px 0 0 0;
}

.interaction_one_contleftbotP4 {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  line-height: 32px;
  height: 32px;
}

.interaction_one_contleftbotP5 {
  width: 90px;
  height: 28px;
  border-radius: 14px;
  text-align: center;
  line-height: 26px;
  box-sizing: border-box;
  border: 1px solid #1d8486;
  color: #1d8486;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #1d8486;
}

.interaction_one_contmiddle {
  width: calc(100% - 645px);
  float: left;
  height: 100%;
  box-sizing: border-box;
  padding: 24px 24px 0;
}

.interaction_one_contmiddlecont {
  width: 100%;
  height: calc(100% - 22px);
  overflow: hidden;
}

.interaction_one_contright {
  width: 265px;
  height: 100%;
  float: left;
  box-sizing: border-box;
  border-left: 1px solid #d5dfe1;
  padding: 24px 24px 0;
}

.interaction_one_contrightlist {
  width: 100%;
  height: auto;
  box-sizing: border-box;
  padding: 8px 0 0 0;
  margin: 0 0 30px;
}

.interaction_discuss_left {
  width: 475px;
  height: 100%;
  float: left;
  box-sizing: border-box;
  border-right: 1px solid #d5dfe1;
  padding: 24px 24px 0;
}

.interaction_discuss_leftmain {
  width: 100%;
  height: calc(100% - 22px);
  overflow-y: auto;
  word-break: break-all;
  font-size: 15px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #1d8486;
  line-height: 26px;
  box-sizing: border-box;
  padding: 24px 0 0 0;
}

.interaction_discuss_leftmain img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 10px auto 0;
  background: #f8f8f8;
}

.interaction_discuss_middle {
  width: 430px;
  height: 100%;
  float: left;
  box-sizing: border-box;
  padding: 24px 24px 0;
}

.interaction_discuss_right {
  width: calc(100% - 905px);
  height: 100%;
  float: left;
  box-sizing: border-box;
  border-left: 1px solid #d5dfe1;
  padding: 24px 24px 0;
}

.interaction_discuss_rightmain {
  width: 100%;
  height: auto;
  max-height: calc(100% - 22px);
  overflow-y: auto;
  word-break: break-all;
  font-size: 15px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #1d8486;
  line-height: 26px;
  box-sizing: border-box;
  padding: 12px 0 0 0;
}

.interaction_discuss_rightlist {
  width: 100%;
  height: auto;
  margin: 0 0 24px 0;
}

.interaction_discuss_rightitem {
  width: 100%;
  height: 92px;
  box-sizing: border-box;
  border-bottom: 1px solid #d5dfe1;
  position: relative;
  padding: 12px 0;
}

.interaction_discuss_rightitem.auto {
  height: auto;
}

.interaction_discuss_righticon {
  width: 48px;
  height: 48px;
  float: left;
  margin: 0 12px 0 0;
  border-radius: 6px;
  overflow: hidden;
}

.interaction_discuss_righticon img {
  width: 100%;
  height: auto;
  min-height: 100%;
}

.interaction_discuss_rightP {
  width: calc(100% - 60px);
  float: left;
}

.interaction_discuss_rightPhelf {
  width: calc(100% - 265px);
}

.interaction_discuss_rightP1 {
  height: 19px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #adb9bb;
  line-height: 19px;
  margin: 0 0 6px 0;
}

.interaction_discuss_rightP1name {
  color: #303a40;
}

.interaction_discuss_rightP2 img {
  float: left;
  margin: 0 5px 0 0;
}

.interaction_discuss_rightP2de {
  margin: 0 0 0 24px;
  font-size: 16px;
  width: 16px;
}

.interaction_discuss_rightP2score {
  width: auto;
  height: 19px;
  float: left;
  margin: 0 0 0 16px;
  padding: 0 5px;
  border-radius: 10px;
  text-align: center;
  line-height: 19px;
  font-size: 13px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #ffffff;
  background: #1d8486;
}

.interaction_discuss_rightP3 {
  height: 39px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  line-height: 20px;
}

.interaction_discuss_rightP3.auto {
  height: auto;
}

.head_userback {
  position: relative;
  top: -2px;
}

.attendclass_index {
  width: 100%;
  height: calc(100% - 75px);
  margin: 75px auto 0;
  overflow: hidden;
  position: relative;
}

.head_time span {
  margin: 0 12px;
}

.attendclass_index_title {
  height: 75px;
  font-size: 57px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #ffffff;
  line-height: 75px;
  letter-spacing: 1px;
  position: absolute;
  left: 230px;
  top: 150px;
}

.attendclass_index_class {
  position: absolute;
  left: 230px;
  top: 250px;
  height: 37px;
  font-size: 28px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #ffffff;
  line-height: 37px;
  letter-spacing: 1px;
}

.attendclass_index_files {
  position: absolute;
  left: 230px;
  top: 365px;
  font-size: 20px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #d5dfe1;
  line-height: 26px;
  letter-spacing: 1px;
}

.attendclass_files_left {
  width: 500px;
  float: left;
  height: 280px;
  box-sizing: border-box;
  padding: 0 0 0 70px;
  overflow: hidden;
  margin: 0 30px 0 0;
  position: relative;
}

.attendclass_files_leftlabel {
  width: 80px;
  position: absolute;
  left: 0;
  top: 0;
}

.attendclass_files_leftlist {
  width: 100%;
  height: 100%;
  overflow-y: auto;
}

.attendclass_files_leftitem {
  height: 26px;
  width: 100%;
  margin: 0 0 25px 0;
}

.attendclass_files_leftitem img {
  height: 20px;
  float: left;
  margin: 3px 8px 0 0;
}

.attendclass_index_btn {
  position: absolute;
  left: 230px;
  top: 700px;
}

.attendclass_right_list {
  position: absolute;
  right: -550px;
  top: 0;
  height: 100%;
  width: 550px;
  transition: all 0.3s;
  background: #ffffff;
}

.attendclass_right_list.active {
  right: 0;
}

.attendclass_right_btn {
  position: absolute;
  left: -70px;
  top: 300px;
}

.attendclass_right_listlabel {
  height: 68px;
  width: 100%;
  line-height: 68px;
  font-size: 18px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  line-height: 60px;
  box-sizing: border-box;
  padding: 0 32px;
}

.attendclass_right_items {
  width: 100%;
  height: calc(100% - 68px);
  overflow-y: auto;
  box-sizing: border-box;
  padding: 0 32px 10px;
}

.attendclass_right_item {
  width: 100%;
  height: 138px;
  background: #e4edee;
  border-radius: 6px;
  margin: 0 0 24px 0;
  position: relative;
  box-sizing: border-box;
  padding: 24px 24px 14px;
}

.attendclass_right_itemstate {
  position: absolute;
  right: 0;
  top: 0;
}

.attendclass_right_itemP1 {
  height: 24px;
  font-size: 18px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #303a40;
  line-height: 24px;
  margin: 0 0 12px 0;
}

.attendclass_right_itemP2 {
  height: 19px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #6a757b;
  line-height: 19px;
  margin: 0 0 24px 0;
}

.attendclass_right_itemP3 {
  height: 21px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #1d8486;
  line-height: 18px;
}

.attendclass_right_itemP3 img {
  float: left;
  margin: 0 5px 0 0;
}

.attendclass_right_itemP4 {
  position: absolute;
  right: 8px;
  bottom: 5px;
}

.attendclass_detail_left {
  float: left;
  width: 275px;
  height: calc(100% - 75px);
  margin: 45px 25px 0 30px;
  position: relative;
}

.attendclass_detail_lefttop {
  width: 100%;
  height: 22px;
  position: relative;
}

.attendclass_detail_lefttopback {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 22px;
  overflow: hidden;
}

.attendclass_detail_lefttopinfo {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 22px;
  text-align: center;
}

.attendclass_detail_toptab {
  width: 108px;
  height: 38px;
  display: inline-block;
  cursor: pointer;
  margin: 0 4px;
  position: relative;
  top: -24px;
  border-radius: 8px 8px 0 0;
  text-align: center;
  line-height: 38px;
  background: linear-gradient(to bottom, rgb(172, 212, 213), rgb(145, 145, 145));
  font-size: 16px;
  font-family: Source Han Serif CN-SemiBold, Source Han Serif CN;
  font-weight: 600;
  color: #303a40;
  letter-spacing: 2px;
}

.attendclass_detail_toptab img {
  position: relative;
  top: 6px;
}

.attendclass_detail_toptab:hover,
.attendclass_detail_toptab.active {
  background: linear-gradient(to right, #1d8486, #3e9151);
  color: #ffffff;
}

.attendclass_detail_leftbottom {
  width: 100%;
  height: calc(100% - 22px);
}

.attendclass_left_list {
  height: calc(100% - 84px);
  padding: 0;
}

.attendclass_left_add {
  width: 100%;
  height: 28px;
  text-align: center;
  margin: 0 0 12px 0;
}

.attendclass_left_addbtn {
  width: 115px;
  height: 28px;
  text-align: center;
  line-height: 28px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #dfd9bd;
  background: #1d8486;
  margin: 0 auto;
  border-radius: 4px;
}

.attendclass_left_item {
  width: 100%;
  height: 40px;
  box-sizing: border-box;
  border-bottom: 1px dashed #707070;
  padding: 8px 30px 0 16px;
  position: relative;
}

.attendclass_left_itemin {
  width: 100%;
  height: 22px;
  cursor: pointer;
  line-height: 22px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #d5dfe1;
  position: relative;
}

.attendclass_left_itemin img {
  float: left;
  margin: 3px 6px 0 4px;
  height: 16px;
}

.attendclass_left_itemin.active {
  background: #84861d;
  color: #ffffff;
}

.attendclass_left_itemdelete {
  position: absolute;
  right: 0;
  height: 40px;
  text-align: center;
  width: 30px;
  font-size: 16px;
  top: 0;
  color: #adb9bb;
  display: none;
  line-height: 40px;
}

.attendclass_left_item:hover .attendclass_left_itemdelete {
  display: block;
}

.attendclass_detail_right {
  float: left;
  width: calc(100% - 740px);
  height: calc(100% - 42px);
  margin: 12px 0 0 0;
  position: relative;
  z-index: 1;
  transition: width 0.3s;
}

.attendclass_detail_right.active {
  width: calc(100% - 500px);
}

.attendclass_detail_righttabs {
  height: 58px;
  width: 100%;
}

.attendclass_detail_righttab {
  width: auto;
  height: 20px;
  margin: 0 8px 0 0;
  float: left;
}

.attendclass_detail_main {
  width: 100%;
  height: 100%;
  position: relative;
}

.attendclass_detail_opentask {
  position: absolute;
  right: 10px;
  bottom: 75px;
  z-index: 1;
}

.attendclass_talk {
  width: 355px;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 70px;
  background: #33393d;
  z-index: 2;
}

.attendclass_talk_close {
  position: absolute;
  right: 14px;
  top: 10px;
}

.attendclass_talk_cont {
  background: #404548;
  width: 100%;
  height: calc(100% - 155px);
  overflow-y: auto;
  margin: 35px 0 0 0;
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  padding: 20px;
}

.attendclass_talk_stand {
  width: 100%;
  height: 30px;
  background: #4d5458;
}

.attendclass_talk_input {
  width: 100%;
  height: 90px;
}

.attendclass_talk_input textarea {
  width: 100%;
  height: 100%;
  line-height: 30px;
  font-size: 15px;
  color: #ffffff;
  box-sizing: border-box;
  background: transparent;
  padding: 0 65px 0 12px;
  resize: none;
}

.attendclass_talk_btn {
  width: 60px;
  height: 22px;
  text-align: center;
  line-height: 22px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #d5dfe1;
  position: absolute;
  right: 10px;
  bottom: 10px;
  background: linear-gradient(to right, #1d8486, #3e9151);
  border-radius: 3px;
}

.course_chatItem {
  height: auto;
  width: 100%;
  box-sizing: border-box;
  padding: 20px 45px 15px 45px;
  position: relative;
  margin: 0 0 15px 0;
}

.course_chatIcon {
  width: 35px;
  height: 35px;
  position: absolute;
  top: 0;
  border-radius: 50%;
  overflow: hidden;
}

.course_chatIcon img {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  height: auto;
  min-height: 100%;
}

.course_chatInfo {
  height: 30px;
  line-height: 30px;
  font-size: 12px;
  font-weight: 400;
  color: #939393;
  position: absolute;
  top: -10px;
}

.course_chatText {
  display: inline-block;
  width: auto;
  max-width: 100%;
  height: auto;
  box-sizing: border-box;
  padding: 10px 16px;
  font-size: 15px;
  font-weight: 400;
  line-height: 30px;
  color: #eeeeee;
  background: #272831;
  border: 1px solid #2f313b;
  border-radius: 6px;
}

.course_chatItem.isnotMine .course_chatIcon {
  left: 0;
}

.course_chatItem.isMine .course_chatIcon {
  right: 0;
}

.course_chatItem.isnotMine .course_chatInfo {
  text-align: left;
  left: 45px;
}

.course_chatItem.isMine .course_chatInfo {
  text-align: right;
  right: 45px;
}

.course_chatItem.isnotMine .course_chatInfo span {
  margin: 0 20px 0 0;
}

.course_chatItem.isMine .course_chatInfo span {
  margin: 0 0 0 20px;
}

.course_chatInfolabel {
  display: inline-block;
  width: auto;
  height: 16px;
  line-height: 16px;
  padding: 0 8px;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #dfd9bd;
  background: linear-gradient(to right, #1d8486, #3e9151);
  border-radius: 8px;
  margin-top: 7px !important;
}

.course_chatItem.isnotMine .course_chatText {
  float: left;
}

.course_chatItem.isMine .course_chatText {
  float: right;
}

.course_chatItem.isMine .course_chatInfo span {
  float: right;
}

.acyivity_box {
  width: 100%;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
  background: #ffffff;
  border-radius: 5px;
  box-sizing: border-box;
  padding: 0 36px 36px;
}

.acyivity_info {
  width: 100%;
  height: 100px;
  position: relative;
}

.acyivity_infolabel {
  width: 100%;
  float: left;
  height: 31px;
  font-size: 24px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  line-height: 31px;
  margin: 28px 0 8px;
}

.acyivity_infotips {
  width: 100%;
  height: 19px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #1d8486;
  line-height: 19px;
}

.acyivity_inforight {
  height: 46px;
  width: auto;
  position: absolute;
  right: 36px;
  top: 36px;
}

.acyivity_inforight_btn {
  width: 130px;
  height: 46px;
  text-align: center;
  line-height: 46px;
  text-align: center;
  background: linear-gradient(to right, #1d8486, #3e9151);
  color: #ffffff;
  font-size: 20px;
  font-family: Source Han Serif CN-Heavy, Source Han Serif CN;
  letter-spacing: 1px;
  border-radius: 23px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  float: left;
  margin: 0 0 0 20px;
}

.acyivity_inforight_state {
  width: auto;
  height: 46px;
  text-align: center;
  line-height: 46px;
  text-align: center;
  color: #1d8486;
  font-size: 20px;
  font-family: Source Han Serif CN-Heavy, Source Han Serif CN;
  letter-spacing: 1px;
  float: left;
  margin: 0 0 0 20px;
}

.acyivity_cont {
  width: 100%;
  height: calc(100% - 100px);
  background: #ebf1f2;
  border-radius: 5px;
  position: relative;
}

.acyivity_contsti {
  box-sizing: border-box;
  padding: 16px 0 0 16px;
}

.acyivity_cont_left {
  width: calc(100% - 555px);
  height: 100%;
  float: left;
  box-sizing: border-box;
  border-right: 1px solid #d5dfe1;
  box-sizing: border-box;
  padding: 0 24px;
}

.acyivity_cont_lefttop {
  height: 73px;
  line-height: 72px;
  box-sizing: border-box;
  border-bottom: 1px solid #d5dfe1;
  font-size: 20px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  vertical-align: middle;
}

.acyivity_cont_lefttop.padding {
  width: calc(100% - 48px);
  margin: 0 auto;
}

.acyivity_cont_lefttop span {
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #de6c6c;
  margin: 0 0 0 12px;
}

.acyivity_cont_lefttop span.info {
  color: #1d8486;
}

.acyivity_cont_leftbottom {
  width: 100%;
  height: calc(100% - 73px);
  overflow-y: auto;
  box-sizing: border-box;
  padding: 20px 0 0 0;
}

.acyivity_cont_leftbtns {
  float: right;
  height: 40px;
  margin: 19px 0 0 0;
}

.acyivity_cont_leftbtn {
  float: left;
  height: 40px;
  width: 115px;
  box-sizing: border-box;
  border: 1px solid #84861d;
  border-radius: 4px;
  text-align: center;
  line-height: 39px;
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #84861d;
  margin: 0 0 0 16px;
}

.acyivity_cont_right {
  width: 555px;
  height: 100%;
  float: left;
  box-sizing: border-box;
  border-right: 1px solid #d5dfe1;
  box-sizing: border-box;
  padding: 0 24px;
}

.acyivity_cont_topic {
  box-sizing: border-box;
  padding: 0 0 0 30px;
}

.acyivity_cont_users {
  width: 100%;
  height: auto;
  display: flex;
  flex-wrap: wrap;
}

.acyivity_cont_user {
  float: left;
  width: 80px;
  height: 80px;
  margin: 0 0 24px 0;
}

.acyivity_cont_usericon {
  width: 48px;
  margin: 0 auto 8px;
  border-radius: 5px;
  overflow: hidden;
  cursor: pointer;
}

.acyivity_cont_usericon img {
  width: 100%;
  height: auto;
  min-height: 100%;
}

.acyivity_cont_username {
  width: 100%;
  height: 21px;
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  line-height: 21px;
  text-align: center;
  cursor: pointer;
}

.acyivity_cont_user:hover .acyivity_cont_username {
  color: #1d8486;
}

.user_score {
  width: 400px;
  height: auto;
}

.active_select_user {
  width: 100%;
  height: auto;
  margin: 0 0 30px 0;
}

.active_select_usericon {
  width: 108px;
  height: 108px;
  margin: 0 auto 16px;
  border-radius: 5px;
  overflow: hidden;
  cursor: pointer;
}

.active_select_usericon img {
  width: 100%;
  height: auto;
  min-height: 100%;
}

.active_select_username {
  width: 100%;
  height: 26px;
  font-size: 20px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  line-height: 26px;
  text-align: center;
  margin: 0 0 4px 0;
}

.active_select_username .male {
  color: #409eff;
}

.active_select_username .famale {
  color: #d977dc;
}

.active_select_userclass {
  height: 19px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #adb9bb;
  line-height: 19px;
  text-align: center;
}

.active_select_score {
  width: 100%;
  text-align: center;
  margin: 0 0 12px;
}

.active_select_tip {
  margin: 0 0 24px;
  text-align: center;
  height: 16px;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #de6c6c;
  line-height: 16px;
}

.acyivity_cont_select {
  width: 400px;
  height: auto;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.acyivity_cont_diss {
  width: 100%;
  height: calc(100% - 100px);
  border-radius: 5px;
  position: relative;
  box-sizing: border-box;
  padding: 60px 150px 0;
  overflow-y: auto;
}

.acyivity_cont_dissform {
  width: 100%;
  height: auto;
}

.acyivity_cont_dissuploads {
  width: 160px;
  height: 130px;
  border-radius: 4px;
  border: 1px dashed #adb9bb;
  position: relative;
  cursor: pointer;
  overflow: hidden;
}

.acyivity_cont_dissuploads img {
  max-width: 100%;
}

.acyivity_cont_disscover {
  width: 160px;
  height: 130px;
  background: #f2f3f3;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #1d8486;
  font-size: 18px;
  line-height: 130px;
}

.acyivity_cont_disstips {
  height: 32px;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #adb9bb;
  line-height: 32px;
}

.acyivity_cont_disslabel {
  width: 130px;
  float: left;
  height: 24px;
  font-size: 18px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  line-height: 24px;
}

.acyivity_cont_disslabel img {
  float: left;
  margin: 3px 5px 0 0;
}

.acyivity_cont_disscont {
  width: calc(100% - 130px);
  float: left;
  height: auto;
}

.acyivity_cont_dissleft {
  float: left;
  height: 100%;
  width: 435px;
  box-sizing: border-box;
  padding: 24px 0 0 24px;
}

.acyivity_cont_disstop {
  height: calc(100% - 310px);
  width: 100%;
  box-sizing: border-box;
  padding: 0 15px 20px 0;
  overflow-y: auto;
}

.acyivity_cont_disstopP1 {
  height: 24px;
  width: 100%;
  font-size: 18px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  line-height: 24px;
  margin: 0 0 16px 0;
}

.acyivity_cont_disstopP1 img {
  float: left;
  margin: 3px 5px 0 0;
}

.acyivity_cont_disstopP2 {
  height: 50px;
  font-size: 16px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #1d8486;
  line-height: 26px;
  margin: 0 0 6px 0;
}

.acyivity_cont_disstopP3 {
  width: auto;
  max-width: 100%;
  display: block;
  margin: 0 auto;
  background: #f8f8f8;
  border-radius: 8px;
}

.acyivity_cont_dissbottom {
  height: 310px;
  width: 100%;
}

.acyivity_cont_dissbottomlable {
  height: 50px;
  width: 100%;
  line-height: 50px;
  box-sizing: border-box;
  border-bottom: 1px solid #d5dfe1;
  font-size: 18px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  padding: 0 8px;
}

.acyivity_cont_disscloud {
  width: 100%;
  height: 260px;
}

.acyivity_cont_dissright {
  float: left;
  width: calc(100% - 490px);
  margin: 0 0 0 50px;
  height: 100%;
  box-sizing: border-box;
  padding: 12px 0 0 0;
}

.acyivity_cont_disslist {
  width: 100%;
  height: calc(100% - 50px);
  overflow-y: auto;
  box-sizing: border-box;
  padding: 0 24px 20px 0;
}

.interaction_discuss_rightnum {
  width: 205px;
  float: right;
  height: auto;
  box-sizing: border-box;
  padding: 0 0 0 40px;
}

.interaction_discuss_rightbtn {
  float: right;
  height: 28px;
  line-height: 28px;
  width: 58px;
  margin: 0 0 0 12px;
  background: #1d8486;
  text-align: center;
  font-size: 13px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #dfd9bd;
  border-radius: 14px;
}

.acyivity_test_right {
  width: 245px;
  height: 100%;
  float: left;
  box-sizing: border-box;
  border-right: 1px solid #d5dfe1;
  box-sizing: border-box;
  padding: 0;
}

.acyivity_test_left {
  width: calc(100% - 245px);
  height: 100%;
  float: left;
  box-sizing: border-box;
  border-right: 1px solid #d5dfe1;
  box-sizing: border-box;
  padding: 0 24px;
}

.acyivity_cont_leftbottomall {
  box-sizing: border-box;
  padding: 0 0 0 23px;
}

.acyivity_infotime {
  width: auto;
  height: 26px;
  position: absolute;
  left: 50%;
  top: 40px;
  transform: translateX(-50%);
  line-height: 26px;
  font-size: 24px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #1d8486;
  margin: 0 0 0 -100px;
}

.acyivity_infotime img {
  float: left;
  margin: 0 5px 0 0;
}

.reource_showP1 {
  width: 100%;
  height: 60px;
  box-sizing: border-box;
  padding: 0 50px;
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  border-bottom: 1px solid #e9ebeb;
  margin: 0 0 16px 0;
}

.reource_showP2 {
  height: 21px;
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  line-height: 21px;
  margin: 0 0 16px 0;
  box-sizing: border-box;
  padding: 0 50px;
}

.reource_showP3 {
  width: 100%;
  box-sizing: border-box;
  padding: 0 50px;
}

.reource_showdialog_body {
  padding: 0 0 20px 0;
  box-sizing: border-box;
}

.files_upload {
  width: 600px;
  height: auto;
}

.files_before_upload {
  width: 1100px;
  height: auto;
}

.upload_dialog_body {
  box-sizing: border-box;
  padding: 20px 40px;
  width: 100%;
  max-height: 500px;
  overflow-y: auto;
}

.files_view {
  width: 1100px;
  height: auto;
}

.fileview_dialog_body {
  padding: 20px 30px;
  height: 650px;
  overflow: hidden;
  box-sizing: border-box;
}

.fileview_dialog_body video,
.fileview_dialog_body iframe {
  width: 100%;
  height: 100%;
}

.fileview_dialog_body video {
  background: #333333;
}

.fileview_dialog_body img {
  display: block;
  width: auto;
  max-width: 100%;
  margin: 0 auto;
}

.piece_name {
  margin: 0 0 10px;
}

.chapter_namespan {
  margin: 0 0 10px;
}

.chapter_name {
  width: 100%;
  height: auto;
  line-height: 40px;
  font-size: 26px;
}

.section_name {
  width: 100%;
  height: auto;
  line-height: 40px;
  font-size: 20px;
  margin: 0 0 20px;
}

.question_delete {
  position: absolute;
  right: -8px;
  top: -8px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #ff0000;
  color: #ffffff;
  cursor: pointer;
  line-height: 16px;
  text-align: center;
  font-size: 12px;
  border: 1px solid #ff0000;
}

.question_delete:hover {
  border: 1px solid #ffffff;
}

.fils_list_new {
  background: #ffffff;
  border-radius: 8px;
  box-sizing: border-box;
  padding: 12px 0 0;
  overflow-y: auto;
  height: auto;
  min-height: calc(100% - 162px);
}

.fils_list_newmini {
  min-height: initial;
}

.fils_item_new {
  width: calc(14.28% - 20px);
  height: 155px;
  margin: 0 20px 20px 0;
  float: left;
  box-sizing: border-box;
  padding: 10px 10px 0;
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.04);
  border-radius: 5px;
}

.fils_item_new.small {
  background: #ffffff;
  width: calc(50% - 10px);
  margin: 0 5px 10px;
  cursor: pointer;
  padding: 0;
  height: auto;
}

.fils_item_new:hover {
  background: #f2f3f3;
  border-radius: 6px;
}

.fils_itemcover_new {
  position: absolute;
  z-index: 1;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  cursor: pointer;
}

.fils_itemselect_new {
  position: absolute;
  z-index: 1;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  cursor: pointer;
  background: rgba(0, 0, 0, 0.2);
}

.fils_item_newicon {
  width: auto;
  height: 85px;
  width: 100%;
  position: relative;
  margin: 0 0 4px 0;
  overflow: hidden;
}

.fils_item_newicon img {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  max-width: 90%;
  max-height: 90%;
}

.fils_item_newname {
  width: 100%;
  height: 30px;
  margin: 0 0 3px 0;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #303a40;
  line-height: 30px;
  box-sizing: border-box;
  padding: 0 10px;
}

.fils_item_newname.more {
  height: 50px;
  line-height: 25px;
}

.fils_item_newtime {
  width: 100%;
  height: 16px;
  text-align: center;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #cdd6d9;
  line-height: 16px;
}

.edit_info {
  cursor: text;
}

.change_input {
  width: calc(100% - 30px);
  display: block;
  margin: 0 auto;
}

.change_input .el-input__wrapper {
  width: 100%;
}

.change_input .el-input__inner {
  height: 26px;
  width: 100%;
  line-height: 26px;
  text-align: center;
}

.context_menus {
  background: #ffffff;
  border: 1px solid #aaaaaa;
  width: 115px;
  height: auto;
}

.context-menu-list:hover {
  background: #e6e6e6 !important;
}

.nav-icon-fontawe {
  left: 11px !important;
}

.nav-name-right {
  margin: 0 20px 0 25px !important;
}

.fils_item_newshare {
  position: absolute;
  right: 10px;
  top: 10px;
  width: 50px;
  height: 20px;
  background: rgba(29, 132, 134, 0.16);
  border-radius: 10px;
  color: #1d8486;
  text-align: center;
  line-height: 20px;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #1d8486;
  z-index: 1;
}

.reource_sort {
  cursor: pointer;
  float: right;
  height: 46px;
  width: auto;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #6a757b;
  line-height: 46px;
  margin: 0 15px 0 0;
}

.reource_sort img {
  float: left;
  margin: 15px 8px 0 0;
}

.acyivity_contsti_cont {
  float: left;
  margin: 0 16px 16px 0;
  background: #ffffff;
  border-radius: 6px;
}

.acyivity_contsti1 {
  width: calc(53% - 16px);
  height: calc(55% - 16px);
}

.acyivity_contsti2 {
  width: calc(47% - 16px);
  height: calc(55% - 16px);
}

.acyivity_contsti3 {
  width: calc(62% - 16px);
  height: calc(45% - 16px);
}

.acyivity_contsti4 {
  width: calc(38% - 16px);
  height: calc(45% - 16px);
}

.acyivity_contsti_title {
  width: 100%;
  height: 53px;
  box-sizing: border-box;
  padding: 0 16px;
  line-height: 52px;
  border-bottom: 1px solid #f2f3f3;
  font-size: 16px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #303a40;
}

.acyivity_contsti_box {
  width: 100%;
  height: calc(100% - 53px);
  overflow: hidden;
}

.drawerTitle {
  font-size: 20px;
  color: #333333;
}

.test_itemhide_listmore {
  height: calc(100% - 25px);
  width: 100%;
  box-sizing: border-box;
  padding: 0 15px;
  overflow-y: auto;
}

.test_itemhide_listmorepand {
  box-sizing: border-box;
  padding: 0 100px 0 60px;
}

.noinfo {
  width: 100%;
  height: 220px;
  text-align: center;
  line-height: 220px;
  font-size: 26px;
  font-weight: bold;
  color: #999999;
  position: relative;
  z-index: 1;
}

.info_cancel {
  display: block;
  width: 90%;
  margin: 5px auto;
  height: 30px;
  line-height: 30px;
  border-radius: 5px;
  box-sizing: border-box;
  padding: 0 10px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #9c9c9c;
}

.course_teacher_show {
  float: left;
  width: calc(14.28% - 30px);
  margin: 0 15px 30px;
  background: #ffffff;
  box-shadow: 0px 0px 6px 1px rgba(29, 132, 134, 0.14);
  height: 210px;
  box-sizing: border-box;
  padding: 25px 0 0;
  text-align: center;
}

.course_teacher_show:hover {
  box-shadow: 0px 0px 6px 1px rgba(29, 132, 134, 0.44);
}

.course_teacher_showicon {
  width: 96px;
  height: 96px;
  margin: 0 auto 16px;
  border-radius: 50%;
  overflow: hidden;
}

.course_teacher_showicon img {
  width: 100%;
  height: auto;
  min-height: 100%;
}

.course_teacher_showname {
  height: 21px;
  font-size: 16px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #303a40;
  line-height: 21px;
  margin: 0 auto 8px;
  text-align: center;
}

.course_teacher_showdesc {
  height: 17px;
  font-size: 13px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #adb9bb;
  line-height: 17px;
  text-align: center;
}

.course_study_over {
  overflow: hidden;
}

.datileinfo_btn {
  width: auto;
  padding: 0 11px;
  height: 36px;
  text-align: center;
  line-height: 36px;
  background: #3d73f7;
  color: #ffffff;
  font-size: 16px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #ffffff;
  border-radius: 4px;
  float: left;
}

.datileinfo_btnnormal {
  width: auto;
  height: 30px;
  text-align: center;
  line-height: 30px;
  text-align: center;
  font-size: 16px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #5d6373;
  margin: 0 30px 0 0;
}

.datileinfo_btnnormal:hover {
  color: #3d73f7;
}

.datileinfo_btnnormal i {
  margin: 0 5px 0 0;
  position: relative;
  top: 2px;
}

.classroom_detail_newbtn {
  width: auto;
  padding: 0 20px;
  height: 32px;
  text-align: center;
  line-height: 32px;
  text-align: center;
  background: #ffffff;
  color: #3d3d3d;
  font-size: 14px;
  font-family: Source Han Serif CN-Heavy, Source Han Serif CN;
  letter-spacing: 1px;
  border-radius: 16px;
  border: 1px solid #e4edee;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  float: left;
  margin: 19px 0 0 0;
}

.datileinfo_btnnormal.small {
  height: 32px;
  line-height: 32px;
  border-radius: 16px;
  margin: -1px 20px 0 0;
}

.datileinfo_btnclear {
  width: auto;
  padding: 0 20px 0 0;
  height: 32px;
  text-align: center;
  line-height: 32px;
  text-align: center;
  color: #3d3d3d;
  font-size: 14px;
  font-family: Source Han Serif CN-Heavy, Source Han Serif CN;
  letter-spacing: 1px;
  border-radius: 15px;
  float: left;
  margin: 0 12px 0 0;
}

.datileinfo_btnclear.active {
  color: #2383f9;
}

.datileinfo_btnmar {
  margin: 12px 0 0 0;
}

.head_top_box {
  width: 100%;
  height: 80px;
  box-sizing: border-box;
  padding: 0 30px 0 25px;
  position: absolute;
  z-index: 2;
  background: linear-gradient(180deg, #f0f2ff 0%, #eef0ff 100%);
}

.head_logo {
  float: left;
  margin: 17px 12px 0 0;
  cursor: pointer;
}

.head_top_user {
  height: 44px;
  float: right;
  width: auto;
  margin: 18px 0 0 0;
  cursor: pointer;
  position: relative;
}

.head_left_box {
  width: 130px;
  height: calc(100% - 80px);
  position: absolute;
  left: 0;
  top: 80px;
  overflow: hidden;
  box-sizing: border-box;
  border-right: 1px solid #e9e5fa;
  background: #faf9ff;
}

.head_left_menus {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  box-sizing: border-box;
}

.head_left_menu {
  width: 100%;
  height: 110px;
  margin: 0 auto;
  text-align: center;
  overflow: hidden;
  box-sizing: border-box;
  padding: 30px 0 0 0;
  font-family: Source Han Sans CN;
  font-size: 14px;
  cursor: pointer;
  margin: 0 0 30px;
}

.head_left_menu img {
  display: block;
  margin: 0 auto 8px;
}

.head_left_menu:hover,
.head_left_menu.active {
  color: #2383f9;
}

.head_top_controltext {
  width: auto;
  float: left;
  margin: 0 12px 0 0;
  height: 44px;
  line-height: 44px;
  font-size: 15px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  color: #3d3d3d;
}

.all_new_cont {
  width: 100%;
  height: 100%;
  /* margin: 80px 0 0 130px; */
  background: #ffffff;
  overflow: hidden;
  position: relative;
}

.attendclass_new_cont {
  width: 100%;
  height: calc(100% - 80px);
  margin: 80px 0 0 0;
  background: linear-gradient(270deg, #fafafa 0%, rgba(214, 234, 244, 0.3) 77%, #def0fa 100%);
  overflow: hidden;
  position: relative;
}

.pptmain_new_cont {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.real_label_text_new {
  display: inline-block;
  height: 40px;
  line-height: 40px;
  font-size: 20px;
  font-family: PingFang SC, PingFang SC;
  font-weight: bold;
  color: #000000;
}

.leftp {
  box-sizing: border-box;
  padding: 0 0 0 30px;
}

.real_controls_new {
  display: inline-block;
  height: 40px;
  line-height: 40px;
  font-size: 16px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #2383f9;
  margin: 0 0 0 20px;
}

.real_controls_new i {
  position: relative;
  top: 2px;
  margin: 0 0 0 5px;
}

.real_controls_new.index {
  float: right;
  margin: 0 30px 0 0;
  font-size: 14px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #1890ff;
}

.real_controls_new.more {
  float: right;
  color: #666666;
  margin: 0 30px 0 0;
}

.real_selects_new {
  display: inline-block;
  margin: 4px 20px 0 0;
  height: 32px;
  line-height: 32px;
}

.real_selects_new.more {
  margin: 0 42px 0 0;
}

.real_selects_newnum {
  margin: 0 20px 0 0;
}

.real_label_text_tip {
  display: inline-block;
  height: 40px;
  line-height: 42px;
  font-size: 17px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #444444;
  margin: 0 0 0 30px;
}

.real_cont_new {
  width: calc(100% - 100px);
  height: 100%;
  margin: 0 auto;
  box-sizing: border-box;
  padding: 30px 0 20px;
}

.delete_cont_new {
  width: calc(100% - 50px);
  height: 100%;
  margin: 0 0 0 50px;
  box-sizing: border-box;
  padding: 30px 0 20px;
}

.textbook_item_new {
  width: calc(20% - 40px);
  float: left;
  height: auto;
  margin: 0 40px 20px 0;
  background: #fafafa;
  cursor: pointer;
  box-sizing: border-box;
  border: 1px solid #f2f3f3;
  padding: 0 0 12px;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 3px 6px 1px #fafafa;
}

.textbook_item_new.all {
  width: 100%;
  margin: 0 auto 20px;
}

.textbook_itemicon_new {
  width: 100%;
  height: 125px;
  overflow: hidden;
}

.textbook_itemicon_new.all {
  width: 100%;
  height: 100px;
  overflow: hidden;
}

.textbook_itemicon_new img {
  width: 100%;
  height: auto;
  min-height: 100%;
}

.textbook_itemp1_new {
  height: 25px;
  font-size: 16px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  font-weight: bold;
  color: #303a40;
  line-height: 25px;
  margin: 8px 0;
  text-indent: 16px;
}

.textbook_itemp2_new {
  height: 18px;
  font-size: 14px;
  font-family: Microsoft YaHei-Light, Microsoft YaHei;
  font-weight: 400;
  color: #e99315;
  line-height: 18px;
  text-indent: 5px;
  margin: 0 0 12px 0;
  box-sizing: border-box;
  padding: 0 95px 0 0;
  text-indent: 16px;
  position: relative;
}

.textbook_itemp2_new span {
  position: absolute;
  right: 10px;
  top: 0;
  text-align: right;
  color: #2383f9;
}

.textbook_itemp3_new {
  height: 17px;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #6a757b;
  line-height: 17px;
  box-sizing: border-box;
  padding: 0 0 0 40px;
  position: relative;
  margin: 0 0 12px 0;
}

.textbook_itemp3_new img {
  position: absolute;
  left: 15px;
  top: 0px;
}

.textbook_itemp4_new {
  display: none;
  position: absolute;
  right: 5px;
  bottom: 5px;
}

.textbook_item_new:hover {
  border: 1px solid #1d8486;
  box-shadow: 0 3px 6px 1px rgba(29, 132, 134, 0.15);
}

.textbook_item_new:hover .textbook_itemp4_new {
  display: block;
}

.textbook_itemp5_new {
  position: absolute;
  left: 0;
  top: 110%;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.302);
  transition: all 0.3s;
}

.textbook_itemp6_new {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.textbook_item_new:hover .textbook_itemp5_new {
  top: 0;
}

.real_label_tooltip_new {
  color: #2383f9;
  font-size: 24px;
  cursor: pointer;
  position: relative;
  top: 3px;
}

.all_new_detail {
  width: 100%;
  height: calc(100% - 80px);
  margin: 80px 0 0 0;
  overflow: hidden;
}

.all_new_detail.active {
  background: #ffffff;
}

.classroom_left {
  width: 300px;
  height: calc(100% - 80px);
  margin: 50px 50px 0 0;
  float: left;
  background: #faf9f3;
  border: 1px solid #26499d;
  border-radius: 4px;
  position: relative;
  box-sizing: border-box;
  padding: 35px 0 0;
}

.classroom_left:before {
  content: ' ';
  position: absolute;
  left: 50%;
  top: -35px;
  height: 35px;
  width: 170px;
  margin: 0 0 0 -85px;
  background: #faf9f3;
  border: 1px solid #26499d;
  border-bottom: none;
  border-radius: 4px;
}

.classroom_left_title {
  width: 130px;
  position: absolute;
  left: 50%;
  margin: 0 0 0 -65px;
  top: -35px;
  height: 50px;
  text-align: center;
  line-height: 50px;
  font-family: Source Han Sans CN;
  font-size: 21px;
  color: #2383f9;
  border-bottom: 2px solid #2383f9;
}

.classroom_left_cont {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  box-sizing: border-box;
  padding: 0 20px;
}

.classroom_right {
  width: calc(100% - 350px);
  float: right;
  height: calc(100% - 30px);
  position: relative;
}

.classroom_right_main {
  width: 100%;
  height: calc(100% - 130px);
}

.classroom_right_btns {
  height: auto;
  margin: 0 0 20px 0;
}

.classroom_right_btn {
  width: auto;
  height: 36px;
  float: left;
  border-radius: 4px;
  background: #3d73f7;
  text-align: center;
  line-height: 36px;
  font-size: 16px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #ffffff;
  letter-spacing: 1px;
  position: relative;
  float: right;
  padding: 0 11px;
  margin: 4.5px 0 0 20px;
}

.classroom_right_btn.left {
  float: left;
  margin: 4.5px 20px 0 0;
}

.classroom_right_btn.colorful {
  background: #eff3ff;
  color: #3d73f7;
}

.classroom_right_input {
  width: calc(100% - 650px);
  height: 40px;
  float: right;
  margin: 5px 0 0 0;
}

.classroom_right_inputitem {
  width: 100%;
  height: 100%;
  position: relative;
}

.classroom_right_inputitem input {
  width: 100%;
  height: 100%;
  outline: none;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background: #e7eaf0;
  border-radius: 22.5px;
  padding: 0 65px 0 15px;
  box-sizing: border-box;
  font-size: 15px;
  background: #eff3ff;
}

.classroom_right_inputicon {
  position: absolute;
  right: 25px;
  top: 50%;
  transform: translateY(-50%);
}

.classroom_right_tabs {
  float: right;
  height: 40px;
  line-height: 40px;
}

.classroom_right_tab {
  height: 30px;
  float: left;
  margin: 5px 0 0 15px;
  width: 30px;
  text-align: center;
  cursor: pointer;
  position: relative;
  border-radius: 3px;
}

.classroom_right_tab img {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.classroom_right_tab:hover,
.classroom_right_tab.active {
  background: #ececed;
}

.classroom_right_list {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 0 15px 0 0;
  overflow-y: auto;
}

.classroom_one_item {
  width: 100%;
  height: auto;
}

.classroom_one_itemlabel {
  width: 100%;
  height: 30px;
  float: left;
  margin: 0 0 30px 0;
  position: relative;
}

.classroom_one_itemlabel:before {
  content: ' ';
  position: absolute;
  left: 0;
  top: 50%;
  width: 100%;
  margin: -0.5px 0 0 0;
  border-top: 1px solid #d8d8d8;
}

.classroom_one_labelcont {
  width: auto;
  position: absolute;
  left: 0;
  top: 0;
  height: 30px;
  background: #ffffff;
  padding: 0 10px 0 0;
  line-height: 30px;
  font-size: 18px;
  color: #e99315;
  letter-spacing: 1px;
}

.classroom_one_labelcont img {
  float: left;
  margin: 8px 6px 0 0;
}

.classroom_one_of {
  width: auto;
  position: absolute;
  right: 0;
  top: 0;
  height: 30px;
  background: #ffffff;
  padding: 0 0 0 10px;
  line-height: 30px;
  font-size: 16px;
  color: #2383f9;
  letter-spacing: 1px;
  cursor: pointer;
}

.classroom_one_list {
  width: 100%;
  float: left;
  height: auto;
}

.classroom_one_info {
  width: calc(33.3% - 30px);
  margin: 0 15px 30px;
  height: auto;
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.3);
  float: left;
  border-radius: 5px;
}

.classroom_one_infotop {
  height: auto;
  width: 100%;
  background: #ececed;
  box-sizing: border-box;
  padding: 10px;
}

.classroom_one_infotopP1 {
  font-size: 16px;
  height: 30px;
  line-height: 30px;
  color: #3d3d3d;
  margin: 0 0 14px 0;
}

.classroom_one_infotopP2 {
  font-family: Source Han Sans CN;
  font-size: 15px;
  color: #717071;
  line-height: 30px;
  height: 30px;
  box-sizing: border-box;
  position: relative;
}

.classroom_one_infotopP2.icon {
  padding: 0 0 0 20px;
}

.classroom_one_infotopP2 img {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

.classroom_one_infotopP3 {
  margin: 14px 0 0;
  font-size: 14px;
}

.classroom_one_infobot {
  height: auto;
  width: 100%;
  box-sizing: border-box;
  padding: 14px;
}

.classroom_right_tabinfo {
  float: left;
  margin: 0 0 0 15px;
  font-size: 15px;
  line-height: 35px;
}

.classroom_right_tabtext {
  float: left;
  margin: 0 15px 0 0;
  font-size: 15px;
  line-height: 40px;
}

.classroom_right_table {
  width: 100%;
  height: calc(100% - 65px);
}

.classroom_right_menus {
  width: calc(100% - 450px);
  float: left;
  margin: 0 0 0 35px;
  height: 40px;
  position: relative;
}

.classroom_right_menus .el-tabs__nav-wrap::after {
  display: none;
}

.classroom_right_menus .el-tabs__item:hover,
.classroom_right_menus .el-tabs__item.is-active {
  color: #e99315 !important;
}

.attendclass_textbook_right {
  width: 70px;
  height: 100%;
  position: absolute;
  right: 10px;
  top: 0;
  box-sizing: border-box;
  padding: 70px 0 0;
  text-align: center;
}

.attendclass_textbook_fileitem {
  width: 28px;
  text-align: center;
  margin: 0 auto 20px;
  height: auto;
  cursor: pointer;
  font-family: Source Han Sans CN;
  font-size: 14px;
  color: #3d3d3d;
  line-height: 22px;
}

.attendclass_textbook_fileitem img {
  display: block;
  margin: 0 auto 7px;
}

.attendclass_textbook_fileitem:hover {
  color: #ff2066;
}

.attendclass_textbook_fileitem.other:hover {
  color: #2383f9;
}

.attendclass_textbook_filestand {
  height: 15px;
  margin: 0 auto;
}

.textbook_right_files {
  position: absolute;
  right: 0;
  top: 0;
  width: 455px;
  height: 100%;
  background: #d9e2ea;
  z-index: 1;
  border-left: 1px solid #67654e;
}

.textbook_right_filesshow {
  position: absolute;
  right: 0;
  top: 0;
  width: 455px;
  height: 100%;
  background: #d9e2ea;
  z-index: 1;
  border-left: 1px solid #67654e;
}

.textbook_right_filesshow.FILE {
  width: 700px;
}

.textbook_right_filesshow.LINK,
.textbook_right_filesshow.VIDEO {
  width: 100%;
}

.textbook_right_filesname {
  width: 100%;
  height: 50px;
  line-height: 50px;
  box-sizing: border-box;
  padding: 0 100px 0 20px;
  position: relative;
}

.textbook_right_filesclose {
  width: 50px;
  height: 50px;
  text-align: center;
  line-height: 50px;
  font-size: 22px;
  position: absolute;
  right: 0;
  top: 0;
}

.textbook_right_filescont {
  width: 100%;
  height: calc(100% - 50px);
  overflow-y: auto;
  background: #ffffff;
  box-sizing: border-box;
}

.textbook_right_filescont iframe,
.textbook_right_filescont video {
  width: calc(100% - 4px);
  height: calc(100% - 4px);
  overflow: hidden;
  border: none;
  display: block;
  margin: 0 auto;
}

.textbook_right_filescont img {
  width: auto;
  max-width: calc(100% - 4px);
  display: block;
  margin: 0 auto;
}

.textbook_right_filescont video {
  background: #333333;
}

.textbook_right_fileslist {
  width: 100%;
  height: calc(100% - 50px);
  overflow-y: auto;
  box-sizing: border-box;
}

.textbook_right_filesscroll {
  width: calc(100% - 24px);
  height: calc(100% - 50px);
  overflow-y: auto;
  box-sizing: border-box;
  padding: 0 10px 0 5px;
  margin: 0 auto;
}

.attendclass_left_item_info {
  position: relative;
  border-radius: 5px;
  overflow: hidden;
}

.attendclass_left_item_video {
  width: calc(50% - 10px);
  margin: 0 20px 20px 0;
  height: auto;
}

.attendclass_left_item_video:nth-child(2n) {
  margin: 0 0 20px;
}

.attendclass_left_item_videoname {
  width: 100%;
  height: 30px;
  line-height: 30px;
  font-size: 15px;
  color: #304243;
}

.attendclass_left_item_videocont {
  height: auto;
  width: 100%;
  aspect-ratio: 16 / 9;
  overflow: hidden;
  background: #000000;
  position: relative;
}

.attendclass_left_item_videocont video {
  width: 100%;
  height: 100%;
}

.attendclass_left_item_videoclick {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.attendclass_left_item_videoclick img {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 40%;
  transform: translate(-50%, -50%);
}

.attendclass_left_item_file {
  width: 100%;
  height: 42px;
  margin: 0 0 15px 0;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #e4edee;
  box-sizing: border-box;
  line-height: 40px;
  position: relative;
  padding: 0 50px 0 40px;
}

.attendclass_left_item_file span {
  cursor: pointer;
}

.attendclass_left_item_fileicon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
}

.attendclass_left_item_filedelete {
  width: 40px;
  height: 40px;
  position: absolute;
  right: 0;
  top: 0;
  text-align: center;
  line-height: 40px;
  color: red;
  display: none;
  background: #eeeeee;
  z-index: 2;
  cursor: pointer;
  border-radius: 4px;
  font-size: 18px;
}

.attendclass_left_item_info:hover .attendclass_left_item_filedelete {
  display: block;
}

.attendclass_left_item_filedelete:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.resources_btns_new {
  width: 100%;
  height: auto;
  box-sizing: border-box;
  padding: 10px;
  background: rgba(0, 0, 0, 0.051);
  border-radius: 5px;
}

.real_input_new {
  float: left;
}

.reource_sort_new {
  cursor: pointer;
  float: right;
  height: 34px;
  width: auto;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #6a757b;
  line-height: 34px;
  margin: 0 15px 0 0;
}

.reource_sort_new img {
  float: left;
  margin: 9px 8px 0 0;
}

.reource_add_btns {
  height: 34px;
}

.upload_btn {
  width: calc(33.3% - 55px);
  float: left;
  margin: 0 55px 40px 0;
  height: 120px;
  border-radius: 5px;
  border: 1px solid #2383f9;
  background: #ffffff;
  position: relative;
  box-sizing: border-box;
  padding: 0 0 0 120px;
  line-height: 120px;
  font-family: Source Han Sans CN;
  font-size: 21px;
  color: #2383f9;
}

.upload_btn:nth-child(3n) {
  margin: 0 0 40px 0;
}

.upload_btn img {
  position: absolute;
  left: 45px;
  top: 50%;
  transform: translateY(-50%);
}

.upload_btn_tip {
  width: 20px;
  height: 20px;
  position: absolute;
  left: 5px;
  top: 5px;
  text-align: center;
  line-height: 20px;
  font-size: 20px;
  color: #2383f9;
}

.ppt_item_new {
  width: calc(16.6% - 40px);
  margin: 0 40px 30px 0;
  height: 150px;
  background: #ececed;
  border-radius: 4px;
  float: left;
  box-sizing: border-box;
  padding: 11px 0 0 0;
  position: relative;
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.302);
  overflow: hidden;
  transition: all 0.3s;
  cursor: pointer;
}

.ppt_item_new.big {
  width: calc(20% - 40px);
  margin: 0 25px 30px 15px;
}

.ppt_item_p1 {
  width: 100%;
  height: 65px;
  box-sizing: border-box;
  padding: 0 10px;
  line-height: 30px;
  font-family: 思源黑体;
  font-size: 16px;
  font-weight: normal;
  color: #3d3d3d;
  overflow: hidden;
  margin: 0 0 5px;
}

.ppt_item_p2 {
  width: 100%;
  height: 55px;
  position: absolute;
  left: 0;
  bottom: 0;
  background: #fafafa;
  box-sizing: border-box;
  padding: 0 55px 0 10px;
  color: #2383f9;
  line-height: 74px;
  font-size: 12px;
}

.ppt_item_p3 {
  position: absolute;
  right: 10px;
  bottom: 0;
}

.ppt_item_p4 {
  position: absolute;
  left: 0;
  top: 110%;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.302);
  transition: all 0.3s;
}

.ppt_item_p5 {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  width: 100%;
}

.ppt_item_p6 {
  position: absolute;
  right: 10px;
  bottom: 10px;
}

.ppt_item_p7 {
  width: 100%;
  height: auto;
}

.ppt_item_p8 {
  height: 30px;
  line-height: 30px;
  margin: 0 0 5px 0;
  box-sizing: border-box;
  text-align: center;
}

.ppt_item_p8:last-child {
  margin: 0;
}

.ppt_item_p8:hover {
  background: #f1f1f1;
  cursor: pointer;
}

.ppt_item_p9 {
  position: absolute;
  left: 0;
  top: 0;
  width: auto;
  height: 20px;
  padding: 0 12px;
  background: #2383f9;
  color: #ffffff;
  font-size: 12px;
  border-bottom-right-radius: 10px;
  line-height: 20px;
}

.ppt_item_new:hover .ppt_item_p4 {
  top: 0;
}

.real_filecont_new {
  width: calc(100% - 175px);
  height: 100%;
  box-sizing: border-box;
  position: relative;
  float: left;
}

.real_filecont_left {
  width: 200px;
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  overflow-y: auto;
  background: #eef1f6;
  box-sizing: border-box;
  text-align: center;
}

.real_filecont_leftP1 {
  font-size: 18px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #000000;
  height: 58px;
  box-sizing: border-box;
  line-height: 58px;
  text-align: center;
  border-bottom: 1px solid #e4e4e4;
}

.real_filecont_leftP2 {
  font-family: 思源黑体;
  font-size: 14px;
  color: #3d3d3d;
  height: 40px;
  line-height: 40px;
  margin: 0 0 10px;
  cursor: pointer;
}

.real_filecont_leftP2.active {
  color: #2383f9;
}

.files_btns_new {
  width: 100%;
  height: 40px;
  position: relative;
  box-sizing: border-box;
}

.files_input_new {
  float: left;
  margin: 2.5px 20px 0 0;
  height: 100%;
}

.fils_main_scroll {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 20px 30px 10px;
  overflow-y: auto;
}

.fils_nav_new {
  float: left;
  height: 24px;
  position: relative;
  margin: 8px 0 0 0;
  box-sizing: border-box;
  padding: 0 0 0 70px;
  line-height: 24px;
}

.fils_nav_new.nomargin {
  margin: 8px 0 0 0;
}

.fils_navarr_new {
  position: absolute;
  left: 0;
  top: 0;
  height: 24px;
}

.fils_item_newselect {
  position: absolute;
  left: 15px;
  top: 15px;
  width: 22px;
  height: 22px;
  text-align: center;
  line-height: 22px;
  box-sizing: border-box;
  border: 1px solid #000000;
  cursor: pointer;
  color: #fafafa;
  background: #fafafa;
  z-index: 1;
}

.fils_item_newselect.active {
  background: #2383f9;
  border: 1px solid #2383f9;
}

.recyclebin_btns {
  text-align: center;
}

.real_classcont_new {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 0 0 0 370px;
  position: relative;
}

.real_classcont_left {
  width: 360px;
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  overflow-y: auto;
  box-sizing: border-box;
  border-right: 1px solid #e4e4e4;
}

.real_classcont_left .el-tree {
  background: rgba(255, 255, 255, 0.5);
}

.real_classcont_left .el-tree-node__content {
  height: 40px;
}

.real_classcont_node {
  position: relative;
  width: 100%;
  height: 35px;
  line-height: 35px;
  box-sizing: border-box;
  padding: 0 35px 0 0;
  cursor: pointer;
}

.real_classcont_node.active {
  background: #f2f3ff;
}

.real_classcont_node.active .real_classcont_nodelabel {
  color: #3d73f7;
}

.real_classcont_nodelabel {
  width: 100%;
  overflow: hidden;
}

.real_classcont_nodelabelactive {
  display: none;
  width: auto;
  position: relative;
  left: 0;
  animation-name: leftStep;
  animation-duration: 5s;
  animation-iteration-count: infinite;
}

@keyframes leftStep {
  from {
    left: 0;
  }
  to {
    left: -80%;
  }
}

.real_classcont_nodetext:hover .real_classcont_nodelabelnormal {
  display: none;
}

.real_classcont_nodetext:hover .real_classcont_nodelabelactive {
  display: block;
}

.real_classcont_nodetext {
  width: 100%;
  height: 35px;
  position: relative;
  box-sizing: border-box;
  padding: 0 0 0 40px;
}

.real_classcont_nodeicon {
  width: 20px;
  height: 35px;
  text-align: center;
  line-height: 35px;
  font-size: 16px;
  position: absolute;
  left: 0;
  top: 0;
}

.real_classcont_nodeit {
  width: auto;
  height: auto;
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
}

.real_classcont_nodeimg {
  position: relative;
  top: -2px;
  margin: 0 4px 0 0;
  width: 16px;
}

.real_classcont_btns {
  position: absolute;
  right: 0;
  top: 0;
  line-height: 35px;
  width: 35px;
  height: 35px;
  text-align: center;
}

.real_classcont_btns:hover {
  color: #3d73f7;
}

.select_syllabus {
  color: #2383f9;
}

.real_index_new {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 20px 40px 0 40px;
  overflow-y: auto;
}

.mypopover {
  padding: 5px 0 !important;
  width: 100px !important;
  min-width: 0px !important;
}

.classroom_new_cont {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  box-sizing: border-box;
  padding: 40px 140px 20px 40px;
}

.classroom_new_top {
  width: 100%;
  height: auto;
  box-sizing: border-box;
  padding: 18px 26px;
  border-radius: 10px;
  background: #e7eaf0;
}

.classroom_new_input {
  width: 100%;
  height: auto;
}

.classroom_new_top .el-form-item__label {
  height: 36px;
  line-height: 36px;
}

.classroom_new_bottom {
  width: 100%;
  height: auto;
  box-sizing: border-box;
  padding: 30px 0 0 0;
}

.resources_select_main {
  width: 620px;
  height: auto;
  box-sizing: border-box;
}

.resources_select_headtop {
  width: 100%;
  height: 36px;
  text-align: center;
  line-height: 36px;
  background: #f9f9f9;
  font-size: 14px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #333333;
}

.resources_select_head {
  width: calc(100% - 30px);
  margin: 0 auto;
  height: 50px;
  border-bottom: 1px solid #d8d8d8;
  position: relative;
  box-sizing: border-box;
  padding: 0 50px 0 0;
}

.resources_select_headclose {
  width: 50px;
  height: 50px;
  text-align: center;
  cursor: pointer;
  line-height: 50px;
  font-size: 17px;
  position: absolute;
  right: 0;
  top: 0;
}

.resources_select_headtab {
  width: auto;
  float: left;
  height: 50px;
  margin: 0 35px 0 0;
  font-family: 思源黑体;
  font-size: 14px;
  color: #3d3d3d;
  line-height: 50px;
  cursor: pointer;
}

.resources_select_headtab i {
  position: relative;
  top: 1px;
}

.resources_select_headtab:last-child {
  margin: 0;
}

.resources_select_headtab:hover,
.resources_select_headtab.active {
  color: #2383f9;
}

.resources_select_body {
  width: 100%;
  height: auto;
  box-sizing: border-box;
  padding: 10px 0 5px;
}

.resources_select_search {
  width: calc(100% - 30px);
  margin: 0 auto 8px;
  height: 36px;
  position: relative;
}

.resources_select_search input {
  width: 100%;
  height: 100%;
  outline: none;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  padding: 0 45px 0 15px;
  box-sizing: border-box;
  font-size: 13px;
}

.resources_select_searchicon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  height: 16px;
}

.resources_select_list {
  width: 100%;
  height: 180px;
  overflow-y: auto;
  box-sizing: border-box;
  padding: 0 0 0 14px;
}

.resources_select_item {
  position: relative;
  overflow: hidden;
}

.resources_select_itemIV {
  width: calc(20% - 8px);
  float: left;
  height: 80px;
  margin: 0 6px 10px 0;
  background: #d8d8d8;
  cursor: pointer;
}

.resources_select_itemIV:nth-child(4n) {
  margin: 0 0 10px 0;
}

.resources_select_itemicon {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.resources_select_itemiconcover {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1;
  display: none;
}

.resources_select_item:hover .resources_select_itemiconcover {
  display: block;
}

.resources_select_itemicon img {
  width: 100%;
  height: auto;
  min-height: 100%;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

.resources_select_itemtext {
  width: 100%;
  height: 100%;
  line-height: 80px;
  box-sizing: border-box;
  padding: 0 10px;
  font-family: 思源黑体;
  font-size: 14px;
  color: #3d3d3d;
  position: absolute;
  left: 0;
  top: 0;
  text-align: center;
}

.resources_select_itemcollect {
  position: absolute;
  right: 0;
  top: 0;
  height: 20px;
  width: 20px;
  text-align: center;
  line-height: 20px;
  cursor: pointer;
  font-size: 18px;
  z-index: 2;
}

.resources_select_itemcollect .normal {
  color: #ffffff;
}

.resources_select_itemuse {
  position: absolute;
  right: 0;
  bottom: 0;
  width: auto;
  height: 25px;
  line-height: 25px;
  font-family: 思源黑体;
  font-size: 14px;
  color: #ffffff;
  padding: 0 10px;
  cursor: pointer;
  display: none;
  z-index: 2;
  background: rgba(0, 0, 0, 0.6);
}

.resources_select_item:hover .resources_select_itemuse {
  display: block;
}

.resources_select_itemLD {
  width: calc(100% - 14px);
  height: 55px;
  margin: 0 0 5px 0;
  cursor: pointer;
  box-sizing: border-box;
  padding: 3px 20px 0 40px;
  position: relative;
}

.resources_select_itemLD:hover {
  background: #e7eaf0;
}

.resources_select_itemP1 {
  height: 23px;
  font-family: 思源黑体;
  font-size: 16px;
  line-height: 23px;
  color: #333333;
  margin: 2px 0 4px;
}

.resources_select_itemP2 {
  height: 20px;
  font-family: 思源黑体;
  font-size: 14px;
  line-height: 20px;
  color: #939393;
}

.resources_select_itemP3 {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 18px;
}

.resources_select_itemLD:hover .resources_select_itemP3 {
  color: #2383f9;
}

.el-popover.el-popper {
  z-index: 99999 !important;
}

.resources_select_dialog {
  width: 100%;
  height: auto;
  max-height: 500px;
  overflow: hidden;
}

.resources_select_dialog img,
.resources_select_dialog video,
.resources_select_dialog iframe {
  width: 100%;
}

.resources_select_dialog iframe {
  height: 500px;
}

.pptmain_new_textbook {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 1100px;
  height: 700px;
  z-index: 1;
  margin: -350px 0 0 -550px;
  background: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: width 0.2s;
  z-index: 2;
}

.pptmain_new_textbook.link {
  width: 1200px;
  height: 700px;
}

.pptmain_new_textbook.isSmall {
  width: 550px;
  height: 400px;
}

.pptmain_textbook_head {
  width: 100%;
  height: 30px;
  line-height: 30px;
  text-align: center;
  position: relative;
  background: #ccc;
  box-sizing: border-box;
  padding: 0 35px;
}

.pptmain_textbook_body {
  width: 100%;
  height: calc(100% - 30px);
}

.pptmain_textbook_left {
  position: absolute;
  left: 0;
  top: 0;
  height: 30px;
  line-height: 32px;
  width: 30px;
  text-align: center;
  cursor: pointer;
}

.pptmain_textbook_left:hover {
  opacity: 0.65;
}

.pptmain_textbook_right {
  position: absolute;
  right: 0;
  top: 0;
  height: 30px;
  line-height: 32px;
  width: 30px;
  text-align: center;
  cursor: pointer;
}

.pptmain_textbook_right:hover {
  opacity: 0.65;
}

.topic_select_new {
  width: 100%;
  height: auto;
  background: #ffffff;
  border-bottom: 1px solid #eeeeee;
}

.topic_select_left {
  width: 330px;
  height: 600px;
  box-sizing: border-box;
  border-right: 1px solid #eeeeee;
  position: relative;
  overflow: hidden;
  float: left;
}

.topic_select_lefttop {
  width: 100%;
  height: 45%;
  overflow: hidden;
  box-sizing: border-box;
  transition: height 0.3s;
  border-bottom: 1px solid #d8d8d8;
}

.topic_select_leftscroll {
  width: 100%;
  height: calc(100% - 40px);
  overflow-y: auto;
  box-sizing: border-box;
  padding: 10px 15px 15px;
}

.topic_select_lefttitle {
  height: 40px;
  line-height: 40px;
  box-sizing: border-box;
  padding: 0 0 0 40px;
  position: relative;
  background: #ededed;
}

.topic_select_item {
  width: 100%;
  height: 30px;
  margin: 0 0 6px 0;
  line-height: 30px;
  font-family: 思源黑体;
  font-size: 15px;
  font-weight: normal;
  font-feature-settings: 'kern' on;
  color: #333333;
  cursor: pointer;
  box-sizing: border-box;
  padding: 0 45px 0 5px;
  position: relative;
  border-radius: 3px;
}

.topic_select_itemdel {
  position: absolute;
  right: 5px;
  top: 5px;
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  display: none;
  background: #e2e6ed;
  border-radius: 5px;
  color: red;
}

.topic_select_item:last-child {
  margin: 0;
}

.topic_select_item:hover,
.topic_select_item.active {
  background: #e7eaf0;
}

.topic_select_item:hover .topic_select_itemdel {
  display: block;
}

.topic_select_itemdel:hover {
  background: #d5dfe1;
}

.topic_select_item.active {
  color: #2383f9;
}

.topic_select_lefticon {
  position: absolute;
  left: 5px;
  top: 50%;
  transform: translateY(-50%);
}

.topic_select_leftbottom {
  width: 100%;
  height: 55%;
  overflow: hidden;
  box-sizing: border-box;
  transition: height 0.3s;
}

.topic_topic_item {
  width: 100%;
  height: 60px;
  margin: 0 0 6px 0;
  cursor: pointer;
  box-sizing: border-box;
  padding: 0 40px 0 10px;
  position: relative;
  border-radius: 3px;
}

.topic_topic_itemp1 {
  height: 30px;
  font-family: 思源黑体;
  font-size: 16px;
  line-height: 30px;
  color: #3d3d3d;
}

.topic_topic_itemp2 {
  height: 30px;
  font-family: 思源黑体;
  line-height: 30px;
  font-size: 14px;
  color: #939393;
}

.topic_topic_item:hover,
.topic_topic_item.active {
  background: #e7eaf0;
}

.topic_topic_item.active .topic_topic_itemp1 {
  color: #2383f9;
}

.topic_select_control {
  width: auto;
  height: 40px;
  padding: 0 12px;
  position: absolute;
  right: 10px;
  top: 0;
  line-height: 40px;
  cursor: pointer;
  font-family: 思源黑体;
  font-size: 14px;
  font-feature-settings: 'kern' on;
  color: #2383f9;
}

.topic_select_lefttop.active {
  height: calc(100% - 40px);
}

.topic_select_leftbottom.active {
  height: 40px;
}

.topic_select_itemuse {
  position: absolute;
  right: 0;
  top: 0;
  width: auto;
  height: 20px;
  line-height: 20px;
  font-family: 思源黑体;
  font-size: 12px;
  color: #fafafa;
  padding: 0 8px;
  border-bottom-left-radius: 10px;
  cursor: pointer;
  background: #2383f9;
  display: none;
}

.topic_topic_itemselect {
  position: absolute;
  right: 0;
  top: 50%;
  margin: -10px 0 0 0;
  width: auto;
  height: 20px;
  line-height: 20px;
  font-family: 思源黑体;
  font-size: 12px;
  color: #d14424;
  padding: 0 8px;
  border-bottom-left-radius: 10px;
  cursor: pointer;
}

.topic_select_leftsearch {
  height: auto;
  width: 100%;
  box-sizing: border-box;
  padding: 8px 0 0 0;
}

.topic_select_leftmore {
  width: 100%;
  height: calc(100% - 90px);
  overflow-y: auto;
  box-sizing: border-box;
  padding: 0 15px 15px;
}

.topic_select_right {
  width: calc(100% - 330px);
  height: 600px;
  float: left;
  overflow: hidden;
  position: relative;
  border: 2px solid rgba(0, 0, 0, 0);
  box-sizing: border-box;
  transition: border-color 0.3s;
}

.topic_select_right.warningIt {
  border: 2px solid red;
}

.topic_select_righttop {
  height: calc(100% - 60px);
  width: 100%;
  overflow-y: auto;
  box-sizing: border-box;
  padding: 20px;
}

.topic_select_righttop.all {
  height: 100%;
}

.topic_select_rightbottom {
  height: 60px;
  width: 100%;
  box-sizing: border-box;
  padding: 13px 20px 0;
  position: absolute;
  left: 0;
  bottom: 0;
  text-align: right;
  box-shadow: 0 0px 4px rgba(0, 0, 0, 0.1);
}

.topic_select_btns {
  width: 100%;
  height: auto;
  padding: 20px 0;
  text-align: center;
}

.classroom_detail_new {
  width: 100%;
  height: calc(100% - 80px);
  margin: 80px 0 0 0;
  background: linear-gradient(270deg, #fafafa 0%, rgba(214, 234, 244, 0.3) 77%, #def0fa 100%);
  overflow: hidden;
  position: relative;
  background: linear-gradient(180deg, rgba(118, 154, 238, 0.3) 0%, rgba(217, 226, 234, 0.1) 100%);
  box-sizing: border-box;
  padding: 70px 60px 30px;
}

.classroom_detail_newtitle {
  height: 70px;
  width: 100%;
  box-sizing: border-box;
  padding: 0 60px;
  line-height: 70px;
  font-family: 思源黑体;
  font-size: 21px;
  font-weight: normal;
  letter-spacing: 0em;
  color: #14181f;
  position: absolute;
  left: 0;
  top: 0;
}

.classroom_detail_newleft {
  width: 60px;
  height: auto;
  position: absolute;
  left: 0;
  bottom: 0;
  padding: 0 0 40px;
}

.classroom_detail_newleftitem {
  cursor: pointer;
  width: 28px;
  height: auto;
  text-align: center;
  margin: 0 auto 40px;
  font-family: 思源黑体;
  font-size: 14px;
  text-align: center;
  color: #3d3d3d;
}

.classroom_detail_newleftitem.right {
  margin: 15px auto 0;
}

.classroom_detail_newicon {
  font-size: 23px;
  margin: 0 auto 5px;
}

.classroom_detail_newleftitem:hover,
.classroom_detail_newleftitem.active {
  color: #2383f9;
}

.classroom_detail_newright {
  width: 100%;
  height: 100%;
  overflow: hidden;
  border: 1px solid #939393;
  border-radius: 5px;
  background: #959ca6;
}

.courseware_detail_left {
  width: 275px;
  float: left;
  height: 100%;
  background: #d9e2ea;
  box-sizing: border-box;
  padding: 60px 0 0 0;
  position: relative;
}

.courseware_detail_left.more {
  padding: 215px 0 0 0;
}

.courseware_detail_leftselect {
  height: 115px;
  width: 100%;
  box-sizing: border-box;
  padding: 0 8px;
}

.courseware_detail_lefttypes {
  width: calc(33.3% - 4px);
  margin: 0 2px 6px;
  height: 30px;
  text-align: center;
  line-height: 30px;
  font-family: 思源黑体;
  font-size: 12px;
  color: #9e9e9e;
  float: left;
  background: #ffffff;
  cursor: pointer;
}

.courseware_detail_lefttypes:hover,
.courseware_detail_lefttypes.active {
  color: #ffffff;
  background: #2383f9;
}

.courseware_detail_leright {
  width: calc(100% - 275px);
  height: 100%;
  float: left;
  background: #ffffff;
}

.courseware_detail_leright.active {
  width: 100%;
}

.courseware_detail_leright.scroll {
  overflow-y: auto;
}

.courseware_detail_lefttitle {
  height: 60px;
  box-sizing: border-box;
  padding: 0 60px 0 20px;
  line-height: 60px;
  font-family: 思源黑体;
  font-size: 18px;
  font-weight: normal;
  color: #2383f9;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}

.courseware_detail_leftclose {
  width: 60px;
  height: 60px;
  text-align: center;
  cursor: pointer;
  line-height: 60px;
  font-size: 18px;
  position: absolute;
  right: 0;
  top: 0;
  color: #939393;
}

.courseware_detail_leftcontent {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 1px 20px 0;
  overflow-y: auto;
}

.courseware_detail_thumbnail {
  width: 100%;
  height: 125px;
  margin: 0 auto 20px;
  box-sizing: border-box;
  background: #ffffff;
  overflow: hidden;
  cursor: pointer;
}

.courseware_detail_thumbnail:hover,
.courseware_detail_thumbnail.active {
  outline: 1px solid #d14424;
}

.classroom_detail_newlittle {
  width: 60px;
  height: auto;
  position: absolute;
  right: 0;
  bottom: 0;
  padding: 0 0 30px;
}

.courseware_detail_topics {
  width: 100%;
  height: 700px;
  overflow: hidden;
  border: 1px solid #2383f9;
  border-radius: 5px;
}

.courseware_detail_topics.small {
  height: 500px;
}

.courseware_detail_item {
  width: 100%;
  height: auto;
  margin: 0 0 6px 0;
  cursor: pointer;
  box-sizing: border-box;
  padding: 0 0 0 50px;
  position: relative;
  border-radius: 3px;
}

.courseware_detail_itemp1 {
  height: 30px;
  font-family: 思源黑体;
  font-size: 16px;
  line-height: 30px;
  color: #3d3d3d;
}

.courseware_detail_itemp2 {
  height: 30px;
  font-family: 思源黑体;
  line-height: 30px;
  font-size: 14px;
  color: #939393;
}

.courseware_detail_item:hover,
.courseware_detail_item.active {
  background: #e7eaf0;
}

.courseware_detail_item.active .courseware_detail_itemp1 {
  color: #2383f9;
}

.courseware_detail_item_lefticon {
  position: absolute;
  left: 10px;
  top: 18px;
}

.courseware_detail_itemview {
  box-sizing: border-box;
  padding: 10px 20px 10px 0;
}

.courseware_detail_leftsearch {
  margin: 0 10px;
  height: 30px;
  box-sizing: border-box;
  border: 1px solid #939393;
  background: #ffffff;
  box-sizing: border-box;
  padding: 0 35px 0 0;
  position: relative;
}

.courseware_detail_leftsearch input {
  width: 100%;
  height: 28px;
  line-height: 28px;
  box-sizing: border-box;
  padding: 0 5px 0 10px;
  outline: none;
  border: none;
}

.courseware_detail_leftsearchbtn {
  position: absolute;
  right: 0;
  top: 0;
  height: 30px;
  width: 35px;
  text-align: center;
  font-size: 18px;
  line-height: 30px;
  cursor: pointer;
}

.courseware_detail_leftfiles {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.courseware_detail_leftfilesview {
  width: 100%;
  height: calc(100% - 145px);
  overflow-y: auto;
  box-sizing: border-box;
  padding: 15px 12px 10px;
}

.courseware_detail_topicview {
  box-sizing: border-box;
  padding: 40px 0 0 0;
  width: 90%;
  margin: 0 auto;
}

.courseware_detail_fileview {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.courseware_detail_fileview img {
  max-width: 100%;
  max-height: 100%;
}

.courseware_detail_fileview video,
.courseware_detail_fileview iframe {
  width: 100%;
  height: 100%;
  background: #000000;
}

.real_classcont_left .el-tree-node.is-current > .el-tree-node__content {
  background-color: #f2f3ff !important;
  color: #303a40;
}

.ppt_item_index {
  width: calc(33.3% - 42px);
  float: left;
  margin: 0 42px 40px 0;
  box-sizing: border-box;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #e4e4e4;
  position: relative;
  cursor: pointer;
  transition: all 0.3s;
}

.ppt_item_index:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ppt_item_indexcover {
  width: 100%;
  height: 210px;
  position: relative;
  overflow: hidden;
}

.ppt_item_indexcover_it {
  width: 100%;
  height: auto;
  min-height: 100%;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  background: #eeeeee;
}

.ppt_item_indexcover_name {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  width: 85%;
  font-size: 30px;
  font-family: PingFang SC, PingFang SC;
  font-weight: bold;
  color: #000000;
  line-height: 35px;
  letter-spacing: 3px;
}

.ppt_item_indexinfo {
  width: 100%;
  height: 60px;
  box-sizing: border-box;
  padding: 0 16px;
}

.ppt_item_indexinfo_teacher {
  width: auto;
  height: 34px;
  float: left;
  margin: 14px 0 0 0;
  font-size: 16px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #333333;
  line-height: 34px;
}

.ppt_item_indexinfo_teachericon {
  width: 34px;
  height: 34px;
  border-radius: 50%;
  float: left;
  position: relative;
  margin: 0 6px 0 0;
  overflow: hidden;
}

.ppt_item_indexinfo_teachericon img {
  width: 100%;
  height: auto;
  min-height: 100%;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

.ppt_item_index_time {
  float: right;
  height: 60px;
  font-size: 14px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #999999;
  line-height: 60px;
}

.ppt_item_index_timebtns {
  float: right;
  height: 24px;
  margin: 18px 0 0 0;
  display: none;
}

.ppt_item_index_timebtns.all {
  display: block;
  margin: 25px 0 0 0;
}

.ppt_item_index_timebtn {
  width: auto;
  height: 24px;
  line-height: 24px;
  float: left;
  padding: 0 6px 0 5px;
  font-size: 14px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #ffffff;
  border-radius: 4px;
  margin: 0 0 0 10px;
}

.ppt_item_index_timebtn.type1 {
  background: #2d6dff;
}

.ppt_item_index_timebtn.type2 {
  background: #ffffff;
  color: #2d6dff;
  border: 1px solid #2d6dff;
}

.ppt_item_index_timebtn i {
  position: relative;
  top: 1px;
  left: 1px;
}

.ppt_item_index:hover .ppt_item_index_time {
  display: none;
}

.ppt_item_index:hover .ppt_item_index_timebtns {
  display: block;
}

.fils_item_index {
  width: calc(16.66% - 20px);
  float: left;
  height: auto;
  margin: 0 20px 30px 0;
  box-sizing: border-box;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #e4e4e4;
  position: relative;
  padding: 0 0 11px 0;
}

.fils_item_index.nopar {
  padding: 0 0 3px 0;
}

.fils_item_indexicon {
  width: 100%;
  height: 97px;
  background: #faf9ff;
  border-bottom: 1px solid #e4e4e4;
  position: relative;
}

.fils_item_indexicon img {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  max-height: 90%;
  max-width: 90%;
}

.fils_item_indexname {
  height: 18px;
  font-size: 16px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #000000;
  line-height: 18px;
  box-sizing: border-box;
  padding: 0 6px;
  text-align: left;
  margin: 11px 0 8px 0;
}

.fils_item_indextime {
  height: 24px;
  width: 100%;
  box-sizing: border-box;
  padding: 0 10px 0 12px;
}

.fils_item_indexinfo_teacher {
  width: auto;
  height: 24px;
  float: left;
  font-size: 14px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #333333;
  line-height: 24px;
}

.fils_item_indexinfo_teachericon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  float: left;
  position: relative;
  margin: 0 3px 0 0;
  overflow: hidden;
}

.fils_item_indexinfo_teachericon img {
  width: 100%;
  height: auto;
  min-height: 100%;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

.fils_item_index_time {
  float: right;
  height: 24px;
  font-size: 12px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #999999;
  line-height: 24px;
}

.textbook_top_control {
  width: 54px;
  height: 54px;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  text-align: center;
  line-height: 54px;
}

.textbook_top_control1 {
  font-size: 18px;
  position: relative;
  top: 4px;
  cursor: pointer;
}

.textbook_top_control2 {
  font-size: 20px;
  position: relative;
  top: 5px;
  padding: 3px;
  cursor: pointer;
  border-radius: 4px;
}

.textbook_top_control2:hover {
  background: #eff3ff;
}

.el-popover.el-popper {
  min-width: initial !important;
}

.textbook_top_controls {
  width: 95px;
  height: auto;
}

.textbook_top_controls.small {
  width: 85px;
  text-align: center;
  margin: 0 auto;
  padding: 0 5px 0 0;
}

.textbook_top_controls.big {
  width: 100px;
  text-align: center;
  margin: 0 auto;
}

.textbook_top_controlit {
  width: 100%;
  height: 38px;
  line-height: 38px;
  font-size: 14px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  color: #737880;
  cursor: pointer;
}

.textbook_top_controlit:hover {
  color: #3d73f7;
}

.textbook_page {
  position: absolute;
  left: 50%;
  top: 50%;
  width: auto;
  height: 30px;
  transform: translate(-50%, -50%);
}

.textbook_more {
  float: right;
  height: 36px;
  margin: 9px 0 0 0;
}

.textbook_more_control {
  float: left;
  height: 30px;
  margin: 3px 0 0;
}

.real_filecont_leftlist {
  width: 100%;
  height: calc(100% - 58px);
  overflow-y: auto;
  box-sizing: border-box;
  padding: 20px 15px;
}

.real_classcont_cont {
  width: 100%;
  height: auto;
  box-sizing: border-box;
}

.ppt_item_pptinfo {
  width: 100%;
  height: 73px;
  box-sizing: border-box;
  padding: 0 16px;
}

.ppt_item_pptinfo_teacher {
  width: auto;
  height: 40px;
  float: left;
  margin: 17px 0 0 0;
}

.ppt_item_pptinfo_teachericon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  float: left;
  position: relative;
  margin: 0 6px 0 0;
  overflow: hidden;
}

.ppt_item_pptinfo_teachericon img {
  width: 100%;
  height: auto;
  min-height: 100%;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

.ppt_item_pptinfo_teachername {
  float: left;
  height: 40px;
  width: auto;
}

.ppt_item_pptinfo_teachernameP1 {
  height: 16px;
  font-size: 16px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #333333;
  margin: 2px 0 10px;
  line-height: 16px;
}

.ppt_item_pptinfo_teachernameP2 {
  height: 12px;
  font-size: 14px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #999999;
  line-height: 12px;
}

.ppt_item_ppt_timebtns {
  float: right;
  height: 73px;
  line-height: 73px;
}

.ppt_item_ppt_timebtn {
  height: 73px;
  line-height: 73px;
  padding: 0 10px;
}

.little_margin {
  margin: 0 0 0 5px;
}

.ppt_item_indexstate {
  position: absolute;
  left: 14px;
  top: 14px;
  width: 52px;
  height: 24px;
  text-align: center;
  line-height: 24px;
  border-radius: 3px;
  background: #3d73f7;
  font-size: 12px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #ffffff;
  line-height: 24px;
  z-index: 1;
}

.ppt_item_indexover {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 2;
}

.ppt_item_indexselect {
  position: absolute;
  right: 15px;
  bottom: 15px;
  width: 25px;
  height: 25px;
  background: #ffffff;
  border-radius: 2px 2px 2px 2px;
  color: #ffffff;
  border: 1px solid #e4e4e4;
  text-align: center;
  line-break: 25px;
  font-size: 20px;
}

.ppt_item_indexselect.active {
  background: #3d73f7;
}

.class_dialog_body {
  width: 580px;
  height: auto;
  padding: 20px 50px;
}

.cover_upload {
  width: 100%;
  height: auto;
}

.cover_upload_img {
  float: left;
  height: 76px;
  width: 126px;
  border-radius: 5px;
  background: #e4e4e4;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.cover_upload_i {
  position: absolute;
  left: 50%;
  top: 50%;
  font-size: 20px;
  color: #333333;
  transform: translate(-50%, -50%);
  margin: -8px 0 0 0;
}

.cover_upload_tip {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 21px;
  text-align: center;
  line-height: 21px;
  background: rgba(0, 0, 0, 0.6);
  font-size: 12px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
}

.cover_upload_text {
  float: left;
  width: 200px;
  height: auto;
  text-align: left;
  margin: 20px 0 0 20px;
  font-size: 12px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  color: #666666;
  line-height: 18px;
}

.cover_upload_imgit {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  height: auto;
  min-height: 100%;
}

.class_textbook_select {
  width: 430px;
  height: auto;
}

.class_textbook_selecthead {
  width: 100%;
  height: 36px;
  background: #f9f9f9;
  border: 1px solid #e7e7e7;
  font-size: 14px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #333333;
  line-height: 36px;
}

.class_textbook_selectheadhelf {
  width: 50%;
  float: left;
  text-align: center;
}

.class_textbook_selectbody {
  width: 100%;
  height: auto;
  max-height: 280px;
  background: #ffffff;
}

.class_textbook_selectbodyleft {
  float: left;
  width: 50%;
  height: 100%;
  box-sizing: border-box;
  padding: 5px;
  overflow-y: auto;
  border-right: 1px solid #e4e4e4;
}

.class_textbook_selectitem {
  width: 100%;
  height: 30px;
  box-sizing: border-box;
  padding: 0 8px;
  line-height: 30px;
  font-size: 14px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.9);
  position: relative;
  margin: 0 0 5px;
  cursor: pointer;
}

.class_textbook_selectitem.select {
  box-sizing: border-box;
  padding: 0 24px 0 8px;
}

.class_textbook_selectitemicon {
  position: absolute;
  right: 8px;
  font-size: 16px;
  top: 50%;
  transform: translateY(-50%);
}

.class_textbook_selectitem:hover,
.class_textbook_selectitem.active {
  background: rgba(209, 68, 36, 0.1);
  font-size: 14px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  color: #d14424;
}

.resource_filecont_left {
  width: 175px;
  height: 100%;
  float: left;
  overflow-y: auto;
  box-sizing: border-box;
  padding: 20px 10px;
  border-right: 1px solid #e9e5fa;
}

.resource_filecont_lefitem {
  width: 100%;
  height: 46px;
  margin: 0 0 12px;
  font-size: 18px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #000000;
  line-height: 46px;
  box-sizing: border-box;
  padding: 0 0 0 14px;
  cursor: pointer;
}

.resource_filecont_lefitem:hover,
.resource_filecont_lefitem.active {
  background: #f2f3ff;
  color: #3d73f7;
}

.files_add_types {
  float: left;
  height: 20px;
  margin: 12.5px 0 0 20px;
}

.files_add_type {
  float: left;
  line-height: 20px;
  font-size: 16px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #777777;
  margin: 0 30px 0 0;
  cursor: pointer;
}

.files_add_type:hover,
.files_add_type.active {
  color: #3d73f7;
}

.the_icon {
  position: relative;
  top: 2.5px;
  margin: 0 4px 0 0;
}

.el-message-box__btns {
  margin: 0 0 8px;
}

.recyclebin_list_new {
  padding: 12px 0 0;
  height: auto;
  min-height: calc(100% - 122px);
}

.all_new_cont.index_stc {
  box-sizing: border-box;
  padding: 10px;
  background: #f2f2f2;
}

.index_stc_all {
  width: 100%;
  height: 50%;
}

.index_stc_cont {
  width: calc(100% - 20px);
  margin: 10px auto;
  height: calc(100% - 20px);
  background: #ffffff;
  box-sizing: border-box;
  border-radius: 5px;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.index_stc_conthead {
  height: 50px;
  width: 100%;
  line-height: 50px;
  text-indent: 1em;
  box-sizing: border-box;
  border-bottom: 1px solid #e1e1e1;
  font-size: 20px;
}

.index_stc_contbody {
  width: 100%;
  height: calc(100% - 50px);
  position: relative;
}

.index_stc_contbody.centerin {
  line-height: 40px;
  box-sizing: border-box;
  padding: 15px;
  font-size: 18px;
  text-align: center;
  height: auto;
  max-height: calc(100% - 50px);
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

.index_stc_contbody_chart {
  height: 100%;
  width: 350px;
  float: left;
  position: relative;
}

.index_stc_contbody_chartdata {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 150px;
  text-align: center;
}

.index_stc_contbody_chartdataP1 {
  font-size: 16px;
  line-height: 25px;
  height: 25px;
  color: #666666;
}

.index_stc_contbody_chartdataP2 {
  font-size: 25px;
  height: 30px;
  line-height: 30px;
  color: #333333;
}

.index_stc_contbody_chartcont {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

.index_stc_contbody_list {
  float: left;
  width: calc(100% - 350px);
  height: 200px;
  box-sizing: border-box;
  padding: 0 50px;
  position: absolute;
  right: 0;
  top: 50%;
  margin: -100px 0 0 0;
}

.index_stc_contbody_list_item {
  width: 50%;
  height: 50px;
  float: left;
  padding: 0 20px;
  line-height: 50px;
  position: relative;
}

.index_stc_contbody_list_item:before {
  content: ' ';
  position: absolute;
  left: 0;
  top: 50%;
  height: 10px;
  width: 10px;
  margin: -5px 0 0 0;
  border-radius: 50%;
}

.index_stc_contbody_list_item:nth-child(1):before {
  background: #3ba0ff;
}

.index_stc_contbody_list_item:nth-child(2):before {
  background: #975fe4;
}

.index_stc_contbody_list_item:nth-child(3):before {
  background: #36cbcb;
}

.index_stc_contbody_list_item:nth-child(4):before {
  background: #975fe4;
}

.index_stc_contbody_list_item:nth-child(5):before {
  background: #4dcb73;
}

.index_stc_contbody_list_item:nth-child(6):before {
  background: #f2637b;
}

.index_stc_contbody_list_item:nth-child(7):before {
  background: #fad337;
}

.index_stc_contbody_list_item:nth-child(8):before {
  background: #975fe4;
}

.index_stc_contbody_icon {
  width: 90px;
  height: auto;
  margin: 20px auto;
  display: block;
}

.index_stc_table_text {
  height: 40px;
  line-height: 40px;
  color: #1890ff;
  cursor: pointer;
}

.index_stc_contlast {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 20px 20px 20px 300px;
  position: relative;
}

.index_stc_contlast_icon {
  width: 230px;
  height: calc(100% - 40px);
  position: absolute;
  left: 40px;
  top: 20px;
  overflow: hidden;
}

.index_stc_contlast_icon img {
  width: 100%;
  height: auto;
  min-height: 100%;
}

.index_stc_contlast_P1 {
  height: 62px;
  line-height: 62px;
  font-size: 20px;
}

.index_stc_contnum {
  width: 33.3%;
  float: left;
  height: 100%;
  box-sizing: border-box;
  padding: 45px 0 0 0;
  text-align: center;
  line-height: 50px;
  font-size: 17px;
}

.index_stc_contnumin {
  color: #1890ff;
  font-size: 22px;
}
.index_stc_contnum img {
  width: 90px;
  height: 90px;
  margin: 0 auto 30px;
}
