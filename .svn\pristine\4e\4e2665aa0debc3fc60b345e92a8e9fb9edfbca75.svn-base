<template>
  <section ref="app-min" class="app-main">
    <transition name="fade-transform" mode="out-in">
      <keep-alive include="Process">
        <router-view :key="key" />
      </keep-alive>
    </transition>
    <div v-if="isShowSidebar" class="sidebar">
      <div class="top" @click="scrollToTop">置顶</div>
      <div class="goBack" @click="$router.push('/repository')">返回</div>
      <div @click="scrollToBottom">置底</div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'AppMain',
  data() {
    return {
      isShowSidebar: false
    }
  },
  computed: {
    key() {
      return this.$route.path
    }
  },
  watch: {
    $route(to, from) {
      if (to.name === 'RepositoryDetails') {
        this.isShowSidebar = true
      } else {
        this.isShowSidebar = false
      }
    }
  },
  created() {
    if (this.$route.name === 'RepositoryDetails') {
      this.isShowSidebar = true
    } else {
      this.isShowSidebar = false
    }
  },
  methods: {
    scrollToTop() {
      if (this.$refs['app-min']) {
        this.$nextTick(function () {
          this.$refs[['app-min']].scrollTo({ top: 0, behavior: 'smooth' })
        })
      }
    },
    scrollToBottom() {
      const that = this
      if (this.$refs['app-min']) {
        this.$refs['app-min'].scrollTo({ top: that.$refs['app-min'].scrollHeight, behavior: 'smooth' })
      }
    }
  }
}
</script>

<style scoped>
.app-main {
  /*50 = navbar  */
  min-height: calc(100vh - 68px);
  width: 100%;
  position: relative;
}
.fixed-header + .app-main {
  padding-top: 50px;
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
.sidebar {
  position: fixed;
  right: 50px;
  top: 350px;
  z-index: 999;
  div {
    width: 50px;
    height: 50px;
    line-height: 50px;
    color: #fff;
    text-align: center;
    background-color: #3465df;
    cursor: pointer;

    &:last-of-type {
      border-top: 2px solid #fff;
      border-radius: 0 0 8px 8px;
    }
  }
  .top {
    border-radius: 8px 8px 0 0;
  }
  .goBack {
    border-top: 2px solid #fff;
  }
}
</style>
