<template>
  <div class="app-container">
    <div class="log_container">
      <div class="log_header">
        <span :class="{ checked: activeName === '记录' }" @click="activeName = '记录'">日志记录</span>
        <span :class="{ checked: activeName === '统计' }" @click="activeName = '统计'">日志统计</span>
      </div>
      <div class="log_body">
        <div v-if="activeName !== '统计'" class="log_body_top">
          <div class="search">
            <el-form ref="searchForm" label-width="80px" inline class="searchForm">
              <el-form-item v-if="activeName === '记录'" label="客户名称:">
                <el-input
                  v-model="queryInfo.customerName"
                  class="customerName"
                  size="small"
                  placeholder="请输入客户名称"
                  clearable
                  @keyup.enter.native="getSecretLog_logList"
                  @clear="getSecretLog_logList"
                ></el-input>
              </el-form-item>
              <el-form-item v-if="activeName === '记录'" label="创建时间:">
                <el-date-picker
                  v-model="date"
                  class="date"
                  size="small"
                  type="daterange"
                  range-separator="→"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  clearable
                  @change="datePickerChange"
                >
                </el-date-picker>
              </el-form-item>
              <!-- <el-form-item v-if="activeName === '统计'" label="时间:" label-width="50px">
                <el-date-picker v-model="logStatisInfo.time" type="date" placeholder="选择日期" size="small" value-format="yyyy-MM-dd" @change="getlogStatisList"> </el-date-picker>
              </el-form-item> -->
              <el-form-item>
                <!-- <el-button v-if="activeName === '记录'" type="success" size="small" @click="getSecretLog_logList">查询</el-button> -->
                <!-- <el-button v-else type="success" size="small" @click="getlogStatisList">查询</el-button> -->
                <el-button type="primary" size="small" plain @click="reset">重置</el-button>
              </el-form-item>
            </el-form>
            <el-button type="primary" size="small" icon="el-icon-plus" @click="$router.push('/secretLog/addLog')">写日志</el-button>
          </div>
          <div class="person">
            <div v-for="item in userList" :key="item.userId" :class="{ checkedPerson: queryInfo.userId === item.userId }" @click="personSearch(item)">
              <img :src="item.headurl" alt="" />
              <span>{{ item.realName }}</span>
            </div>
          </div>
        </div>
        <div v-if="activeName === '记录'" class="log_content">
          <div class="log_content_left">
            <ul v-infinite-scroll="load" :infinite-scroll-delay="300" :infinite-scroll-distance="1">
              <li v-for="item in list" :key="item.logId">
                <div class="list_left">
                  <span>{{ item.createTime.substring(0, item.createTime.indexOf(' ')) }}</span>
                  <span>{{ item.createTime.substring(item.createTime.indexOf(' ') + 1, item.createTime.length) }}</span>
                </div>
                <div :class="[{ checked: logId === item.logId }, 'list_right']" @click="goDetails(item)">
                  <div>{{ item.name }}</div>
                  <div style="margin: 16px 0 12px 0">客户名称：{{ CustomerNames(item) }}</div>
                  <div>客户联系人：{{ ContactsNames(item) }}</div>
                </div>
              </li>
              <li v-loading="listLoading" element-loading-text="拼命加载中" element-loading-spinner="el-icon-loading" class="loadingText"></li>
            </ul>
          </div>
          <div class="log_content_right">
            <el-descriptions :column="1">
              <template v-slot:title>
                <span class="name">日志详情</span>
                <span class="comment" @click="openComment"> <i class="el-icon-chat-dot-square"></i> 评论({{ commentList.length }})</span>
              </template>
              <el-descriptions-item label="昨日计划">
                <el-table :data="detailsInfo.yesterdays" style="max-width: 1300px" border :header-cell-style="{ background: '#fafafa', borderColor: '#EEEEEF', color: '#657081', fontWeight: '400' }">
                  <el-table-column align="center" prop="customerName" label="客户名称" :formatter="showCustomerName"> </el-table-column>
                  <el-table-column align="center" prop="majorName" label="所属专业" width="120" :formatter="showMajorName"> </el-table-column>
                  <el-table-column align="center" prop="contactsNames" label="联系人" :formatter="showContactsName"> </el-table-column>
                  <el-table-column align="center" prop="result" width="500" label="拜访计划"> </el-table-column>
                </el-table>
              </el-descriptions-item>
              <el-descriptions-item label="当日完成">
                <el-table :data="detailsInfo.days" style="max-width: 1300px" border :header-cell-style="{ background: '#fafafa', borderColor: '#EEEEEF', color: '#657081', fontWeight: '400' }">
                  <el-table-column align="center" prop="customerName" label="客户名称" :formatter="showCustomerName"> </el-table-column>
                  <el-table-column align="center" prop="majorName" label="所属专业" width="120" :formatter="showMajorName"> </el-table-column>
                  <el-table-column align="center" prop="contactsNames" label="联系人" :formatter="showContactsName"> </el-table-column>
                  <el-table-column align="center" prop="result" width="500" label="拜访结果"> </el-table-column>
                </el-table>
              </el-descriptions-item>
              <el-descriptions-item label="明日计划">
                <el-table :data="detailsInfo.plans" style="max-width: 1300px" border :header-cell-style="{ background: '#fafafa', borderColor: '#EEEEEF', color: '#657081', fontWeight: '400' }">
                  <el-table-column align="center" prop="customerName" label="客户名称" :formatter="showCustomerName"> </el-table-column>
                  <el-table-column align="center" prop="majorName" label="所属专业" width="120" :formatter="showMajorName"> </el-table-column>
                  <el-table-column align="center" prop="contactsNames" label="联系人" :formatter="showContactsName"> </el-table-column>
                  <el-table-column align="center" prop="result" width="500" label="拜访计划"> </el-table-column>
                </el-table>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
        <div v-else class="logStatistics">
          <logSatistics />
        </div>
      </div>
    </div>

    <!-- 日志评论 -->
    <el-drawer :visible.sync="commentDrawer" custom-class="commentDrawer" direction="rtl">
      <template v-slot:title>
        <span>{{ detailsInfo.name }}</span>
      </template>

      <div v-if="commentList.length || writeCommentStatus">
        <div class="writeCommentBox">
          <el-input v-model="commentContent" type="textarea" placeholder="请输入评论内容" resize="none" :autosize="{ minRows: 4, maxRows: 6 }" maxlength="150" show-word-limit></el-input>
          <el-button type="primary" size="small" @click="sendComment">发送</el-button>
        </div>
        <div class="commentList">
          <div class="commentTitle">评论({{ commentList.length }})</div>
          <div class="commentItem" v-for="item in commentList" :key="item.commentId">
            <div class="commentPhoto">
              <img v-if="item.headurl" :src="item.headurl" alt="" />
              <img v-else src="@/assets/login/logo.png" alt="" />
            </div>
            <div class="commentContent">
              <div>{{ item.realName }}</div>
              <div>{{ item.content }}</div>
              <div class="time-info">{{ formatCommentTime(item.createTime) }}</div>
            </div>
          </div>
        </div>
      </div>
      <el-empty v-else>
        <template v-slot:image>
          <img src="@/assets/secretLog/noData.png" alt="" />
        </template>
        <template v-slot:description>
          <div>暂无评论</div>
          <el-button icon="el-icon-edit" type="primary" size="small" @click="writeComment">写评论</el-button>
        </template>
      </el-empty>
    </el-drawer>
  </div>
</template>

<script>
import { getList } from '@/api/systemUser'
import { secretLog_logList, secretLog_logCommentList, secretLog_addComment } from '@/api/secretLog'

import { formatDate, virtualCustomer } from '@/filters'
import { mapGetters } from 'vuex'
import logSatistics from '@/views/secretLog/logSatistics'
export default {
  name: 'SecretLog',
  components: {
    logSatistics
  },
  data() {
    return {
      activeName: '记录',
      // 日志记录
      queryInfo: {
        customerName: null,
        startTime: null,
        endTime: null,
        createUser: null,
        userId: null,
        pageNum: 1,
        pageSize: 10
      },
      date: null,
      list: [],
      total: 0,
      logId: null,
      userList: [],
      detailsInfo: {},
      listLoading: false, // 加载列表的loading
      // 日志评论相关
      commentList: [],
      commentDrawer: false,
      writeCommentStatus: false,
      commentContent: ''
    }
  },
  computed: {
    ...mapGetters(['realName', 'organizationId', 'userId'])
  },
  created() {
    // 穿个参代表是初始化的时候执行的，用于初始化选中第一个日志
    this.getSecretLog_logList(0)
    this.getUserList()
  },
  methods: {
    async getUserList() {
      if (this.userId === '02071766556523' || this.userId === 'euryd123') {
        const { data } = await getList({ pageNum: 1, pageSize: 100, organizationId: 56642510 })
        const { data: filialeData } = await getList({ pageNum: 1, pageSize: 100, organizationId: 3 })
        this.userList = [...data.list, ...filialeData.list]
      } else {
        const organizationId = this.organizationId === 3 ? 3 : 56642510
        const { data } = await getList({ pageNum: 1, pageSize: 100, organizationId })
        this.userList = data.list
      }
    },
    load() {
      console.log(this.list.length, this.total)
      if (this.list.length === this.total) {
        return
      } else {
        this.listLoading = true
        this.queryInfo.pageSize += 5
        this.getSecretLog_logList()
      }
    },

    CustomerNames(row) {
      const names = []
      row.days.forEach((item) => {
        names.push(this.showCustomerName(item))
      })
      return names.join(',')
    },
    ContactsNames(row) {
      const names = []
      row.days.forEach((item) => {
        names.push(this.showContactsName(item))
      })
      return names.join(',')
    },
    showCustomerName(row) {
      // 判断row.customerId是否在virtualCustomer中
      const isVirtual = virtualCustomer.some((item) => item.value === row.customerId)
      if (isVirtual) {
        const [{ label }] = virtualCustomer.filter((item) => item.value === row.customerId)
        return label
      } else {
        return row.customerName
      }
    },
    showMajorName(row) {
      if (row.majorId === '999999990') {
        return '虚拟专业'
      } else {
        return row.majorName
      }
    },
    showContactsName(row) {
      if (row.contactsIds === '99999989') {
        return '虚拟联系人'
      } else {
        return row.contactsNames
      }
    },
    tabClick(val) {
      if (val.label === '日志记录') {
        this.getSecretLog_logList()
      } else {
        this.getlogStatisList()
      }
    },
    //   获取日志记录
    async getSecretLog_logList(type) {
      const { data } = await secretLog_logList(this.queryInfo)
      this.list = data.list
      this.total = data.total
      console.log(data.total)
      if (type === 0) {
        this.logId = this.list[0].logId
        this.goDetails(this.list[0])
      }
      this.listLoading = false
    },
    // 时间搜索的格式化
    datePickerChange(val) {
      if (val) {
        this.queryInfo.startTime = formatDate(val[0], 'yyyy-MM-dd hh:ss:mm')
        this.queryInfo.endTime = formatDate(val[1], 'yyyy-MM-dd') + ' 23:59:59'
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getSecretLog_logList()
    },
    // 重置搜索
    reset() {
      this.queryInfo = {
        customerName: null,
        startTime: null,
        endTime: null,
        createUser: null,
        userId: null,
        pageNum: 1,
        pageSize: 10
      }
      this.date = null
      this.getSecretLog_logList()
    },
    personSearch(item) {
      this.queryInfo.userId = item.userId
      this.getSecretLog_logList()
    },

    goDetails(item) {
      this.logId = item.logId
      this.detailsInfo = { ...item }
      this.getLogCommentList()
    },
    // #region 日志评论相关
    async getLogCommentList() {
      const { data } = await secretLog_logCommentList(this.logId)
      this.commentList = data
      console.log(this.commentList)
    },
    openComment() {
      this.commentDrawer = true
    },
    writeComment() {
      this.writeCommentStatus = true
    },
    async sendComment() {
      const data = {
        logId: this.logId,
        content: this.commentContent
      }
      try {
        await secretLog_addComment(data)
        this.$message.success('添加成功')
        this.writeCommentStatus = false
        this.commentContent = ''
        this.getLogCommentList()
      } catch (error) {
        console.log(error)
      }
    },
    // 修改格式化时间的方法
    formatCommentTime(time) {
      const date = new Date(time)
      const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
      const weekDay = weekDays[date.getDay()]
      const dateStr = time.split(' ')[0]
      const timeStr = time.split(' ')[1]
      return `${dateStr} ${timeStr}    ${weekDay}`
    }
    // #endregion
  }
}
</script>

<style scoped lang="scss">
.app-container {
  background: #e8eaed;
  min-height: 100%;
  padding-bottom: 0px;
  .log_container {
    display: flex;
    flex-direction: column;
    width: 1754px;
    height: 825px;
    background: #fff;
    border-radius: 8px 8px 8px 8px;
    .log_header {
      height: 56px;
      padding-top: 15px;
      background: url('../../assets/secretLog/secretLog_header.png') no-repeat;
      background-size: 100%;
      span {
        display: inline-block;
        width: 88px;
        height: 30px;
        margin-left: 30px;
        line-height: 30px;
        border-radius: 4px;
        background: rgba($color: #fff, $alpha: 0.2);
        font-size: 15px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        font-weight: bold;
        color: #ffffff;
        text-align: center;
        cursor: pointer;
      }
      .checked {
        background: #fff;
        color: #0b1a44;
      }
    }
    .log_body {
      flex: 1;
      height: calc(100% - 56px);
      overflow: hidden;

      .log_body_top {
        .search {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-right: 37px;
          .searchForm {
            padding-top: 10px;
            padding-left: 10px;
            .customerName {
              width: 172px;
              height: 36px;
              ::v-deep {
                .el-input__inner {
                  width: 172px;
                  height: 36px;
                  background: #ffffff;
                  border: 1px solid #d8dbe1;
                }
              }
            }
            .date {
              ::v-deep {
                width: 240px;
                height: 36px;
                background: #ffffff;
                border-radius: 4px 4px 4px 4px;
                border: 1px solid #d8dbe1;
                // .el-range__icon{
                //   display: none;
                // }
                .el-range__close-icon {
                  line-height: 28px;
                }
              }
            }
            ::v-deep {
              .el-form-item {
                .el-form-item__label {
                  font-size: 14px;
                  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                  font-weight: 400;
                  color: #0b1a44;
                }
              }
            }
          }
        }
        .person {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          padding-left: 20px;
          padding-top: 8px;
          border-top: 1px solid #eeeeef;
          & > div {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 16px;
            margin-bottom: 8px;
            width: 128px;
            height: 42px;
            border-radius: 4px 4px 4px 4px;
            border: 1px solid #eeeeef;
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #0b1a44;
            cursor: pointer;
            img {
              margin-right: 8px;
              width: 28px;
              height: 28px;
              border-radius: 50%;
              object-fit: cover;
            }
          }
          .checkedPerson {
            background: #e9ebef;
            color: #3464e0;
          }
        }
      }

      .log_content {
        display: flex;
        justify-content: space-between;
        height: calc(100% - 181px);
        .log_content_left {
          height: 100%;
          padding-bottom: 10px;
          width: 574px;
          background: #f5f5f5;
          overflow: auto;
          /* 设置滚动条的样式 */
          &::-webkit-scrollbar {
            width: 3px;
          }
          &::-webkit-scrollbar-thumb {
            background-color: #3465df;
            border-radius: 30px;
          }
          ul {
            position: relative;
            padding-bottom: 110px;
            li {
              display: flex;
              justify-content: space-between;
              width: 100%;
              .list_left {
                padding-left: 33px;
                padding-top: 38px;
                padding-right: 8px;
                box-sizing: border-box;
                text-align: right;

                & > span:first-of-type {
                  display: inline-block;
                  width: 67px;
                  font-size: 12px;
                  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                  font-weight: 400;
                  color: #0b1a44;
                }
                & > span:last-of-type {
                  font-size: 12px;
                  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                  font-weight: 400;
                  color: #868b9f;
                }
              }
              .list_right {
                width: 466px;
                min-height: 118px;

                padding: 19px 10px 19px 20px;
                background: #ffffff;
                border: 1px solid #eeeeef;
                border-bottom: none;
                cursor: pointer;
                line-height: 18px;
                & > div:first-of-type {
                  font-size: 14px;
                  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
                  font-weight: bold;
                  color: #0b1a44;
                }
                & > div:nth-of-type(n + 2) {
                  font-size: 12px;
                  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                  font-weight: 400;
                  color: #657081;
                }
              }
              .checked {
                position: relative;
                background: #f1f3fb;
                &::before {
                  content: '';
                  position: absolute;
                  left: 0;
                  top: 0;
                  width: 4px;
                  height: 100%;
                  background: #3465df;
                }
              }
              &:last-of-type {
                .list_right {
                  border-bottom: 1px solid #eeeeef;
                }
              }
            }
          }
          .loadingText {
            position: absolute;
            bottom: 82px;
            left: 60%;
            transform: translateX(-50%);
          }
        }
        .log_content_right {
          flex: 1;
          padding-left: 40px;
          padding-top: 24px;
          padding-bottom: 50px;
          padding-right: 100px;
          overflow: auto;
          ::v-deep {
            .el-descriptions__title {
              width: 100%;
              display: flex;
              justify-content: space-between;
              align-items: center;
              .name {
                font-size: 18px;
                font-family: Microsoft YaHei-Bold, Microsoft YaHei;
                font-weight: bold;
                color: #3a5aae;
              }
              .comment {
                margin-left: 30px;
                font-size: 18px;
                font-family: Microsoft YaHei-Bold, Microsoft YaHei;
                font-weight: bold;
                color: #3465df;
                cursor: pointer;
              }
            }
            .el-descriptions-item__label {
              min-width: 60px;
              font-size: 14px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #868b9f;
            }
            .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
              padding-bottom: 32px;
            }
            .planContent {
              font-size: 14px;
              font-family: Microsoft YaHei-Bold, Microsoft YaHei;
              font-weight: bold;
              color: #0b1a44;
            }
            // 修改表格内的文字
            .el-table__body {
              .el-table__row {
                .cell {
                  font-size: 12px;
                  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
                  font-weight: bold;
                  color: #0b1a44;
                }
              }
            }
            .name {
              font-size: 14px;
              font-family: Microsoft YaHei-Bold, Microsoft YaHei;
              font-weight: bold;
              color: #0b1a44;
            }
          }
        }
      }
    }
  }
}
::v-deep {
  .el-dialog__body {
    padding: 20px 50px;
  }
  .el-drawer__header {
    margin-bottom: 0;
    padding-bottom: 20px;
    border-bottom: 1px solid #e6e6e6;
    font-size: 18px;
  }
  .el-drawer__header > :first-child {
    color: #000;
    font-weight: bold;
  }
  .el-drawer__body {
    padding: 0 15px;
    padding-top: 15px;
    .tbody {
      margin-top: 25px;
    }
    .el-descriptions-item__label {
      font-size: 16px;
      font-weight: bold;
      color: #000;
    }
  }
  .commentDrawer {
    background: #e8eaed;
    .writeCommentBox {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      .el-textarea__inner {
        min-height: 60px !important;
      }
      .el-button {
        margin-top: 15px;
      }
    }
    .commentList {
      margin-top: 20px;
      .commentTitle {
        font-size: 16px;
        font-weight: bold;
        color: #0b1a44;
        margin-bottom: 16px;
      }
      .commentItem {
        display: flex;
        padding: 16px 32px;

        border-bottom: 1px solid #657081;

        .commentPhoto {
          width: 40px;
          height: 40px;
          margin-right: 12px;
          border-radius: 50%;
          overflow: hidden;
          flex-shrink: 0;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .commentContent {
          flex: 1;

          & > div:nth-child(1) {
            font-size: 16px;
            font-weight: bold;
            color: #0b1a44;
            margin-bottom: 8px;
          }

          & > div:nth-child(2) {
            font-size: 16px;
            line-height: 1.5;
            color: #657081;
            margin-bottom: 8px;
            word-break: break-all;
          }

          & > div:nth-child(3) {
            font-size: 14px;
            color: #868b9f;
            display: flex;
            align-items: center;
            gap: 8px;
          }

          .time-info {
            font-size: 14px;
            color: #868b9f;
            white-space: pre; // 保留空格
          }
        }

        &:last-child {
          border-bottom: none;
        }
      }
    }
    .writeCommentBox {
      background: #fff;
      padding: 16px;
      border-radius: 4px;
      margin-bottom: 20px;

      .el-textarea__inner {
        min-height: 80px !important;
        font-size: 14px;
      }

      .el-button {
        margin-top: 12px;
      }
    }
    .el-empty {
      .el-empty__image {
        width: 400px;
        height: 400px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .el-empty__description {
        font-size: 18px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        font-weight: bold;
        color: #000;
        .el-button {
          margin-top: 15px;
        }
      }
    }
  }
}
</style>
