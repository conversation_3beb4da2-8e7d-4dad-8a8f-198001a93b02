<template>
  <div class="">
    <el-dialog :visible="showDialog" width="1214px" custom-class="scoreDialog" @close="close">
      <template v-slot:title>
        <div class="header">
          <span>{{ scoreType }}</span>
          <span>{{ scoreTitle }} </span>
        </div>
      </template>
      <div class="scoreDialog_left">
        <el-row class="titleRow">
          <div>
            <el-tooltip
              effect="dark"
              popper-class="stateTooltip"
              content="清晰描述工作任务目标，避免超过30分以上的工作计划内容，如果只有一个项目开发，将项目的大模块拆分出来，重点工作填充绿色单元格，临时/置换工作用红色文字。"
              placement="top"
            >
              <img src="@/assets/performance/state3.png" alt="" />
            </el-tooltip>
            <span>目标和任务:</span>
          </div>
          <div>{{ scoreInfo.content }}</div>
        </el-row>
        <el-row class="describeRow">
          <div>
            <div>
              <el-tooltip
                effect="dark"
                popper-class="stateTooltip"
                content="这里总分数不要超过100分，如果有临时工作置换，将临时工作分数写为*x分*，这样不会计算分数，要保证置换的工作与原计划工作分数一致"
                placement="top"
              >
                <img src="@/assets/performance/state3.png" alt="" />
              </el-tooltip>
              <span>权 <span v-html="'&emsp;'"></span> 重:</span>
            </div>
            <div class="titleDescribe">(工作量或重要性占比)</div>
          </div>
          <div>{{ scoreInfo.weight }}</div>
        </el-row>
        <el-row class="describeRow">
          <div>
            <div>
              <el-tooltip effect="dark" popper-class="stateTooltip" content="考核标准写清楚，通过什么标准可以对前面的目标和任务进行检查，例如完成xx模块工作，百分比多少" placement="top">
                <img src="@/assets/performance/state3.png" alt="" />
              </el-tooltip>
              <span>考核标准:</span>
            </div>
            <div class="titleDescribe">(要有完成准确的节点)</div>
          </div>
          <div>{{ scoreInfo.standard }}</div>
        </el-row>
        <el-row class="describeRow">
          <div>
            <div>
              <el-tooltip effect="dark" popper-class="stateTooltip" content="如有扣分项目说明扣分原因，满分代表工作内容、进度、细节无瑕疵" placement="top">
                <img src="@/assets/performance/state3.png" alt="" />
              </el-tooltip>
              <span>绩效分析:</span>
            </div>
            <div class="titleDescribe">(完成情况及扣分原因)</div>
          </div>
          <div>{{ scoreInfo.analyse }}</div>
        </el-row>
        <el-row class="titleRow taskRow">
          <div>
            <span>任务状态:</span>
          </div>
          <div>
            <img v-if="scoreInfo.state === 1" src="@/assets/performance/normal.png" alt="" />
            <img v-if="scoreInfo.state === 2" src="@/assets/performance/emphasis.png" alt="" />
            <img v-if="scoreInfo.state === 4" src="@/assets/performance/substitution.png" alt="" />
            <span>{{ scoreInfo.state === 1 ? '正常' : scoreInfo.state === 2 ? '重点' : scoreInfo.state === 3 ? '置换' : scoreInfo.state === 4 ? '被置换' : '---' }}</span>
          </div>
        </el-row>
        <el-row class="titleRow outputRow">
          <div>输出物:</div>
          <div>
            <template v-if="scoreInfo.fileReqs && scoreInfo.fileReqs.length">
              <div v-for="(item, index) in scoreInfo.fileReqs" :key="index" class="files">
                <span>{{ item.fileName }}</span>
                <span>{{ item.fileSize }}KB</span>
                <span @click="download(item)">下载</span>
              </div>
            </template>
            <template v-else>
              <div class="output">{{ scoreInfo.output }}</div>
            </template>
          </div>
        </el-row>
      </div>
      <div class="scoreDialog_right">
        <div class="scoreDialog_right_header">
          <div class="score">
            <span>自评分</span>
            <span>{{ scoreInfo.selfScore ? scoreInfo.selfScore : '---' }}</span>
          </div>
          <div class="score">
            <span>上级评分</span>
            <span>{{ scoreInfo.seniorScore ? scoreInfo.seniorScore : '---' }}</span>
          </div>
          <div class="score">
            <span>联查评分</span>
            <span>{{ scoreInfo.togetherScore ? scoreInfo.togetherScore : '---' }}</span>
          </div>
          <div class="score">
            <span>最终得分</span>
            <span>{{ scoreInfo.lastScore ? scoreInfo.lastScore : '---' }}</span>
          </div>
        </div>
        <div class="scoreDialog_right_body">
          <template v-if="scoreType === '上级评分'">
            <div class="superiors">上级评分: <span>加权处理50%</span></div>
            <div class="superiorsScore">
              <el-input v-model="seniorScore" placeholder="请输入"></el-input>
              <span>分</span>
            </div>
          </template>
          <template v-else>
            <div class="superiors">联查评分:</div>
            <div class="superiorsScore">
              <el-input v-model="togetherScore" placeholder="请输入"></el-input>
              <span>分</span>
            </div>
            <div class="message">根据联查小组的检查进行分数复核，联 查的时候已经出来基本分值30%</div>
          </template>
        </div>
        <div class="scoreDialog_right_footer">
          <div class="cancel" @click="cancel">取 消</div>
          <div class="confirm" @click="confirm">确 定</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: '',
  props: {
    showDialog: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      scoreInfo: {},
      type: 0, // 0上级评分 1联查评分
      seniorScore: null, // 上级评分
      togetherScore: null // 联查评分
    }
  },
  computed: {
    scoreType() {
      return this.type ? '联查评分' : '上级评分'
    },
    scoreTitle() {
      return this.scoreInfo.type === 1 ? '（月度关键业绩85%）' : this.scoreInfo.type === 2 ? '（团队合作考核指标5%）' : this.scoreInfo.type === 3 ? '（工作态度考核指标5%）' : '（特殊情况加分）'
    }
  },
  methods: {
    close() {
      this.$emit('update:showDialog', false)
    },
    inputBlur() {
      if (isNaN(this.seniorScore)) {
        this.$message.warning('请输入数字')
        this.seniorScore = null
        return false
      }
      if (parseInt(this.seniorScore) > this.scoreInfo.weight) {
        this.$message.warning('上级评分不能大于权重')
        this.seniorScore = null
        return false
      }
      return true
    },
    inputBlur2() {
      if (isNaN(this.togetherScore)) {
        this.$message.warning('请输入数字')
        this.togetherScore = null
        return false
      }
      if (parseInt(this.togetherScore) > this.scoreInfo.weight) {
        this.$message.warning('联查评分不能大于权重')
        this.togetherScore = null
        return false
      }
      return true
    },
    confirm() {
      if (this.scoreType === '上级评分') {
        if (this.inputBlur()) {
          if (!this.seniorScore) return this.$message.warning('上级评分不能为空')
          // if (!this.scoreInfo.seniorScore) return this.$message.warning('上级评分不能为空')
          this.scoreInfo.seniorScore = parseInt(this.seniorScore)
          this.$emit('scoreOver', this.scoreInfo, '上级评分')
          this.$nextTick(() => {
            this.cancel()
          })
        } else {
          // this.scoreInfo.seniorScore = 0
        }
      } else {
        if (this.inputBlur2()) {
          this.scoreInfo.togetherScore = parseInt(this.togetherScore)
          if (!this.scoreInfo.togetherScore) return this.$message.warning('联查评分不能为空')
          this.$emit('scoreOver', this.scoreInfo, '联查评分')
          this.$nextTick(() => {
            this.cancel()
          })
        } else {
          this.scoreInfo.togetherScore = 0
        }
      }
    },
    cancel() {
      this.scoreInfo = {}
      this.seniorScore = null
      this.togetherScore = null
      this.close()
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep {
  .scoreDialog {
    position: relative;
    height: 607px;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: none;
    .el-dialog__header {
      padding: 0;
      padding-left: 24px;
      background: #d8dbe1;
      height: 56px;
      background: #3464e0;
      .header {
        display: flex;
        align-items: center;
        width: 100%;
        height: 100%;
        & > span:first-of-type {
          font-size: 18px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #ffffff;
        }
        & > span:last-of-type {
          margin-left: 4px;
          font-size: 13px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #ffffff;
        }
      }
      .el-dialog__headerbtn {
        .el-icon-close {
          color: #ffffff;
        }
      }
    }
    .el-dialog__body {
      position: absolute;
      display: flex;
      height: 551px;
      padding: 0;
      .scoreDialog_left {
        width: 885px;
        .scoreRow {
          align-items: center;
          padding-top: 32px;
          padding-left: 56px;
          margin-bottom: 32px;
          .score {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            width: 124px;
            height: 80px;
            border-radius: 13px;
            & > span:first-of-type {
              font-size: 14px;
              font-weight: bold;
              color: #0b1a44;
            }
            & > span:last-of-type {
              margin-top: 12px;
              font-size: 24px;
              font-weight: bold;
              color: #0b1a44;
            }
            &:first-of-type {
              background: #f2e9df;
            }
            &:nth-of-type(2) {
              background: #dfeaf2;
            }
            &:nth-of-type(3) {
              background: #f0f2df;
            }
            &:nth-of-type(4) {
              background: #f2e1df;
              & > span:last-of-type {
                color: #eb6557;
              }
            }
          }
          .describe {
            width: 227px;
            margin-left: 12px;
            font-size: 12px;
            font-weight: 400;
            color: #a3a8bb;
            line-height: 20px;
          }
        }
        .titleRow {
          display: flex;
          align-items: flex-start;
          margin-top: 32px;
          padding-left: 33px;
          padding-right: 43px;
          & > div:first-of-type {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            margin-right: 24px;
            width: 132px;
            font-size: 14px;
            font-weight: 400;
            color: #0b1a44;
            img {
              margin-right: 4px;
            }
          }
          & > div:last-of-type {
            width: 678px;
            font-size: 14px;
            font-weight: bold;
            color: #0b1a44;
            // line-height: 20px;
          }
        }
        .describeRow {
          display: flex;
          align-items: flex-start;
          margin-top: 32px;
          padding-left: 33px;
          padding-right: 43px;
          & > div:first-of-type {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            margin-right: 24px;
            width: 132px;
            font-size: 14px;
            font-weight: 400;
            color: #0b1a44;

            & > div:first-of-type {
              display: flex;
              align-items: center;
              img {
                margin-right: 4px;
              }
            }
            .titleDescribe {
              margin-top: 8px;
              font-size: 12px;
              font-weight: 400;
              color: #b1bac7;
            }
          }
          & > div:last-of-type {
            width: 678px;
            font-size: 14px;
            font-weight: bold;
            color: #0b1a44;
            line-height: 20px;
          }
        }
        .taskRow {
          display: flex;
          align-items: center;
          & > div:last-of-type {
            display: flex;
            align-items: center;
            img {
              margin-right: 4px;
            }
          }
        }
        .outputRow {
          padding-bottom: 55px;
          & > div:first-of-type {
            font-weight: bold;
          }
          & > div:last-of-type {
            .files {
              margin-bottom: 16px;
              // & > span:first-of-type {
              //   display: inline-block;
              //   width: 150px;
              // }
              & > span {
                margin-right: 12px;
                font-size: 14px;
                font-weight: 400;
                color: #3464e0;
              }
              & > span:last-of-type {
                margin-left: 8px;
                font-size: 14px;
                font-weight: 400;
                color: #b1bac7;
                cursor: pointer;
              }
            }
          }
        }
      }
      .scoreDialog_right {
        flex: 1;
        background: #f7f8eb;
        padding-top: 17px;
        padding-left: 33px;
        padding-right: 32px;
        .scoreDialog_right_header {
          display: flex;
          flex-wrap: wrap;
          .score {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            width: 124px;
            height: 80px;
            border-radius: 13px;
            & > span:first-of-type {
              font-size: 14px;
              font-weight: bold;
              color: #0b1a44;
            }
            & > span:last-of-type {
              margin-top: 12px;
              font-size: 24px;
              font-weight: bold;
              color: #0b1a44;
            }
            &:first-of-type {
              background: #f2e9df;
            }
            &:nth-of-type(2) {
              margin-right: 0;
              background: #dfeaf2;
            }
            &:nth-of-type(3) {
              margin-top: 16px;
              background: #f0f2df;
            }
            &:nth-of-type(4) {
              margin-right: 0;
              margin-top: 16px;
              background: #f2e1df;
              & > span:last-of-type {
                color: #eb6557;
              }
            }
          }
        }
        .scoreDialog_right_body {
          margin-top: 56px;
          padding-left: 63px;
          .superiors {
            font-size: 20px;
            font-family: Microsoft YaHei-Bold, Microsoft YaHei;
            font-weight: bold;
            color: #eb6557;
            span {
              font-size: 12px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #ee8375;
            }
          }
          .superiorsScore {
            display: flex;
            align-items: flex-end;
            margin-top: 34px;
            .el-input {
              width: 126px;
              border-bottom: #eb6557 1px solid;
              .el-input__inner {
                height: 34px;
                border: none;
                background: transparent;
                text-align: center;
                color: #eb6557;
                font-size: 26px;
                font-weight: bold;
                &::placeholder {
                  font-size: 18px;
                  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
                  font-weight: bold;
                  color: #f2bdb0;
                }
              }
            }
            & > span {
              margin-left: 8px;
              font-size: 20px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #0b1a44;
            }
          }
          .message {
            width: 204px;
            margin-top: 40px;
            font-size: 12px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #ee8375;
            line-height: 20px;
          }
        }
        .scoreDialog_right_footer {
          position: absolute;
          right: 34px;
          bottom: 40px;
          display: flex;
          align-items: center;
          cursor: pointer;
          .confirm,
          .cancel {
            width: 118px;
            height: 44px;
            line-height: 44px;
            font-size: 18px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            text-align: center;
            border-radius: 8px;
          }
          .cancel {
            margin-right: 24px;
            color: #0b1a44;
            background: #fdfdfc;
            &:hover {
              background: #e5e4e0;
            }
          }
          .confirm {
            background: #3465df;
            color: #fff;
            &:hover {
              background: #355fce;
            }
          }
        }
      }
    }
  }
}
</style>
