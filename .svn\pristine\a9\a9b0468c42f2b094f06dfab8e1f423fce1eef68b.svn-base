<template>
  <div style="width: 100%; height: 100%">
    <div id="meetingStatisEchart" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script>
import resize from '../mixins/resize'
export default {
  name: '',
  mixins: [resize],
  data() {
    return {
      chart: null
    }
  },
  methods: {
    init(data) {
      this.chart = this.$echarts.init(document.getElementById('meetingStatisEchart'))
      this.setOpiton(data)
    },
    setOpiton(data) {
      const that = this
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: 'rgba(0,0,0,0.7)',
          borderColor: 'gray',
          padding: [12, 16, 16, 16],
          formatter: function (params) {
            let str = `
            <div class="meetingStatisEchart">
              <span class="meetingStatisEchartTitle">${params[0].name}</span>
            </div>
              `
            params[0].data.msg.forEach((item) => {
              str += `
              <div class="meetingStatisEchartText">
               <span></span>
               <span>会议数量:</span>
               <span>${item.meetingCount}场</span>
              </div>
               <div class="meetingStatisEchartText">
                <span></span>
                <span>会议用时:</span>
                <span>${parseFloat(item.times / 60).toFixed(1)}h</span>
              </div>
               <div class="meetingStatisEchartText">
                <span style="visibility: hidden;"></span>
                <span>参会人数:</span>
                <span>${item.number}人</span>
              </div>

              `
            })
            return str
          }
        },
        legend: {
          x: 'right' // 可设定图例在左、右、居中
        },
        xAxis: {
          type: 'category',
          data: data.x
        },
        yAxis: [
          {
            name: '数量(场)',
            type: 'value',
            min: 0 // 最小值
          },
          {
            name: '用时(h)',
            type: 'value',
            min: 0
          }
        ],
        grid: {
          //   top: '0',
          left: '2%',
          right: '0',
          bottom: '0',
          containLabel: true
        },
        series: [
          {
            name: '会议数量',
            data: data.y,
            type: 'bar',
            barWidth: 17,
            itemStyle: {
              color: '#3464e0'
            }
          },
          {
            name: '会议用时',
            type: 'line',
            smooth: true,
            yAxisIndex: 1,
            data: data.line,
            lineStyle: {
              color: '#33b4cf',
              shadowColor: '#5cfbff', // 透明的颜色
              shadowOffsetX: 0,
              shadowOffsetY: 0,
              opacity: 1, // 透明度
              shadowBlur: 8, // 阴影大小
              type: 'solid', // 实线
              width: 2
            },
            symbolSize: 10,
            showSymbol: false,
            itemStyle: {
              color: '#33b4cf' // 改变折线点的颜色
            }
          }
        ]
      }
      /** 处理双y轴刻度对不齐的情况*/
      const arr = option.series[0].data.map((item) => item.value)
      option.yAxis[0].max = Math.ceil(Math.max.apply(null, arr) / 5) * 5
      option.yAxis[0].interval = Math.ceil(Math.max.apply(null, arr) / 5)
      option.yAxis[1].max = Math.ceil(Math.max.apply(null, option.series[1].data) / 5) * 5
      option.yAxis[1].interval = Math.ceil(Math.max.apply(null, option.series[1].data) / 5)
      option.yAxis[0].min = 0
      option.yAxis[1].min = 0
      /** over*/

      this.chart.setOption(option)
      this.chart.on('click', function (params) {
        console.log(params)
        that.$emit('jumpPage')
      })
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep {
  .meetingStatisEchart {
    .meetingStatisEchartTitle {
      font-size: 12px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #ffffff;
    }
  }
  .meetingStatisEchartText {
    display: flex;
    align-items: center;
    font-size: 12px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #ffffff;
    & > span:first-of-type {
      display: inline-block;
      width: 6px;
      height: 6px;
      margin-right: 5px;
      border-radius: 6px;
      background: #33b4cf;
    }
    & > span:last-of-type {
      margin-left: 5px;
    }
  }
}
</style>
