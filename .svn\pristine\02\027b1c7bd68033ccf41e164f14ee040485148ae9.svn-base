import request from '@/utils/request'
/** 会议列表 */
export function meetingList(params) {
  return request({
    url: '/meeting/meetingList',
    method: 'get',
    params
  })
}
/** 会议室预约时间判断 */
export function meetingJudgeTime(data) {
  return request({
    url: '/meeting/judgeTime',
    method: 'POST',
    data
  })
}
/** 添加会议 */
export function meetingSave(data) {
  return request({
    url: '/meeting/save',
    method: 'POST',
    data
  })
}
/** 修改会议 */
export function meetingUpdate(data) {
  return request({
    url: '/meeting/update',
    method: 'POST',
    data
  })
}
/** 详情 */
export function meetingDetails(params) {
  return request({
    url: '/meeting/details',
    method: 'GET',
    params
  })
}
/** 删除文件 */
export function meetingDeleteFile(params) {
  return request({
    url: '/meeting/deleteFile',
    method: 'GET',
    params
  })
}
/** 修改文件名称 */
export function meetingUpdateFileName(params) {
  return request({
    url: '/meeting/updateFileName',
    method: 'GET',
    params
  })
}
/** 签到 */
export function meetingSign(params) {
  return request({
    url: '/meeting/sign',
    method: 'GET',
    params
  })
}
/** 人员确定 */
export function meetingDefine(params) {
  return request({
    url: '/meeting/define',
    method: 'GET',
    params
  })
}
/** 更新状态 */
export function meetingUpdateStatus(params) {
  return request({
    url: '/meeting/updateStatus',
    method: 'GET',
    params
  })
}
/** 补充会议纪要 */
export function meetingSummary(data) {
  return request({
    url: '/meeting/summary',
    method: 'POST',
    data
  })
}
/** 修改服务器文件名称 */
export function meetingRenameFile(params) {
  return request({
    url: '/meeting/renameFile',
    method: 'GET',
    params
  })
}
