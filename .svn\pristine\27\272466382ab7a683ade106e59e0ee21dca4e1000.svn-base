<template>
  <div class="app-container">
    <el-card>
      <div slot="header">项目管理</div>
      <!-- 搜索查询 -->
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form ref="queryInfo" :model="queryInfo" label-width="80px" inline>
            <el-form-item label="客户名称:">
              <el-input v-model="queryInfo.customerName" size="small" placeholder="请输入客户名称" clearable></el-input>
            </el-form-item>
            <el-form-item label="项目名称:">
              <el-input v-model="queryInfo.name" size="small" placeholder="请输入项目名称" clearable></el-input>
            </el-form-item>
            <el-form-item label="负责人名称:" label-width="90px">
              <el-input v-model="queryInfo.realName" size="small" placeholder="请输入项目名称" clearable></el-input>
            </el-form-item>
            <el-form-item label="负责部门:">
              <el-select ref="selecteltree" v-model="queryInfo.organizationName" size="small" clearable placeholder="请选择部门" @clear="onSelectClear" @focus="getMenuTreeOfParent">
                <el-option v-for="item in menu" :key="item.id" :label="item.organizationName" :value="item.id" style="display: none" />
                <el-tree style="padding-left: 15px" :data="menu" node-key="id" empty-text="暂无菜单" highlight-current :expand-on-click-node="false" :props="defaultProps" current-node-key="id" default-expand-all @node-click="handleNodeClick" />
              </el-select>
            </el-form-item>
            <el-form-item label="开始时间:">
              <el-date-picker v-model="queryInfo.startTime" type="date" size="small" placeholder="选择开始时间" clearable> </el-date-picker>
            </el-form-item>
            <el-form-item label="结束时间:">
              <el-date-picker v-model="queryInfo.endTime" type="date" size="small" placeholder="选择结束时间" clearable> </el-date-picker>
            </el-form-item>
            <el-form-item label="当前版本阶段:" label-width="110px">

              <el-select v-model="queryInfo.stage" placeholder="请选择当前版本阶段" size="small" clearable>
                <el-option v-for="item in stageOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="success" size="small" @click="getProjectList"> 查询 </el-button>
              <el-button type="primary" size="small" @click="reset">重置 </el-button>
              <el-button type="primary" size="small" @click="showDialog = true">添加项目 </el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <!-- 表格 -->
      <div>
        <el-table :data="projectTable" style="width: 100%" border>
          <el-table-column align="center" prop="code" label="项目编号" width="width"> </el-table-column>
          <el-table-column align="center" prop="name" label="项目名称" width="width"> </el-table-column>
          <el-table-column align="center" prop="customerName" label="客户名称" width="width"> </el-table-column>
          <el-table-column align="center" prop="organizationName" label="负责部门" width="width"> </el-table-column>
          <el-table-column align="center" prop="realName" label="项目负责人" width="width"> </el-table-column>
          <el-table-column align="center" prop="stage" label="当前版本阶段" width="width">
            <template v-slot="{ row }">
              <span>{{ row.stage | projectStage }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="updateTime" label="更新时间" width="width"> </el-table-column>
          <el-table-column align="center" prop="prop" label="操作" width="width">
            <template v-slot="{ row }">
              <el-button type="warning" size="small" @click="edit(row)">编辑</el-button>
              <el-button type="danger" size="small" @click="del(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination style="text-align: center; margin-top: 15px" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 15, 20, 30]" :total="total" :page-size.sync="queryInfo.pageSize" :current-page.sync="queryInfo.pageNum" @size-change="getProjectList" @current-change="getProjectList" />
      </div>
    </el-card>
    <!-- 添加的dialog -->
    <el-dialog :title="title" :visible.sync="showDialog" width="400px" :close-on-click-modal="false">
      <el-form ref="addFormRef" class="addFormClass" :model="addFormInfo" label-width="90px" :rules="rules">
        <el-form-item label="项目名称:" prop="projectName">
          <el-input v-model="addFormInfo.projectName" placeholder="请输入项目名称,20字以内" size="small" maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="客户名称:" prop="customerName">
          <el-input v-model="addFormInfo.customerName" size="small" placeholder="请输入客户名称,20字以内" maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="项目类型:" prop="state">
          <el-radio v-model="addFormInfo.state" :label="1">研发</el-radio>
        </el-form-item>
        <el-form-item label="项目负责人:" label-width="99px" prop="userId">
          <el-select v-model="addFormInfo.userId" placeholder="请选择项目负责人" size="small" @focus="getUserList">
            <el-option v-for="item in userList" :key="item.userId" :label="item.realName" :value="item.userId"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="项目描述:">
          <el-input v-model="addFormInfo.description" size="small" type="textarea" placeholder="请输入项目描述,500字以内" maxlength="500"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="showDialog = false">取 消</el-button>
        <el-button type="primary" @click="addProject">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { projectList, projectSaveProject, projectDelete, projectDevelopList } from '@/api/project.js'
import { getOrganizationTree } from '@/api/organization'
import { getList } from '@/api/systemUser'
import { mapGetters } from 'vuex'
export default {
  name: 'Project',
  data() {
    return {
      queryInfo: {
        name: null, // 项目名称
        customerName: null, // 客户名称
        realName: null, // 负责人名称
        organizationId: null, // 负责部门
        organizationName: null,
        type: 1,
        startTime: null,
        endTime: null,
        stage: null, // 阶段

        pageNum: 1,
        pageSize: 10
      },
      menu: [],
      defaultProps: {
        label: 'organizationName',
        children: 'children'
      },
      stageOptions: [
        {
          label: '未立项',
          value: 1
        },
        {
          label: '调研中',
          value: 2
        },
        {
          label: '研发中',
          value: 3
        },
        {
          label: '已归档',
          value: 4
        },
        {
          label: '已交付',
          value: 5
        }
      ],
      stageOptions_after: [
        {
          label: '施工准备中',
          value: 1
        },
        {
          label: '施工中',
          value: 2
        },
        {
          label: '竣工',
          value: 3
        }
      ],
      total: null,
      projectTable: [],
      showDialog: false,
      title: '添加项目',
      addFormInfo: {
        projectName: null, // 项目名称
        customerName: null, // 客户名称
        userId: null, // 负责人id
        state: 1, // 项目状态 1 研发 2 施工
        description: null, // 描述
        relationId: null, // 关联研发项目id
        type: 1 // 类型 1 项目管理 2 售后记录
      },
      rules: {
        projectName: [
          {
            required: true,
            tigger: 'blur',
            message: '项目名称不能为空'
          }
        ],
        customerName: [
          {
            required: true,
            tigger: 'blur',
            message: '客户名称不能为空'
          }
        ],
        userId: [
          {
            required: true,
            tigger: 'change',
            message: '负责人不能为空',
            type: 'string' || 'number'
          }
        ],
        state: [
          {
            required: true,
            tigger: 'change',
            message: '项目类型不能为空',
            type: 'number'
          }
        ]
      },
      userList: [],
      type: 1,
      projectDevelopList: [],
      timeout: null
    }
  },
  computed: {
    ...mapGetters(['organizationId', 'keyList'])
  },
  mounted() {
    this.getProjectList()
  },
  methods: {
    async getProjectList() {
      const { data } = await projectList(this.queryInfo)
      this.projectTable = data.list
      this.total = data.total
    },
    reset() {
      this.queryInfo = {
        name: null, // 项目名称
        customerName: null, // 客户名称
        realName: null, // 负责人名称
        organizationId: null, // 负责部门
        organizationName: null,
        type: this.type,
        startTime: null,
        endTime: null,
        stage: null, // 阶段
        pageNum: 1,
        pageSize: 10
      }
      this.getProjectList()
    },
    addProject() {
      this.$refs['addFormRef'].validate(async (val) => {
        if (val) {
          await projectSaveProject(this.addFormInfo)
          if (this.addFormInfo.type === 2) {
            this.$message.success('添加售后记录成功')
          } else {
            this.$message.success('添加项目成功')
          }
          this.showDialog = false
          this.getProjectList()
        }
      })
    },
    edit(row) {
      this.$router.push(`/project/details/${row.id}/${this.type}`)
    },
    del(row) {
      this.$confirm('确定要删除吗, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await projectDelete({ id: row.id, type: this.type })
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getProjectList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 获取当前部门下的用户
    async getUserList() {
      const { data } = await getList({ pageNum: 1, pageSize: 200, organizationId: this.organizationId })
      this.userList = data.list
    },
    onSelectClear() {
      this.queryInfo.organizationId = null
    },
    getMenuTreeOfParent() {
      getOrganizationTree().then((res) => {
        this.menu = res.data
      })
    },
    handleNodeClick(node) {
      this.queryInfo.organizationName = node.organizationName
      this.queryInfo.organizationId = node.id
      this.$refs['selecteltree'].blur()
    },

    querySearchAsync(queryString, cb) {
      if (queryString !== '') {
        clearTimeout(this.timeout)

        this.timeout = setTimeout(async () => {
          const { data } = await projectDevelopList({ name: this.addFormInfo.projectName })
          this.projectDevelopList = data
          const cbList = []
          this.projectDevelopList.forEach((item) => {
            cbList.push({
              value: item.name,
              id: item.id
            })
          })
          console.log(cbList)
          cb(cbList)
        }, 1000 * Math.random())
      }
    },
    handleSelect(item) {
      this.addFormInfo.relationId = item.id
    }
  }
}
</script>

<style scoped lang="scss">
.addFormClass {
  ::v-deep {
    .el-input,
    .el-textarea {
      width: 250px;
    }
  }
}
</style>
