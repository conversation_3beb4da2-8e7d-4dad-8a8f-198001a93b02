<template>
  <div class="app-container">
    <!-- 搜索查询——添加 -->
    <el-row style="min-width: 1200px; margin-bottom: 20px" type="flex" justify="space-between">
      <el-col :span="21">
        <el-form ref="form" :model="searchForm" label-width="85px" inline>
          <el-form-item label="专业名称:">
            <el-input v-model="searchForm.majorName" size="small" clearable placeholder="请输入专业名称" />
          </el-form-item>
          <el-form-item>
            <el-button type="success" size="small" @click="getMajorList">查询</el-button>
            <el-button type="primary" size="small" @click="add">添加专业</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-table :data="majorTreeList" style="width: 100%" row-key="id" default-expand-all>
      <el-table-column prop="majorCode" label="专业代码" width="400"> </el-table-column>
      <el-table-column prop="majorName" label="专业名称" width="width" align="center"> </el-table-column>
      <el-table-column prop="description" label="描述" width="500" align="center"> </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="width" align="center">
        <template v-slot="{ row }">
          <span>{{ row.createTime | formatDate }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="width" align="center">
        <template v-slot="{ row }">
          <el-button type="warning" size="small" @click="edit(row)">修改专业</el-button>
          <el-button type="danger" size="small" :disabled="row.parentId === 0" @click="del(row)">删除专业</el-button>
        </template>
      </el-table-column>
    </el-table>
    <improvement ref="improvement" :show-dialog.sync="showDialog" @success="getMajorList" />
  </div>
</template>

<script>
import { majorList, majorRemove } from '@/api/specialty'
import improvement from './components/improvement'
export default {
  name: '',
  components: {
    improvement
  },
  data() {
    return {
      majorTreeList: [],
      searchForm: {
        pageNum: 1,
        pageSize: 200,
        majorName: null
      },
      showDialog: false
    }
  },
  created() {
    this.getMajorList()
  },
  methods: {
    async getMajorList() {
      const { data } = await majorList(this.searchForm)
      this.majorTreeList = data
    },
    add() {
      this.showDialog = true
    },
    edit(row) {
      this.showDialog = true
      this.$refs['improvement'].showData(row)
    },
    del(row) {
      console.log(row)
      this.$confirm('确定要删除该专业吗?', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          try {
            if (row.children && row.children.length >= 1) {
              return this.$message.warning('不能删除拥有子级的专业')
            }
            await majorRemove({ id: row.id })
            this.$message.success('删除成功')
            this.getMajorList()
          } catch (err) {
            console.log(err)
          }
        })
        .catch(() => {
          this.$message.info('已取消删除')
        })
    }
  }
}
</script>

<style scoped lang="scss"></style>
