<template>
  <div class="app-container">
    <div class="content">
      <el-card>
        <div slot="header"></div>
        <div v-if="processDetails.processInstanceId">
          <el-descriptions :column="1">
            <el-descriptions-item label="事项名称">{{ processDetails.title }}</el-descriptions-item>
            <el-descriptions-item label="事项类型">
              <el-tag size="small">{{ processDetails.processName }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag size="small">{{ processDetails.status | processStatus }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="发起时间">{{ processDetails.createTime }}</el-descriptions-item>
            <el-descriptions-item label="发起人">{{ processDetails.username }}</el-descriptions-item>
          </el-descriptions>
          <el-descriptions :column="1" title="详情:" style="margin-top: 15px">
            <el-descriptions-item v-for="item in formDetails" :key="item.formId" :label="item.name">
              <span v-if="item.componentType !== 'DDPhotoField' && item.componentType !== 'DDAttachment' && item.componentType !== 'TableField' && item.value !== 'null'">{{ item.value }}</span>
              <div v-if="item.componentType === 'DDPhotoField'">
                <el-image v-for="list in item.value" :key="list" style="width: 100px" :src="list" :preview-src-list="item.value"> </el-image>
              </div>
              <div v-if="item.componentType === 'DDAttachment' && item.value !== 'null'" type="primary" :href="item.value">
                <a v-for="list in item.value" :key="list.url" :href="list.url" target="blank">{{ list.url }}</a>
              </div>
              <el-descriptions v-if="item.componentType === 'TableField' && item.value[0].rowValue.length >= 1" :column="1" border size="small">
                <el-descriptions-item v-for="list in item.value[0].rowValue" :key="list.label" :label="list.label">{{ list.value }}</el-descriptions-item>
              </el-descriptions>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-card>
      <el-card>
        <div slot="header"></div>
        <div>
          <el-timeline>
            <el-timeline-item v-for="(item, index) in processDetails.detailsTaskDtoList" :key="item.taskId" type="primary" :timestamp="item.createTime" placement="top">
              <el-card :header="item.username" style="width: 100%">
                <el-descriptions :column="1">
                  <el-descriptions-item label="当前状态">
                    <el-tag size="small">{{ item.taskStatus | currentTaskStatus }}</el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item v-if="processDetails.detailsTaskDtoList[index].records.length >= 1" label="评论">
                    <div>
                      <div v-for="list in processDetails.detailsTaskDtoList[index].records" :key="list.date" class="records">
                        <div class="remark">
                          <span> {{ list.remark }}</span>
                          <span v-if="list.remark" style="color: #1890ff; margin-left: 15px">{{ list.date }}</span>
                        </div>
                      </div>
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item label="结果">
                    <el-tag size="small">{{ item.taskResult | processResult }}</el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="结束时间">
                    {{ item.finishTime }}
                  </el-descriptions-item>
                </el-descriptions>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
        <div v-if="processDetails.isReview === '1'" style="margin-top: 35px">
          <el-row :gutter="10" type="flex" align="middle" justify="space-between" style="border-bottom: 1px solid #ababab; padding-bottom: 15px">
            <el-col :span="6">
              <span style="font-size: 18px; font-weight: 600">审批</span>
            </el-col>
            <el-col :span="6">
              <el-button size="small" @click="$router.push('/process/management')">取 消</el-button>
              <el-button size="small" type="primary" @click="approval">确 定</el-button>
            </el-col>
          </el-row>
          <el-form ref="form" :model="formInfo" label-width="90px" style="margin-top: 35px" :rules="rules">
            <el-form-item label="审批意见:">
              <el-input v-model="formInfo.remark" type="textarea" placeholder="请输入审批意见，200字以内" maxlength="200"></el-input>
            </el-form-item>
            <el-form-item label="审批结果:" prop="result">
              <el-radio-group v-model="formInfo.result">
                <el-radio label="agree" border size="small">同意</el-radio>
                <el-radio label="refuse" border size="small">拒绝</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </div>
    <el-row :gutter="10" type="flex" justify="center" style="margin-top: 35px">
      <el-col :span="1.5">
        <el-button type="primary" @click="$router.push('/process/management')">关 闭</el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { details, execute } from '@/api/process'
import { formatDate } from '@/filters'

export default {
  name: 'ProcessDetails',
  data() {
    return {
      processDetails: {},
      formDetails: [],
      formInfo: {
        processInstanceId: null,
        remark: null,
        result: null,
        actionerUserid: null,
        taskId: null,
        processCode: null
      },
      rules: {
        result: [
          {
            required: true,
            tigger: 'change',
            message: '请选择审批结果'
          }
        ]
      }
    }
  },
  created() {
    this.getDetails()
  },
  methods: {
    async getDetails() {
      const { data } = await details({ processInstanceId: this.$route.params.processInstanceId })
      this.processDetails = data
      this.formDetails = data.tprocessInstanceForms
      console.log(this.formDetails)

      this.formDetails.forEach((item) => {
        if (item.componentType === 'DDHolidayField' || item.componentType === 'DDDateField' || item.componentType === 'DDDateRangeField' || item.componentType === 'DDGooutField') {
          // 开始时间-结束时间格式处理
          if (item.componentType === 'DDDateRangeField' || item.componentType === 'DDGooutField' || item.componentType === 'DDHolidayField') {
            item.name = JSON.parse(item.name).join(' - ')
            item.value = `${JSON.parse(item.value)[0]} - ${JSON.parse(item.value)[1]}`
          }
          // 附件格式问题
        } else if (item.componentType === 'DDAttachment') {
          item.value = JSON.parse(item.value)
        } else if (item.componentType === 'DDPhotoField') {
          item.value = JSON.parse(item.value)
          // 补卡申请时间格式处理
        } else if (item.name === 'repairCheckTime') {
          item.name = '补卡申请时间'
          item.value = formatDate(new Date(parseInt(item.value)))
          // 发票类型格式处理
        } else if (item.componentType === 'DDMultiSelectField') {
          item.value = JSON.parse(item.value).join(',')
          // 协作明细格式处理
        } else if (item.componentType === 'TableField') {
          item.value = JSON.parse(item.value)
          console.log(item.value)
        }
      })
      this.formDetails = this.formDetails.filter((item) => {
        return item.componentType !== 'DDBizSuite' && item.componentType !== 'TableField '
      })
      console.log(this.processDetails)
    },
    approval() {
      this.$refs['form'].validate(async (val) => {
        if (val) {
          this.formInfo.processInstanceId = this.processDetails.processInstanceId
          this.formInfo.processCode = this.processDetails.processCode
          this.formInfo.actionerUserid = this.processDetails.detailsTaskDtoList[this.processDetails.detailsTaskDtoList.length - 1].userId
          this.formInfo.taskId = this.processDetails.detailsTaskDtoList[this.processDetails.detailsTaskDtoList.length - 1].taskId
          await execute(this.formInfo)
          this.$message.success('审批成功')
          this.$router.push('/process/management')
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.content {
  width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  ::v-deep {
    .el-card {
      width: 49%;
    }
  }
  .records {
    margin-bottom: 15px;
    border-radius: 8px;
    box-shadow: 0 0 2px #999;
    padding: 10px;
    box-sizing: border-box;
    .remark {
      width: 510px;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      span {
        min-width: 129px;
      }
    }
  }
}
::v-deep {
  .el-descriptions-item__container {
    display: flex !important;
    // align-items: center;
  }
  .el-timeline {
    padding-left: 0;
  }
}
</style>
