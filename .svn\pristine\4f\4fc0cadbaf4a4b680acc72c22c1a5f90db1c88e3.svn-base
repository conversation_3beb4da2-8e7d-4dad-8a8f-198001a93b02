<template>
  <div class="contractNew_FileUpload">
    <!-- 定稿合同 培训现场图 培训签字单 上传 -->
    <el-dialog :title="uploadFileDialogTitle" :visible.sync="finalizeFileDialog" width="420px" @close="dialogClose">
      <el-upload
        drag
        :action="action"
        :accept="accept"
        :multiple="belongType !== 19 && belongType !== 0"
        :headers="header"
        :data="dataObj"
        :file-list="fileList"
        :before-upload="beforeUpload"
        :on-success="uploadSuccess"
        :on-remove="uploadRemove"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div v-if="isExist" class="exist">已存在文件</div>
        <div slot="tip" class="el-upload__tip" style="color: red; line-height: 15px">{{ uploadFileType }}</div>
      </el-upload>
      <div slot="footer">
        <el-button @click="finalizeFileDialog = false">取 消</el-button>
        <el-button type="primary" @click="finalizeFileConfirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'

import { meetingSaveFile } from '@/api/meeting'
import { contractConstructionUpdate, contractConstructionRecordUpdate } from '@/api/contractNew'
export default {
  name: '',
  data() {
    return {
      meetingId: null,
      uploadFileDialogTitle: '定稿合同',
      finalizeFileDialog: false, // 上传定稿合同
      action: window.config.VUE_APP_BASE_API + '/system/upload/file',
      belongType: null,
      header: {
        Authorization: null
      },
      dataObj: {
        fileName: ''
      },
      fileList: [],
      isExist: false, // 是否存在文件
      info: null
    }
  },
  computed: {
    ...mapGetters(['token']),
    accept() {
      if (this.belongType === 21 || this.belongType === 17 || this.belongType === 25) {
        return '.jpg, .png'
      } else {
        return '.doc, .docx, .xls, .xlsx, .pdf, .jpg, .png, .zip'
      }
    },
    uploadFileType() {
      if (this.belongType === 21 || this.belongType === 17 || this.belongType === 25) {
        return '上传多个文件、上传类型: .jpg .png格式，且大小不超过50MB '
      } else {
        return '只能上传一个文件、 上传类型: .doc .docx .xls .xlsx .pdf .jpg .png .zip格式，且大小不超过50MB'
      }
    }
  },
  created() {},
  methods: {
    dialogClose() {
      this.fileList = []
    },
    uploadFile(type, info, meetingId) {
      if (type === 19) {
        this.uploadFileDialogTitle = '定稿合同'
      } else if (type === 21) {
        this.uploadFileDialogTitle = '培训现场图'
        this.isExist = info.sceneFiles?.length
      } else if (type === 17) {
        this.uploadFileDialogTitle = '培训签字单'
        this.isExist = info.signFiles?.length
      } else if (type === 25) {
        this.uploadFileDialogTitle = '现场装修效果图'
        this.isExist = info.renovationFiles?.length
      } else if (type === 0) {
        this.uploadFileDialogTitle = '现场勘测文件'
        this.isExist = info.surveyFile
      }
      this.info = info
      this.meetingId = meetingId
      this.belongType = type
      this.finalizeFileDialog = true
    },
    // 添加合同- 上传前触发事件
    beforeUpload(file) {
      this.dataObj.fileName = 'contract/' + file.name
      this.header.Authorization = `Bearer ${this.token}`
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isDOC = file.type === 'application/msword'
      const isDOCX = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      const isXLS = file.type === 'application/vnd.ms-excel'
      const isXLSX = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      const isPDF = file.type === 'application/pdf'
      const isZIP = file.type === 'application/x-zip-compressed'
      const isLt2M = file.size / 1024 / 1024 < 50
      if (!isLt2M) {
        this.$message.error('上传附件大小不能超过 50MB!')
      }
      if (!isJPG && !isPNG && !isDOC && !isDOCX && !isXLS && !isXLSX && !isPDF && !isZIP) {
        this.$message.warning('只能上传.doc .docx .xls .xlsx .pdf .jpg .png .zip 格式的附件')
      }
      return (isJPG || isPNG || isDOC || isDOCX || isXLS || isXLSX || isPDF || isZIP) && isLt2M
    },
    // 添加合同- 上传成功事件
    uploadSuccess(res, file, fileList) {
      if (res.code !== 200) {
        this.$message.error(res.message)
      } else {
        if (this.belongType === 19 || this.belongType === 0) {
          this.fileList = [file]
        } else {
          this.fileList = fileList
        }
      }
      console.log(file)
    },
    // 添加合同- 上传删除事件
    uploadRemove(file, fileList) {
      this.fileList = fileList
      console.log(this.fileList)
    },

    async finalizeFileConfirm() {
      if (!this.fileList.length) return this.$message.warning('暂未上传附件')
      if (this.belongType === 19) {
        const file = this.fileList[0]
        const fileInfo = {
          fileName: file.name,
          fileSize: Math.floor(file.size / 1024),
          fileUrl: file.response.data[0],
          belongType: this.belongType,
          meetingId: parseInt(this.meetingId)
        }
        await meetingSaveFile([fileInfo])
      } else if (this.belongType === 17 || this.belongType === 21 || this.belongType === 25) {
        const list = this.fileList.map((file) => {
          return {
            fileName: file.name,
            fileSize: Math.floor(file.size / 1024),
            fileUrl: file.response.data[0],
            belongType: this.belongType,
            meetingId: parseInt(this.meetingId)
          }
        })
        await meetingSaveFile(list)
        if (this.belongType === 25) {
          await contractConstructionUpdate({ contractId: this.$route.params.contractId, constructionId: this.meetingId, reqs: list })
        } else {
          console.log(this.info)
          const fileType = this.belongType === 17 ? 'signReqs' : 'sceneReqs'
          await contractConstructionRecordUpdate({ ...this.info, [fileType]: list })
        }
      } else if (this.belongType === 0) {
        const file = this.fileList[0]
        await contractConstructionUpdate({ contractId: this.$route.params.contractId, constructionId: this.meetingId, surveyFile: file.response.data[0] })
      }
      this.finalizeFileDialog = false
      this.$message.success('上传成功!')
      this.$emit('refreshData')
    }
  }
}
</script>
<style scoped lang="scss">
.exist {
  margin-top: 10px;
  color: #15d36a;
}
</style>
