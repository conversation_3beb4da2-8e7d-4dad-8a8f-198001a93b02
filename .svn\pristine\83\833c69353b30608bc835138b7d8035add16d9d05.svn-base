import request from '@/utils/request'

/** 问题工作跟踪列表 */
export function workTrackList(params) {
  return request({
    url: '/workTrack/list',
    method: 'GET',
    params
  })
}
/** 添加问题工作跟踪 */
export function workTrackAdd(data) {
  return request({
    url: '/workTrack/add',
    method: 'post',
    data
  })
}
/** 修改问题工作跟踪 */
export function workTrackUpdate(data) {
  return request({
    url: '/workTrack/update',
    method: 'post',
    data
  })
}
/** 删除问题工作跟踪   @param {number} id  问题跟踪id*/
export function workTrackRemove(id) {
  return request({
    url: '/workTrack/remove',
    method: 'DELETE',
    params: {
      id
    }
  })
}
/** 问题工作跟踪详情 @param {number} id  问题跟踪id  */
export function workTrackDetail(id) {
  return request({
    url: '/workTrack/detail',
    method: 'GET',
    params: {
      id
    }
  })
}
