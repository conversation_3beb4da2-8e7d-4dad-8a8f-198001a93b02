<template>
  <div class="app-container">
    <el-drawer title="浏览人列表" :visible.sync="showDrawer" direction="rtl" :size="800">
      <el-table :data="list" style="width: 95%; margin: 0 auto" border>
        <el-table-column align="center" prop="headurl" label="头像" width="width">
          <template v-slot="{ row }">
            <el-image v-if="row.headurl" style="width: 40px; height: 40px" :src="row.headurl" :preview-src-list="[row.headurl]"></el-image>
            <span v-else>暂无头像 </span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="realName" label="姓名" width="width"> </el-table-column>
        <el-table-column align="center" prop="createTime" label="浏览时间" width="width"> </el-table-column>
      </el-table>
      <el-pagination
        v-if="list.length > 0"
        style="text-align: center; margin-top: 25px"
        layout="total, prev, pager, next"
        :page-sizes="[5, 10, 15, 20]"
        background
        :total="total"
        :page-size.sync="pageSize"
        :current-page.sync="pageNum"
        @size-change="getList"
        @current-change="getList"
      />
    </el-drawer>
  </div>
</template>
<script>
import { knowledgeBaseBrowseList } from '@/api/repository'
export default {
  name: '',
  data() {
    return {
      knowledgeBaseId: null,
      showDrawer: false,
      pageNum: 1,
      pageSize: 10,
      list: [],
      total: 0
    }
  },
  created() {},
  methods: {
    async openDrawer(knowledgeBaseId) {
      this.knowledgeBaseId = knowledgeBaseId
      const { data } = await knowledgeBaseBrowseList({ knowledgeBaseId, pageNum: this.pageNum, pageSize: this.pageSize })
      this.list = data.list
      this.total = data.total
      if (!this.list.length) return this.$message.warning('暂无浏览人')
      this.showDrawer = true
    },
    async getList() {
      const { data } = await knowledgeBaseBrowseList({ knowledgeBaseId: this.knowledgeBaseId, pageNum: this.pageNum, pageSize: this.pageSize })
      this.list = data.list
      this.total = data.total
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .el-drawer__header {
    font-size: 18px;
  }
}
</style>
