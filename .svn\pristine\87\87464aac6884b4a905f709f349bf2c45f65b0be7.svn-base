<template>
  <div class="Comment">
    <el-row type="flex" align="middle" justify="space-between">
      <div class="title">
        <img src="@/assets/contractNew/commentIcon.png" alt="" />
        评论区
      </div>
      <div class="lookAll">
        <el-button type="primary" size="small" icon="el-icon-plus" @click="addComment">新增评论</el-button>
      </div>
    </el-row>
    <div class="commentBox">
      <el-timeline>
        <el-timeline-item v-for="activity in list" :key="activity.commentId">
          <div class="commentInfo">
            <div class="content">{{ activity.content }}</div>
            <div class="asideInfo">
              <div>
                评论人:
                <section>
                  <el-avatar :src="activity.headurl" shape="circle" :size="24" fit="cover" class="user-avatar" @error="true">
                    <img src="@/assets/login/logo.png" />
                  </el-avatar>
                  <span>{{ activity.realName }}</span>
                </section>
              </div>
              <div>时间: {{ activity.createTime }}</div>
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>

    <!-- 新增评论 -->
    <el-dialog title="新增评论" :visible.sync="commentDialog" width="600px" @close="close">
      <div>
        <el-form ref="form" :model="formInfo" :rules="rules" label-width="90px">
          <el-form-item label="评论内容" prop="content">
            <el-input v-model="formInfo.content" type="textarea" placeholder="请输入评论内容" maxlength="400" resize="none" rows="5"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { contractCommentList, contractCommentAdd } from '@/api/contractNew'
export default {
  name: '',
  props: {
    contractId: {
      type: String,
      default: '0'
    },
    type: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      queryInfo: {
        pageNum: 1,
        pageSize: 10
      },
      list: [],
      commentDialog: false,
      formInfo: {
        contractId: this.contractId,
        content: null
      },
      rules: {
        content: [{ required: true, message: '请输入评论内容', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getCommentList()
  },
  methods: {
    async getCommentList() {
      const { data } = await contractCommentList({ ...this.queryInfo, contractId: this.contractId })
      this.list = data.list
      console.log('评论内容', data.list)
    },
    addComment() {
      this.commentDialog = true
    },
    close() {
      this.formInfo.content = null
      this.$refs['form'].resetFields()
      this.commentDialog = false
    },
    confirm() {
      this.$refs['form'].validate((val) => {
        if (val) {
          const loading = this.$loading({
            text: '数据保存中，请稍后...',
            background: 'rgba(0,0,0,0.7)'
          })
          contractCommentAdd(this.formInfo).then(() => {
            loading.close()
            this.close()
            this.$message.success('评论成功！')
            this.getCommentList()
            // this.$emit('success')
          })
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.Comment {
  height: 100%;
  padding: 20px;
  padding-right: 0;
  .title {
    display: flex;
    align-items: center;
    font-family: Source Han Sans CN;
    font-weight: 500;
    font-size: 18px;
    color: #000000;
    img {
      margin-right: 8px;
      width: 38px;
      height: 38px;
    }
  }

  .lookAll {
    display: felx;
    align-items: center;
    padding-right: 20px;
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #666666;
    cursor: pointer;
  }
  .commentBox {
    width: 100%;
    height: calc(100% - 30px);
    padding-right: 20px;
    overflow: auto;
    &::-webkit-scrollbar {
      width: 3px;
    }
    &::-webkit-scrollbar-thumb {
      width: 3px;
      border-block: 10px;
      background: #3465df;
    }
  }
}
::v-deep {
  .el-timeline {
    margin-top: 26px;
  }
  .el-timeline-item__node--normal {
    left: 1px;
    width: 8px;
    height: 8px;
    background: #3465df;
  }
  .el-timeline-item__tail {
    border-color: #3465df;
  }
  .el-timeline-item__wrapper {
    padding-left: 25px;
  }
  .commentInfo {
    .content {
      margin-right: 20px;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #000000;
      word-wrap: break-word;
    }
    .asideInfo {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 10px;
      & > div {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #999999;
      }
      & > div:first-of-type {
        display: flex;
        align-items: center;
        section {
          display: flex;
          align-items: center;
          margin-left: 5px;
          padding-right: 10px;
          background: #f0f5ff;
          border-radius: 60px;

          span {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            color: #666666;
          }
        }
      }
    }
  }
  .el-dialog__header {
    border-bottom: 1px solid #eeeeef;
    .el-dialog__title {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: bold;
      font-size: 18px;
      color: #0b1a44;
    }
  }
  .el-dialog__body {
    padding: 24px 0;
    padding-bottom: 0;
    .el-form {
      padding-left: 20px;
      padding-right: 20px;
      .el-form-item__label {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: 14px;
        color: #0b1a44;
      }
    }
  }
  .el-dialog__footer {
    & > div {
      display: flex;
      justify-content: center;
      .el-button {
        padding: 0;
        width: 114px;
        height: 38px;
        border-radius: 4px;
        &:first-of-type {
          margin-right: 10px;
          background: #f2f4ff;
          color: #868b9f;
          &:hover {
            color: #868b9f;
            border-color: #f2f4ff;
          }
        }
      }
    }
  }
}
</style>
