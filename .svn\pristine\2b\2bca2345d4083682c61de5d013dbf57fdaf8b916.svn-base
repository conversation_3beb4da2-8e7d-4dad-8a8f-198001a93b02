<template>
  <div class="">
    <el-dialog title="上传excel" :visible="showDialog" width="400px" @close="close">
      <el-upload class="upload-demo" drag :action="action" :data="dataObj" :headers="headers" :before-upload="beforeUpload" :file-list="fileList" :on-success="uploadSuccess">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div @click.stop>
          <a class="el-download__text" :href="download_url + '/cloudFile/workmanage/contacttemplate.xlsx'">下载模板</a>
        </div>
      </el-upload>
      <div slot="footer">
        <el-row type="flex" justify="center">
          <el-button type="primary" @click="close">关 闭</el-button>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: '',
  props: {
    showDialog: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      action: window.config.VUE_APP_BASE_API + '/secret/customer/importContacts',
      headers: {
        Authorization: null
      },
      download_url: window.config.VUE_APP_DOWNLOAD_URL,
      fileList: [],
      dataObj: {
        customerId: this.$route.params.id
      }
    }
  },
  computed: {
    ...mapGetters(['token'])
  },
  methods: {
    close() {
      this.fileList = []
      this.$emit('update:showDialog', false)
    },
    beforeUpload(file) {
      this.headers.Authorization = `Bearer ${this.token}`
      const isXLS = file.type === 'application/vnd.ms-excel'
      const isXLSX = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      if (!isXLS && !isXLSX) {
        this.$message.warning('只能上传.xls .xlsx 格式的附件')
      }
      return isXLS || isXLSX
    },
    uploadSuccess(res, file, fileList) {
      if (res.code !== 200) return this.$message.error(res.message)
      this.fileList = fileList
      this.$message.success('上传成功')
      this.$emit('success')
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep {
  .el-dialog {
    border-radius: 8px !important;
  }
  .el-dialog__body {
    padding: 10px 20px !important;
  }
  .el-upload__text {
    margin-bottom: 15px;
  }
  .el-download__text {
    color: #0bca05;
    font-size: 16px;
  }
}
</style>
