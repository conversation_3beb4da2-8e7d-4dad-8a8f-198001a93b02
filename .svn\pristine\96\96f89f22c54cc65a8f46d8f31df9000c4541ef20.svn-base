<template>
  <div class="app-container">
    <!-- 搜索查询——添加 -->
    <el-row style="min-width: 1200px; margin-bottom: 20px" type="flex" justify="space-between">
      <el-col :span="21">
        <el-form ref="form" :model="searchForm" label-width="85px" inline>
          <el-form-item label="部门名称:">
            <el-input v-model="searchForm.organizationName" size="small" clearable placeholder="部门名称" />
          </el-form-item>
          <el-form-item>
            <el-button type="success" size="small" @click="searchOrganization">查询</el-button>
            <el-button type="primary" size="small" @click="addOrganization">添加部门</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <!-- 列表展示 -->
    <el-table v-loading="listLoading" :data="list" element-loading-text="Loading" row-key="id" default-expand-all fit highlight-current-row>
      <el-table-column label="名称" prop="organizationName" />
      <el-table-column label="排序" align="center" prop="sort" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
      ><template v-slot="{ row }">{{ row.createTime | formatDate }}</template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="400">
        <template v-slot="scope">
          <el-button size="small" type="warning" @click="editOrganization(scope.row)">修改部门</el-button>
          <el-button size="small" type="danger" @click="delOrganization(scope.row)">删除部门</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加和修改的组件 -->
    <improvement ref="improvement" :organization-dialog.sync="organizationDialog" :is-show-select="isShowSelect" @addSuccess="getOrganizationList" />
  </div>
</template>

<script>
import { getOrganizationList, delOrganization } from '@/api/organization.js'
import improvement from './components/improvement.vue'
export default {
  name: 'Organization',
  components: {
    improvement
  },
  data() {
    return {
      listLoading: false,
      list: [],
      searchForm: {
        organizationName: null
      },
      organizationDialog: false,
      isShowSelect: true
    }
  },
  created() {
    this.getOrganizationList()
  },
  methods: {
    // 获取部门列表
    getOrganizationList() {
      this.listLoading = true
      getOrganizationList().then((response) => {
        console.log(response)
        if (response.data.length <= 0) {
          this.isShowSelect = false
        } else {
          this.isShowSelect = true
        }
        this.list = response.data
        this.total = response.data.length
        this.listLoading = false
      })
    },
    // 查询部门
    searchOrganization() {
      getOrganizationList(this.searchForm).then((res) => {
        console.log(res)
        this.list = res.data
      })
    },
    // 添加部门
    addOrganization() {
      this.organizationDialog = true
    },
    // 修改部门
    async editOrganization(row) {
      await this.$refs['improvement'].showData({ ...row, value: null })
      this.organizationDialog = true
    },
    // 删除部门
    delOrganization(row) {
      this.$confirm('确定要删除该部门吗?', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async() => {
          try {
            await delOrganization(row.id)
            this.$message.success('删除成功')
            this.getOrganizationList()
          } catch (err) {
            console.log(err)
          }
        })
        .catch(() => {
          this.$message.info('已取消删除')
        })
    }
  }
}
</script>

<style></style>
