<template>
  <div class="app-container">
    <el-tabs v-model="activeName" type="card">
      <el-tab-pane label="签到统计" name="统计">
        <div class="signStatistics">
          <el-card style="width: 160px">
            <div slot="header">人员列表</div>
            <div>
              <el-tree :data="personList" :props="defaultProps" default-expand-all check-on-click-node @node-click="handleNodeClick"></el-tree>
            </div>
          </el-card>
          <el-card style="flex: 1; margin-left: 10px">
            <div slot="header">
              <span>签到日历</span>
            </div>
            <div>
              <el-row type="flex" justify="space-between">
                <div>提示: <span class="rounds" style="background: red; margin: 0 10px"></span>签到异常 <span class="rounds" style="background: green; margin: 0 10px"></span>签到正常</div>
                <div>
                  本月签到<span style="color: #409eff; font-weight: bold; margin: 0 5px">{{ signCount }}</span> 次
                  <el-date-picker v-model="month" type="month" placeholder="选择年月" size="small" style="margin-left: 10px" @change="monthChange"> </el-date-picker>
                </div>
              </el-row>
              <el-calendar v-model="signDate">
                <template v-slot:dateCell="{ date, data }">
                  <div class="round" :style="my_status(data.day, 0)"></div>
                  <el-tooltip class="item" effect="light" placement="bottom" :disabled="my_status(data.day, 1)" popper-class="atooltip">
                    <template v-slot:content>
                      <div v-for="item in my_status(data.day, 2)" :key="item.time">
                        <div style="margin-bottom: 10px">{{ item.type | signType }} ({{ item.time }})</div>
                        <div style="margin-bottom: 5px">{{ item.location }}</div>
                      </div>
                    </template>
                    <div style="text-align: center; width: 100%; height: 100%; line-height: 65px">{{ data.day.split('-').slice(2).join('-') }}</div>
                  </el-tooltip>
                </template>
              </el-calendar>
            </div>
          </el-card>
        </div>
      </el-tab-pane>
      <el-tab-pane label="签到列表" name="列表">
        <el-row :gutter="10">
          <el-form ref="form" label-width="80px" inline>
            <el-form-item label="姓名:" label-width="55px">
              <el-input v-model="queryInfo.realName" size="small" placeholder="请输入姓名" clearable></el-input>
            </el-form-item>
            <el-form-item label="签到时间:">
              <el-date-picker v-model="date" size="small" type="daterange" range-separator="→" start-placeholder="开始日期" end-placeholder="结束日期" clearable @change="datePickerChange"> </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" plain size="small" @click="reset">重置</el-button>
              <el-button type="primary" size="small" @click="getSignList">查询</el-button>
            </el-form-item>
          </el-form>
        </el-row>
        <div class="signList">
          <el-card style="margin-right: 10px">
            <div slot="header">
              <span>签到列表</span>
            </div>
            <div>
              <el-table :data="list" style="width: 100%" border @row-click="getSignDetails">
                <el-table-column align="center" prop="realName" label="姓名" width="width"> </el-table-column>
                <el-table-column align="center" prop="time" label="日期" width="width"> </el-table-column>
                <el-table-column align="center" prop="intoCount" label="进校次数" width="width"> </el-table-column>
                <el-table-column align="center" prop="outCount" label="出校次数" width="width"> </el-table-column>
                <el-table-column align="center" prop="nightCount" label="晚签次数" width="width"> </el-table-column>
              </el-table>
              <el-pagination v-if="list.length > 0" layout="total,prev, pager, next" style="margin-top: 15px; text-align: right" :page-sizes="[5, 10, 15, 20]" background :total="total" :page-size.sync="queryInfo.pageSize" :current-page.sync="queryInfo.pageNum" @size-change="getSignList" @current-change="getSignList" />
            </div>
          </el-card>
          <el-card>
            <div slot="header">
              <span>签到详情</span>
            </div>
            <div>
              <el-table :data="listDetails" style="width: 100%" border>
                <el-table-column align="center" prop="time" label="签到时间" width="width"> </el-table-column>
                <el-table-column align="center" prop="type" label="签到类型" width="width">
                  <template v-slot="{ row }">
                    <span>{{ row.type | signType }}</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="location" label="定位" width="width"> </el-table-column>
              </el-table>
            </div>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { formatDate } from '@/filters'
import { secret_signList, secret_signStatis, sysUserList } from '@/api/sign'
export default {
  name: '',
  data() {
    return {
      activeName: '统计',
      // 签到统计
      signPersonInfo: {
        organizationId: 56642510,
        pageNum: 1,
        pageSize: 2000
      },
      personList: [],
      defaultProps: {
        children: 'children',
        label: 'realName'
      },
      signStatisticsInfo: {
        userId: null,
        time: null
      },
      signDate: new Date(),
      signSignStatisticsList: [],
      signCount: 0,
      month: null,
      // 签到列表
      queryInfo: {
        realName: null,
        startTime: null,
        endTime: null,
        createUser: null,
        pageNum: 1,
        pageSize: 10
      },
      date: null,
      list: [],
      listDetails: [],
      total: 0
    }
  },
  created() {
    this.getSignList()
    this.getUserList()
  },
  mounted() {
    console.log(this.personList)
  },
  methods: {
    // 获取签到统计列表
    getUserList() {
      sysUserList(this.signPersonInfo).then((response) => {
        this.personList = [
          {
            realName: '销售员',
            children: response.data.list
          }
        ]
        if (response.data.list.length > 0) {
          this.signStatisticsInfo.userId = response.data.list[0].userId
          this.signStatisticsInfo.time = formatDate(new Date(), 'yyyy-MM')
          this.getSignStatis()
        }
      })
    },
    // 签到统计
    async getSignStatis() {
      const { data } = await secret_signStatis(this.signStatisticsInfo)
      this.signSignStatisticsList = data.list
      this.signCount = data.signCount
      console.log(data)
    },
    handleNodeClick(data) {
      if (data.userId) {
        this.signStatisticsInfo.userId = data.userId
        this.signStatisticsInfo.time = formatDate(new Date(), 'yyyy-MM')
        this.getSignStatis()
      }
    },
    my_status(date, type) {
      /** 0:校验日期签到状态  1：校验文字提示是否显示 2:返回当前日期文字提示的数据 */
      if (type === 0) {
        const result = this.signSignStatisticsList.find((item) => item.time === date)
        return result ? (result.status === 0 ? 'background:red' : 'background:green') : 'display:none'
      } else if (type === 1) {
        const result = this.signSignStatisticsList.some((item) => item.time === date)
        return !result
      } else if (type === 2) {
        const data = this.signSignStatisticsList.find((item) => item.time === date)
        return data && data.list ? data.list : []
      }
    },
    monthChange(val) {
      if (val) {
        this.signStatisticsInfo.time = formatDate(val, 'yyyy-MM')
      } else {
        this.signStatisticsInfo.time = formatDate(new Date(), 'yyyy-MM')
      }
      this.getSignStatis()
    },
    // 获取签到列表
    async getSignList() {
      const { data } = await secret_signList(this.queryInfo)
      console.log(data)
      this.list = data.list
      if (this.list.length > 0) {
        this.listDetails = this.list[0].list
      } else {
        this.listDetails = []
      }
      this.total = data.total
      console.log(data)
    },
    // 重置
    reset() {
      this.queryInfo = {
        realName: null,
        startTime: null,
        endTime: null,
        createUser: null,
        pageNum: 1,
        pageSize: 10
      }
      this.date = null
      this.getSignList()
    },
    // 获取签到详情
    getSignDetails(row) {
      this.listDetails = row.list
    },
    // 时间搜索的格式化
    datePickerChange(val) {
      if (val) {
        this.queryInfo.startTime = formatDate(val[0])
        this.queryInfo.endTime = formatDate(val[1], 'yyyy-MM-dd') + ' 23:59:59'
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getSignList()
    }
  }
}
</script>

<style scoped lang="scss">
.signStatistics {
  display: flex;
}

.signList {
  display: flex;
  .el-card {
    flex: 1;
  }
}
.rounds {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
}
.round {
  position: absolute;
  left: 50%;
  top: 15px;
  transform: translateX(-50%);
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

::v-deep {
  .el-calendar-table thead th:before {
    content: '周';
  }
  .el-calendar__header {
    display: none;
  }
  .el-calendar-day {
    position: relative;
  }

  .el-date-editor--year,
  .el-date-editor--month {
    width: 150px;
    .el-input__inner {
      width: 100%;
    }
  }
}
</style>
<style lang="scss">
.atooltip {
  padding: 10px 40px !important;
  font-size: 14px;
  color: #333;
  background: #fff !important;
  border: 1px solid #eee !important;
  box-shadow: 0 0 10px #999 !important;
}
</style>
