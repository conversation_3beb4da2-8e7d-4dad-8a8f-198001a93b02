import { login, logout, findUserRoleConfiguration } from '@/api/user'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { resetRouter } from '@/router'

const getDefaultState = () => {
  return {
    token: getToken(),
    name: '',
    avatar: '',
    realName: '',
    organizationId: null,
    keyList: []
  }
}

const state = getDefaultState()

const mutations = {
  RESET_STATE: (state) => {
    Object.assign(state, getDefaultState())
  },
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_REALNAME: (state, realName) => {
    state.realName = realName
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_KEYLIST: (state, list) => {
    state.keyList = list
  },
  SET_ORGANIZATIONID: (state, organizationId) => {
    state.organizationId = organizationId
  }
}

const actions = {
  // user login
  login({ commit }, userInfo) {
    const { username, password } = userInfo
    return new Promise((resolve, reject) => {
      login({ username: username.trim(), password: password })
        .then((response) => {
          const { data } = response
          console.log(data)
          commit('SET_TOKEN', data)
          setToken(data)
          resolve()
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  // get user info
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      findUserRoleConfiguration()
        .then((res) => {
          const { data } = res
          console.log(data)
          if (!data) {
            return reject('验证失败请重新登录')
          }
          commit('SET_KEYLIST', data.keyList)
          commit('SET_NAME', data.user.username)
          commit('SET_REALNAME', data.user.realName)
          commit('SET_ORGANIZATIONID', data.user.organizationId)
          // commit('SET_userId', data.user.userId)
          commit('SET_AVATAR', data.user.headurl)
          resolve(data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  // user logout
  logout({ commit, state }) {
    return new Promise((resolve, reject) => {
      logout(state.token)
        .then(() => {
          removeToken() // must remove  token  first
          resetRouter()
          commit('RESET_STATE')
          resolve()
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise((resolve) => {
      removeToken() // must remove  token  first
      commit('RESET_STATE')
      resolve()
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
