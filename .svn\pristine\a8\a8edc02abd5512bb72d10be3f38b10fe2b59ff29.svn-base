// 小微秘 区域管理
import request from '@/utils/request'
/** 添加 */
export function secretSave(data) {
  return request({
    url: '/secret/region/save',
    method: 'POST',
    data
  })
}
/** 区域树 */
export function secretSecretTree(params) {
  return request({
    url: '/secret/region/secretTree',
    method: 'GET',
    params
  })
}

/** 修改 */
export function secretSecretUpdate(data) {
  return request({
    url: '/secret/region/secretUpdate',
    method: 'POST',
    data
  })
}

/** 删除 */
export function secretSecretRemove(params) {
  return request({
    url: '/secret/region/secretRemove',
    method: 'DELETE',
    params
  })
}
/** 添加人员 */
export function secretSaveUser(data) {
  return request({
    url: '/secret/region/saveUser',
    method: 'POST',
    data
  })
}

/** 区域人员列表 */
export function secretUserList(params) {
  return request({
    url: '/secret/region/userList',
    method: 'GET',
    params
  })
}
