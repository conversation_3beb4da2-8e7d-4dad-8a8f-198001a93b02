<template>
  <div class="app-container">
    <div class="project_box">
      <!-- 搜索查询 -->
      <el-row>
        <el-col :span="4" class="nav_title">
          <span>项目管理</span>
          <span class="addButton" @click="showDialog = true">
            <img src="@/assets/project/add_icon.png" alt="" />
            添加项目
          </span>
        </el-col>
        <el-col :span="20">
          <el-form ref="queryInfo" :model="queryInfo" label-width="80px" inline class="searchForm">
            <el-form-item label="客户名称:" class="title">
              <el-input v-model="queryInfo.customerName" size="small" placeholder="请输入客户名称" clearable></el-input>
            </el-form-item>
            <el-form-item label="项目名称:" class="projectName">
              <el-input v-model="queryInfo.name" size="small" placeholder="请输入项目名称" clearable></el-input>
            </el-form-item>
            <el-form-item label="负责部门:" class="responsible_department">
              <el-select ref="selecteltree" v-model="queryInfo.organizationName" size="small" clearable placeholder="请选择部门" @clear="onSelectClear" @focus="getMenuTreeOfParent">
                <el-option v-for="item in menu" :key="item.id" :label="item.organizationName" :value="item.id" style="display: none" />
                <el-tree style="padding-left: 15px" :data="menu" node-key="id" empty-text="暂无菜单" highlight-current :expand-on-click-node="false" :props="defaultProps" current-node-key="id" default-expand-all @node-click="handleNodeClick" />
              </el-select>
            </el-form-item>
            <el-form-item label="发起时间:" class="date">
              <el-date-picker v-model="date" size="small" type="daterange" range-separator="→" start-placeholder="开始日期" end-placeholder="结束日期" @change="datePickerChange"> </el-date-picker>
            </el-form-item>
            <!-- <el-form-item label="负责人名称:" label-width="90px">
              <el-input v-model="queryInfo.realName" size="small" placeholder="请输入项目名称" clearable></el-input>
            </el-form-item> -->

            <!-- <el-form-item label="开始时间:">
              <el-date-picker v-model="queryInfo.startTime" type="date" size="small" placeholder="选择开始时间" clearable> </el-date-picker>
            </el-form-item>
            <el-form-item label="结束时间:">
              <el-date-picker v-model="queryInfo.endTime" type="date" size="small" placeholder="选择结束时间" clearable> </el-date-picker>
            </el-form-item> -->
            <el-form-item label="项目阶段:" class="responsible_department">
              <el-select v-model="queryInfo.stage" placeholder="请选择项目阶段" size="small" clearable>
                <el-option v-for="item in stageOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item class="button">
              <el-button type="primary" size="small" @click="getProjectList"> 查询 </el-button>
              <el-button type="primary" plain size="small" @click="reset">重置 </el-button>
              <!-- <el-button type="primary" size="small" @click="showDialog = true">添加项目 </el-button> -->
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <!-- 表格 -->
      <div class="table">
        <el-table :data="projectTable">
          <template v-if="projectTable.length > 0">
            <el-table-column align="center" prop="code" label="项目编号" width="width">
              <template v-slot="{ row }">
                <div class="code"><img src="@/assets/project/table_code_icon.png" alt="" style="margin-right: 8px" />{{ row.code }}</div>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="name" label="项目名称" width="width">
              <template v-slot="{ row }">
                <div class="name"><img src="@/assets/project/table_name_icon.png" alt="" /> {{ row.name }}</div>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="customerName" label="客户名称" width="width"> </el-table-column>
            <el-table-column align="center" prop="organizationName" label="负责部门" width="width"> </el-table-column>
            <el-table-column align="center" prop="realName" label="项目负责人" width="width"> </el-table-column>
            <el-table-column align="center" prop="stage" label="项目阶段" width="width">
              <template v-slot="{ row }">
                <span class="stage" :style="[{ color: row.stage === 1 ? '#868B9F' : row.stage === 2 || row.stage === 3 || row.stage === 4 ? '#0B1A44' : '#23BB87' }, { borderColor: row.stage === 1 ? '#D8DBE1' : row.stage === 2 || row.stage === 3 || row.stage === 4 ? '#F9C6A3' : '#e0f6ee' }, , { backgroundColor: row.stage === 1 ? '#faf9f9' : row.stage === 2 || row.stage === 3 || row.stage === 4 ? '#ffead6' : '#23BB87' }]">{{ row.stage | projectStage }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="updateTime" label="更新时间" width="width"> </el-table-column>
            <el-table-column align="center" prop="prop" label="操作" width="200">
              <template v-slot="{ row }">
                <!-- 修改 删除 详情 -->
                <div class="operate">
                  <span @click.stop="edit(row, 0)">详情</span>
                  <img src="@/assets/meeting/edit.png" alt="" @click.stop="edit(row, 1)" />
                  <img src="@/assets/meeting/del.png" alt="" @click.stop="del(row)" />
                </div>

                <!-- <el-button type="warning" size="small" @click="edit(row)">编辑</el-button>
              <el-button type="danger" size="small" @click="del(row)">删除</el-button> -->
              </template>
            </el-table-column>
          </template>
        </el-table>
        <el-empty v-if="projectTable.length <= 0">
          <template v-slot:image>
            <img src="@/assets/project/noData_bg.png" alt="" />
          </template>
          <template v-slot:description>
            <img src="@/assets/project/noData_text.png" alt="">
          </template>
        </el-empty>
        <el-pagination v-if="projectTable.length > 0" background style="text-align: center; margin-top: 30px" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[5, 10, 15, 30]" :total="total" :page-size.sync="queryInfo.pageSize" :current-page.sync="queryInfo.pageNum" @size-change="getProjectList" @current-change="getProjectList" />
      </div>
    </div>

    <!-- 添加的dialog -->
    <el-dialog :title="title" :visible.sync="showDialog" width="400px" :close-on-click-modal="false">
      <el-form ref="addFormRef" class="addFormClass" :model="addFormInfo" label-width="90px" :rules="rules">
        <el-form-item label="项目名称:" prop="projectName">
          <el-input v-model="addFormInfo.projectName" placeholder="请输入项目名称,20字以内" size="small" maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="客户名称:" prop="customerName">
          <el-input v-model="addFormInfo.customerName" size="small" placeholder="请输入客户名称,20字以内" maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="项目类型:" prop="state">
          <el-radio v-model="addFormInfo.state" :label="1">研发</el-radio>
        </el-form-item>
        <el-form-item label="项目负责人:" label-width="99px" prop="userId">
          <el-select v-model="addFormInfo.userId" placeholder="请选择项目负责人" size="small" @focus="getUserList">
            <el-option v-for="item in userList" :key="item.userId" :label="item.realName" :value="item.userId"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="项目描述:">
          <el-input v-model="addFormInfo.description" size="small" type="textarea" placeholder="请输入项目描述,500字以内" maxlength="500"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="showDialog = false">取 消</el-button>
        <el-button type="primary" @click="addProject">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { projectList, projectSaveProject, projectDelete, projectDevelopList } from '@/api/project.js'
import { getOrganizationTree } from '@/api/organization'
import { getList } from '@/api/systemUser'
import { formatDate } from '@/filters'

import { mapGetters } from 'vuex'
export default {
  name: 'Project',
  data() {
    return {
      queryInfo: {
        name: null, // 项目名称
        customerName: null, // 客户名称
        realName: null, // 负责人名称
        organizationId: null, // 负责部门
        organizationName: null,
        type: 1,
        startTime: null,
        endTime: null,
        stage: null, // 阶段
        pageNum: 1,
        pageSize: 5
      },
      date: null,
      menu: [],
      defaultProps: {
        label: 'organizationName',
        children: 'children'
      },
      stageOptions: [
        {
          label: '未立项',
          value: 1
        },
        {
          label: '调研中',
          value: 2
        },
        {
          label: '研发中',
          value: 3
        },
        {
          label: '已归档',
          value: 4
        },
        {
          label: '已交付',
          value: 5
        }
      ],
      stageOptions_after: [
        {
          label: '施工准备中',
          value: 1
        },
        {
          label: '施工中',
          value: 2
        },
        {
          label: '竣工',
          value: 3
        }
      ],
      total: null,
      projectTable: [],
      showDialog: false,
      title: '添加项目',
      addFormInfo: {
        projectName: null, // 项目名称
        customerName: null, // 客户名称
        userId: null, // 负责人id
        state: 1, // 项目状态 1 研发 2 施工
        description: null, // 描述
        relationId: null, // 关联研发项目id
        type: 1 // 类型 1 项目管理 2 售后记录
      },
      rules: {
        projectName: [
          {
            required: true,
            tigger: 'blur',
            message: '项目名称不能为空'
          }
        ],
        customerName: [
          {
            required: true,
            tigger: 'blur',
            message: '客户名称不能为空'
          }
        ],
        userId: [
          {
            required: true,
            tigger: 'change',
            message: '负责人不能为空',
            type: 'string' || 'number'
          }
        ],
        state: [
          {
            required: true,
            tigger: 'change',
            message: '项目类型不能为空',
            type: 'number'
          }
        ]
      },
      userList: [],
      type: 1,
      projectDevelopList: [],
      timeout: null
    }
  },
  computed: {
    ...mapGetters(['organizationId', 'keyList'])
  },
  mounted() {
    this.getProjectList()
  },
  methods: {
    async getProjectList() {
      const { data } = await projectList(this.queryInfo)
      this.projectTable = data.list

      this.total = data.total
    },
    datePickerChange(val) {
      this.queryInfo.startTime = formatDate(val[0])
      this.queryInfo.endTime = formatDate(val[1])
      this.getProjectList()
    },
    reset() {
      this.queryInfo = {
        name: null, // 项目名称
        customerName: null, // 客户名称
        realName: null, // 负责人名称
        organizationId: null, // 负责部门
        organizationName: null,
        type: this.type,
        startTime: null,
        endTime: null,
        stage: null, // 阶段
        pageNum: 1,
        pageSize: 5
      }
      this.getProjectList()
    },
    addProject() {
      this.$refs['addFormRef'].validate(async (val) => {
        if (val) {
          await projectSaveProject(this.addFormInfo)
          if (this.addFormInfo.type === 2) {
            this.$message.success('添加售后记录成功')
          } else {
            this.$message.success('添加项目成功')
          }
          this.showDialog = false
          this.getProjectList()
        }
      })
    },
    edit(row, type) {
      this.$router.push(`/project/details/${row.id}/${type}`)
    },
    del(row) {
      this.$confirm('确定要删除吗, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await projectDelete({ id: row.id, type: this.type })
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getProjectList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 获取当前部门下的用户
    async getUserList() {
      const { data } = await getList({ pageNum: 1, pageSize: 200, organizationId: this.organizationId })
      this.userList = data.list
    },
    onSelectClear() {
      this.queryInfo.organizationId = null
    },
    getMenuTreeOfParent() {
      getOrganizationTree().then((res) => {
        this.menu = res.data
      })
    },
    handleNodeClick(node) {
      this.queryInfo.organizationName = node.organizationName
      this.queryInfo.organizationId = node.id
      this.$refs['selecteltree'].blur()
    },

    querySearchAsync(queryString, cb) {
      if (queryString !== '') {
        clearTimeout(this.timeout)

        this.timeout = setTimeout(async () => {
          const { data } = await projectDevelopList({ name: this.addFormInfo.projectName })
          this.projectDevelopList = data
          const cbList = []
          this.projectDevelopList.forEach((item) => {
            cbList.push({
              value: item.name,
              id: item.id
            })
          })
          console.log(cbList)
          cb(cbList)
        }, 1000 * Math.random())
      }
    },
    handleSelect(item) {
      this.addFormInfo.relationId = item.id
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  background: #e8eaed;
  min-height: 100%;
  box-shadow: none;
}
.project_box {
  border-radius: 8px;
  background: url('../../assets/project/project_bg.png') no-repeat;
  background-size: 100% 100%;
  padding-top: 56px;
  padding-left: 37px;
  padding-right: 37px;
  padding-bottom: 45px;
  min-height: 800px;
  .nav_title {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-left: 19px !important;
    span {
      &:first-of-type {
        font-size: 18px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        font-weight: bold;
        color: #0b1a44;
      }
    }
    .addButton {
      display: block;
      margin-top: 23px;
      width: 107px;
      height: 41px;
      line-height: 41px;
      background: #3464e0;
      border-radius: 4px 4px 4px 4px;
      text-align: center;
      font-size: 16px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #ffffff;
      cursor: pointer;
      &:hover{
        background: #355fce;
      }
    }
  }

  .table {
    position: relative;
    .code {
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #0b1a44;
    }
    .name {
      display: flex;
      align-items: flex-start;
      justify-content: center;
      font-size: 14px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #0b1a44;
      margin-right: 5px;
    }
    .stage {
      display: block;
      margin: 0 auto;
      width: 54px;
      height: 22px;
      line-height: 22px;
      border: 1px solid;
    }
    .operate {
      display: flex;
      align-items: center;
      justify-content: center;
      span {
        margin-right: 10px;
        width: 72px;
        height: 28px;
        line-height: 28px;
        background: #f2f4ff;
        opacity: 1;
        font-size: 14px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        color: #3464e0;
        border-radius: 4px;
        cursor: pointer;
        &:hover {
          font-weight: bold;
        }
      }
      img {
        cursor: pointer;
      }
    }
    ::v-deep {
      .el-table {
        background: url('../../assets/project/table_bg.png') no-repeat;
        background-size: cover;
        border-radius: 6px;
        min-height: 580px;
        &::before {
          display: none;
        }
      }
      .el-table__empty-block {
        display: none;
      }
      // 去掉表格身体背景样式
      .el-table tr {
        background: transparent;
        padding-left: 40px;
        padding-right: 40px;
      }
      // 去掉表头背景样式
      .el-table th.el-table__cell {
        background: transparent;
        padding: 24px 0;
        padding-bottom: 4px;
      }
      // 更改表头单元格样式
      .el-table th.el-table__cell > .cell {
        font-size: 14px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        font-weight: bold;
        color: #0b1a44;
      }
      // 去掉单元格边框
      .el-table td.el-table__cell,
      .el-table th.el-table__cell.is-leaf {
        border: none;
      }
      // 每行鼠标经过得样式
      .el-table__body tr:hover > td {
        background-color: transparent;
      }
      .el-table__body tr.current-row > td {
        background-color: transparent;
      }
      .el-table__header {
        width: initial !important;
        padding-left: 40px;
        padding-right: 40px;
      }
      .el-table__body {
        width: initial !important;
        padding-left: 40px;
        padding-right: 40px;
        -webkit-border-vertical-spacing: 20px; // 垂直间距
      }
      .el-table .el-table__row {
        background: #fff;
        border-radius: 6px;
      }
      .el-table td.el-table__cell div {
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #0b1a44;
      }

      .el-empty {
        position: absolute;
        top: 49%;
        left: 50%;
        transform: translate(-50%, -50%);

        display: flex;
        flex-direction: column;
        align-items: center;
        .el-empty__image{
             width: 312px;
            height: 184px;
          img{
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
  .searchForm {
    .title {
      ::v-deep {
        .el-form-item__content {
          .el-input {
            width: 284px;
            height: 36px;
            width: 284px;
            .el-input__inner {
              height: 36px;
              border: 1px solid #eeeeef;
            }
          }
        }
      }
    }
    .projectName {
      ::v-deep {
        .el-form-item__content {
          .el-input {
            width: 264px;
            height: 36px;
            .el-input__inner {
              height: 36px;
              background: #ffffff;
              border: 1px solid #eeeeef;
            }
          }
        }
      }
    }
    .responsible_department {
      ::v-deep {
        .el-form-item__content {
          .el-input {
            width: 168px;
            height: 36px;
            .el-input__inner {
              height: 36px;

              background: #ffffff;
              border: 1px solid #eeeeef;
            }
          }
        }
      }
    }
    .date {
      ::v-deep {
        .el-form-item__content {
          .el-date-editor--daterange.el-input,
          .el-date-editor--daterange.el-input__inner,
          .el-date-editor--timerange.el-input,
          .el-date-editor--timerange.el-input__inner {
            width: 274px;
            height: 36px;
            background: #ffffff;
            border-radius: 4px 4px 4px 4px;
          }
          .el-range-editor--small .el-range__close-icon,
          .el-range-editor--small .el-range__icon {
            line-height: 31px;
          }
          // .el-range-input {
          //   background-color: #fff;
          // }
        }
      }
    }
    .button {
      float: right;
      margin-right: 45px;
      ::v-deep {
        .el-button--primary.is-plain {
          border-color: #3464e0;
          background: transparent;
          color: #3464e0;
          &:active {
            background: transparent !important;
          }
        }
        .el-button--primary.is-plain:focus,
        .el-button--primary.is-plain:hover {
          color: #355fce !important;
          border-color: #355fce !important;
          background-color: transparent;
        }
        .el-button--primary:focus,
        .el-button--primary:hover {
          background-color: #355fce;
        }
      }
    }
    .processName {
      // display: flex;
      ::v-deep {
        .el-radio-button {
          margin-bottom: 10px;
        }
        .el-radio-button .el-radio-button__inner {
          background: transparent;
        }
        .el-radio-button__inner {
          padding: 8px 14px;
          border-color: transparent;
          border-radius: 4px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
        }
        .el-radio-button__orig-radio:checked + .el-radio-button__inner {
          border-color: #3464e0;
          color: #3464e0;
        }
      }
    }
    .status {
      ::v-deep {
        .el-radio-button {
          margin-bottom: 0;
        }
      }
    }
    ::v-deep {
      .el-input__inner {
        border: 1px solid #eeeeef;
        &::placeholder {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #b1bac7;
        }
      }
    }
  }
  ::v-deep {
    .el-form-item__label {
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #0b1a44;
    }
    .el-empty__image {
      width: 298px;
      height: 188px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .el-empty__description {
      img {
        width: 74px;
        height: 21px;
      }
    }
  }
}

.addFormClass {
  ::v-deep {
    .el-input,
    .el-textarea {
      width: 250px;
    }
  }
}
</style>
