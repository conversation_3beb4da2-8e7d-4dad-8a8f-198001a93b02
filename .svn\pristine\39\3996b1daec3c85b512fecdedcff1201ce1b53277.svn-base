<template>
  <div class="app-container">
    <div class="emulation_content">
      <div class="header">
        <span class="title">虚拟仿真</span>
        <span class="addButton" @click="add"></span>
        <el-form class="searchForm" :model="queryInfo" label-width="80px" inline>
          <el-form-item label="实验名称:">
            <el-input v-model="queryInfo.name" placeholder="请输入实验名称" maxlength="40"></el-input>
          </el-form-item>
          <el-form-item label="专 业:">
            <el-select v-model="queryInfo.majorId" placeholder="请选择专业" @focus="getAllMajor">
              <el-option v-for="item in majorList" :key="item.majorId" :label="item.majorName" :value="item.majorId"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item style="margin-left: 20px">
            <el-button type="primary" size="small" @click="getList('search')">查询</el-button>
            <el-button type="primary" plain size="small" @click="reset">重置</el-button>
          </el-form-item></el-form>
      </div>
      <div v-loading.fullscreen.lock="loading" class="body" element-loading-text="数据加载中" element-loading-background="rgba(0, 0, 0, 0.9)">
        <ul class="emulation_list">
          <li v-for="item in list" :key="item.emulationId">
            <div class="emulationType" :style="{ background: item.mode === 1 ? '#577ee5' : '#f38f49' }">{{ item.mode === 1 ? '考核模式' : '学习模式' }}</div>
            <div class="previewImgBox" @click="previewEmulation(item)">
              <img class="previewImg" :src="item.coverUrl" alt="" />
            </div>
            <div class="textInfo">
              <div class="textTitle">{{ item.name }}</div>
              <div class="text">
                <span>版本: {{ item.version }}</span>
                <span>创建时间:{{ item.createTime }}</span>
                <div>
                  <el-tooltip popper-class="emulationTooltip" effect="dark" content="考试记录" placement="top">
                    <svg-icon icon-class="record" @click.stop="examRecord(item)"></svg-icon>
                  </el-tooltip>
                  <!-- <el-tooltip popper-class="emulationTooltip" effect="dark" content="赋分模型" placement="top">
                    <svg-icon icon-class="model" @click.stop="openModel(item)"></svg-icon>
                  </el-tooltip> -->
                  <el-popover placement="right" popper-class="editAnddel" width="150px" trigger="hover">
                    <div @click.stop="feedbackRecord(item)">
                      <i class="el-icon-time"></i>
                      <!-- <svg-icon icon-class="record"></svg-icon> -->
                      <span>反馈记录</span>
                    </div>
                    <div @click.stop="edit(item)">
                      <svg-icon icon-class="edit"></svg-icon>
                      <span>编辑</span>
                    </div>
                    <div @click.stop="del(item)">
                      <i class="el-icon-delete-solid"></i>
                      <span>删除</span>
                    </div>
                    <svg-icon slot="reference" icon-class="three_point" @click.stop></svg-icon>
                  </el-popover>
                </div>
              </div>
            </div>
          </li>
        </ul>
        <el-pagination
          v-if="list.length > 0"
          style="margin-top: 32px; text-align: right"
          layout="total,  prev, pager, next"
          background
          :total="total"
          :page-size.sync="queryInfo.pageSize"
          :current-page.sync="queryInfo.pageNum"
          @size-change="getList"
          @current-change="getList"
        />
      </div>
    </div>
    <!-- 删除弹窗 -->
    <el-dialog :visible.sync="delDialog" custom-class="delDialog" width="413px">
      <template v-slot:title>
        <img src="@/assets/library/hint.png" alt="" class="hint" />
        <span class="hintText">提示</span>
      </template>
      <div class="delText">确定删除该实验吗？</div>
      <div class="operate">
        <span class="closeButton" @click="delDialog = false">取 消</span>
        <span class="confirmButton" @click="confirmDel">确 定</span>
      </div>
    </el-dialog>
    <!-- 反馈记录 -->
    <el-dialog title="反馈记录" :visible.sync="feedbackDialog" width="1000px">
      <el-table :data="feedbackList" style="width: 100%" border>
        <el-table-column align="center" prop="createTime" label="创建时间" width="200"> </el-table-column>
        <el-table-column align="center" prop="realName" label="创建人" width="200"> </el-table-column>
        <el-table-column align="center" prop="remark" label="反馈说明" width="width" show-overflow-tooltip> </el-table-column>
      </el-table>
      <div slot="footer">
        <el-button type="primary" @click="feedbackDialog = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { emulationList, emulationRemove, emulationFeedbackList, allMajor } from '@/api/emulation'
import { resourceLogin } from '@/api/Library'
import { mapGetters } from 'vuex'

export default {
  name: '',
  data() {
    return {
      queryInfo: {
        name: null,
        majorId: null,
        pageNum: 1,
        pageSize: 8
      },
      list: [],
      total: 0,
      baseUrl: window.config.VUE_APP_DOWNLOAD_URL,
      majorList: [],
      delDialog: false,
      emulationId: null,
      loading: false,
      timer: null,
      feedbackList: [],
      feedbackDialog: false
    }
  },
  computed: {
    ...mapGetters(['name'])
  },
  created() {
    resourceLogin({ username: this.name }).then((res) => {
      if (res.data) {
        this.loading = true
        window.localStorage.setItem('zfkj_admin_library_token', res.data)
        this.$nextTick(() => {
          if (window.localStorage.getItem('zfkj_admin_library_token')) {
            this.getList()
          } else {
            this.timer = setInterval(() => {
              if (window.localStorage.getItem('zfkj_admin_library_token')) {
                clearInterval(this.timer)
                this.timer = null
                this.getList()
              }
            }, 500)
          }
        })
      }
    })
  },

  methods: {
    async getList(isSearch) {
      this.loading = false

      if (isSearch === 'search') {
        this.queryInfo.pageNum = 1
      }
      const { data } = await emulationList(this.queryInfo)
      this.list = data.list
      this.total = data.total
    },
    reset() {
      this.queryInfo = {
        name: null,
        majorId: null,
        pageNum: 1,
        pageSize: 10
      }
      this.getList()
    },
    async getAllMajor() {
      const { data } = await allMajor()
      this.majorList = data
    },
    add() {
      this.$router.push('/emulation/add/0')
    },
    edit(item) {
      this.$router.push(`/emulation/edit/${item.emulationId}`)
    },
    del(item) {
      this.delDialog = true
      this.emulationId = item.emulationId
    },
    async confirmDel() {
      await emulationRemove({ id: this.emulationId })
      this.$message.success('删除成功')
      this.delDialog = false
      this.getList()
    },
    previewEmulation(item) {
      const link = this.$router.resolve(`/previewEmulation/${item.emulationId}`)
      window.open(link.href, '_blank', 'height=1080, width=1920, fullscreen="yes"')
    },
    openModel(item) {
      this.$router.push(`/emulation/model/${item.emulationId}`)
    },
    examRecord(item) {
      this.$router.push(`/emulation/examRecord/${item.emulationId}`)
    },
    async feedbackRecord(item) {
      const { data } = await emulationFeedbackList({ emulationId: item.emulationId })
      this.feedbackList = data
      this.feedbackDialog = true
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  height: 100%;
  width: 100%;
  background: #e8eaed;
  padding-top: 24px;
  padding-bottom: 32px;
  .emulation_content {
    width: 100%;
    height: 100%;
    border-radius: 4px;
    overflow: hidden;
    .header {
      display: flex;
      align-items: center;
      width: 100%;
      height: 97px;
      background: #ffffff;
      padding-left: 61px;
      .title {
        font-size: 18px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        font-weight: bold;
        color: #0b1a44;
      }
      .addButton {
        margin-left: 20px;
        width: 116px;
        height: 42px;
        background: url('~@/assets/emulation/add.png') no-repeat;
        background-size: cover;
        cursor: pointer;
        &:hover {
          background: url('~@/assets/emulation/add_hover.png') no-repeat;
          background-size: cover;
        }
      }
      ::v-deep {
        .searchForm {
          margin-left: 32px;
          .el-form-item {
            margin-bottom: 0;
            .el-form-item__label {
              font-size: 14px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #0b1a44;
            }
            .el-input {
              .el-input__inner {
                width: 284px;
                height: 36px;
                background: #ffffff;
                border: 1px solid #d8dbe1;
              }
            }
          }
        }
      }
    }
    .body {
      position: relative;
      display: flex;
      justify-content: center;
      height: calc(100% - 97px);
      padding-top: 43px;
      width: 100%;
      background: #f5f5f5;
      .emulation_list {
        display: flex;
        align-content: flex-start;
        flex-wrap: wrap;
        width: 1576px;
        max-width: 1576px;
        li {
          position: relative;
          margin-right: 40px;
          margin-bottom: 32px;
          width: 364px;
          height: 260px;
          background: #ffffff;
          box-shadow: 0px 6px 12px 1px rgba(185, 203, 220, 0.24);
          border-radius: 12px 12px 12px 12px;
          overflow: hidden;
          cursor: pointer;
          &:hover {
            .previewImg {
              transform: scale(1.2);
            }
          }
          &:nth-of-type(4n) {
            margin-right: 0;
          }
          .emulationType {
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            top: 0;
            left: 0;
            width: 91px;
            height: 27px;
            border-radius: 12px 0;
            z-index: 10;
            font-size: 13px;
            color: #fff;
          }
          .previewImgBox {
            width: 100%;
            height: 167px;
            text-align: center;
            overflow: hidden;
            .previewImg {
              height: 100%;
              width: 100%;
              // object-fit: cover;
              transition: all 0.2s;
            }
          }
          .textInfo {
            padding-left: 16px;
            padding-top: 16px;
            .textTitle {
              font-size: 16px;
              font-family: Microsoft YaHei-Bold, Microsoft YaHei;
              font-weight: bold;
              color: #0b1a44;
            }
            .text {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding-top: 16px;
              font-size: 13px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #b1bac7;
              & > div {
                .svg-icon,
                i {
                  margin-right: 18px;
                  font-size: 20px;
                  color: #d8dce2;
                  &:hover {
                    color: #b1bac7;
                  }
                }
              }
            }
          }
        }
      }
      .el-pagination{
        position: absolute;
        bottom: 30px;
        left: 50%;
        transform: translateX(-50%);
      }
    }
  }
  // 删除弹窗的样式
  ::v-deep {
    .delDialog {
      height: 206px;
      border-radius: 8px !important;
      .el-dialog__header {
        display: flex;
        align-items: center;
        padding-top: 12px;
        padding-left: 24px;
        padding-right: 20px;
        padding-bottom: 8px;
        border-bottom: 1px solid #eeeeef;
        .hint {
          width: 30px;
          height: 30px;
        }
        .hintText {
          margin-left: 6px;
          font-size: 16px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #303a40;
        }
        .el-dialog__headerbtn {
          right: 20px;
          top: 20px;
          .el-dialog__close {
            font-weight: bold;
          }
        }
      }
      .delText {
        margin-top: 10px;
        margin-bottom: 30px;
        text-align: center;
        font-size: 16px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #303a40;
      }
      .operate {
        display: flex;
        justify-content: center;
        .closeButton {
          width: 114px;
          height: 38px;
          line-height: 36px;
          background: #ffffff;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #d8dbe1;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          color: #a3a8bb;
          text-align: center;
          cursor: pointer;
          &:hover {
            background: #f5f5f5;
          }
        }
        .confirmButton {
          margin-left: 32px;
          width: 114px;
          height: 38px;
          line-height: 36px;
          background: #3464e0;
          border-radius: 4px 4px 4px 4px;
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #ffffff;
          text-align: center;
          cursor: pointer;
          &:hover {
            background: #355fce;
          }
        }
      }
    }
  }
}
</style>
<style lang="scss">
.emulationTooltip {
  background: #0b1a44 !important;
}
.editAnddel {
  padding: 0;
  & > div {
    display: flex;
    align-items: center;
    padding: 10px 0;
    padding-left: 30px;
    cursor: pointer;
    &:hover {
      background: #eaebef;
    }
    .svg-icon,
    i {
      margin-right: 10px;
      font-size: 20px;
      color: #b1bac7;
      cursor: pointer;
    }
    & > span {
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #b1bac7;
    }
  }
  .popper__arrow {
    display: none;
  }
}
</style>
