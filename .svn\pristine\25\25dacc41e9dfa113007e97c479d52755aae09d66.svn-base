<template>
  <div class="app-container">
    <el-card class="area" style="box-shadow: none">
      <div slot="header">
        <span style="cursor: pointer" @click="showMap = false">区域管理</span>
        <span style="float: right; cursor: pointer" @click="lookMap">地图分布</span>
      </div>
      <el-tree :data="secretTree" :props="defaultProps" :expand-on-click-node="false" default-expand-all highlight-current @node-click="handleNodeClick">
        <span slot-scope="{ node, data }" class="custom-tree-node">
          <span v-if="!node.data.flag"><i :class="data.parentId === 1 || data.parentId === 0 ? 'el-icon-folder-opened' : 'el-icon-document'"></i> {{ node.label }}</span>
          <el-input v-else ref="addInput" v-model="node.data.name" placeholder="请输入片区名称" size="mini" @keyup.enter.native="addArea(data)" @blur="addArea_blur(node, data)"></el-input>
          <span>
            <el-button v-if="data.parentId === 1 || data.parentId === 0" type="text" size="mini" @click="() => append(data)"> <i class="el-icon-circle-plus-outline"></i> </el-button>
            <el-button v-if="data.parentId !== 0" type="text" size="mini" @click="() => edit(data)"><i class="el-icon-edit"></i> </el-button>
            <el-button v-if="data.parentId !== 0" type="text" size="mini" @click="() => remove(node, data)"> <i class="el-icon-remove-outline"></i> </el-button>
          </span>
        </span>
      </el-tree>
    </el-card>
    <el-card v-if="!showMap" class="person" style="margin-left: 15px; box-shadow: none">
      <div slot="header"></div>
      <el-transfer v-model="checkedData" filterable filter-placeholder="请输入姓名" :data="gridData" :titles="['所有人员', '选中人员']" :props="{ key: 'userId', label: 'realName' }" @change="transferChange"> </el-transfer>
    </el-card>
    <mapEcharts v-show="showMap" ref="mapEcharts" style="flex: 1" />
  </div>
</template>

<script>
import { secretSecretTree, secretSave, secretSecretUpdate, secretSecretRemove, secretUserList, secretSaveUser } from '@/api/area'
import { getList } from '@/api/systemUser'
import map from './map'
export default {
  name: 'Area',
  components: {
    mapEcharts: map
  },
  data() {
    return {
      secretTree: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      checkedData: [],
      gridData: [],
      queryInfo: {
        organizationId: 56642510,
        pageNum: 1,
        pageSize: 200
      },
      regionId: null,
      isAdd: true, // 是否执行添加事件
      showMap: false,
      parentData: {}
    }
  },
  created() {
    this.getSecretTree()
  },
  methods: {
    async getSecretTree() {
      const { data } = await secretSecretTree()
      this.secretTree = this.ergodicData(data)
      this.regionId = this.secretTree[0].regionId
      this.getUserList()
    },
    getUserList() {
      getList(this.queryInfo).then((response) => {
        this.gridData = response.data.list
        if (parseInt(this.regionId) === 1) {
          this.gridData.forEach((item) => {
            this.$set(item, 'disabled', true)
          })
        }
      })
    },
    ergodicData(data) {
      const arr = []
      data.forEach((item) => {
        if (item.children && item.children.length) {
          this.ergodicData(item.children)
        }
        item.flag = false
        arr.push(item)
      })
      return arr
    },
    append(data) {
      this.parentData = data
      //   const newChild = { id: id++, label: 'testtest', children: [] }
      if (!data.children) {
        this.$set(data, 'children', [])
      }
      data.children.push({ name: null, children: [], parentId: data.id, flag: true })
      this.$nextTick(() => {
        this.$refs['addInput'].focus()
      })
    },
    async addArea(data) {
      this.isAdd = false
      if (data.id) {
        await secretSecretUpdate(data)
        this.$message.success('修改片区成功')
      } else {
        await secretSave(data)
        this.$message.success('添加片区成功')
      }
      this.$refs['addInput'].blur()
      this.getSecretTree()
      this.isAdd = true

      //   this.$nextTick(() => {
      //     this.$refs['addInput'].blur()
      //   })
    },
    addArea_blur(node, data) {
      if (data.name) {
        if (this.isAdd) {
          this.addArea(data)
        } else {
          return
        }
      } else {
        this.getSecretTree()
        // this.parentData
        // this.isAdd = false
        // data.flag = false
      }
    },
    remove(node, data) {
      this.$confirm('确定要删除吗, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await secretSecretRemove({ regionId: data.id })
          this.$message.success('删除片区成功')
          this.getSecretTree()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
          this.getSecretTree()
        })
    },
    edit(data) {
      data.flag = true
      this.$nextTick(() => {
        this.$refs['addInput'].focus()
      })
    },

    async handleNodeClick(node) {
      const { data } = await secretUserList({ regionId: node.regionId, pageNum: 1, pageSize: 200 })
      this.regionId = node.regionId
      this.checkedData = data.list.map((item) => item.userId)
      this.getUserList()
    },
    async transferChange(val) {
      await secretSaveUser({ regionId: this.regionId, userIds: val })
    },
    lookMap() {
      this.showMap = true
      this.$nextTick(() => {
        this.$refs['mapEcharts'].init()
      })
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  display: flex;
  width: 100%;
}
.area {
  width: 350px;
  border: 1px solid #eee;
  .area_header {
    border-bottom: 1px solid #eee;
    padding: 10px 20px;
    font-size: 18px;
    font-weight: bold;
  }
}
.person {
  flex: 1;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  i {
    font-size: 18px;
  }
}
::v-deep {
  .el-tree {
    height: 750px;
    overflow: auto;
  }
  .el-tree-node__content {
    height: 28px !important;
  }
  .el-transfer {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .el-transfer-panel {
    display: flex;
    flex-direction: column;
    width: 535px;
    height: 545px;
    .el-transfer-panel__body {
      height: initial !important;
      flex: 1;
      .el-transfer-panel__list.is-filterable {
        height: inherit !important;
      }
    }
  }
}
</style>
