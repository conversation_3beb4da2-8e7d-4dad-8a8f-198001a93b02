<template>
  <div style="width: 100%; height: 100%">
    <div id="trainStatisEchart" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script>
import resize from '../mixins/resize'
export default {
  name: '',
  mixins: [resize],
  data() {
    return {
      chart: null
    }
  },
  methods: {
    init(data) {
      this.chart = this.$echarts.init(document.getElementById('trainStatisEchart'))
      this.setOpiton(data)
    },
    setOpiton(data) {
      const that = this
      this.chart.setOption({
        color: ['#e8eaed', '#6b71f9', '#3464e1', '#ffdc60', '#91cc75', '#3894f9'],
        title: {
          text: '项目情况统计',
          textStyle: {
            color: '#0B1A44',
            fontSize: 14
          }
        },
        legend: {
          x: 'right' // 可设定图例在左、右、居中
        },
        tooltip: {
          trigger: 'item',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: 'rgba(0,0,0,0.7)',
          borderColor: 'gray',
          textStyle: {
            fontSize: 12,
            color: '#fff'
          },
          padding: [12, 16, 16, 16]
        },
        grid: {
          //   top: '0',
          left: '5%',
          right: '0',
          bottom: '0',
          containLabel: true
        },
        series: [
          {
            name: '部门内训',
            data: data,
            type: 'pie',
            radius: '70%'
          }
        ]
      })
      this.chart.on('click', function (params) {
        that.$emit('jumpPage')
      })
    }
  }
}
</script>

<style scoped lang="scss"></style>
