<template>
  <div class="app-container">
    <!-- <el-tabs v-model="selectedName" type="border-card">
      <el-tab-pane label="我审批的">用户管理</el-tab-pane>
      <el-tab-pane label="我发起的">配置管理</el-tab-pane>
      <el-tab-pane label="抄送我的">角色管理</el-tab-pane>
    </el-tabs> -->
    <el-card>
      <div slot="header">
        <el-radio-group v-model="searchInfo.type" @change="getInstanceList">
          <el-radio-button :label="1">我发起的</el-radio-button>
          <el-radio-button :label="3">我审批的</el-radio-button>
          <el-radio-button :label="2">抄送我的</el-radio-button>
        </el-radio-group>
      </div>
      <div>
        <el-row>
          <el-col :span="24">
            <el-form ref="form" label-width="80px" inline class="searchForm">
              <el-form-item label="事项名称:" class="title">
                <el-input v-model="searchInfo.title" size="small" placeholder="请输入事项名称" maxlength="50" clearable></el-input>
              </el-form-item>
              <el-form-item label="发起人:" class="realName">
                <el-input v-model="searchInfo.realName" size="small" placeholder="请输入发起人名称" maxlength="50" clearable></el-input>
              </el-form-item>
              <el-form-item label="发起时间:" class="date">
                <el-date-picker v-model="date" size="small" type="daterange" range-separator="→" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd" @change="datePickerChange"> </el-date-picker>
              </el-form-item>

              <!-- <el-form-item label="结束时间:">
                <el-date-picker v-model="searchInfo.endTime" type="date" size="small" placeholder="选择结束时间"> </el-date-picker>
              </el-form-item>
              <el-form-item label="审核状态:">
                <el-select v-model="searchInfo.status" placeholder="请选择审核状态" size="small" clearable>
                  <el-option label="新创建" value="NEW"> </el-option>
                  <el-option label="审批中" value="RUNNING"> </el-option>
                  <el-option label="被终止" value="TERMINATED"> </el-option>
                  <el-option label="完成" value="COMPLETED"> </el-option>
                  <el-option label="取消" value="CANCELED"> </el-option>
                </el-select>
              </el-form-item> -->
              <el-form-item class="button">
                <el-button type="primary" size="small" @click="getInstanceList"> 查询 </el-button>
                <el-button type="primary" plain size="small" @click="reset">重置 </el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form ref="form" label-width="80px" class="searchForm" label-position="right">
              <el-form-item label="事项类型:" class="processName">
                <el-radio-group v-model="searchInfo.processName" @change="getInstanceList">
                  <el-radio-button :label="null">全部</el-radio-button>
                  <el-radio-button v-for="item in typeList" :key="item.processCode" :label="item.processName">{{ item.processName }}</el-radio-button>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form ref="form" label-width="80px" class="searchForm" label-position="right">
              <el-form-item label="审核状态:" class="processName status">
                <el-radio-group v-model="searchInfo.status" @change="getInstanceList">
                  <el-radio-button :label="null">全部</el-radio-button>
                  <el-radio-button label="NEW">新创建</el-radio-button>
                  <el-radio-button label="RUNNING">审批中</el-radio-button>
                  <el-radio-button label="TERMINATED">被终止</el-radio-button>
                  <el-radio-button label="COMPLETED">完成</el-radio-button>
                  <el-radio-button label="CANCELED">取消</el-radio-button>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <!-- <el-table :data="list" style="width: 100%" border>
          <el-table-column prop="title" label="事项名称" width="width" align="center"> </el-table-column>
          <el-table-column prop="processName" label="事项类型" width="width" align="center"> </el-table-column>
          <el-table-column label="审核状态" width="width" align="center">
            <template v-slot="{ row }">
              <span>{{ row.status | processStatus }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="发起时间" width="width" align="center"> </el-table-column>
          <el-table-column prop="realName" label="发起人" width="width" align="center"> </el-table-column>
          <el-table-column label="操作" width="width" align="center">
            <template v-slot="{ row }">
              <el-button type="primary" size="small" @click="seeDetails(row)">查看详情</el-button>
            </template>
          </el-table-column>
        </el-table> -->
        <div class="table_list">
          <div v-for="item in list" :key="item.processInstanceId" class="list" @click="seeDetails(item)">
            <div class="content">
              <img src="@/assets/process/list_icon.png" alt="" />

              <el-tooltip class="item" effect="dark" :content="item.title" placement="top">
                <div class="list_title universalClass">{{ item.title }}</div>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" :content="item.processName" placement="top">
                <div class="list_processName universalClass">事项类型：{{ item.processName }}</div>
              </el-tooltip>
              <div class="list_createTime universalClass">发起时间：{{ item.createTime }}</div>
              <div class="list_realName universalClass">
                <span>发起人：{{ item.realName }}</span>
                <span v-if="item.status === 'RUNNING' && item.isReview !== '0'" class="list_button examine" @click.stop="seeDetails(item)">审批</span>
                <!-- <span v-else class="list_button">查看</span> -->
              </div>
              <!-- 结果状态 -->
              <template>
                <img v-if="item.status === 'RUNNING'" src="@/assets/process/examine1.png" alt="" class="result" />
                <img v-if="item.status === 'TERMINATED'" src="@/assets/process/examine3.png" alt="" class="result" />
                <img v-if="item.result === 'agree' && item.status === 'COMPLETED'" src="@/assets/process/examine2.png" alt="" class="result" />
                <img v-if="item.result === 'refuse'" src="@/assets/process/examine0.png" alt="" class="result" />
              </template>
            </div>
          </div>
        </div>

        <el-pagination v-if="list.length > 0" style="text-align: right" layout="total, prev, pager, next" background :page-sizes="[10, 15, 20, 30]" :total="total" :page-size.sync="searchInfo.pageSize" :current-page.sync="searchInfo.pageNum" @size-change="getInstanceList" @current-change="getInstanceList" />
        <el-empty v-if="list.length <= 0" :image="noneImg">
          <template v-slot:description>
            <img src="@/assets/process/noneProcess.png" alt="" />
          </template>
        </el-empty>
      </div>
    </el-card>
  </div>
</template>

<script>
import { instanceList, getAllProcess } from '@/api/process'
import noneImg from '@/assets/process/noneData.png'
export default {
  name: 'Process',
  data() {
    return {
      list: [], // 审批列表
      typeList: [], // 事项类型
      searchInfo: {
        title: null,
        processName: null,
        realName: null,
        startTime: null,
        endTime: null,
        status: null,
        type: 1,
        pageSize: 10,
        pageNum: 1
      },
      total: 0,
      date: null,
      noneImg
    }
  },
  created() {
    this.getInstanceList()
    this.getAllProcessType()
  },
  mounted() {
    const moduleTime = window.localStorage.getItem('moduleTime')
    if (moduleTime) {
      this.date = [JSON.parse(moduleTime).startTime, JSON.parse(moduleTime).endTime]
      this.searchInfo.startTime = JSON.parse(moduleTime).startTime
      this.searchInfo.endTime = JSON.parse(moduleTime).endTime
      window.localStorage.removeItem('moduleTime')
    }
  },
  beforeRouteLeave(to, from, next) {
    if (to.name === 'ProcessDetails') {
      from.meta.keepAlive = true
    } else {
      from.meta.keepAlive = false
    }
    next()
  },
  methods: {
    // 获取审批列表
    async getInstanceList() {
      const { data } = await instanceList(this.searchInfo)
      this.list = data.list
      this.total = data.total
      console.log(data)
    },

    // 获取所有事项类型
    async getAllProcessType() {
      const { data } = await getAllProcess()
      this.typeList = data
    },
    // 查看详情
    seeDetails(row) {
      this.$router.push(`/process/management/details/${row.processInstanceId}`)
    },
    datePickerChange(val) {
      if (val) {
        this.searchInfo.startTime = val[0]
        this.searchInfo.endTime = val[1] + ' 23:59:59'
      } else {
        this.searchInfo.startTime = null
        this.searchInfo.endTime = null
      }

      this.getInstanceList()
    },
    // 重置搜索表单
    reset() {
      this.searchInfo = {
        title: null,
        processName: null,
        realName: null,
        startTime: null,
        endTime: null,
        status: null,
        type: 3,
        pageSize: 10,
        pageNum: 1
      }
      this.date = null
      this.getInstanceList()
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  background: #e8eaed;
  min-height: 100%;
  padding-bottom: 18px;
  .el-card {
    box-shadow: none;
    border-radius: 8px;
    .searchForm {
      .title {
        ::v-deep {
          .el-form-item__content {
            .el-input {
              width: 284px;
              height: 36px;
            }
          }
        }
      }
      .realName {
        ::v-deep {
          .el-form-item__content {
            .el-input {
              width: 250px;
              height: 36px;
            }
          }
        }
      }
      .date {
        ::v-deep {
          .el-date-editor--daterange.el-input,
          .el-date-editor--daterange.el-input__inner,
          .el-date-editor--timerange.el-input,
          .el-date-editor--timerange.el-input__inner {
            width: 274px;
          }
          .el-range-input {
            background-color: #fff;
          }
        }
      }
      .button {
        ::v-deep {
          .el-button--primary.is-plain {
            border-color: #3464e0;
            background: transparent;
            color: #3464e0;
            &:active {
              background: transparent !important;
            }
          }
          .el-button--primary.is-plain:focus,
          .el-button--primary.is-plain:hover {
            color: #355fce !important;
            border-color: #355fce !important;
            background-color: transparent;
          }
          .el-button--primary:focus,
          .el-button--primary:hover {
            background-color: #355fce;
          }
        }
      }
      .processName {
        // display: flex;
        ::v-deep {
          .el-radio-button {
            margin-bottom: 10px;
          }
          .el-radio-button .el-radio-button__inner {
            background: transparent;
          }
          .el-radio-button__inner {
            padding: 8px 14px;
            border-color: transparent;
            border-radius: 4px;
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #0b1a44;
          }
          .el-radio-button__orig-radio:checked + .el-radio-button__inner {
            border-color: #3464e0;
            color: #3464e0;
          }
        }
      }
      .status {
        ::v-deep {
          .el-radio-button {
            margin-bottom: 0;
          }
        }
      }
      ::v-deep {
        .el-input__inner {
          border: 1px solid #eeeeef;
          &::placeholder {
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #b1bac7;
          }
        }
      }
    }
    .table_list {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      .list {
        padding: 18px 24px 22px 24px;
        margin-right: 23px;
        margin-bottom: 23px;
        width: 306px;
        height: 218px;
        border-radius: 12px;
        background: #fff;
        box-shadow: 0 0 5px #e8eaed;
        cursor: pointer;
        @media screen and(min-width:1574px) and(max-width:1280px) {
          &:nth-of-type(3n) {
            margin-right: 0;
          }
        }
        @media screen and(min-width:1610px) and(max-width:1900px) {
          &:nth-of-type(4n) {
            margin-right: 0;
          }
        }
        @media screen and(min-width:1900px) and (max-width: 1920px) {
          &:nth-of-type(5n) {
            margin-right: 0;
          }
        }

        &:hover {
          background-color: #f9f9f9;
        }
        .content {
          position: relative;
          img {
            display: block;
          }
          .list_title {
            margin-top: 18px;
            font-weight: bold;
          }
          .list_processName {
            margin-top: 20px;
          }
          .list_createTime {
            margin-top: 12px;
          }
          .list_realName {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 12px;
            .list_button {
              width: 66px !important;
              height: 26px;
              background: #f2f4ff;
              text-align: center;
              line-height: 26px;
              font-weight: bold;
              color: #3464e0;
              border-radius: 4px 4px 4px 4px;
              cursor: pointer;
              &:hover {
                background: #e8ebf8;
              }
            }
            .examine {
              background: #3464e0;
              color: #ffffff;
              &:hover {
                background: #355fce;
              }
            }
          }
          .universalClass {
            width: 100%;
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #0b1a44;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .result {
            position: absolute;
            right: 0;
            top: 0;
          }
        }
      }
    }
    ::v-deep {
      .el-card__header {
        display: flex;
        align-items: flex-end;
        padding-left: 40px !important;
        height: 75px;
        padding: 0;
        background: url('../../assets/process/top_bg.png') no-repeat;
        .el-radio-group {
          .el-radio-button {
            margin-right: 16px;
            .el-radio-button__inner {
              width: 128px !important;
              height: 36px;
              line-height: 36px;
              padding: 0;
              background-color: #3464e0;
              border: none;
              font-size: 16px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #ffffff;
              text-align: center;
              border-radius: 19px 19px 0 0;
            }
            .el-radio-button__orig-radio:checked + .el-radio-button__inner {
              color: #3464e0;
              background-color: #fff;
            }
          }
        }
      }
      .el-card__body {
        background: url('../../assets/process/content_bg.png') no-repeat;
        padding: 15px 55px;
      }
      .el-form-item__label {
        font-size: 14px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        font-weight: bold;
        color: #0b1a44;
      }
      .el-form-item {
        margin-bottom: 15px;
      }
      .el-empty__image {
        width: 298px;
        height: 188px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .el-empty__description {
        img {
          width: 74px;
          height: 21px;
        }
      }
    }
  }
}
@media screen and(min-height:950px) {
  ::v-deep {
    .el-card__body {
      padding: 30px 55px !important;
    }
  }
  .app-container {
    padding-bottom: 20px;
  }
}
</style>
