<template>
  <div class="app-container">
    <el-form ref="searchForm" label-width="80px" inline>
      <el-form-item label="项目名称:">
        <el-input v-model="queryInfo.bidName" size="small" placeholder="请输入项目名称" clearable></el-input>
      </el-form-item>
      <el-form-item label="项目编号:">
        <el-input v-model="queryInfo.bidCode" size="small" placeholder="请输入项目编号" clearable></el-input>
      </el-form-item>
      <el-form-item label="客户名称:">
        <el-input v-model="queryInfo.customerName" size="small" placeholder="请输入客户名称" clearable></el-input>
      </el-form-item>
      <el-form-item label="负责人:" label-width="90px">
        <el-input v-model="queryInfo.realName" size="small" placeholder="请输入负责人" clearable></el-input>
      </el-form-item>
      <el-form-item label="创建时间:">
        <el-date-picker v-model="date" size="small" type="daterange" range-separator="→" start-placeholder="开始日期" end-placeholder="结束日期" clearable @change="datePickerChange"> </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="success" size="small" @click="getBidSecretBidList">查询</el-button>
        <el-button type="primary" size="small" plain @click="reset">重置</el-button>
        <el-button type="primary" size="small" @click="addDialog = true">新增项目</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" style="width: 100%" border @row-click="goDetails">
      <el-table-column prop="bidCode" label="项目编号" width="width" align="center"> </el-table-column>
      <el-table-column prop="bidName" label="项目名称" width="width" align="center"> </el-table-column>
      <el-table-column prop="customerName" label="客户名称" width="width" align="center"> </el-table-column>
      <el-table-column prop="realName" label="负责人" width="width" align="center"> </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="width" align="center"> </el-table-column>
      <el-table-column prop="updateTime" label="更新时间" width="width" align="center"> </el-table-column>
    </el-table>
    <el-pagination v-if="list.length > 0" layout="total,prev, pager, next" style="margin-top: 15px; text-align: right" :page-sizes="[5, 10, 15, 20]" background :total="total" :page-size.sync="queryInfo.pageSize" :current-page.sync="queryInfo.pageNum" @size-change="getBidSecretBidList" @current-change="getBidSecretBidList" />

    <!-- 添加的dialog -->
    <el-dialog title="新增项目" :visible.sync="addDialog" width="550px" :close-on-click-modal="false" @close="close">
      <el-form ref="form" :model="formInfo" label-width="90px" :rules="rules">
        <el-form-item label="项目编号:" prop="bidCode" class="name_input">
          <el-input v-model="formInfo.bidCode" placeholder="请输入项目编号" size="small" maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="项目名称:" prop="bidName" class="name_input">
          <el-input v-model="formInfo.bidName" placeholder="请输入项目名称" size="small" maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="客户名称:" prop="customerId" class="level">
          <el-select v-model="formInfo.customerId" placeholder="请选择客户名称" size="small" @focus="getSecretCustomerCustomerList">
            <el-option v-for="item in allCustomer" :key="item.customerId" :value="item.customerId" :label="item.customerName"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="负责人:" prop="userId" class="level">
          <el-select v-model="formInfo.userId" placeholder="请选择负责人" size="small" @focus="getUserList">
            <el-option v-for="item in userList" :key="item.userId" :label="item.realName" :value="item.userId"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注:" class="name_input">
          <el-input v-model="formInfo.remark" placeholder="请输入备注" type="textarea" size="small" maxlength="20"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="addDialog = false">取 消</el-button>
        <el-button type="primary" @click="subData">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { secretCustomeAllCustomer } from '@/api/clientele'
import { bidSecretBidList, bidSave } from '@/api/bid'
import { getList } from '@/api/systemUser'
import { formatDate } from '@/filters'

export default {
  name: 'Bid',
  data() {
    return {
      queryInfo: {
        bidName: null,
        bidCode: null,
        customerName: null,
        realName: null,
        startTime: null,
        endTime: null,
        pageNum: 1,
        pageSize: 10
      },
      list: [],
      userList: [],
      total: null,
      date: null,
      addDialog: false,
      formInfo: {
        bidCode: null,
        bidName: null,
        customerId: null,
        userId: null,
        type: null,
        remark: null
      },
      rules: {
        bidCode: [{ required: true, message: '请输入项目编号', trigger: 'blur' }],
        bidName: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
        customerId: [{ required: true, message: '请选择客户名称', trigger: 'change', type: 'number' }],
        userId: [{ required: true, message: '请选择负责人', trigger: 'change' }]
      },
      allCustomer: []
    }
  },
  created() {
    this.getBidSecretBidList()
  },
  methods: {
    async getBidSecretBidList() {
      const { data } = await bidSecretBidList(this.queryInfo)
      this.total = data.total
      this.list = data.list
      console.log(data)
    },
    // 获取全部客户
    async getSecretCustomerCustomerList() {
      const { data } = await secretCustomeAllCustomer()
      this.allCustomer = data
      console.log(data)
    },
    // 获取全部销售人员
    async getUserList() {
      const { data } = await getList({ pageNum: 1, pageSize: 200, organizationId: 56642510 })
      this.userList = data.list
    },
    // 时间搜索的格式化
    datePickerChange(val) {
      if (val) {
        this.queryInfo.startTime = formatDate(val[0])
        this.queryInfo.endTime = formatDate(val[1], 'yyyy-MM-dd') + ' 23:59:59'
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getBidSecretBidList()
    },
    goDetails(row) {
      console.log(row)
      this.$router.push(`/Bid/details/${row.bidId}`)
    },
    // 重置搜索
    reset() {
      this.queryInfo = {
        bidName: null,
        bidCode: null,
        customerName: null,
        realName: null,
        startTime: null,
        endTime: null,
        pageNum: 1,
        pageSize: 10
      }
      this.date = null
      this.getBidSecretBidList()
    },
    subData() {
      this.$refs['form'].validate(async (val) => {
        if (!val) return
        await bidSave(this.formInfo)
        this.$message.success('新增项目成功!')
        this.getBidSecretBidList()
        this.addDialog = false
      })
    },
    close() {
      this.formInfo = {
        bidCode: null,
        bidName: null,
        customerId: null,
        userId: null,
        type: null,
        remark: null
      }
      this.$refs['form'].resetFields()
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep {
  .el-dialog__body {
    padding: 30px 50px;
  }
  .el-form-item__content {
    .el-date-editor--daterange.el-input,
    .el-date-editor--daterange.el-input__inner,
    .el-date-editor--timerange.el-input,
    .el-date-editor--timerange.el-input__inner {
      width: 274px;
      background: #ffffff;
      border-radius: 4px 4px 4px 4px;
    }
    // .el-range-input {
    //   background-color: #fff;
    // }
  }
  .name_input {
    .el-input__inner {
      width: 355px;
    }
  }
  .level {
    .el-input__inner {
      width: 170px;
    }
  }
}
</style>
