import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },

  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true
  },
  {
    path: '/',
    component: () => import('@/Home')
  },
  {
    path: '/basicInfo/home',
    component: Layout,
    meta: {
      type: 0
    },
    children: [
      {
        path: '',
        name: '工作台',
        component: () => import('@/views/dashboard/index'),
        meta: { title: '工作台', icon: 'dashboard', type: 0 }
      }
    ]
  }

  // 404 page must be placed at the end !!!
  // { path: '*', redirect: '/404', hidden: true }
]
// 角色路由
export const asyncRoutes = [
  {
    path: '/user',
    component: Layout,
    meta: {
      title: '用户管理',
      flag: 'sys:sys:sys',
      type: 1
    },
    children: [
      {
        path: '',
        name: 'SystemUser',
        component: () => import('@/views/systemUser/index'),
        meta: {
          title: '用户管理',
          icon: 'user',
          flag: 'sys:sys:sys',
          type: 1
        }
      }
    ]
  },
  {
    path: '/organization',
    component: Layout,
    meta: {
      flag: 'org:org:org',
      type: 1
    },
    children: [
      {
        path: '',
        name: 'Organization',
        component: () => import('@/views/organization/index'),
        meta: {
          title: '部门管理',
          icon: 'osce-organization',
          flag: 'org:org:org',
          type: 1
        }
      }
    ]
  },
  {
    path: '/role',
    component: Layout,
    meta: {
      icon: 'role',
      flag: 'role:role:role',
      type: 1
    },
    children: [
      {
        path: '',
        name: 'Role',
        component: () => import('@/views/role/index'),
        meta: {
          title: '角色管理',
          flag: 'role:role:role',
          type: 1
        }
      }
    ]
  },
  {
    path: '/menu',
    component: Layout,
    meta: {
      icon: 'menu',
      flag: 'menu:menu:menu',
      type: 1
    },
    children: [
      {
        path: '',
        name: 'Menu',
        component: () => import('@/views/menu/index'),
        meta: {
          icon: 'menu',
          title: '菜单管理',
          flag: 'menu:menu:menu',
          type: 1
        }
      }
    ]
  },
  {
    path: '/log',
    component: Layout,
    meta: {
      icon: 'log',
      flag: 'log:log:log',
      type: 1
    },
    children: [
      {
        path: '',
        name: 'Log',
        component: () => import('@/views/log/index'),
        meta: {
          title: '日志管理',
          flag: 'log:log:log',
          type: 1
        }
      }
    ]
  },
  {
    path: '/dictionaries',
    component: Layout,
    meta: {
      icon: 'zidian',
      flag: 'Dict:Dict:Dict',
      type: 1
    },
    children: [
      {
        path: '',
        name: 'Dictionaries',
        component: () => import('@/views/dictionaries/index'),
        meta: {
          title: '字典管理',
          flag: 'Dict:Dict:Dict',
          type: 1
        }
      },
      {
        path: '/dictionaries/data/:dictType',
        name: 'DictionariesData',
        hidden: true,
        component: () => import('@/views/dictionaries/components/dictionariesData'),
        meta: {
          title: '字典数据管理',
          flag: 'Dict:Dict:Dict',
          type: 1,
          activeMenu: '/dictionaries'
        }
      }
    ]
  },
  {
    path: '/specialty',
    component: Layout,
    meta: {
      icon: 'specialty',
      flag: 'specialty',
      type: 1
    },
    children: [
      {
        path: '',
        name: 'Specialty',
        component: () => import('@/views/specialty/index'),
        meta: {
          title: '专业管理',
          flag: 'specialty',
          type: 1
        }
      }
    ]
  },
  {
    path: '/product',
    component: Layout,
    meta: {
      icon: '产品管理',
      flag: 'product',
      type: 1
    },
    children: [
      {
        path: '',
        name: 'Product',
        component: () => import('@/views/product/index'),
        meta: {
          title: '产品管理',
          flag: 'product',
          type: 1
        }
      },
      {
        path: '/product/details/:productId',
        hidden: true,
        component: () => import('@/views/product/details'),
        meta: {
          title: '产品详情',
          flag: 'product',
          type: 1,
          activeMenu: '/product'
        }
      }
    ]
  },
  // 流程管理
  {
    path: '/process/management',
    component: Layout,
    meta: { flag: 'process', type: 2 },
    children: [
      {
        path: '',
        component: () => import('@/views/process/index'),
        name: 'Process',
        meta: { title: '流程管理', flag: 'process', icon: 'process', type: 2 }
      },
      {
        path: '/process/management/details/:processInstanceId',
        component: () => import('@/views/process/details'),
        name: 'ProcessDetails',
        hidden: true,
        meta: { title: '查看详情', flag: 'process', type: 2, activeMenu: '/process/management' }
      }
    ]
  },

  // 会议管理
  {
    path: '/meeting',
    component: Layout,
    meta: { flag: 'meeting', type: 3 },
    children: [
      {
        path: '',
        component: () => import('@/views/meeting/index'),
        name: 'Meeting',
        meta: { title: '会议管理', flag: 'meeting', icon: 'meeting', type: 3 }
      },
      {
        path: '/addMeeting/:type',
        component: () => import('@/views/meeting/components/addMeeting'),
        name: 'addMeeting',
        hidden: true,
        meta: { title: '添加会议', flag: 'meeting', type: 3, activeMenu: '/meeting' }
      },
      {
        path: '/meetingDetails/:meetingId',
        component: () => import('@/views/meeting/components/meetingDetails'),
        name: 'meetingDetails',
        hidden: true,
        meta: { title: '会议详情', flag: 'meeting', type: 3, activeMenu: '/meeting' }
      }
    ]
  },
  // 项目管理
  {
    path: '/project',
    component: Layout,
    meta: { flag: 'project', type: 4 },
    children: [
      {
        path: '',
        component: () => import('@/views/project/index'),
        name: 'Project',
        meta: { title: '项目管理', icon: 'project', flag: 'project', type: 4 }
      },

      {
        path: '/project/details/:id/:type',
        component: () => import('@/views/project/details'),
        name: 'ProjectDetails',
        hidden: true,
        meta: { title: '项目详情', flag: 'project', type: 4, activeMenu: '/project' }
      }
    ]
  },
  // 售后记录
  {
    path: '/aftermarket',
    component: Layout,
    meta: { flag: 'Aftermarket', type: 4 },
    children: [
      {
        path: '',
        component: () => import('@/views/project/aftermarket'),
        name: 'Aftermarket',
        meta: { title: '售后记录', flag: 'Aftermarket', type: 4 }
      },
      {
        path: '/project/aftermarket_details/:id/:type',
        component: () => import('@/views/project/aftermarket_details'),
        name: 'ProjectDetails',
        hidden: true,
        meta: { title: '项目详情', flag: 'Aftermarket', type: 4, activeMenu: '/aftermarket' }
      }
    ]
  },
  // 培训管理
  {
    path: '/training',
    component: Layout,
    meta: { flag: 'training', type: 5 },
    children: [
      {
        path: '',
        component: () => import('@/views/training/index'),
        name: 'Training',
        meta: { title: '培训管理', icon: 'training', flag: 'training', type: 5 }
      },
      {
        path: '/addTraining/:type',
        component: () => import('@/views/training/components/addTraining'),
        name: 'addMeeting',
        hidden: true,
        meta: { title: '添加培训', flag: 'training', type: 5, activeMenu: '/training' }
      },
      {
        path: '/trainingDetails/:trainId',
        component: () => import('@/views/training/components/trainingDetails'),
        name: 'trainingDetails',
        hidden: true,
        meta: { title: '培训详情', flag: 'training', type: 5, activeMenu: '/training' }
      }
    ]
  },
  // 知识库管理
  {
    path: '/repository',
    component: Layout,
    meta: { flag: 'repository', type: 5 },
    children: [
      {
        path: '',
        component: () => import('@/views/repository/index'),
        name: 'Repository',
        meta: { title: '知识库管理', icon: 'repository', flag: 'repository', type: 5 }
      },
      {
        path: '/repository/add/:type',
        component: () => import('@/views/repository/components/addRepository'),
        hidden: true,
        name: 'RepositoryAdd',
        meta: { title: '添加知识库', flag: 'repository', type: 5, activeMenu: '/repository' }
      },
      {
        path: '/repository/details/:knowledgeBaseId',
        component: () => import('@/views/repository/components/details'),
        hidden: true,
        name: 'RepositoryDetails',
        meta: { title: '知识库详情', flag: 'repository', type: 5, activeMenu: '/repository' }
      }
    ]
  },
  {
    path: '/institution',
    component: Layout,
    meta: { flag: 'institution', type: 6 },
    children: [
      {
        path: '',
        component: () => import('@/views/institution/index'),
        name: 'Institution',
        meta: { title: '制度管理', icon: 'institution', flag: 'institution', type: 6 }
      }
      // {
      //   path: '/repository/add/:type',
      //   component: () => import('@/views/repository/components/addRepository'),
      //   hidden: true,
      //   name: 'RepositoryAdd',
      //   meta: { title: '添加知识库', flag: 'repository', type: 5, activeMenu: '/repository' }
      // },
      // {
      //   path: '/repository/details/:knowledgeBaseId',
      //   component: () => import('@/views/repository/components/details'),
      //   hidden: true,
      //   name: 'RepositoryDetails',
      //   meta: { title: '知识库详情', flag: 'repository', type: 5, activeMenu: '/repository' }
      // }
    ]
  },
  { path: '*', redirect: '/404', hidden: true }
]

const createRouter = () =>
  new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes

    // routes: [...constantRoutes, ...asyncRoutes]
  })

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
