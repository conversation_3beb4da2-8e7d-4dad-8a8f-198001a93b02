<template>
  <div class="app-container">
    <div class="chanceDetails_top">
      <el-button type="primary" size="small" icon="el-icon-back" @click="$router.push('/chance')">返回</el-button>
      <el-button type="success" size="small" @click="openChangeRecord">修改记录</el-button>
    </div>
    <template v-if="JSON.stringify(chanceDetails) !== '{}'">
      <el-card>
        <div slot="header">
          <span>基本信息</span>
        </div>

        <el-descriptions :column="4">
          <el-descriptions-item label="商机编号">
            <strong>{{ chanceDetails.code }}</strong>
          </el-descriptions-item>
          <el-descriptions-item label="客户">
            <span style="color: #3465df; border-bottom: 1px solid #3465df; cursor: pointer" @click="customerDetails">{{ chanceDetails.customerName }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="专业">{{ chanceDetails.majorName }}</el-descriptions-item>
          <el-descriptions-item label="联系人">{{ chanceDetails.contactsName }}</el-descriptions-item>

          <el-descriptions-item label="商机创建人">{{ chanceDetails.createUserName }}</el-descriptions-item>
          <el-descriptions-item label="商机负责人">{{ chanceDetails.userRealName }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ chanceDetails.createTime }}</el-descriptions-item>

          <el-descriptions-item label="更新时间">{{ chanceDetails.updateTime }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
      <el-card style="margin-top: 15px">
        <div slot="header">
          <div class="chanceDetailsFiles_top">
            <span>商机详情</span>
            <el-button type="primary" size="small" icon="el-icon-edit" @click="edit">编辑</el-button>
          </div>
        </div>

        <el-descriptions :column="4">
          <el-descriptions-item label="项目名称">{{ chanceDetails.name }}</el-descriptions-item>
          <el-descriptions-item label="项目内容">{{ chanceDetails.content }}</el-descriptions-item>
          <el-descriptions-item label="项目等级">
            <el-tag :type="chanceDetails.rank === 'A' ? 'success' : chanceDetails.rank === 'B' ? 'warning' : ''">{{ chanceDetails.rank }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="项目金额">{{ chanceDetails.money }}</el-descriptions-item>
          <el-descriptions-item label="存在竞争对手情况">{{ chanceDetails.competitorInfo }}</el-descriptions-item>
          <el-descriptions-item label="采购形式">{{ chanceDetails.collectType | collectType }}</el-descriptions-item>
          <el-descriptions-item label="招标时间">{{ chanceDetails.inviteTime }}</el-descriptions-item>
          <el-descriptions-item label="项目当前情况">{{ chanceDetails.current }}</el-descriptions-item>
          <el-descriptions-item label="备注">{{ chanceDetails.remark }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
      <el-card style="margin-top: 15px">
        <div slot="header">
          <div class="chanceDetailsFiles_top">
            <span style="font-weight: 600">附件信息</span>
            <el-button type="primary" size="small" icon="el-icon-plus" @click="uploadingFileDialog = true">上传附件</el-button>
          </div>
        </div>
        <el-table :data="chanceDetails.fileDtos" style="width: 100%" border>
          <el-table-column align="center" label="序号" width="width" type="index"> </el-table-column>
          <el-table-column align="center" prop="fileName" label="文件名称" width="width"> </el-table-column>
          <el-table-column align="center" prop="fileSize" label="文件大小" width="width">
            <template v-slot="{ row }">
              <span>{{ row.fileSize }}KB</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="realName" label="上传人" width="width"> </el-table-column>
          <el-table-column align="center" prop="createTime" label="上传时间" width="width"> </el-table-column>
          <el-table-column align="center" prop="prop" label="操作" width="width">
            <template v-slot="{ row }">
              <el-button type="primary" size="small" @click="downloadFile(row)">下载</el-button>
              <el-button type="primary" size="small" @click="rechristen(row)">重命名</el-button>
              <el-button type="danger" size="small" @click="delFile(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </template>
    <!-- 客户详情dialog -->
    <el-dialog title="客户详情" :visible.sync="customerDetailsDialog" width="450px">
      <el-descriptions v-if="customerDetailsDialog" :column="1" class="clientInfo_center">
        <el-descriptions-item label="客户名称">
          <span>{{ clientInfo.customerName }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="所属区域">
          <span>{{ clientInfo.name }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="客户来源">
          <span>{{ clientInfo.source | source }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="客户级别">
          <span>{{ clientInfo.level | level }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="客户负责人">
          <span>{{ clientInfo.realName }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="联系方式">
          <span>{{ clientInfo.phone }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="联系地址">
          <span>{{ clientInfo.address }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="邮箱">
          <span>{{ clientInfo.email }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          <span>{{ clientInfo.createTime }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          <span>{{ clientInfo.updateTime }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="备注">
          <span>{{ clientInfo.remark }}</span>
        </el-descriptions-item>
      </el-descriptions>
      <div slot="footer">
        <!-- <el-button size="small" @click="customerDetailsDialog = false">取 消</el-button> -->
        <el-button type="primary" size="small" @click="customerDetailsDialog = false">关 闭</el-button>
      </div>
    </el-dialog>
    <!-- 上传附件的dialog -->
    <el-dialog title="上传附件" :visible.sync="uploadingFileDialog" width="450px" :close-on-click-modal="false">
      <div style="width: 360px; margin: 0 auto">
        <el-upload class="upload-demo" drag :action="action" :headers="header" :file-list="fileList" multiple :before-upload="beforeUpload" :on-success="uploadSuccess" :on-remove="uploadRemove">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div slot="tip" class="el-upload__tip" style="color: red; line-height: 15px">只能上传.doc .docx .xls .xlsx .pdf .jpg .png .zip格式的附件，且大小不超过50MB</div>
        </el-upload>
      </div>
      <div slot="footer">
        <el-button @click="uploadingFileDialog = false">取 消</el-button>
        <el-button type="primary" @click="uploadingFile">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 文件重命名dialog -->
    <el-dialog title="文件重命名" :visible.sync="rechristenDialog" width="400px">
      <div style="width: 300px; margin: 0 auto">
        <el-input v-model="fileName" placeholder="请输入文件名称" clearable maxlength="50"></el-input>
      </div>
      <div slot="footer">
        <el-button @click="rechristenDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirmRechristen">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 修改商机 -->
    <Update ref="updateRef" @refresh="getChanceDetails" />
    <!-- 修改记录 -->
    <Record ref="recordRef" @refresh="getChanceDetails" />
  </div>
</template>

<script>
import { secretCustomerCustomerDetails } from '@/api/clientele'
import { chanceChanceDetails } from '@/api/chance'
import { meetingSaveFile, meetingRenameFile, meetingUpdateFileName, meetingDeleteFile } from '@/api/meeting'
import { mapGetters } from 'vuex'
import Update from './components/Update'
import Record from './components/Record'
export default {
  name: '',
  components: {
    Update,
    Record
  },
  filters: {
    collectType(type) {
      type = parseInt(type)

      // 1 校内招标 2 公开招标 3 直采
      return type === 1 ? '校内招标' : type === 2 ? '公开招标' : type === 3 ? '直采' : ''
    }
  },
  data() {
    return {
      chanceDetails: {},
      clientInfo: {}, // 客户详情
      customerDetailsDialog: false,
      uploadingFileDialog: false,
      action: window.config.VUE_APP_BASE_API + '/system/upload/file',
      header: {
        Authorization: null
      },

      fileList: [],
      rechristenDialog: false,
      fileName: null,
      oldPath: null,
      fileId: null
    }
  },
  computed: {
    ...mapGetters(['token'])
  },
  created() {
    this.getChanceDetails()
  },
  methods: {
    async getChanceDetails() {
      const { data } = await chanceChanceDetails({ chanceId: this.$route.params.id })
      this.chanceDetails = data
    },
    // 编辑商机
    edit() {
      this.$refs.updateRef.open(this.chanceDetails)
    },

    // 客户详情
    async customerDetails() {
      const { data } = await secretCustomerCustomerDetails({ customerId: this.chanceDetails.customerId })
      this.clientInfo = data
      console.log(this.clientInfo)
      this.customerDetailsDialog = true
    },

    beforeUpload(file) {
      this.header.Authorization = `Bearer ${this.token}`
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isDOC = file.type === 'application/msword'
      const isDOCX = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      const isXLS = file.type === 'application/vnd.ms-excel'
      const isXLSX = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      const isPDF = file.type === 'application/pdf'
      const isZIP = file.type === 'application/x-zip-compressed'
      const isLt2M = file.size / 1024 / 1024 < 50
      if (!isLt2M) {
        this.$message.error('上传附件大小不能超过 50MB!')
      }
      if (!isJPG && !isPNG && !isDOC && !isDOCX && !isXLS && !isXLSX && !isPDF && !isZIP) {
        this.$message.warning('只能上传.doc .docx .xls .xlsx .pdf .jpg .png .zip 格式的附件')
      }
      return (isJPG || isPNG || isDOC || isDOCX || isXLS || isXLSX || isPDF || isZIP) && isLt2M
    },
    uploadSuccess(response, file, fileList) {
      this.fileList = fileList
    },
    uploadRemove(file, fileList) {
      this.fileList = fileList
    },
    async uploadingFile() {
      const list = []
      this.fileList.forEach((item) => {
        list.push({
          fileName: item.name,
          fileSize: item.size / 1024,
          fileUrl: item.response.data[0],
          meetingId: this.$route.params.id,
          belongType: 10
        })
      })
      await meetingSaveFile(list)
      this.$message.success('上传附件成功')
      this.uploadingFileDialog = false
      this.fileList = []
      this.getChanceDetails()
    },
    // 下载文件
    downloadFile(row) {
      window.open(row.fileUrl, '_blank')
    },
    // 文件重命名
    rechristen(row) {
      this.fileName = row.fileName
      this.oldPath = row.fileUrl.split('workmanage/')[1]
      this.fileId = row.fileId
      this.rechristenDialog = true
    },
    // 确定重命名
    async confirmRechristen() {
      const { data } = await meetingRenameFile({ oldPath: this.oldPath, newPath: this.fileName })
      await meetingUpdateFileName({ fileId: this.fileId, fileName: this.fileName, fileUrl: data })
      this.$message.success('文件重命名成功')
      this.rechristenDialog = false
      this.getChanceDetails()
    },
    // 删除附件
    delFile(row) {
      this.$confirm('确认要删除该文件吗, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await meetingDeleteFile({ fileId: row.fileId })
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getChanceDetails()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 修改记录
    openChangeRecord() {
      this.$refs.recordRef.open(this.$route.params.id)
    }
  }
}
</script>

<style scoped lang="scss">
.chanceDetails_top {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}
.chanceDetailsFiles_top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
::v-deep {
  .el-card {
    .el-card__header {
      font-size: 18px;
      font-weight: 600;
    }
    .el-descriptions {
      font-size: 16px;
    }
  }
  .name_input {
    .el-input__inner {
      width: 1200px;
    }
    .el-textarea__inner {
      width: 1200px;
    }
  }
}
</style>
