// 软件产品库管理
import request from '@/utils/request'
/** 添加 */
export function tSoftwareProductAdd(data) {
  return request({
    url: '/tSoftwareProduct/add',
    method: 'POST',
    data
  })
}
/** 修改 */
export function tSoftwareProductUpdate(data) {
  return request({
    url: '/tSoftwareProduct/update',
    method: 'POST',
    data
  })
}
/** 删除 */
export function tSoftwareProductRemove(params) {
  return request({
    url: '/tSoftwareProduct/remove',
    method: 'DELETE',
    params
  })
}
/** 列表 */
export function tSoftwareProductList(params) {
  return request({
    url: '/tSoftwareProduct/list',
    method: 'GET',
    params
  })
}
/** 列表导出 */
export function tSoftwareProductListExport(params) {
  return request({
    url: '/tSoftwareProduct/listExport',
    method: 'GET',
    params
  })
}
/** 详情 */
export function tSoftwareProductDetail(params) {
  return request({
    url: '/tSoftwareProduct/detail',
    method: 'GET',
    params
  })
}
/** 价格趋势 */
export function tProductHistoryTrend(params) {
  return request({
    url: '/tProductHistory/trend',
    method: 'GET',
    params
  })
}
