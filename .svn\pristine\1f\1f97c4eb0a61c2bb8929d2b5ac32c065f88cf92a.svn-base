<template>
  <div class="app-container">
    <el-row :gutter="10" class="top">
      <el-col :span="24">
        <i class="el-icon-location"></i>
        <span>当前位置：合同管理 /</span>
        <span>合同详情/合同供货信息管理</span>
      </el-col>
    </el-row>
    <div class="box">
      <div class="boxContent">
        <div class="title">
          合同供货信息管理（
          <span>合同名称:{{ formInfo.contractName }}</span>
          <span style="margin-left: 25px">客户名称: {{ formInfo.customerName }}</span>
          ）
        </div>
        <el-form ref="form" :model="formInfo" label-width="186px" :disabled="formInfo.type === 2" inline>
          <el-form-item label="软件供货内容：" class="monopoly">
            <el-input v-model="formInfo.supplySoft" type="textarea" placeholder="请填写软件供货内容" resize="none" maxlength="300" :disabled="!isShi"></el-input>
          </el-form-item>
          <el-form-item label="硬件供货内容：" class="monopoly">
            <el-input v-model="formInfo.supplyHard" type="textarea" placeholder="请填写硬件供货内容" resize="none" maxlength="300" :disabled="!isShi"></el-input>
          </el-form-item>
          <el-form-item label="软件交付日期:" class="half">
            <el-date-picker v-model="formInfo.paySoftTime" type="date" value-format="yyyy-MM-dd" placeholder="软件交付日期" :disabled="!isManager || isShi"> </el-date-picker>
          </el-form-item>
          <el-form-item label="硬件交付日期:" class="half">
            <el-date-picker v-model="formInfo.payHardTime" type="date" value-format="yyyy-MM-dd" placeholder="硬件交付日期" :disabled="!isManager || isShi"> </el-date-picker>
          </el-form-item>
          <el-form-item label="确认软件交付日期:" class="half">
            <el-date-picker v-model="formInfo.confirmPaySoftTime" type="date" value-format="yyyy-MM-dd" placeholder="确认软件交付日期" :disabled="!isShi"> </el-date-picker>
          </el-form-item>
          <el-form-item label="确认硬件交付日期：" class="half">
            <el-date-picker v-model="formInfo.confirmPayHardTime" type="date" value-format="yyyy-MM-dd" placeholder="确认硬件交付日期" :disabled="!isShi"> </el-date-picker>
          </el-form-item>
          <el-form-item label="硬件资金需求:" class="half">
            <el-input v-model.number="formInfo.hardMoney" type="number" placeholder="请输入硬件资金需求" maxlength="10" :disabled="!isManager || isShi"></el-input>
          </el-form-item>
        </el-form>
        <div class="subButton">
          <template v-if="formInfo.type === 2">
            <span class="closeClass" @click="close"><i class="el-icon-back"></i> 返回</span>
          </template>
          <template v-else>
            <span class="closeClass" @click="close"><i class="el-icon-error"></i> 取消</span>
            <span class="submitClass" @click="submit"><i class="el-icon-success"></i> 提交</span>
          </template>
        </div>
      </div>
    </div>
    <!-- 通知人员 -->
    <el-dialog :visible.sync="informDialog" width="413px">
      <template v-slot:title>
        <header>
          <img src="@/assets/contractNew/informIcon.png" alt="" />
          <span>已保存供货信息</span>
        </header>
      </template>
      <div class="body">
        <p>请选择是否通知相关人员</p>
        <el-checkbox-group v-model="informType">
          <el-checkbox :key="1" :label="1">研发</el-checkbox>
          <el-checkbox :key="2" :label="2">售后</el-checkbox>
          <el-checkbox :key="3" :label="3">销售</el-checkbox>
        </el-checkbox-group>
      </div>
      <div slot="footer">
        <el-button @click="informDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirmInform">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { contractDetail, contractUpdateSupply, contractMessage } from '@/api/contractNew.js'
import { mapGetters } from 'vuex'
export default {
  name: '',
  data() {
    return {
      formInfo: {
        contractId: null,
        contractName: null,
        customerId: null, // 客户id
        signingTime: null,
        type: 1,
        supplySoft: null, // 软件供货内容
        supplyHard: null, // 硬件供货内容
        paySoftTime: null, // 软件交付时间
        payHardTime: null, // 硬件交付时间
        confirmPaySoftTime: null, // 确认软件交付时间
        confirmPayHardTime: null, // 确认硬件交付时间
        hardMoney: null // 硬件资金需求
      },
      informDialog: false,
      informType: []
    }
  },
  computed: {
    ...mapGetters(['userId', 'roleName']),
    contractId() {
      return this.$route.params.contractId
    },
    // 石经理
    isShi() {
      return this.userId === '03040668381334'
    },
    isManager() {
      return this.roleName.includes('部门经理')
    }
  },
  created() {
    this.getDetails()
  },

  methods: {
    async getDetails() {
      const { data } = await contractDetail({ id: this.contractId })
      this.formInfo = {
        contractId: data.contractId,
        contractName: data.contractName,
        customerId: data.customerId,
        customerName: data.customerName,
        signingTime: data.signingTime,
        type: data.type,
        supplySoft: data.supplySoft, // 软件供货内容
        supplyHard: data.supplyHard, // 硬件供货内容
        paySoftTime: data.paySoftTime, // 软件交付时间
        payHardTime: data.payHardTime, // 硬件交付时间
        confirmPaySoftTime: data.confirmPaySoftTime, // 确认软件交付时间
        confirmPayHardTime: data.confirmPayHardTime, // 确认硬件交付时间
        hardMoney: data.hardMoney // 硬件资金需求
      }
    },
    close() {
      this.$router.push(`/newContract/details/${this.contractId}`)
    },
    submit() {
      const loading = this.$loading({
        text: '数据保存中，请稍后...',
        background: 'rgba(0,0,0,0.7)'
      })
      contractUpdateSupply(this.formInfo)
        .then(() => {
          loading.close()
          this.$message.success('数据保存成功!')
          this.informDialog = true
          // this.close()
        })
        .catch(() => {
          loading.close()
        })
    },
    confirmInform() {
      if (this.informType.length) {
        this.informType.forEach(async (item) => {
          await contractMessage({ contractName: this.formInfo.contractName, type: item })
        })
        this.close()
      } else {
        this.close()
      }
    }
  }
}
</script>
<style scoped lang="scss">
.app-container {
  position: relative;
  padding: 0;
  background-color: #e8eaed;
  min-height: 100%;
  height: 100%;
  padding-top: 58px;
  padding-right: 40px;
  padding-bottom: 10px;
  .top {
    position: absolute;
    top: 0px;
    width: 100%;
    background-color: #e8eaed;
    padding: 24px 0 16px 0;
    z-index: 999;
    .el-col {
      i {
        font-size: 14px;
        color: #657081;
      }
      span {
        &:first-of-type {
          margin: 0 5px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #657081;
        }
        &:last-of-type {
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
      }
    }
  }
  .box {
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #f5f5f5;
    border-radius: 16px;
    .boxContent {
      width: 100%;
      height: 100%;
      padding: 36px 74px;
      background: #ffffff;
      border-radius: 16px;
      .title {
        display: flex;
        align-items: center;
        padding-left: 16px;
        margin-bottom: 30px;
        width: 100%;
        height: 40px;
        background: #fafafa;
        border-left: 2px solid #3464e0;
        font-family: Microsoft YaHei;
        font-weight: bold;
        font-size: 16px;
        color: #0b1a44;
      }

      ::v-deep {
        .monopoly {
          width: 100%;
          .el-form-item__content {
            width: calc(100% - 186px);
            .el-textarea__inner {
              height: 140px;
              border-radius: 4px 4px 4px 4px;
              border: 1px solid #d8dbe1;
            }
          }
        }
        .el-form-item__label {
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: bold;
          font-size: 14px;
          color: #0b1a44;
        }
        .half {
          width: 50%;
          margin-right: 0;
          .el-form-item__content {
            width: calc(100% - 186px);
            .el-input {
              width: 367px;
              color: #0b1a44;
            }
            .el-date-editor {
              .el-input__inner {
                background: #e9ebef;
              }
            }
          }
        }
      }

      .subButton {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 50px;
        span {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 114px;
          height: 38px;
          font-size: 14px;
          border-radius: 4px;
          cursor: pointer;
          i {
            margin-right: 7px;
          }
        }
        & > .closeClass {
          background: #f2f4ff;
          color: #868b9f;
          i {
            color: #868b9f;
          }
          &:hover {
            background: #e8ebf8;
            i {
              color: #888da1;
            }
          }
        }
        & > .submitClass {
          margin-left: 32px;
          background: #3464e0;
          color: #ffffff;
          i {
            color: #ffffff;
          }
          &:hover {
            background: #355fce;
          }
        }
      }
    }
  }
  ::v-deep {
    .el-dialog {
      height: 250px;
      border-radius: 8px;
      .el-dialog__header {
        border-bottom: 1px solid #eeeeef;
        header {
          display: flex;
          align-items: center;
          img {
            width: 30px;
            height: 30px;
          }
          span {
            margin-left: 6px;
            font-family: Microsoft YaHei, Microsoft YaHei;
            font-weight: 400;
            font-size: 16px;
            color: #303a40;
          }
        }
      }
      .el-dialog__body {
        padding-top: 30px;
        padding-bottom: 0;
        .body {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          p {
            margin-bottom: 20px;
            font-family: Microsoft YaHei, Microsoft YaHei;
            font-weight: 400;
            font-size: 16px;
            color: #303a40;
            line-height: 32px;
          }
          .el-checkbox__inner {
            width: 16px;
            height: 16px;
          }
          .el-checkbox__label {
            color: #4a4a4a;
            font-size: 16px;
          }
          .el-checkbox-group {
            .is-checked {
              .el-checkbox__inner {
                background: #355fce;
                border-color: #355fce;
                &::after {
                  left: 5px;
                  top: 2px;
                }
              }
              .el-checkbox__label {
                color: #4a4a4a;
              }
            }
          }
        }
      }
      .el-dialog__footer {
        padding-top: 30px;
        & > div {
          display: flex;
          justify-content: center;
          .el-button {
            padding: 0;
            width: 114px;
            height: 38px;
            border-radius: 4px;
            border: none;

            &:first-of-type {
              margin-right: 10px;
              background: #f2f4ff;
              color: #868b9f;
              &:hover {
                color: #868b9f;
              }
            }
          }
        }
      }
    }
  }
}
</style>
