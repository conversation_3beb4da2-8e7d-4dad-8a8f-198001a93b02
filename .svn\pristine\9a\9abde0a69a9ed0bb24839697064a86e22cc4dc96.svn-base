<template>
  <div class="ContractInfo">
    <aside>
      <section :class="{ checkedInfo: current === 0 }" @click="current = 0">
        <img src="@/assets/contractNew/icon1.png" alt="" />
        <span>合同基本信息</span>
      </section>
      <section :class="{ checkedInfo: current === 1 }" @click="current = 1">
        <img src="@/assets/contractNew/icon2.png" alt="" />
        <span>合同供货信息</span>
      </section>
      <section :class="{ checkedInfo: current === 2 }" @click="current = 2">
        <img src="@/assets/contractNew/icon3.png" alt="" />
        <span>合同施工信息</span>
      </section>
      <section :class="{ checkedInfo: current === 3 }" @click="current = 3">
        <img src="@/assets/contractNew/icon4.png" alt="" />
        <span>合同货款信息</span>
      </section>
      <el-button v-if="!disabledOperate && organizationId == '5973411'" type="primary" class="finishButton" @click="finish">完成合同</el-button>
    </aside>
    <section class="infoBox">
      <div v-show="current === 0" class="baseInfo">
        <section>
          <label>合同名称: </label>
          <span>{{ info.contractName }}</span>
        </section>
        <section>
          <label>合同编号: </label>
          <span>{{ info.contractCode }}</span>
        </section>
        <section>
          <label>客户名称: </label>
          <span>{{ info.customerName }}</span>
        </section>
        <section>
          <label>签订时间: </label>
          <span>{{ info.signingTime }}</span>
        </section>
        <section>
          <label>销售员: </label>
          <span>{{ info.realName }}</span>
        </section>
        <section class="finalize">
          <label>定稿合同: </label>
          <el-button v-if="info.isUpload === 1" type="primary" size="small">已完成</el-button>
          <span v-else-if="info.isUpload !== 1 && disabledOperate" style="color: #000000">未上传</span>
          <span v-else-if="keyList.includes('contractBase')" @click="uploadFile(19)"> <i class="el-icon-upload2"></i>上传</span>
        </section>
      </div>
      <!-- 供货信息 -->
      <div v-show="current === 1" class="supplyInfo">
        <div>
          <section>
            <label>软件内容: </label>
            <span>{{ info.supplySoft ? info.supplySoft : '暂无' }}</span>
          </section>
          <section>
            <label>软件交付时间: </label>
            <span>{{ info.confirmPaySoftTime ? info.confirmPaySoftTime : '暂无' }}</span>
          </section>
          <section>
            <label>硬件内容: </label>
            <span>{{ info.supplyHard ? info.supplyHard : '暂无' }}</span>
          </section>
          <section>
            <label>硬件交付时间: </label>
            <span>{{ info.confirmPayHardTime ? info.confirmPayHardTime : '暂无' }}</span>
          </section>
          <section>
            <label>硬件资金预算: </label>
            <span>{{ info.hardMoney ? info.hardMoney : '暂无' }}</span>
          </section>
        </div>
        <span v-if="keyList.includes('contractSupply')" class="lookAll" @click="look(1)">查看更多 <i class="el-icon-arrow-right"></i></span>
      </div>
      <!-- 合同施工信息 -->
      <div v-show="current === 2" class="constructionInfo">
        <template v-if="constructionInfo">
          <section>
            <label>施工时间: </label>
            <span>{{ constructionInfo.constructionTime ? constructionInfo.constructionTime : '暂无' }}</span>
          </section>
          <div class="half">
            <section>
              <label>施工人员: </label>
              <span>{{ constructionInfo.user ? constructionInfo.user : '暂无' }}</span>
            </section>
            <section>
              <label>施工方式: </label>
              <span>{{ constructionInfo.phone ? constructionInfo.phone : '暂无' }}</span>
            </section>
          </div>
          <div class="half">
            <section>
              <label>客户联系人: </label>
              <span>{{ constructionInfo.contactsName ? constructionInfo.contactsName : '暂无' }}</span>
            </section>
            <section>
              <label>是否培训: </label>
              <span>{{ constructionInfo.isTrain ? '是' : '否' }}</span>
            </section>
          </div>
          <section>
            <label>联系方式: </label>
            <span>{{ constructionInfo.phone ? constructionInfo.phone : '暂无' }}</span>
          </section>
          <section>
            <label>施工地址: </label>
            <span>{{ constructionInfo.address ? constructionInfo.address : '暂无' }}</span>
          </section>
          <section class="finalize">
            <label>培训现场图: </label>
            <span v-if="!disabledOperate && keyList.includes('contractConstruction')" @click="uploadFile(21)"> <i class="el-icon-upload2"></i> 上传</span>
            <span v-else style="color: #000">在合同附件信息中查看</span>
          </section>
          <section class="finalize">
            <label>培训签字单: </label>
            <span v-if="!disabledOperate && keyList.includes('contractConstruction')" @click="uploadFile(17)"> <i class="el-icon-upload2"></i> 上传</span>
            <span v-else style="color: #000">在合同附件信息中查看</span>
          </section>
        </template>
        <span v-if="keyList.includes('contractConstruction')" class="lookAll" @click="look(2)">查看更多 <i class="el-icon-arrow-right"></i></span>
      </div>
      <!-- 合同货款信息 -->
      <div v-show="current === 3" class="loansInfo">
        <template v-if="moneyDetailDto">
          <section>
            <label>投标保证金: </label>
            <span>{{ moneyDetailDto.bidMoney ? moneyDetailDto.bidMoney : 0 }}</span>
          </section>
          <section>
            <label>质保金: </label>
            <span>{{ moneyDetailDto.qualityMoney ? moneyDetailDto.qualityMoney : 0 }}</span>
          </section>
          <section>
            <label>履约保证金:</label>
            <span>{{ moneyDetailDto.performanceMoney ? moneyDetailDto.performanceMoney : 0 }}</span>
          </section>
          <section>
            <label>剩余货款:</label>
            <span>{{ moneyDetailDto.remainMoney ? moneyDetailDto.remainMoney : 0 }}</span>
          </section>
          <section>
            <label>最近更新时间:</label>
            <span>{{ moneyDetailDto.updateTime ? moneyDetailDto.updateTime : '暂无' }}</span>
          </section>
        </template>
        <span v-if="organizationId == '5973411'" class="lookAll" @click="look(3)">查看更多 <i class="el-icon-arrow-right"></i></span>
      </div>
    </section>
    <!-- 定稿合同 培训现场图 培训签字单 上传 -->
    <el-dialog :title="uploadFileDialogTitle" :visible.sync="finalizeFileDialog" width="420px" @close="dialogClose">
      <el-upload
        drag
        :action="action"
        :multiple="belongType !== 19"
        :headers="header"
        :data="dataObj"
        :file-list="fileList"
        :before-upload="beforeUpload"
        :on-success="uploadSuccess"
        :on-remove="uploadRemove"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div slot="tip" class="el-upload__tip" style="color: red; line-height: 15px">只能上传一个文件、 上传类型: .doc .docx .xls .xlsx .pdf .jpg .png .zip格式的附件，且大小不超过50MB</div>
      </el-upload>
      <div slot="footer">
        <el-button @click="finalizeFileDialog = false">取 消</el-button>
        <el-button type="primary" @click="finalizeFileConfirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { meetingSaveFile } from '@/api/meeting'
import { mapGetters } from 'vuex'
import { contractReceiveJudgeOver, contractReceiveOver } from '@/api/contractNew'
export default {
  name: '',
  props: {
    info: {
      type: Object,
      default: () => {
        return {}
      }
    },
    contractId: {
      type: String,
      default: '0'
    },
    type: {
      type: Number,
      default: 1
    }
  },

  data() {
    return {
      current: 0,
      uploadFileDialogTitle: '定稿合同',
      finalizeFileDialog: false, // 上传定稿合同
      action: window.config.VUE_APP_UPLOAD_Library_URL + '/system/upload/file',
      header: {
        Authorization: null
      },
      fileList: [],
      dataObj: {
        fileName: ''
      },
      belongType: null
    }
  },
  computed: {
    ...mapGetters(['token', 'keyList', 'organizationId']),
    constructionInfo() {
      return this.info.constructionDetailDto
    },
    moneyDetailDto() {
      return this.info.moneyDetailDto
    },
    disabledOperate() {
      return this.type === 2
    }
  },
  created() {},
  methods: {
    uploadFile(type) {
      if (type === 19) {
        this.uploadFileDialogTitle = '定稿合同'
      } else if (type === 21) {
        this.uploadFileDialogTitle = '培训现场图'
        this.fileList = this.constructionInfo.sceneFiles.map((file) => {
          return {
            name: file.fileName,
            size: file.fileSize * 1024,
            url: file.fileUrl,
            belongType: file.belongType
          }
        })
      } else if (type === 17) {
        this.uploadFileDialogTitle = '培训签字单'
        this.fileList = this.constructionInfo.signFiles.map((file) => {
          return {
            name: file.fileName,
            size: file.fileSize * 1024,
            url: file.fileUrl
          }
        })
      }

      this.belongType = type
      this.finalizeFileDialog = true
    },
    // 添加合同- 上传前触发事件
    beforeUpload(file) {
      this.dataObj.fileName = 'contract/' + file.name
      this.header.Authorization = `Bearer ${this.token}`
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isDOC = file.type === 'application/msword'
      const isDOCX = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      const isXLS = file.type === 'application/vnd.ms-excel'
      const isXLSX = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      const isPDF = file.type === 'application/pdf'
      const isZIP = file.type === 'application/x-zip-compressed'
      const isLt2M = file.size / 1024 / 1024 < 50
      if (!isLt2M) {
        this.$message.error('上传附件大小不能超过 50MB!')
      }
      if (!isJPG && !isPNG && !isDOC && !isDOCX && !isXLS && !isXLSX && !isPDF && !isZIP) {
        this.$message.warning('只能上传.doc .docx .xls .xlsx .pdf .jpg .png .zip 格式的附件')
      }
      return (isJPG || isPNG || isDOC || isDOCX || isXLS || isXLSX || isPDF || isZIP) && isLt2M
    },
    // 添加合同- 上传成功事件
    uploadSuccess(res, file, fileList) {
      if (res.code !== 200) {
        this.$message.error(res.message)
      } else {
        if (this.belongType === 19) {
          this.fileList = [file]
        } else {
          this.fileList = fileList
        }
      }
      console.log(file)
    },
    // 添加合同- 上传删除事件
    uploadRemove(file, fileList) {
      this.fileList = fileList
    },
    async finalizeFileConfirm() {
      if (!this.fileList.length) return this.$message.warning('暂未上传附件')
      if (this.belongType === 19) {
        const file = this.fileList[0]
        const fileInfo = {
          fileName: file.name,
          fileSize: Math.floor(file.size / 1024),
          fileUrl: file.response.data[0],
          belongType: this.belongType,
          meetingId: parseInt(this.contractId)
        }
        await meetingSaveFile([fileInfo])
      } else {
        const list = this.fileList.map((file) => {
          return {
            fileName: file.name,
            fileSize: Math.floor(file.size / 1024),
            fileUrl: file.belongType ? file.url : file.response.data[0],
            belongType: this.belongType,
            meetingId: parseInt(this.contractId)
          }
        })
        await meetingSaveFile(list)
      }
      this.finalizeFileDialog = false
      this.$message.success('上传成功!')
      this.$emit('refreshData')
    },
    dialogClose() {
      this.fileList = []
    },
    look(type) {
      if (type === 1) {
        this.$router.push(`/newContract/supplyInfo/${this.contractId}`)
      } else if (type === 3) {
        this.$router.push(`/newContract/constructionInfo/${this.contractId}/${this.moneyDetailDto.moneyId}`)
      }
    },
    // 完成合同
    finish() {
      contractReceiveJudgeOver({ contractId: this.contractId }).then((res) => {
        if (res.data === 1) {
          this.$confirm('确定完成吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(() => {
              const loading = this.$loading({
                text: '数据保存中，请稍后...',
                background: 'rgba(0,0,0,0.7)'
              })
              contractReceiveOver({ contractId: this.contractId })
                .then((res) => {
                  loading.close()
                  this.$message.success('数据保存成功！')
                  this.$router.push('/newContract')
                })
                .catch(() => {
                  loading.close()
                })
            })
            .catch(() => {
              this.$message({
                type: 'info',
                message: '已取消'
              })
            })
        } else {
          this.$message.error('当前无法结束合同，请检查合同未完成内容！')
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.ContractInfo {
  display: flex;
  aside {
    position: relative;
    section {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 171px;
      height: 50px;
      border-radius: 4px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #666666;
      cursor: pointer;
      img {
        width: 40px;
        height: 40px;
        margin-right: 5px;
        object-fit: scale-down;
      }
    }
    .checkedInfo {
      background: #3465df;
      color: #ffffff;
    }
    .finishButton {
      position: absolute;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
    }
  }
  .infoBox {
    flex: 1;
    height: 453px;
    margin-left: 10px;
    background: #f0f3fc;
    border-radius: 4px;
    border: 1px solid #3465df;
    .baseInfo,
    .supplyInfo,
    .constructionInfo,
    .loansInfo {
      position: relative;
      width: 100%;
      height: 100%;
      padding: 30px 20px;
      overflow: auto;
      &::-webkit-scrollbar {
        width: 3px;
      }
      &::-webkit-scrollbar-thumb {
        width: 3px;
        border-radius: 3px;
        background: #3a6ff4;
      }

      section {
        display: flex;
        margin-top: 25px;
        &:first-of-type {
          margin-top: 0;
        }
        label {
          min-width: 95px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 18px;
          color: #656565;
          text-align: right;
        }
        span {
          margin-left: 15px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 18px;
          color: #000000;
        }
      }
      .finalize {
        label {
          margin-right: 15px;
        }
        span {
          margin-left: 0;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 18px;
          color: #3a6ff4;
          cursor: pointer;
        }
      }
    }
    .supplyInfo {
      padding-right: 0;
      padding-top: 0;
      padding-bottom: 40px;
      & > div {
        width: 100%;
        height: 100%;
        padding-top: 30px;
        overflow: auto;
        &::-webkit-scrollbar {
          width: 3px;
        }
        &::-webkit-scrollbar-thumb {
          width: 3px;
          border-radius: 3px;
          background: #3a6ff4;
        }
      }
      section {
        label {
          width: 115px;
        }
      }
    }
    .constructionInfo {
      .half {
        display: flex;
        align-items: center;
        section {
          margin-top: 25px;
          &:first-of-type {
            margin-right: 15px;
          }
        }
      }
    }
    .loansInfo {
      section {
        label {
          min-width: 110px;
        }
      }
    }
    .lookAll {
      position: absolute;
      left: 50%;
      bottom: 10px;
      transform: translateX(-50%);
      display: flex;
      align-items: center;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #666666;
      cursor: pointer;
    }
  }
}
::v-deep {
  .el-dialog {
    .el-dialog__header {
      font-family: Microsoft YaHei;
      font-weight: bold;
      font-size: 18px;
      color: #0b1a44;
    }
    .el-upload,
    .el-upload-dragger {
      width: 100%;
    }
  }
}
</style>
