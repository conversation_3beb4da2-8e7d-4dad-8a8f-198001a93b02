<template>
  <div class="app-container">
    <div class="top">
      <i class="el-icon-location"></i>
      <span>当前位置：<router-link to="/process/management">流程管理</router-link> /</span>
      <span>流程详情</span>
    </div>
    <div class="process_details_content">
      <div class="content_left">
        <img src="@/assets/process/detailsTitle.png" alt="" />
        <el-timeline>
          <el-timeline-item v-for="item in processDetails.detailsTaskDtoList" :key="item.taskId" type="primary" placement="top">
            <template v-slot:dot>
              <img src="@/assets/process/dot.png" alt="" />
            </template>

            <div class="info">
              <div class="title" style="margin-top: 8px">
                <span v-if="item.username"> {{ item.username }}</span>
                <span v-else>未找到审批人</span>

                <template>
                  <span v-if="item.taskResult === 'AGREE'" class="status success">同意</span>
                  <span v-if="item.taskResult === 'REFUSE'" class="status error">拒绝</span>
                  <span v-if="item.taskStatus === 'RUNNING'" class="status conduct">处理中</span>
                </template>
              </div>
              <div class="time">{{ item.createTime }}</div>
              <div v-if="item.records.length >= 1" class="records">
                <el-collapse v-model="item.activeNames">
                  <el-collapse-item :title="`审批意见(${item.records.length}条)`" name="1">
                    <div v-for="list in item.records" :key="list.date" class="remark">
                      <span>{{ list.date }}</span>
                      <el-tooltip v-if="list.remark" class="item" effect="dark" :content="list.remark" placement="bottom">
                        <span v-if="list.remark">{{ list.remark }}</span>
                      </el-tooltip>
                      <span v-else>暂无评论</span>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
      <div v-if="processDetails" class="content_right">
        <div class="title">员工{{ processDetails.processName }}申请单</div>
        <el-descriptions class="margin-top" :column="1" border>
          <el-descriptions-item label="发起人">{{ processDetails.username }}</el-descriptions-item>
          <el-descriptions-item label="发起时间">{{ processDetails.createTime }}</el-descriptions-item>
          <el-descriptions-item label="事项类型">
            {{ processDetails.processName }}
          </el-descriptions-item>
          <el-descriptions-item label="事项名称">{{ processDetails.title }}</el-descriptions-item>
          <el-descriptions-item v-if="processDetails.status" label="状态">
            {{ processDetails.status | processStatus }}
          </el-descriptions-item>
          <el-descriptions-item v-for="item in formDetails" :key="item.formId" :label="item.name">
            <span v-if="item.componentType !== 'DDPhotoField' && item.componentType !== 'DDAttachment' && item.componentType !== 'TableField' && item.value !== 'null'">{{ item.value }}</span>
            <div v-if="item.componentType === 'DDPhotoField'">
              <el-image v-for="list in item.value" :key="list" style="width: 100px" :src="list" :preview-src-list="item.value"> </el-image>
            </div>
            <div v-if="item.componentType === 'DDAttachment' && item.value !== 'null'" type="primary" :href="item.value">
              <a v-for="list in item.value" :key="list.url" class="fileUrl" :href="list.url" target="blank" style="color: #3465df">{{ list.url }}</a>
            </div>
            <el-descriptions v-if="item.componentType === 'TableField' && item.value[0].rowValue.length >= 1" :column="1" border size="small">
              <el-descriptions-item v-for="list in item.value[0].rowValue" :key="list.label" :label="list.label">{{ list.value }}</el-descriptions-item>
            </el-descriptions>
          </el-descriptions-item>
        </el-descriptions>
        <!-- 结果状态 -->
        <template>
          <img v-if="processDetails.status === 'RUNNING'" src="@/assets/process/examine1_big.png" alt="" class="result" />
          <img v-if="processDetails.status === 'TERMINATED'" src="@/assets/process/examine3_big.png" alt="" class="result" />
          <img v-if="processDetails.result === 'agree' && processDetails.status === 'COMPLETED'" src="@/assets/process/examine2_big.png" alt="" class="result" />
          <img v-if="processDetails.result === 'refuse'" src="@/assets/process/examine0_big.png" alt="" class="result" />
        </template>

        <div v-if="processDetails.isReview === '1'" style="margin-top: 24px" class="examine_approve">
          <el-input v-model="formInfo.remark" type="textarea" placeholder="请输入审批意见，200字以内" maxlength="200" class="textAreaIput"></el-input>
          <span class="cancelButton" @click="$router.push('/process/management')">返回</span>
          <span class="agreeButton" @click="approval('agree')">
            <i class="el-icon-circle-check"></i>
            同意</span>
          <span class="refuseButton" @click="approval('refuse')">
            <i class="el-icon-circle-close"></i>
            拒绝</span>
          <!-- <span class="confirmButton" @click="approval">确定</span> -->
        </div>
        <el-row v-else :gutter="10" type="flex" justify="center" style="margin-top: 24px">
          <el-col :span="1.5">
            <span class="gobackButton" @click="$router.push('/process/management')"> 返 回</span>
          </el-col>
        </el-row>
      </div>
    </div>
    <div v-show="showButton" class="previous" @click="last"><i class="el-icon-back"></i></div>
    <div v-show="showButton" class="next" @click="next"><i class="el-icon-right"></i></div>
  </div>
</template>

<script>
import { details, execute, instanceList } from '@/api/process'
import { formatDate } from '@/filters'

export default {
  name: 'ProcessDetails',
  data() {
    return {
      processDetails: {},
      formDetails: [],
      formInfo: {
        processInstanceId: null,
        remark: null,
        result: null,
        actionerUserid: null,
        taskId: null,
        processCode: null
      },
      rules: {
        result: [
          {
            required: true,
            tigger: 'change',
            message: '请选择审批结果'
          }
        ]
      },
      showButton: false
    }
  },
  created() {
    this.getDetails()
  },
  mounted() {
    setTimeout(() => {
      this.showButton = true
    }, 500)
  },

  methods: {
    async getDetails() {
      const { data } = await details({ processInstanceId: this.$route.params.processInstanceId })
      this.processDetails = data
      this.formDetails = data.tprocessInstanceForms
      this.processDetails.detailsTaskDtoList.forEach((item) => {
        this.$set(this.processDetails.detailsTaskDtoList, 'activeNames', [])
      })
      // 判断是否是请假和加班，是则不能显示图片和加班核算方式
      if (this.processDetails.processCode === 'PROC-EF6Y0XWVO2-6NM7HFQKO53QG2RZF1AY2-94N8LRKI-386') {
        this.formattingDescribe('请假')
      } else if (this.processDetails.processCode === 'PROC-G5SKLVFV-R6VQPWDLP8QPEV1Z1GZY1-QXMNC4BJ-1') {
        this.formattingDescribe('加班')
      }
      this.formDetails.forEach((item) => {
        if (item.componentType === 'DDHolidayField' || item.componentType === 'DDDateField' || item.componentType === 'DDDateRangeField' || item.componentType === 'DDGooutField') {
          // 开始时间-结束时间格式处理
          if (item.componentType === 'DDDateRangeField' || item.componentType === 'DDGooutField' || item.componentType === 'DDHolidayField') {
            item.name = JSON.parse(item.name).join(' - ')
            item.value = `${JSON.parse(item.value)[0]} - ${JSON.parse(item.value)[1]}`
          }
          // 附件格式问题
        } else if (item.componentType === 'DDAttachment') {
          item.value = JSON.parse(item.value)
        } else if (item.componentType === 'DDPhotoField') {
          item.value = JSON.parse(item.value)
          // 补卡申请时间格式处理
        } else if (item.name === 'repairCheckTime') {
          item.name = '补卡申请时间'
          item.value = formatDate(new Date(parseInt(item.value)))
          // 发票类型格式处理
        } else if (item.componentType === 'DDMultiSelectField') {
          item.value = JSON.parse(item.value).join(',')
          // 协作明细格式处理
        } else if (item.componentType === 'TableField') {
          item.value = JSON.parse(item.value)
          console.log(item.value)
        }
      })
      this.formDetails = this.formDetails.filter((item) => {
        return item.componentType !== 'DDBizSuite' && item.componentType !== 'TableField '
      })
    },
    async approval(result) {
      if (result) {
        this.formInfo.result = result
      }
      this.formInfo.processInstanceId = this.processDetails.processInstanceId
      this.formInfo.processCode = this.processDetails.processCode
      this.formInfo.actionerUserid = this.processDetails.detailsTaskDtoList[this.processDetails.detailsTaskDtoList.length - 1].userId
      this.formInfo.taskId = this.processDetails.detailsTaskDtoList[this.processDetails.detailsTaskDtoList.length - 1].taskId
      await execute(this.formInfo)
      this.$message.success('审批成功')
      this.$router.push('/process/management')
    },
    // 去掉请假的图片和加班的核算方式
    formattingDescribe(type) {
      if (type === '请假') {
        this.formDetails = this.formDetails.filter((item) => item.componentType !== 'DDPhotoField')
      } else {
        const val = JSON.parse(this.formDetails.filter((item) => item.name === '加班')[0].value)
        const startTime = val[3]
        const endTime = val[4]
        const duration = val[6]
        const arr = [
          {
            name: startTime.props.label,
            value: startTime.value
          },
          {
            name: endTime.props.label,
            value: endTime.value
          },
          {
            name: `加班${duration.props.label}`,
            value: `${duration.value}小时`
          }
        ]
        this.formDetails = this.formDetails.filter((item) => item.componentType !== 'DDPhotoField' && item.componentType !== 'DDSelectField')
        arr.forEach((item) => {
          this.formDetails.push(item)
        })
      }
    },
    // 上一个
    async last() {
      const { data } = await instanceList({ pageSize: 2000, pageNum: 1 })
      console.log(this.$route)
      const index = data.list.findIndex((item) => {
        return item.processInstanceId === this.$route.params.processInstanceId
      })
      if (index === 0) {
        return this.$message.warning('这是第一条数据')
      }
      const processInstanceId = data.list[index - 1].processInstanceId
      this.$router.push(`/process/management/details/${processInstanceId}`)
    },
    // 下一个
    async next() {
      const { data } = await instanceList({ pageSize: 2000, pageNum: 1 })
      const index = data.list.findIndex((item) => {
        return item.processInstanceId === this.$route.params.processInstanceId
      })

      if (index === data.list.length) {
        return this.$message.warning('这是最后一条数据')
      }

      const processInstanceId = data.list[index + 1].processInstanceId
      this.$router.push(`/process/management/details/${processInstanceId}`)
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  position: relative;
  padding-top: 15px;
  padding-left: 5px;
  background-color: #e8eaed;
  min-height: 100%;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;

  .top {
    display: flex;
    align-items: center;
    padding-left: 32px;
    background: #335bc4;
    height: 44px;
    width: 100%;
    border-radius: 10px 10px 0 0;
    i,
    span:first-of-type {
      font-size: 14px;
      color: #c2ceed;
    }
    span:last-of-type {
      color: #fff;
      font-size: 14px;
      margin-left: 5px;
    }
  }
  .process_details_content {
    padding-top: 45px;
    padding-left: 85px;
    padding-bottom: 85px;
    border-radius: 0 0 6px 6px;
    display: flex;
    justify-content: flex-start;

    background-color: #fff;
    .content_left {
      position: relative;
      width: 300px;
      min-height: 600px;
      padding-top: 63px;
      padding-left: 43px;
      background-color: #f5f6fd;
      border-radius: 14px;
      & > img {
        position: absolute;
        top: -13px;
        left: 0;
      }
      .info {
        position: relative;

        .title {
          font-size: 16px;
          color: #0b1a44;
          margin-bottom: 12px;
          .status {
            position: absolute;
            top: -3px;
            margin-left: 8px;
            padding: 3px 5px;
            color: #fff;
          }
          .success {
            background-color: #23bb87;
          }
          .conduct {
            background-color: #ffaa70;
          }
          .error {
            background-color: #eb6557;
          }
        }
        .time {
          font-size: 14px;
          color: #a3a8bb;
        }
        .records {
          ::v-deep {
            .el-collapse {
              border: none;
              .el-collapse-item__header {
                border: none;
                background: transparent;
                font-size: 14px;
                color: #a3a8bb;
                .el-collapse-item__arrow {
                  position: relative;
                  margin: initial;
                  margin-left: 5px;
                  width: 15px;
                  height: 15px;
                  border: 1px solid #3464e0;
                  border-radius: 50%;
                  text-align: center;
                  color: #3464e0;
                  &::before {
                    position: absolute;
                    left: 54%;
                    top: 54%;
                    transform: translate(-50%, -50%);
                    line-height: 10px;
                    font-size: 12px;
                  }
                }
              }
              .el-collapse-item__header.focusing:focus:not(:hover) {
                color: #3464e0;
              }
              .el-collapse-item__wrap {
                background: #f5f6fd;
                border: none;
                .el-collapse-item__content {
                  color: #a3a8bb;
                  .remark {
                    display: flex;
                    flex-direction: column;
                  }
                }
              }
            }
          }
          .remark {
            margin-bottom: 10px;
          }
        }
      }
      ::v-deep {
        .el-timeline {
          .el-timeline-item {
            position: relative;
            .el-timeline-item__tail {
              top: 6px;
              border-left-style: dotted;
              border-width: 2px;
              border-color: #3464e0;
            }
          }
          .el-timeline-item__dot {
            position: absolute;
            top: 0;
          }
          .el-timeline-item__timestamp {
            display: none;
          }
        }
      }
    }
    .content_right {
      position: relative;
      width: 1000px;
      margin-left: 116px;
      .title {
        height: 48px;
        line-height: 48px;
        background-color: #d3e7ff;
        font-size: 16px;
        font-weight: bold;
        color: #0b1a44;
        text-align: center;
      }
      .fileUrl {
        &:hover {
          text-decoration: underline;
        }
      }
      .result {
        position: absolute;
        right: 0;
        top: 0;
        width: 110px;
        height: 110px;
      }
      .examine_approve {
        span {
          display: inline-block;
          cursor: pointer;
          width: 102px;
          height: 40px;
          margin-top: 24px;
          margin-right: 24px;
          border-radius: 4px;
          text-align: center;
          line-height: 40px;
          font-size: 14px;
          font-weight: bold;
          color: #fff;
        }
        .cancelButton {
          background: #f4f7fd;
          color: #657081;
        }
        .agreeButton {
          background: #23bb87;
        }
        .refuseButton {
          background: #eb6557;
        }
        .confirmButton {
          background: #3464e0;
        }
        .textAreaIput {
          ::v-deep {
            .el-textarea__inner {
              height: 128px;
              background: #f5f5f5;
              border-color: #eee;
            }
          }
        }
      }
      .gobackButton {
        display: inline-block;
        width: 102px;
        height: 40px;
        padding: 0;
        border-radius: 4px;
        text-align: center;
        line-height: 40px;
        font-size: 14px;
        font-weight: bold;
        background: #f4f7fd;
        color: #657081;
        border: none;
        cursor: pointer;
      }
      ::v-deep {
        .el-descriptions-item__content {
          font-size: 16px;
          color: #0b1a44;
        }
        .el-descriptions .is-bordered .el-descriptions-item__cell {
          padding: 18px 24px;
        }
        .el-descriptions-item__label.is-bordered-label {
          font-size: 16px;
          color: #868b9f;
          background: #f4f7fb;
        }
      }
    }
    // .records {
    //   margin-bottom: 15px;
    //   border-radius: 8px;
    //   box-shadow: 0 0 2px #999;
    //   padding: 10px;
    //   box-sizing: border-box;
    //   .remark {
    //     width: 510px;
    //     display: flex;
    //     justify-content: space-between;
    //     align-items: flex-end;
    //     span {
    //       min-width: 129px;
    //     }
    //   }
    // }
  }
  .previous,
  .next {
    position: absolute;
    top: 400px;
    width: 50px;
    height: 50px;
    line-height: 50px;
    border-radius: 50%;
    background: #fff;
    box-shadow: 0 0 5px #a3a8bb;
    text-align: center;
    font-size: 28px;
    color: #3465df;
    cursor: pointer;
  }
  .previous {
    left: 20px;
  }
  .next {
    right: 40px;
  }
}

::v-deep {
  .el-descriptions-item__container {
    display: flex !important;
    // align-items: center;
  }
  .el-timeline {
    padding-left: 0;
  }
}
</style>
