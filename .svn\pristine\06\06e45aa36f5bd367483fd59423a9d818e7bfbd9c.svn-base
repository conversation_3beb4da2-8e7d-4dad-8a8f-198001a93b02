<!-- eslint-disable vue/require-v-for-key -->

<template>
  <div class="real_filecont_new">
    <div class="fils_main_scroll">
      <div class="files_btns_new clearfix">
        <div class="files_input_new clearfix">
          <div class="real_inputitem more">
            <div class="classroom_right_inputitem">
              <input v-model="searchForm.resourceName" placeholder="素材名称" @keyup.enter="currentChange(1)" />
              <img src="@/assets/resources/classroom_right_inputitem.png" class="classroom_right_inputicon like_btn" @click="currentChange(1)" />
            </div>
          </div>
        </div>
        <div class="files_add_btns l clearfix">
          <div class="files_add_types">
            <div class="files_add_type" @click="resourceTypeChange({ value: '' })"><i class="el-icon-s-unfold" /> 全部</div>
            <div
              v-for="item in resourceTypes"
              :key="item.value"
              class="files_add_type"
              :class="searchForm.resourceType == item.value ? 'active' : ''"
              :label="item.value"
              @click="resourceTypeChange(item)"
            >
              <i v-if="item.value == 'PICTURE'" class="el-icon-picture-outline" />
              <i v-if="item.value == 'VIDEO'" class="el-icon-video-camera" />
              <i v-if="item.value == 'LINK'" class="el-icon-link" />
              <i v-if="item.value == 'FILE'" class="el-icon-document" />
              <IconExperiment v-if="item.value == 'VR'" />
              <IconEeg v-if="item.value == 'DICOM'" /> {{ item.name }}
            </div>
          </div>
        </div>
      </div>
      <div class="fils_list initial_table fils_list_new beautifulScroll">
        <div class="real_label clearfix">
          <div class="fils_nav_new">
            <div class="fils_navarr_new">
              <div class="fils_navarritem" :class="navs.length > 0 && activeIndex > -1 ? 'active' : ''" @click="backInit()">
                <img src="@/assets/resources/fils_navarritem1.png" class="active" />
                <img src="@/assets/resources/fils_navarritem10.png" class="normal" />
              </div>
              <div class="fils_navarritem" :class="navs.length > 0 && activeIndex < navs.length - 1 ? 'active' : ''" @click="prueInit()">
                <img src="@/assets/resources/fils_navarritem2.png" class="active" />
                <img src="@/assets/resources/fils_navarritem20.png" class="normal" />
              </div>
            </div>
            <span class="pointer" @click="backAll">我的收藏</span>
            <template v-for="(item, index) in navs">
              <span v-if="index <= activeIndex" :class="index > -1 && index < navs.length - 1 ? 'pointer' : ''" @click="backIndex(item, index)">
                <i class="el-icon-arrow-right"></i>{{ item.resourceName }}
              </span>
            </template>
          </div>
        </div>
        <emptymain :emptytext="'暂无收藏'" :emptyheight="'615px'" :emptylist="dataList">
          <img src="@/assets/resources/files_empty.png" />
        </emptymain>
        <div class="clearfix">
          <template v-for="(item, index) in dataList">
            <div class="fils_item_index" @click.stop>
              <div
                v-show="resourceId != item.resourceId && !multipleType"
                v-filecontextmenu="getMenus(item)"
                class="fils_itemcover_new"
                @click.stop="setItem(item)"
                @click.right.stop="showMenu(item)"
              ></div>
              <div class="fils_item_indexicon">
                <img v-if="item.fileType == 1" src="@/assets/resources/fils_nametype1new.png" />
                <img v-if="item.fileType == 2 && item.resourceType == 'VIDEO'" src="@/assets/resources/fils_nametype2new.png" />
                <img v-if="item.fileType == 2 && item.resourceType == 'PICTURE'" :src="item.url" />
                <img v-if="item.fileType == 2 && item.resourceType == 'LINK'" src="@/assets/resources/fils_nametype4new.png" />
                <img v-if="item.fileType == 2 && item.resourceType == 'VR'" src="@/assets/resources/fils_nametype5new.png" />
                <img v-if="item.fileType == 2 && item.resourceType == 'FILE'" src="@/assets/resources/fils_nametype6new.png" />
              </div>
              <div class="fils_item_newname text-ellipsis" @click.stop>
                <span v-if="resourceId != item.resourceId">
                  {{ getFileType(item) }}
                  <span>{{ item.resourceName }}</span>
                </span>
                <el-input v-if="resourceId == item.resourceId" ref="change_input" v-model="resourceName" class="change_input" @keyup.enter.native="checkEdit(index)"></el-input>
              </div>
              <div class="fils_item_indextime">
                <div class="fils_item_indexinfo_teacher">
                  <div class="fils_item_indexinfo_teachericon">
                    <img v-if="item.headPortraitUrl" :src="baseurl + item.headPortraitUrl" />
                    <img v-else src="@/assets/resources/user_infoicon.png" />
                  </div>
                  {{ item.teacherName }}
                </div>
                <div class="fils_item_index_time">
                  {{ item.collectTime }}
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div class="topic_pages">
        <el-pagination
          :hide-on-single-page="true"
          background
          :current-page="searchForm.pageNum"
          :page-size="searchForm.pageSize"
          layout="prev, pager, next, total, jumper"
          :total="searchForm.total"
          @current-change="currentChange"
        >
        </el-pagination>
      </div>
      <div v-if="addShow" class="normal_dialog_back">
        <div class="normal_dialog reource_link">
          <div class="normal_dialog_title">
            <span>添加外链</span>
            <div class="normal_dialog_close" @click="clearCheck()">
              <i class="el-icon-close"></i>
            </div>
          </div>
          <div class="normal_dialog_body beautifulScroll topic_dialog_body">
            <el-form ref="addForm" :model="addForm" :rules="rules">
              <el-form-item label="名称" :label-width="'80px'" prop="resourceName">
                <el-col :span="22">
                  <el-input v-model="addForm.resourceName" placeholder="请输入名称"></el-input>
                </el-col>
              </el-form-item>
              <el-form-item label="URL地址" :label-width="'80px'" prop="url">
                <el-col :span="22">
                  <el-input v-model="addForm.url" placeholder="请输入URL地址"></el-input>
                </el-col>
              </el-form-item>
            </el-form>
          </div>
          <div class="normal_dialog_foot">
            <div class="normal_dialog_foot3 like_btn" @click="clearCheck()">取消</div>
            <div class="normal_dialog_foot1 like_btn" @click="beforeCheck">确定</div>
          </div>
        </div>
      </div>
      <div v-if="deleteShow" class="normal_dialog_back">
        <div class="normal_dialog reource_delete">
          <div class="normal_dialog_title">
            <span><i class="el-icon-warning"></i>提示</span>

            <div class="normal_dialog_close" @click="clearDelete()">
              <i class="el-icon-close"></i>
            </div>
          </div>
          <div class="normal_dialog_body beautifulScroll">
            <div class="reource_delete1">该资源删除后，所有人员都将无法查看到此资源信息</div>
            <div class="reource_delete2">确定删除该资源吗？</div>
            <div class="reource_delete3">
              <span class="like_btn" @click="openAbout">查看关联记录</span>
            </div>
          </div>
          <div class="normal_dialog_foot">
            <div class="normal_dialog_foot3 like_btn" @click="clearDelete()">取消</div>
            <div class="normal_dialog_foot1 like_btn" @click="beforeDelete()">确定</div>
          </div>
        </div>
      </div>
      <div v-if="deleteShowAbout" class="normal_dialog_back">
        <div class="normal_dialog">
          <div class="normal_dialog_title">
            <span> <i class="el-icon-warning"></i> 素材关联记录</span>

            <div class="normal_dialog_close" @click="deleteShowAbout = false">
              <i class="el-icon-close"></i>
            </div>
          </div>
          <div class="normal_dialog_body beautifulScroll">
            <el-table :data="deleteShowList" border stripe height="100%" style="width: 750px; margin: 25px">
              <template #empty>
                <emptymain slot="empty" :emptytext="'暂无关联记录'" :emptyheight="'405px'" :emptylist="deleteShowList"> </emptymain>
              </template>
              <el-table-column prop="classroomName" label="课程名称" align="center"> </el-table-column>
              <el-table-column prop="classroomName" label="关联时间" align="center"> </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
      <div v-if="moveShow" class="normal_dialog_back">
        <div class="normal_dialog reource_move">
          <div class="normal_dialog_title">
            <span>移动</span>
            <div class="normal_dialog_close" @click="clearMove()">
              <i class="el-icon-close"></i>
            </div>
          </div>
          <div class="normal_dialog_body beautifulScroll reource_move_body">
            <div class="reource_move_tree">
              <el-tree ref="reourceTree" :data="moveList" :props="defaultProps" node-key="id" :expand-on-click-node="false" @node-click="handleNodeClick">
                <template #default="{ node, data }">
                  <div class="custom_tree_node"><img src="@/assets/resources/fils_nametype1.png" />{{ data.resourceName }}</div>
                </template>
              </el-tree>
            </div>
            <el-form ref="moveForm" :model="moveForm" :rules="rules">
              <el-form-item prop="moveSelectId">
                <el-input v-show="false" v-model="moveForm.moveSelectId"></el-input>
              </el-form-item>
            </el-form>
          </div>
          <div class="normal_dialog_foot">
            <div class="normal_dialog_foot3 like_btn" @click="clearMove()">取消</div>
            <div class="normal_dialog_foot1 like_btn" @click="beforeMove()">确定</div>
          </div>
        </div>
      </div>
      <div v-if="beforeuploadShow" class="normal_dialog_back">
        <div class="normal_dialog files_before_upload">
          <div class="normal_dialog_title">
            <span>上传附件</span>
            <div class="normal_dialog_close" @click="beforeuploadShow = false">
              <i class="el-icon-close"></i>
            </div>
          </div>
          <div class="normal_dialog_body beautifulScroll upload_dialog_body clearfix">
            <div class="upload_btn like_btn" @click.stop="openUploadFolder('uploadFile_img')">
              <div class="upload_btn_tip" @click.stop>
                <el-tooltip class="item" effect="dark" content="支持格式.jpg/.png" placement="top-start">
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </div>
              <img src="@/assets/resources/upload_btn1.png" />图片素材
            </div>
            <div class="upload_btn like_btn" @click.stop="openUploadFolder('uploadFile_video')">
              <div class="upload_btn_tip" @click.stop>
                <el-tooltip class="item" effect="dark" content="支持格式.mp4" placement="top-start">
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </div>
              <img src="@/assets/resources/upload_btn2.png" />视频素材
            </div>
            <div class="upload_btn like_btn" @click.stop="openUploadFolder('uploadFile_pdf')">
              <div class="upload_btn_tip" @click.stop>
                <el-tooltip class="item" effect="dark" content="支持格式.pdf" placement="top-start">
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </div>
              <img src="@/assets/resources/upload_btn3.png" />文档素材
            </div>
            <!-- <div class="upload_btn like_btn" @click.stop="openAddLink"><img src="@/assets/resources/upload_btn4.png" />外部链接</div>
            <div class="upload_btn like_btn" @click.stop=""><img src="@/assets/resources/upload_btn1.png" />医学影像片源</div> -->
          </div>
        </div>
      </div>
      <div v-if="uploadShow" class="normal_dialog_back">
        <div class="normal_dialog files_upload">
          <div class="normal_dialog_title">
            <span>上传附件</span>
          </div>
          <div class="normal_dialog_body beautifulScroll upload_dialog_body">
            <div v-for="(item, index) in viewFileUpList" class="upload_file upload_fileitem clearfix">
              <div class="upload_fileP1 text-ellipsis">
                {{ item.name }}
                <!--<div class="upload_fileP1del" @click.stop='cancelRequest(viewFileIndex)' v-if="viewFileIndex == index">取消</div>-->
                <div v-if="viewFileIndex > index" class="upload_fileP1suc">完成</div>
              </div>
              <div v-if="viewFileIndex == index" class="upload_fileP2">
                <div class="upload_fileP2info" :style="getWidth"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="viewShow" class="normal_dialog_back">
        <div class="normal_dialog files_view">
          <div class="normal_dialog_title">
            <span>文件预览</span>
            <div class="normal_dialog_close" @click="viewShow = false">
              <i class="el-icon-close"></i>
            </div>
          </div>
          <div v-if="viewShow" class="normal_dialog_body fileview_dialog_body">
            <video v-if="playType == 'VIDEO'" :src="playType == 'VIDEO' && playUrl ? playUrl : ''" autoplay controls="controls" controlsList="nodownload"></video>
            <iframe v-if="playType == 'FILE' && fileType == 'pdf'" :src="'./pdfjs/web/viewer.html?file=' + playUrl"></iframe>
            <el-image v-if="playType == 'PICTURE'" :src="playUrl" :preview-src-list="srcList"> </el-image>
          </div>
          <div class="normal_dialog_foot center">
            <div class="normal_dialog_foot1 like_btn" @click="viewShow = false">确定</div>
          </div>
        </div>
      </div>

      <el-dialog custom-class="bigScreen" :visible.sync="dialogVisible" width="100%" height="100%" top="0">
        <VueOfficeDocx
          v-if="dialogVisible && playType == 'FILE' && fileType == 'word'"
          v-loading="loading"
          element-loading-text="拼命加载中..."
          :src="playUrl"
          style="width: 100%; height: 100%"
          @rendered="loading = false"
        ></VueOfficeDocx>

        <vue-office-excel
          v-if="dialogVisible && playType == 'FILE' && fileType == 'excel'"
          v-loading="loading"
          :src="playUrl"
          element-loading-text="拼命加载中..."
          style="width: 100%; height: 100%"
          @rendered="loading = false"
        />
        <vue-office-pptx
          v-if="dialogVisible && playType == 'FILE' && fileType == 'pptx'"
          v-loading="loading"
          :src="playUrl"
          element-loading-text="拼命加载中..."
          style="width: 100%; height: 100%"
          @rendered="loading = false"
        />
      </el-dialog>
    </div>
  </div>
</template>

<script>
// 引入VueOfficeDocx组件
import VueOfficeDocx from '@vue-office/docx'
// 引入相关样式
import '@vue-office/docx/lib/index.css'
// 引入VueOfficeExcel组件
import VueOfficeExcel from '@vue-office/excel'
// 引入相关样式
import '@vue-office/excel/lib/index.css'

import VueOfficePptx from '@vue-office/pptx'
import { putProgress } from '@/utils/resourceUpload.js'

import {
  selectResourceDetail,
  saveResourceFile,
  saveResource,
  saveLinkResource,
  updateState,
  removeResource,
  updateResourceSite,
  updateResourceName,
  resourceTree,
  resourceCollectList,
  updateCollectState,
  updateBatchPublicState,
  resourceBatchRemove
} from '@/api/resource.js'
export default {
  components: {
    VueOfficeDocx,
    VueOfficeExcel,
    VueOfficePptx
  },
  data() {
    return {
      navs: [],
      activeIndex: -1,
      parentId: 0,
      dataList: [],
      viewShow: false,
      playType: '',
      playUrl: '',
      moveShow: false,
      moveList: [],
      moveForm: {
        moveId: '',
        moveSelectId: ''
      },
      defaultProps: {
        children: 'children',
        label: 'resourceName'
      },
      deleteShowAbout: false,
      deleteShowList: [],
      deleteShow: false,
      deleteId: '',
      resourceId: '',
      resourceName: '',
      addShow: false,
      addForm: {
        resourceName: '',
        url: ''
      },
      multipleType: false,
      multipleSelects: [],
      viewFileUpList: [],
      viewFileIndex: 0,
      viewFileMax: 0,
      uploadShow: false,
      beforeuploadShow: false,
      uploadForm: {
        uploadPress: 0,
        uploadFlag: false
      },
      rules: {
        resourceName: [
          {
            required: true,
            message: '请输入外链名称'
          }
        ],
        url: [
          {
            required: true,
            message: '请输入URL地址'
          }
        ],
        moveSelectId: [
          {
            required: true,
            message: '请选择移动到的文件夹'
          }
        ]
      },
      rightClickInfo: {},
      searchForm: {
        resourceName: '',
        resourceType: '',
        orderBy: 'updateTime',
        ascDesc: 'desc',
        pageNum: 1,
        pageSize: 18,
        total: 0
      },
      resourceTypes: [
        {
          name: '图片',
          value: 'PICTURE'
        },
        {
          name: '视频',
          value: 'VIDEO'
        },
        // {
        //   name: '外部链接',
        //   value: 'LINK'
        // },
        {
          name: '文档',
          value: 'FILE'
        }
        // {
        //   name: '仿真实验',
        //   value: 'VR'
        // },
        // {
        //   name: '影像片源',
        //   value: 'DICOM'
        // }
      ],
      loading: false,
      fileType: '',
      dialogVisible: false,
      srcList: []
    }
  },
  computed: {
    getWidth() {
      return 'width:' + this.uploadForm.uploadPress + '%;'
    }
  },
  watch: {
    parentId(val) {
      this.currentChange(1)
    }
  },
  created() {
    this.getDataList()
  },
  mounted() {},
  methods: {
    getFileType(item) {
      if (item.fileType === 1) {
        return
      } else {
        switch (item.resourceType) {
          case 'VIDEO':
            return '【视频】'
          case 'PICTURE':
            return '【图片】'
          case 'LINK':
            return '【链接】'
          case 'FILE':
            return '【文档】'
          case 'VR':
            return '【实验】'
          case 'DICOM':
            return '【片源】'
          default:
            break
        }
      }
    },
    changeMultiple() {
      if (this.multipleSelects && this.multipleSelects.length > 0) {
        this.multipleSelects = []
      } else {
        this.dataList.map((item) => {
          if (this.multipleSelects.indexOf(item.resourceId) <= -1) {
            this.multipleSelects.push(item.resourceId)
          }
        })
      }
    },
    openMultiple() {
      this.multipleType = true
      this.multipleSelects = []
    },
    closeMultiple() {
      this.multipleType = false
      this.multipleSelects = []
    },
    chengSelect(item) {
      var delIndex = this.multipleSelects.indexOf(item.resourceId)
      if (delIndex > -1) {
        this.multipleSelects.splice(delIndex, 1)
      } else {
        this.multipleSelects.push(item.resourceId)
      }
    },
    openfiles(activeTab) {
      this.$emit('openfiles', activeTab)
    },
    getMenus(item, type) {
      var contextmenus = []
      var contextmenusEdit = [
        {
          text: '编辑',
          handler: this.handleritEdit
        },
        {
          text: '删除',
          handler: this.handleritDelete
        },
        {
          text: '移动',
          handler: this.handleritMove
        }
      ]
      //   var sharemenus = [
      //     {
      //       text: '分享',
      //       handler: this.handleritShare
      //     }
      //   ]
      //   var unSharemenus = [
      //     {
      //       text: '取消分享',
      //       handler: this.handleritUnShare
      //     }
      //   ]
      //   var collectmenus = [
      //     {
      //       text: '收藏',
      //       handler: this.handleritCollect
      //     }
      //   ]
      var unCollectmenus = [
        {
          text: '取消收藏',
          handler: this.handleritUnCollect
        }
      ]
      contextmenus = contextmenus.concat(contextmenusEdit)
      //				if(item.isPublic == 1) {
      //					contextmenus = contextmenus.concat(unSharemenus);
      //				} else {
      //					contextmenus = contextmenus.concat(sharemenus);
      //				}
      contextmenus = contextmenus.concat(unCollectmenus)
      return contextmenus
    },
    handleritEdit() {
      this.openEdit(this.rightClickInfo)
    },
    handleritDelete() {
      this.openDelete(this.rightClickInfo)
    },
    handleritShare() {
      this.openShare(this.rightClickInfo)
    },
    handleritUnShare() {
      this.openShare(this.rightClickInfo)
    },
    handleritCollect() {
      this.openCollect(this.rightClickInfo)
    },
    handleritUnCollect() {
      this.openCollect(this.rightClickInfo)
    },
    handleritMove() {
      this.openMove(this.rightClickInfo)
    },
    showMenu(item, event) {
      this.rightClickInfo = item
    },
    openCollect(item) {
      this.$confirm('是否取消收藏该资源?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      })
        .then(() => {
          updateCollectState({
            resourceId: item.resourceId,
            state: 2
          }).then((res) => {
            if (res.code + '' === '200') {
              this.$message({
                message: res.message,
                type: 'success'
              })
              this.currentChange(1)
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        })
        .catch(() => {})
    },
    openShareMore() {
      if (this.multipleSelects && this.multipleSelects.length > 0) {
        this.$confirm('是否分享所选资源？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        })
          .then(() => {
            updateBatchPublicState(this.multipleSelects).then((res) => {
              if (res.code + '' === '200') {
                this.$message({
                  message: res.message,
                  type: 'success'
                })
                this.multipleSelects = []
                this.currentChange(1)
              } else {
                this.$message({
                  message: res.message,
                  type: 'error'
                })
              }
            })
          })
          .catch(() => {})
      } else {
        this.closeMultiple()
      }
    },
    openShare(item) {
      console.log(typeof item.isPublic)

      // eslint-disable-next-line eqeqeq
      var message = item.isPublic == '1' ? '是否取消分享该资源?' : '是否分享该资源?'
      this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      })
        .then(() => {
          updateState({
            resourceId: item.resourceId,
            // eslint-disable-next-line eqeqeq
            state: item.isPublic == '1' ? 2 : 1
          }).then((res) => {
            if (res.code + '' === '200') {
              this.$message({
                message: res.message,
                type: 'success'
              })
              this.currentChange(1)
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        })
        .catch(() => {})
    },
    beforeMove() {
      if (this.postData) {
        return
      }
      this.postData = true
      setTimeout(() => {
        this.postData = false
      }, 1000)
      this.$refs.moveForm.validate((valid) => {
        if (valid) {
          var data = {
            resourceId: this.moveForm.moveId,
            parentId: this.moveForm.moveSelectId
          }
          updateResourceSite(data).then((res) => {
            if (res.code + '' === '200') {
              this.$message({
                type: 'success',
                message: res.message
              })
              this.currentChange(1)
              this.clearMove()
            } else {
              this.$message({
                type: 'error',
                message: res.message
              })
            }
          })
        }
      })
    },
    handleNodeClick(data) {
      this.moveForm.moveSelectId = data.id
    },
    clearMove() {
      this.moveForm.moveId = ''
      this.moveForm.moveSelectId = ''
      this.$nextTick(() => {
        this.$refs.moveForm.clearValidate()
        this.moveShow = false
      })
    },
    openMove(item) {
      resourceTree().then((res) => {
        this.moveList = [
          {
            resourceName: '我的收藏',
            id: '0',
            children: res.data || []
          }
        ]
        this.moveForm.moveId = item.resourceId
        this.moveShow = true
      })
    },
    openDeleteMore() {
      if (this.multipleSelects && this.multipleSelects.length > 0) {
        this.$confirm('是否删除所选资源？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        })
          .then(() => {
            resourceBatchRemove(this.multipleSelects).then((res) => {
              if (res.code + '' === '200') {
                this.$message({
                  message: res.message,
                  type: 'success'
                })
                this.multipleSelects = []
                this.currentChange(1)
              } else {
                this.$message({
                  message: res.message,
                  type: 'error'
                })
              }
            })
          })
          .catch(() => {})
      } else {
        this.closeMultiple()
      }
    },
    openDelete(item) {
      this.deleteId = item.resourceId
      this.deleteShow = true
    },
    beforeDelete() {
      removeResource({
        resourceId: this.deleteId
      }).then((res) => {
        if (res.code + '' === '200') {
          this.$message({
            type: 'success',
            message: res.message
          })
          this.deleteShow = false
          this.deleteId = ''
          this.currentChange(1)
        } else {
          this.$message({
            type: 'error',
            message: res.message
          })
        }
      })
    },
    clearDelete() {
      this.deleteShow = false
      this.deleteId = ''
    },
    openAbout() {
      this.deleteShowAbout = true
    },
    beforeCheck() {
      if (this.postData) {
        return
      }
      this.postData = true
      setTimeout(() => {
        this.postData = false
      }, 1000)
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          var data = Object.assign({}, this.addForm, {
            parentId: this.parentId
          })
          saveLinkResource(data).then((res) => {
            if (res.code + '' === '200') {
              this.$message({
                type: 'success',
                message: res.message
              })
              this.clearCheck()
              this.currentChange(1)
            } else {
              this.$message({
                type: 'error',
                message: res.message
              })
            }
          })
        }
      })
    },
    openAddLink() {
      this.beforeuploadShow = false
      this.addShow = true
    },
    clearCheck() {
      this.addForm.resourceName = ''
      this.addForm.url = ''
      this.$nextTick(() => {
        this.$refs.addForm.clearValidate()
        this.addShow = false
      })
    },
    openAdd() {
      saveResourceFile({
        resourceName: '新建文件夹',
        parentId: this.parentId
      }).then((res) => {
        this.currentChange(1)
        // this.$nextTick(() => {
        // this.openEdit(this.dataList[0])
        // })
      })
    },
    checkEdit(index) {
      // eslint-disable-next-line eqeqeq
      if (this.resourceName != this.dataList[index].resourceName) {
        updateResourceName({
          resourceId: this.resourceId,
          resourceName: this.resourceName
        }).then((res) => {
          this.dataList[index].resourceName = this.resourceName
          this.closeEdit()
        })
      } else {
        this.closeEdit()
      }
    },
    closeEdit() {
      this.resourceId = ''
      this.resourceName = ''
    },
    openEdit(item) {
      this.resourceId = item.resourceId
      this.resourceName = item.resourceName
      this.$nextTick(() => {
        if (this.$refs.change_input.length) {
          this.$refs.change_input[0].focus()
        } else {
          this.$refs.change_input.focus()
        }
      })
    },
    backAll() {
      this.activeIndex = -1
      this.parentId = 0
    },
    prueInit() {
      if (this.navs.length > 0 && this.activeIndex < this.navs.length - 1) {
        this.activeIndex++
        this.parentId = this.navs[this.activeIndex].resourceId
      }
    },
    backIndex(item, index) {
      if (index > -1 && index < this.navs.length - 1) {
        this.navs.splice(index + 1, this.navs.length - 1 - index)
        this.activeIndex = this.navs.length - 1
        this.parentId = item.resourceId
      }
    },
    backInit() {
      if (this.navs.length > 0 && this.activeIndex > -1) {
        this.activeIndex--
        if (this.activeIndex > -1) {
          this.parentId = this.navs[this.activeIndex].resourceId
        } else {
          this.parentId = 0
        }
      }
    },
    setItem(item) {
      // eslint-disable-next-line eqeqeq
      if (item.fileType == 1) {
        if (this.activeIndex > -1 && this.activeIndex < this.navs.length - 1) {
          this.navs.splice(this.activeIndex + 1, this.navs.length - 1 - this.activeIndex)
          this.navs.push(item)
          this.activeIndex = this.navs.length - 1
          this.parentId = item.resourceId
        } else {
          // eslint-disable-next-line eqeqeq
          if (this.activeIndex == -1) {
            this.navs = []
          }
          this.navs.push(item)
          this.activeIndex = this.navs.length - 1
          this.parentId = item.resourceId
        }
      } else {
        selectResourceDetail({
          resourceId: item.resourceId
        }).then((res) => {
          this.playType = item.resourceType

          if (item.resourceType === 'FILE') {
            const suffix = res.data.url.substring(res.data.url.lastIndexOf('.') + 1).toLowerCase()
            if (suffix === 'doc' || suffix === 'docx') {
              this.fileType = 'word'
              this.playUrl = res.data.url
              this.loading = true
              this.dialogVisible = true
            } else if (suffix === 'xlsx') {
              this.fileType = 'excel'
              this.playUrl = res.data.url
              this.loading = true
              this.dialogVisible = true
            } else if (suffix === 'pptx') {
              this.fileType = 'pptx'
              this.playUrl = res.data.url
              this.loading = true
              this.dialogVisible = true
            } else {
              this.fileType = 'pdf'
              this.playUrl = encodeURIComponent(res.data.url)
              this.viewShow = true
            }
          }

          if (item.resourceType === 'VIDEO' || item.resourceType === 'PICTURE') {
            this.playUrl = res.data.url
            this.viewShow = true
          }
          if (item.resourceType === 'LINK' || item.resourceType === 'VR') {
            this.playUrl = res.data.url
            this.viewShow = true
          }
        })
      }
    },
    openbeforeUpload() {
      this.beforeuploadShow = true
    },
    openUploadFolder(uploadFilebox) {
      if (this.uploadForm.uploadFlag) {
        this.$message({
          title: '提示',
          message: '正在上传,请稍后。。',
          type: 'info'
        })
      } else {
        this.$refs[uploadFilebox].click()
      }
    },
    beforeformRequest(event) {
      var files = event.target.files
      if (files.length > 0) {
        var viewFileUpList = []
        for (var i = 0; i < files.length; i++) {
          var item = files[i]
          var resourceType = item.name.substring(item.name.lastIndexOf('.') + 1)
          if (resourceType === 'pdf') {
            item.resourceType = 'FILE'
          }
          if (resourceType === 'jpg' || resourceType === 'png' || resourceType === 'gif') {
            item.resourceType = 'PICTURE'
          }
          if (resourceType === 'mp4') {
            item.resourceType = 'VIDEO'
          }
          if (item.resourceType) {
            item.resourceSize = (item.size / 1024 / 1024).toFixed(2)
            item.resourceName = item.name
            item.overState = false
            viewFileUpList.push(item)
          }
        }
        this.viewFileUpList = viewFileUpList
        this.viewFileIndex = 0
        this.viewFileMax = viewFileUpList.length - 1
        this.uploadShow = true
        this.formRequest()
      }
    },
    formRequest() {
      var file = this.viewFileUpList[this.viewFileIndex]
      const key = `szhjxpt/teacherfiles/${file.name}`
      putProgress(
        key,
        file,
        (progress) => {
          this.uploadForm.uploadFlag = true
          this.uploadForm.uploadPress = parseInt(progress * 100)
        },
        (source) => {
          this.viewFileUpList[this.viewFileIndex].source = source
        }
      )
        .then((res) => {
          this.uploadForm.uploadPress = 0
          this.viewFileUpList[this.viewFileIndex].overState = true
          this.addFile(key, file)
        })
        .catch((err) => {
          console.log(err)
        })
    },
    cancelRequest(index) {
      this.viewFileUpList[index].source.cancel('已取消上传')
    },
    addFile(key, file) {
      var data = Object.assign({}, file, {
        parentId: this.parentId,
        url: key
      })
      saveResource(data).then((res) => {
        if (res.code + '' === '200') {
          this.getDataList()
          this.nextUpload()
        }
      })
    },
    nextUpload() {
      var viewFileIndex = this.viewFileIndex
      var viewFileMax = this.viewFileMax
      if (viewFileIndex < viewFileMax) {
        this.viewFileIndex++
        this.formRequest()
      } else {
        this.clearSectionVideo()
      }
    },
    clearSectionVideo() {
      this.viewFileUpList = []
      this.viewFileIndex = 0
      this.viewFileMax = 0
      this.uploadForm.uploadFlag = false
      this.uploadForm.uploadPress = 0
      var array = ['uploadFile_img', 'uploadFile_video', 'uploadFile_pdf']
      array.map((uploadFilebox) => {
        this.$refs[uploadFilebox].value = ''
      })
      this.uploadShow = false
    },
    reGet() {
      this.searchForm.resourceName = ''
      this.searchForm.resourceType = ''
      this.searchForm.orderBy = 'updateTime'
      this.searchForm.ascDesc = 'desc'
      this.currentChange(1)
    },
    sortChange() {
      // eslint-disable-next-line eqeqeq
      if (this.searchForm.ascDesc == 'desc') {
        this.searchForm.ascDesc = 'asc'
        this.currentChange(1)
        return
      }
      // eslint-disable-next-line eqeqeq
      if (this.searchForm.ascDesc == 'asc') {
        this.searchForm.ascDesc = 'desc'
        this.currentChange(1)
        return
      }
    },
    resourceTypeChange(item) {
      // eslint-disable-next-line eqeqeq
      if (this.searchForm.resourceType == item.value) {
        this.searchForm.resourceType = ''
      } else {
        this.searchForm.resourceType = item.value
      }
      this.currentChange(1)
    },
    currentChange(pageNum) {
      this.searchForm.pageNum = pageNum
      this.getDataList()
    },
    getDataList() {
      this.dataList = []
      this.$nextTick(() => {
        var data = Object.assign({}, this.searchForm, {
          parentId: this.parentId,
          type: 1
        })
        resourceCollectList(data).then(async (res) => {
          this.dataList = res.data.list
          this.srcList = this.dataList
            .filter((item) => {
              return item.resourceType === 'PICTURE'
            })
            .map((item) => {
              return item.url
            })
          this.searchForm.total = res.data.total
        })
      })
    }
  }
}
</script>
