<template>
  <div class="app-container">
    <el-row :gutter="10" class="top">
      <el-col :span="24">
        <i class="el-icon-location"></i>
        <span>当前位置：<router-link to="/training">培训管理</router-link> /</span>
        <span>添加培训</span>
      </el-col>
    </el-row>
    <div class="box">
      <div>{{ showTitle }}</div>
      <!-- <img src="@/assets/meeting/addMeeting_bg.png" alt="" class="bgImg" /> -->
      <!-- 1. 培训信息 -->
      <el-form ref="form" :model="formInfo" label-width="110px" :rules="rules">
        <!-- <el-form-item label="培训编号:" prop="trainCode">
            <el-input v-model="formInfo.trainCode" placeholder="请输入培训编号,20字以内" maxlength="20" size="small" oninput="value = value.replace(/[\u4E00-\u9FA5]/g,'')" clearable></el-input>
          </el-form-item> -->
        <el-form-item label="培训标题:" prop="trainName">
          <el-input v-model="formInfo.trainName" class="trainName" :disabled="formInfo.trainStatus === 2" placeholder="请输入本次培训标题" maxlength="20" size="small" clearable show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="培训内容:">
          <el-input v-model="formInfo.remark" class="remark" :disabled="formInfo.trainStatus === 2" placeholder="请输入本次培训内容" type="textarea" maxlength="200" size="small" clearable show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="主讲人:" prop="trainUserName">
          <el-input v-model="formInfo.trainUserName" class="trainUserName" :disabled="formInfo.trainStatus === 2" placeholder="请输入主讲人名称" maxlength="20" size="small" clearable show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="培训状态:" prop="trainStatus">
          <el-radio v-model="formInfo.trainStatus" class="trainStatus" :label="1">未开始</el-radio>
          <!-- <el-radio v-model="formInfo.trainStatus" class="trainStatus" :label="2">进行中</el-radio> -->
        </el-form-item>
        <el-form-item label="预定时间段:" prop="startTime" class="startTime">
          <el-date-picker v-model="value1" size="small" :disabled="formInfo.trainStatus === 2" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" clearable @change="pickerChange" @blur="dateBlur"> </el-date-picker>
        </el-form-item>
        <el-form-item label="选择培训地点:" prop="meetingRoomName" label-width="110px">
          <div class="meetingRoomName">
            <div class="first" @click="checkedRoom('第一会议室')">
              <span v-if="fristRoomIsOccupy" class="occupy">该时间段被占用</span>
              <span class="name">第一会议室</span>
              <img v-if="formInfo.meetingRoomName === '第一会议室'" class="checked" src="@/assets/meeting/checked.png" alt="" />
            </div>
            <div class="second" @click="checkedRoom('第二会议室')">
              <span v-if="LastRoomIsOccupy" class="occupy">该时间段被占用</span>
              <span class="name">第二会议室</span>
              <img v-if="formInfo.meetingRoomName === '第二会议室'" class="checked" src="@/assets/meeting/checked.png" alt="" />
            </div>
            <div @click="checkedRoom('其他地点')">
              <span class="name">其他地点 <i class="el-icon-edit"></i></span>
              <img v-if="formInfo.meetingRoomName !== '第一会议室' && formInfo.meetingRoomName !== '第二会议室' && formInfo.meetingRoomName !== null" class="checked" src="@/assets/meeting/checked.png" alt="" />
            </div>
            <div v-show="isShowRoom" class="restsRoom">
              <el-input v-model="formInfo.meetingRoomName" placeholder="请输入培训地点" size="small"></el-input>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="培训部门:" prop="orgs">
          <el-checkbox-group v-model="checkList" @change="departmentChange">
            <el-checkbox v-for="item in allDepartment" :key="item.id" :label="item.id">{{ item.organizationName }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="培训类型:">
          <el-radio v-model="formInfo.powerType" :disabled="formInfo.trainStatus === 2" class="trainStatus powerType" :label="1">部门内部</el-radio>
          <el-radio v-model="formInfo.powerType" :disabled="formInfo.trainStatus === 2" class="trainStatus powerType" :label="2">公司培训</el-radio>
          <el-radio v-model="formInfo.powerType" :disabled="formInfo.trainStatus === 2" class="trainStatus powerType" :label="3">外部培训</el-radio>
        </el-form-item>
        <!-- <el-form-item label="参与人员:" prop="users" class="attendUsers">
          <div class="checkPerson">
            <div v-for="item in checkedTableData" :key="item.userId">
              <div>
                <span>{{ item.realName }}</span>
                <img v-if="item.sex === 'F'" src="@/assets/meeting/wuman.png" alt="" />
                <img v-else src="@/assets/meeting/man.png" alt="" />
              </div>
              <span>{{ item.organizationName }}-{{ item.jobName }}</span>
              <img class="close" src="@/assets/meeting/close.png" alt="" @click="removeData(item)" />
            </div>
          </div>

          <el-button round icon="el-icon-plus" class="addButton" @click="appendPerson">添加</el-button>
        </el-form-item> -->
        <el-form-item label="上传附件:" class="uploadFile">
          <ul class="fileList">
            <li v-for="(item, index) in fileList" :key="item.name">
              <div>
                <div class="fileImg">
                  <img src="@/assets/meeting/file.png" alt="" />
                  <span v-if="item.fileSize">{{ item.fileSize }}KB</span>
                  <span v-else>{{ item.size | formattingFileSize }}</span>
                </div>
                <div>
                  <el-tooltip class="item" effect="dark" :content="item.name" placement="top">
                    <span v-if="!item.flag">{{ item.name }}</span>
                  </el-tooltip>
                  <el-input v-if="item.flag" v-model="fileName" size="small" type="textarea" @blur.stop="uploadingConfirm(index)"></el-input>
                  <i v-if="!item.flag" class="el-icon-edit" @click="rechristen(item, index)"></i>
                </div>
              </div>
              <div v-if="item.percentage && item.percentage !== 100">
                <el-progress :percentage="item.percentage"></el-progress>
              </div>
              <div>
                <span v-if="item.name && item.createTime">{{ realName }}上传于{{ item.createTime | formatDate }}</span>
                <span v-else>{{ realName }}上传于{{ new Date() | formatDate }}</span>
                <i class="el-icon-download" @click="downloadFile(item)"></i>
                <i class="el-icon-delete" @click="delFile(item)"></i>
              </div>
            </li>
          </ul>
          <el-upload
            class="upload-demo"
            drag
            :action="`${actionUrl}/system/upload/file`"
            :headers="header"
            :file-list="fileList"
            multiple
            :before-upload="beforeUpload"
            :show-file-list="false"
            :on-success="uploadSuccess"
            :on-progress="uploadProgress"
            :on-remove="uploadRemove"
          >
            <div class="uploadIcon">
              <i class="el-icon-circle-plus-outline"></i>
              点击添加文件
            </div>
            <div class="el-upload__text">支持doc /docx /xls /xlsx /pdf /jpg /png 格式,大小不超过50MB</div>
            <div slot="tip" class="el-upload__tip" style="color: red; line-height: 15px"></div>
          </el-upload>
        </el-form-item>
      </el-form>
      <el-divider></el-divider>
      <el-row :gutter="10" type="flex" justify="center" class="addMeeting_Button">
        <el-col :span="6">
          <el-button v-if="formInfo.trainId" type="primary" @click="submitData">修改培训</el-button>
          <el-button v-else type="primary" @click="submitData">添加培训</el-button>
        </el-col>
      </el-row>
      <!-- --------------------------------------------- -->
    </div>

    <el-drawer title="添加参与人员" :visible.sync="addPersonDialog" custom-class="addPersonDrawer" size="1012px">
      <template v-slot:title>
        <span class="drawerTitle">添加参与人员</span>
      </template>
      <!-- 当前选中的人员 -->
      <div class="checkPerson" style="min-height: 150px">
        <div v-for="item in selectData" :key="item.userId">
          <div>
            <span>{{ item.realName }}</span>
            <img v-if="item.sex === 'F'" src="@/assets/meeting/wuman.png" alt="" />
            <img v-else src="@/assets/meeting/man.png" alt="" />
          </div>
          <span>{{ item.organizationName }}-{{ item.jobName }}</span>
          <img class="close" src="@/assets/meeting/close.png" alt="" @click="removeSelectData(item)" />
        </div>
      </div>
      <!-- 添加人员的框 -->
      <el-row :gutter="0" style="border-top: 2px solid #eee">
        <el-col :span="6" style="border-right: 2px solid #eee; border-bottom: 2px solid #eee; padding: 0; min-height: 600px">
          <div class="treeTitle">山东中飞科技有限公司</div>
          <el-tree style="padding-left: 40px" :data="menu" node-key="id" empty-text="暂无菜单" highlight-current :expand-on-click-node="false" :props="defaultProps" current-node-key="id" default-expand-all @node-click="handleNodeClick" />
        </el-col>
        <el-col :span="18" style="padding-top: 15px; padding-left: 18px; padding-right: 18px; border-bottom: 2px solid #eee; min-height: 600px">
          <el-row :gutter="10" style="margin-bottom: 5px">
            <el-col :span="24" class="serarchName">
              <el-input v-model="addPersonFormInfo.realName" style="width: 150px" size="small" clearable placeholder="请输入姓名" maxlength="20">
                <template v-slot:suffix>
                  <i class="el-icon-search" @click="getPersonList"></i>
                </template>
              </el-input>
            </el-col>
          </el-row>
          <el-table :data="tableData" :header-cell-style="{ background: '#d8dbe1', color: '#0B1A44' }" style="width: 100%" border :row-key="getRowKeys" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="width" :reserve-selection="true"> </el-table-column>
            <el-table-column prop="realName" label="姓名" width="width" align="center"> </el-table-column>
            <el-table-column prop="sex" label="性别" width="width" align="center">
              <template v-slot="{ row }">
                {{ row.sex | sexHandle }}
              </template>
            </el-table-column>
            <el-table-column prop="organizationName" label="部门" width="width" align="center"> </el-table-column>
            <el-table-column prop="jobName" label="岗位" width="width" align="center"> </el-table-column>
          </el-table>
        </el-col>
      </el-row>

      <el-row class="addPersonPagination" type="flex" align="middle" style="margin-top: 15px">
        <el-col :span="18">
          <el-pagination :pager-count="5" background style="text-align: center" layout="total, sizes, prev, pager, next" :page-sizes="[5, 10, 15, 20]" :total="total" :page-size.sync="addPersonFormInfo.pageSize" :current-page.sync="addPersonFormInfo.pageNum" @size-change="getPersonList" @current-change="getPersonList" />
        </el-col>
        <el-col :span="6">
          <el-button size="small" @click="addPersonDialog = false">取 消</el-button>
          <el-button type="primary" size="small" @click="addPerson">确 定</el-button>
        </el-col>
      </el-row>
    </el-drawer>
  </div>
</template>

<script>
import { meetingJudgeTime, meetingRenameFile } from '@/api/meeting'
import { trainSave, trainUdpate } from '@/api/training'
import { getList } from '@/api/systemUser'
import { getOrganizationTree } from '@/api/organization'

import { formatDate } from '@/filters'
import { mapGetters } from 'vuex'

export default {
  name: 'AddMeeting',
  data() {
    return {
      active: 0,
      fristRoomIsOccupy: false,
      LastRoomIsOccupy: false,
      formInfo: {
        trainCode: null,
        trainName: null,
        meetingRoomName: null,
        startTime: null,
        endTime: null,
        trainStatus: 1,
        trainUserName: null,
        remark: null,
        powerType: 1,
        users: [],
        meetingFileReqs: [],
        orgs: null
      },
      allDepartment: [], // 所有的部门
      checkList: [], // 选中的培训部门
      rules: {
        meetingRoomName: [
          {
            required: true,
            tigger: 'chagne',
            message: '请选择培训地点'
          }
        ],
        trainName: [
          {
            required: true,
            tigger: 'blur',
            message: '请输入培训主题'
          }
        ],
        trainStatus: [
          {
            required: true,
            tigger: 'chagne',
            message: '请选择培训状态',
            type: 'number'
          }
        ],
        startTime: [
          {
            required: true,
            tigger: 'blur',
            message: '请选择预约时间段'
          }
        ],
        trainUserName: [
          {
            required: true,
            tigger: 'blur',
            message: '请输入主讲人'
          }
        ],
        users: [
          {
            required: true,
            tigger: 'chagne',
            message: '请选择参与人员',
            type: 'array'
          }
        ],
        orgs: [
          {
            required: true,
            tigger: 'change',
            message: '请选择培训部门'
          }
        ]
      },
      isShowRoom: false,
      value1: null,
      /** 2.培训人员 */
      checkedTableData: [], // 确定的人员列表
      tableData: [], // 人员列表
      selectData: [], // 多选框选中的数据
      addPersonDialog: false,
      addPersonFormInfo: {
        realName: null,
        organizationName: null,
        organizationId: null,
        pageNum: 1,
        pageSize: 5
      },
      defaultProps: {
        label: 'organizationName',
        children: 'children'
      },
      menu: [],
      total: 0,
      uploadingFileDialog: false,
      // 添加人员的假数据分页
      pagination: {
        size: 5,
        page: 1,
        total: 0
      },
      /** 3.上传附件 */
      header: {
        Authorization: null
      },
      actionUrl: window.config.VUE_APP_BASE_API,
      fileList: [],
      rechristenDialog: false,
      fileName: null,
      uid: null,
      time: null,
      oldPath: null
    }
  },
  computed: {
    ...mapGetters(['name', 'token', 'realName']),
    showTitle() {
      return this.formInfo.trainId ? '修改培训' : '添加新培训'
    }
  },
  watch: {
    addPersonDialog(val) {
      if (val) {
        this.getPersonList()
        this.getMenuTreeOfParent()
      }
    }
  },
  async created() {
    this.getMenuTreeOfParent()
    if (this.$route.params.type === '1') {
      const data = JSON.parse(window.localStorage.getItem('trainingData'))
      this.edit(data)
    }
  },

  methods: {
    pickerChange(val) {
      this.formInfo.startTime = formatDate(val[0], 'yyyy-MM-dd hh:mm:ss')
      this.formInfo.endTime = formatDate(val[1], 'yyyy-MM-dd hh:mm:ss')
      return val
    },
    // 日期时间选择器失去焦点事件
    async dateBlur() {
      const info1 = {
        startTime: this.formInfo.startTime,
        endTime: this.formInfo.endTime,
        meetingRoomName: '第一会议室',
        type: 2,
        id: this.formInfo.trainId ? this.formInfo.trainId : null,
        userIds: []
      }
      const info2 = {
        startTime: this.formInfo.startTime,
        endTime: this.formInfo.endTime,
        meetingRoomName: '第二会议室',
        id: this.formInfo.trainId ? this.formInfo.trainId : null,
        type: 2,
        userIds: []
      }
      const { data: fristData } = await meetingJudgeTime(info1)
      const { data: lastData } = await meetingJudgeTime(info2)
      this.formInfo.meetingRoomName = null
      if (fristData !== 1) {
        this.fristRoomIsOccupy = true
      } else {
        this.fristRoomIsOccupy = false
      }
      if (lastData !== 1) {
        this.LastRoomIsOccupy = true
      } else {
        this.LastRoomIsOccupy = false
      }
      console.log(fristData, lastData)
    },
    // 选择会议室
    checkedRoom(data) {
      if (this.formInfo.trainStatus === 2) {
        return false
      }
      if (data === '第一会议室') {
        if (this.fristRoomIsOccupy !== true) {
          this.formInfo.meetingRoomName = data
          this.isShowRoom = false
        }
      } else if (data === '第二会议室') {
        if (this.LastRoomIsOccupy !== true) {
          this.formInfo.meetingRoomName = data
          this.isShowRoom = false
        }
      } else {
        this.formInfo.meetingRoomName = data
        this.isShowRoom = true
      }
      this.$refs['form'].validateField('meetingRoomName')
    },
    departmentChange(val) {
      if (val.length >= 1) {
        this.formInfo.orgs = val.join(',')
      } else {
        this.formInfo.orgs = null
      }
      this.$refs['form'].validateField('orgs')
    },
    /** 2.培训人员 */
    // 获取人员列表
    async getPersonList() {
      console.log(this.name)
      const { data } = await getList(this.addPersonFormInfo)
      this.tableData = data.list
      this.total = data.total
      if (this.checkedTableData.length < 1) {
        // this.checkedTableData = this.tableData.filter((item) => {
        //   return item.username === this.name
        // })
        console.log(this.checkedTableData)
      }
    },
    appendPerson() {
      if (this.formInfo.meetingId) {
        this.selectData = this.formInfo.userDtos
      }
      this.addPersonDialog = true
    },
    removeSelectData(row) {
      this.selectData = this.selectData.filter((item) => item.userId !== row.userId)
    },
    // 确认添加的人员
    addPerson() {
      this.checkedTableData.push(...this.selectData)
      this.checkedTableData = this.deWeight(this.checkedTableData)
      const userIds = this.checkedTableData.map((item) => item.userId)
      this.formInfo.users = [...userIds]
      this.$refs['form'].validateField('users')
      console.log(this.$refs['form'].validateField('users'))
      this.addPersonDialog = false
      document.body.click()
    },
    deWeight(arr) {
      for (var i = 0; i < arr.length - 1; i++) {
        for (var j = i + 1; j < arr.length; j++) {
          if (arr[i].userId === arr[j].userId) {
            // id为需要去重字段
            arr.splice(j, 1)
            // 因为数组长度减小1，所以直接 j++ 会漏掉一个元素，所以要 j--
            j--
          }
        }
      }
      return arr
    },

    // 培训人员列表移除按钮触发
    removeData(row) {
      this.checkedTableData = this.checkedTableData.filter((item) => item.userId !== row.userId)
      this.formInfo.users = this.checkedTableData.map((item) => item.userId)
      this.formInfo.userDtos = this.checkedTableData
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectData = val
    },
    getRowKeys(row) {
      return row.userId
    },
    // 选择部门 单选模式下用户点击清空按钮时触发
    onSelectClear() {
      this.addPersonFormInfo.organizationId = null
    },
    // 获取部门树
    getMenuTreeOfParent() {
      getOrganizationTree().then((res) => {
        // this.menu = res.data
        this.allDepartment = res.data[0].children
      })
    },
    // 树形节点被点击触发的事件
    handleNodeClick(node) {
      this.addPersonFormInfo.organizationId = node.id
      this.addPersonFormInfo.organizationName = node.organizationName
      this.getPersonList()
      // this.$refs['selecteltree'].blur()
      console.log(node)
    },
    // 人员列表的假分页
    handleSizeChange(val) {
      this.pagination.size = val
    },
    handleCurrentChange(val) {
      this.pagination.page = val
    },
    /** 2.培训人员  结束 */

    /** 3. 上传附件 */
    beforeUpload(file) {
      this.header.Authorization = `Bearer ${this.token}`
      console.log(file.type)
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isDOC = file.type === 'application/msword'
      const isDOCX = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      const isXLS = file.type === 'application/vnd.ms-excel'
      const isXLSX = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      const isPDF = file.type === 'application/pdf'
      const isLt2M = file.size / 1024 / 1024 < 50
      if (!isLt2M) {
        this.$message.error('上传附件大小不能超过 50MB!')
      }
      if (!isJPG && !isPNG && !isDOC && !isDOCX && !isXLS && !isXLSX && !isPDF) {
        this.$message.warning('只能上传.doc .docx .xls .xlsx .pdf .jpg .png 格式的附件')
      }
      return (isJPG || isPNG || isDOC || isDOCX || isXLS || isXLSX || isPDF) && isLt2M
    },
    // 上传时的钩子
    uploadProgress(event, file, fileList) {
      console.log(event, file, fileList)
    },
    // 上传成功回调
    uploadSuccess(response, file, fileList) {
      this.fileList = fileList
    },
    // 文件列表移除文件时的钩子
    uploadRemove(file, fileList) {
      this.fileList = fileList
    },
    // 下载文件
    downloadFile(row) {
      if (row.fileId) {
        window.open(row.fileUrl, '_blank')
      } else {
        window.open(row.response.data[0], '_blank')
      }
    },
    // 删除文件
    delFile(row) {
      console.log(row)
      this.fileList = this.fileList.filter((item) => item.uid !== row.uid)
    },
    // 重命名
    rechristen(row, index) {
      this.$set(this.fileList[index], 'flag', true)
      this.fileName = row.name
      if (!row.fileId) {
        this.oldPath = row.response.data[0].split('workmanage/')[1]
      } else {
        this.oldPath = row.fileUrl.split('workmanage/')[1]
      }
      this.uid = row.uid
      // this.rechristenDialog = true
    },
    // 重命名触发事件
    async uploadingConfirm(index) {
      const { data } = await meetingRenameFile({ oldPath: this.oldPath, newPath: this.fileName })
      this.fileList.forEach((item) => {
        if (item.uid === this.uid) {
          item.name = this.fileName
          if (!item.fileId) {
            item.response.data[0] = data
          } else {
            item.fileUrl = data
          }
        }
      })
      this.fileList[index].flag = false
      // this.rechristenDialog = false
    },
    // 提交数据
    async submitData() {
      const info = {
        startTime: this.formInfo.startTime,
        endTime: this.formInfo.endTime,
        meetingRoomName: this.formInfo.meetingRoomName,
        userIds: this.formInfo.userIds
      }
      this.$refs['form'].validate(async (val) => {
        if (val) {
          if (this.formInfo.trainId) {
            const uploadTime = [this.formInfo.startTime, this.formInfo.endTime]
            if (uploadTime[0] !== this.time[0] || uploadTime[1] !== this.time[1]) {
              const { data } = await meetingJudgeTime(info)
              if (data !== 1) {
                const message = data.join(',')
                return this.$message.warning(`${message}培训时间与当前培训冲突，请重新选择人员或重新选择时间`)
              }
            }

            this.$set(this.formInfo, 'meetingFileReqs', [])
            if (this.fileList.length >= 1) {
              this.fileList.forEach((item) => {
                this.formInfo.meetingFileReqs.push({
                  fileName: item.name,
                  fileSize: item.fileSize || item.size / 1024,
                  fileUrl: item.fileUrl || item.response.data[0]
                })
              })
            }
            console.log(this.formInfo)
            await trainUdpate(this.formInfo)
            this.$message.success('修改培训成功')
            this.$router.push('/training')

            // this.$emit('update:showDialog', false)
          } else {
            const { data } = await meetingJudgeTime(info)
            if (data !== 1) {
              const message = data.join(',')
              return this.$message.warning(`${message}培训时间与当前培训冲突，请重新选择人员或重新选择时间`)
            }
            if (this.fileList.length >= 1) {
              this.fileList.forEach((item) => {
                this.formInfo.meetingFileReqs.push({
                  fileName: item.name,
                  fileSize: item.size / 1024,
                  fileUrl: item.response.data[0]
                })
              })
            }
            await trainSave(this.formInfo)
            this.$message.success('添加培训成功')
            this.$router.push('/training')
            // this.$emit('update:showDialog', false)
          }
        }
      })
    },
    // 修改数据回显
    edit(data) {
      if (data.meetingRoomName !== '第一会议室' && data.meetingRoomName !== '第二会议室') {
        this.isShowRoom = true
      }
      console.log(data)
      this.checkList = data.orgs.split(',').map((item) => parseInt(item))
      this.time = [data.startTime, data.endTime] // 把时间保存起来
      this.formInfo = { ...data, users: [] }
      this.formInfo.users = data.userDtos.map((item) => item.userId)
      this.value1 = [data.startTime, data.endTime]
      this.checkedTableData = data.userDtos
      this.fileList = data.fileDtos
      this.fileList.forEach((item, index) => {
        this.$set(this.fileList[index], 'name', item.fileName)
        item['uid'] = item.fileId
      })
    },
    close() {
      this.active = 0
      /** 1.培训信息数据重置 */
      this.formInfo = {
        trainCode: null,
        meetingRoomName: null,
        trainName: null,
        trainStatus: 1,
        remark: null,
        startTime: null,
        endTime: null,
        trainUserName: null,
        powerType: 1,
        users: [],
        meetingFileReqs: []
      }
      this.value1 = null
      /** 2.培训人员数据重置 */
      this.checkedTableData = []
      this.tableData = [] // 人员列表
      this.selectData = [] // 多选框选中的数据
      this.addPersonFormInfo = {
        realName: null,
        organizationName: null,
        organizationId: null,
        pageNum: 1,
        pageSize: 5
      }
      this.isDestory = true
      this.menu = []
      /** 3.上传附件数据重置 */
      this.fileList = []
      this.fileName = null
      this.uid = null
      this.$emit('update:showDialog', false)

      this.$emit('success')
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  position: relative;

  padding: 0;
  background-color: #e8eaed;
  min-height: 100%;
  padding-top: 58px;
  padding-bottom: 30px;
}
::v-deep {
  // .el-date-editor {
  //   width: 350px;
  // }
  .el-input {
    width: 300px;
  }
  .el-textarea {
    width: 300px;
  }
  .select {
    .el-input {
      width: 200px;
    }
  }
  .el-pagination__jump {
    .el-input {
      width: 50px;
    }
  }
  .el-button--primary.is-plain:focus,
  .el-button--primary.is-plain:hover {
    color: #409eff;
    background: #ecf5ff;
    border-color: #b3d8ff;
  }
}
.top {
  position: absolute;
  top: 0px;
  width: 100%;
  background-color: #e8eaed;
  padding: 24px 0 16px 0;
  z-index: 999;
  .el-col {
    i {
      font-size: 14px;
      color: #657081;
    }
    span {
      &:first-of-type {
        margin: 0 5px;
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #657081;
      }
      &:last-of-type {
        font-size: 14px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        font-weight: bold;
        color: #0b1a44;
      }
    }
  }
}
.box {
  // margin-top:58px;
  position: relative;
  width: 1754px;
  min-height: 826px;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  padding-left: 40px;
  padding-top: 40px;
  padding-bottom: 20px;
  box-sizing: border-box;
  & > div {
    margin-bottom: 32px;
    font-size: 18px;
    font-family: Microsoft YaHei-Bold, Microsoft YaHei;
    font-weight: bold;
    color: #0b1a44;
  }
  .bgImg {
    position: absolute;
    right: 105px;
    top: 73px;
    width: 290px;
    height: 200px;
  }
  ::v-deep {
    .el-form-item__label {
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #0b1a44;
    }
    .trainName {
      width: 500px;

      .el-input__inner {
        width: 500px;
        height: 40px;
        border-radius: 4px 4px 4px 4px;
        opacity: 1;
        border: 1px solid #d8dbe1;
      }
    }
    .remark {
      width: 500px;
      .el-textarea__inner {
        width: 500px;
        height: 104px;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #d8dbe1;
      }
    }
    .trainStatus {
      .el-radio__input.is-checked .el-radio__inner {
        // background: #3464e0;
        background: #fff;
        width: 18px;
        height: 18px;
        border-color: #3464e0;
        // border: none;
        &::after {
          background-color: #3464e0;
          width: 8px;
          height: 8px;
        }
      }
      .el-radio__inner {
        width: 18px;
        height: 18px;
      }
      .el-radio__input.is-checked + .el-radio__label {
        color: #0b1a44;
      }
      .el-radio__label {
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #0b1a44;
      }
    }
    .startTime {
      .el-range-editor--small.el-input__inner {
        height: 38px;

        background: #e9ebef;
      }
      .el-range-separator {
        line-height: 32px;
      }
      .el-range-editor--small .el-range__close-icon,
      .el-range-editor--small .el-range__icon {
        line-height: 32px;
        font-size: 16px;
      }
      .el-range-editor--small .el-range-input {
        height: 100%;
        background: #e9ebef;
      }
    }
    .meetingRoomName {
      display: flex;
      & > div {
        position: relative;
        display: inline-block;
        margin-right: 32px;
        width: 202px;
        height: 120px;
        background: #565555;
        border-radius: 8px 8px 8px 8px;
        cursor: pointer;

        .occupy {
          position: absolute;
          top: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 124px;
          height: 26px;
          background: #eb6557;
          border-radius: 0px 0px 8px 8px;
          line-height: 26px;
          font-size: 12px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #ffffff;
          text-align: center;
        }
        .name {
          position: relative;
          display: block;
          line-height: 120px;
          width: 90px;
          margin: 0 auto;
          text-align: center;
          font-size: 14px;
          font-family: '黑体';
          font-weight: bold;
          letter-spacing: 2px;
          color: #ffffff;
          .el-icon-edit {
            position: absolute;
            top: 45%;
            right: -13px;
            color: #ff7e26;
          }
        }

        .checked {
          position: absolute;
          bottom: 0;
          right: 0;
        }
      }
      .first {
        background: url('../../../assets/meeting/firstMeeting.png') no-repeat;
        background-size: 100% 100%;
      }
      .second {
        background: url('../../../assets/meeting/secondMeeting.png') no-repeat;
        background-size: 100% 100%;
      }
      .restsRoom {
        .el-input {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 144px;
          .el-input__inner {
            border: none;
            background: #616161;
            color: #fff;
          }
        }
      }
    }
    .attendUsers {
      .checkPerson {
        display: flex;
        flex-wrap: wrap;
        & > div {
          position: relative;
          width: 140px;
          height: 58px;
          padding: 8px 8px 8px 10px;
          background: #f5f5f5;
          border-radius: 4px 4px 4px 4px;
          box-sizing: border-box;
          margin-right: 18px;
          margin-bottom: 18px;
          & > div {
            display: flex;
            align-items: center;
            line-height: 0;
            span {
              margin-right: 3px;
              font-size: 14px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #0b1a44;
            }
          }
          span {
            font-size: 12px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #a3a8bb;
            text-overflow: ellipsis; /*文字隐藏后添加省略号*/
            white-space: nowrap; /*强制不换行*/
          }
          .close {
            width: 18px;
            height: 18px;
            position: absolute;
            right: -4px;
            top: -4px;
            cursor: pointer;
          }
        }
      }
      .addButton {
        padding: 0;
        width: 80px;
        height: 32px;
        background: #eaeffc;
        // border-radius: 20px 20px 20px 20px;
        border: none;
        span {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #3464e0;
        }
        .el-icon-plus {
          font-weight: 600;
          color: #3464e0;
        }
      }
    }
    .uploadFile {
      .el-form-item__content {
        display: flex;
      }
      .fileList {
        display: flex;
        li {
          margin-right: 32px;
          padding: 14px 10px 20px 10px;
          width: 268px;
          height: 124px;
          background: #f9f9f9;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #eeeeef;
          box-sizing: border-box;
          & > div {
            &:first-of-type {
              display: flex;
              justify-content: flex-start;
              align-items: center;
              .fileImg {
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
                img {
                  width: 46px;
                  height: 38px;
                }
                span {
                  font-size: 12px;
                  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                  font-weight: 400;
                  color: #868b9f;
                  line-height: initial;
                }
              }
              & > div {
                margin-left: 3px;
                line-height: initial;
                .el-textarea {
                  width: initial;
                  .el-textarea__inner {
                    width: 100%;
                  }
                }

                &:last-of-type {
                  span {
                    display: inline-block;
                    max-width: 160px;
                    font-size: 14px;
                    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                    font-weight: 400;
                    color: #0b1a44;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  }
                  i {
                    margin-left: 5px;
                    color: #ff7e26;
                    font-size: 18px;
                    cursor: pointer;
                  }
                }
              }
            }
            &:last-of-type {
              margin-top: 18px;
              font-size: 12px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #a3a8bb;
              line-height: initial;
              i {
                font-size: 18px;
                float: right;
                margin-left: 15px;
                cursor: pointer;
              }
              .el-icon-download:hover {
                color: #3464e0;
              }
              .el-icon-delete:hover {
                color: #eb6557;
              }
            }
          }
        }
      }

      .el-upload-dragger {
        width: 268px;
        height: 124px;
        background: #f9f9f9;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #eeeeef;
      }
      .uploadIcon {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 15px;
        i {
          font-size: 18px;
          margin-right: 5px;
        }
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #3464e0;
      }
      .el-upload {
        order: 999 !important;
      }
      .el-upload__text {
        padding: 0 10px;
        margin: 0 auto;
        line-height: 25px;
        font-size: 12px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #a3a8bb;
      }
    }
    .addMeeting_Button {
      // padding-bottom: 28px;
      .el-button {
        width: 260px;
        height: 46px;
        background: #3464e0;
        border-radius: 4px 4px 4px 4px;
        border: none;
      }
    }
  }
}

.addPersonDrawer {
  .drawerTitle {
    font-size: 16px;
    font-family: Microsoft YaHei-Bold, Microsoft YaHei;
    font-weight: bold;
    color: #0b1a44;
  }
  .checkPerson {
    display: flex;
    flex-wrap: wrap;
    padding-left: 24px;
    padding-bottom: 90px;
    & > div {
      position: relative;
      width: 134px;
      height: 54px;
      padding: 8px 8px 8px 10px;
      background: #f5f5f5;
      border-radius: 4px 4px 4px 4px;
      box-sizing: border-box;
      margin-right: 18px;
      margin-bottom: 18px;
      & > div {
        display: flex;
        align-items: center;
        line-height: 0;
        margin-bottom: 5px;
        span {
          margin-right: 3px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
        }
      }
      span {
        font-size: 12px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #a3a8bb;
        text-overflow: ellipsis; /*文字隐藏后添加省略号*/
        white-space: nowrap; /*强制不换行*/
      }
      .close {
        width: 18px;
        height: 18px;
        position: absolute;
        right: -4px;
        top: -4px;
        cursor: pointer;
      }
    }
  }
  .treeTitle {
    width: 100%;
    padding-top: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
    text-align: center;
    font-size: 14px;
    font-family: Microsoft YaHei-Bold, Microsoft YaHei;
    font-weight: bold;
    color: #4a4a4a;
  }
}
::v-deep {
  .el-drawer__header {
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
    margin-bottom: 0px;
  }
  .el-drawer__body {
    overflow-y: scroll;
    overflow-x: hidden;
    padding-top: 22px;
  }

  .addPersonDrawer {
    .serarchName {
      .el-input {
        width: 244px !important;
        height: 30px;
        border-radius: 4px 4px 4px 4px;
        margin-bottom: 9px;

        .el-input__inner {
          height: 100%;
          border-radius: 7px;
        }
        .el-input__inner:focus {
          border-color: #dcdfe6;
          opacity: 1;
        }
        .el-input__suffix {
          display: flex;
          align-items: center;
          margin-right: 10px;

          .el-icon-search {
            font-size: 16px;
            color: #868b9f;
            cursor: pointer;
          }
        }
      }
    }
  }
}
</style>
