<template>
  <div class="app-container">
    <div class="log_container">
      <div class="log_header">
        <span :class="{ checked: activeName === '记录' }" @click="activeName = '记录'">日志记录</span>
        <span :class="{ checked: activeName === '统计' }" @click="activeName = '统计'">日志统计</span>
      </div>
      <div class="log_body">
        <div class="log_body_top">
          <div class="search">
            <el-form ref="searchForm" label-width="80px" inline class="searchForm">
              <el-form-item v-if="activeName === '记录'" label="客户名称:">
                <el-input
                  v-model="queryInfo.customerName"
                  class="customerName"
                  size="small"
                  placeholder="请输入客户名称"
                  clearable
                  @keyup.enter.native="getSecretLog_logList"
                  @clear="getSecretLog_logList"
                ></el-input>
              </el-form-item>
              <el-form-item v-if="activeName === '记录'" label="创建时间:">
                <el-date-picker
                  v-model="date"
                  class="date"
                  size="small"
                  type="daterange"
                  range-separator="→"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  clearable
                  @change="datePickerChange"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item v-if="activeName === '统计'" label="时间:" label-width="50px" style="padding-left: 106px">
                <el-date-picker v-model="logStatisInfo.time" type="date" placeholder="选择日期" size="small" value-format="yyyy-MM-dd" @change="getlogStatisList"> </el-date-picker>
              </el-form-item>
              <el-form-item>
                <!-- <el-button v-if="activeName === '记录'" type="success" size="small" @click="getSecretLog_logList">查询</el-button> -->
                <!-- <el-button v-else type="success" size="small" @click="getlogStatisList">查询</el-button> -->
                <el-button type="primary" size="small" plain @click="reset">重置</el-button>
              </el-form-item>
            </el-form>
            <el-button type="primary" size="small" icon="el-icon-plus" @click="$router.push('/secretLog/addLog')">写日志</el-button>
          </div>
          <div class="person">
            <div v-for="item in userList" :key="item.userId" :class="{ checkedPerson: queryInfo.userId === item.userId }" @click="personSearch(item)">
              <img :src="item.headurl" alt="" />
              <span>{{ item.realName }}</span>
            </div>
          </div>
        </div>
        <div v-if="activeName === '记录'" class="log_content">
          <div class="log_content_left">
            <ul v-infinite-scroll="load">
              <li v-for="item in list" :key="item.logId">
                <div class="list_left">
                  <span>{{ item.time }}</span>
                  <span>{{ item.createTime.substring(item.createTime.indexOf(' ') + 1, item.createTime.length) }}</span>
                </div>
                <div :class="[{ checked: logId === item.logId }, 'list_right']" @click="goDetails(item)">
                  <div>{{ item.name }}</div>
                  <div style="margin: 16px 0 12px 0">客户名称：{{ item.customerNames }}</div>
                  <div>客户联系人：{{ item.contactsNames }}</div>
                </div>
              </li>
            </ul>
          </div>
          <div class="log_content_right">
            <el-descriptions title="日志详情" :column="1">
              <el-descriptions-item label="日志名称">
                <span class="name">{{ detailsInfo.name }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="拜访内容">{{ detailsInfo.visitContent }}</el-descriptions-item>
              <el-descriptions-item label="拜访结果">{{ detailsInfo.visitResult }}</el-descriptions-item>
              <el-descriptions-item label="跟踪客户">
                <el-table :data="detailsInfo.customers" style="max-width: 968px" border :header-cell-style="{ background: '#fafafa', borderColor: '#EEEEEF', color: '#657081', fontWeight: '400' }">
                  <el-table-column align="center" prop="customerName" label="客户名称" width="width"> </el-table-column>
                  <el-table-column align="center" prop="contactsName" label="客户联系人" width="width"> </el-table-column>
                  <el-table-column align="center" prop="majorName" label="所属专业" width="width"> </el-table-column>
                  <el-table-column align="center" prop="phone" label="联系方式" width="width"> </el-table-column>
                </el-table>
              </el-descriptions-item>
              <el-descriptions-item label="明日计划" label-class-name="planContent">
                {{ detailsInfo.planContent }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
        <div v-else class="logStatistics">
          <el-table :data="logStatisList" header-cell-class-name="table_header">
            <el-table-column prop="realName" label="销售人员" width="width" align="center" class-name="realName"> </el-table-column>
            <el-table-column v-for="item in headerList" :key="item.label" align="center" class-name="success" :prop="item.value" :label="item.label" width="width">
              <template v-slot="{ row }">
                <span v-if="row[item.value] === '0'" class="error">未提交</span>
                <span v-else>已提交</span>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            v-if="logStatisList.length > 0"
            layout="total,prev, pager, next"
            style="margin-top: 15px; text-align: right"
            :page-sizes="[5, 10, 15, 20]"
            background
            :total="logStatisTotal"
            :page-size.sync="logStatisInfo.pageSize"
            :current-page.sync="logStatisInfo.pageNum"
            @size-change="getlogStatisList"
            @current-change="getlogStatisList"
          />
        </div>
      </div>

      <!--
      <el-form ref="searchForm" label-width="80px" inline>
        <el-form-item v-if="activeName === '日志记录'" label="客户名称:">
          <el-input v-model="queryInfo.customerName" size="small" placeholder="请输入客户名称" clearable></el-input>
        </el-form-item>
        <el-form-item v-if="activeName === '日志记录'" label="创建时间:">
          <el-date-picker v-model="date" size="small" type="daterange" range-separator="→" start-placeholder="开始日期" end-placeholder="结束日期" clearable @change="datePickerChange"> </el-date-picker>
        </el-form-item>
        <el-form-item v-if="activeName === '日志统计'" label="时间:" label-width="50px">
          <el-date-picker v-model="logStatisInfo.time" type="date" placeholder="选择日期" size="small" value-format="yyyy-MM-dd"> </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button v-if="activeName === '日志记录'" type="success" size="small" @click="getSecretLog_logList">查询</el-button>
          <el-button v-else type="success" size="small" @click="getlogStatisList">查询</el-button>
          <el-button type="primary" size="small" @click="addDialog = true">写日志</el-button>
        </el-form-item>
      </el-form> -->
      <!-- <el-tabs v-model="activeName" type="card" @tab-click="tabClick">
        <el-tab-pane label="日志记录" name="日志记录">
          <el-table :data="list" style="width: 100%" border @row-click="goDetails">
            <el-table-column prop="name" label="标题" width="width" align="center"> </el-table-column>
            <el-table-column prop="customerNames" label="客户名称" width="width" align="center"> </el-table-column>
            <el-table-column prop="contactsNames" label="客户联系人" width="width" align="center"> </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="width" align="center"> </el-table-column>
          </el-table>
          <el-pagination v-if="list.length > 0" layout="total,prev, pager, next" style="margin-top: 15px; text-align: right" :page-sizes="[5, 10, 15, 20]" background :total="total" :page-size.sync="queryInfo.pageSize" :current-page.sync="queryInfo.pageNum" @size-change="getSecretLog_logList" @current-change="getSecretLog_logList" />
        </el-tab-pane>
        <el-tab-pane label="日志统计" name="日志统计">

        </el-tab-pane>
      </el-tabs> -->
    </div>

    <!-- 写日志dialog -->
    <!-- <el-dialog title="写日志" :visible.sync="addDialog" width="1000px" @close="addDialogClose">
      <div slot="footer">
        <el-button @click="addDialog = false">取 消</el-button>
        <el-button type="primary" @click="submitLog">确 定</el-button>
      </div>
    </el-dialog> -->
    <!-- 查看详情 -->
    <el-drawer :title="detailsInfo.name" :visible.sync="drawer" direction="rtl">
      <el-descriptions class="margin-top" :column="1" direction="vertical">
        <el-descriptions-item label="拜访内容">{{ detailsInfo.visitContent }}</el-descriptions-item>
        <el-descriptions-item label="拜访结果">{{ detailsInfo.visitResult }}</el-descriptions-item>
        <el-descriptions-item label="跟踪客户">
          <el-table :data="detailsInfo.customers" style="width: 100%" border>
            <el-table-column align="center" prop="customerName" label="客户名称" width="width"> </el-table-column>
            <el-table-column align="center" prop="contactsName" label="客户联系人" width="width"> </el-table-column>
            <el-table-column align="center" prop="majorName" label="所属专业" width="width"> </el-table-column>
            <el-table-column align="center" prop="majorName" label="联系方式" width="width"> </el-table-column>
          </el-table>
        </el-descriptions-item>
        <el-descriptions-item label="明日计划">
          {{ detailsInfo.planContent }}
        </el-descriptions-item>
      </el-descriptions>
    </el-drawer>
  </div>
</template>

<script>
import { getList } from '@/api/systemUser'
import { secretLog_logList, secretLog_logStatis } from '@/api/secretLog'

import { formatDate } from '@/filters'
import { mapGetters } from 'vuex'
export default {
  name: 'SecretLog',
  data() {
    return {
      activeName: '记录',
      // 日志记录
      queryInfo: {
        customerName: null,
        startTime: null,
        endTime: null,
        createUser: null,
        userId: null,
        pageNum: 1,
        pageSize: 10
      },
      date: null,
      list: [],
      total: 0,
      logId: null,
      addDialog: false,
      userList: [],
      // 日志统计
      logStatisInfo: {
        time: null,
        pageNum: 1,
        pageSize: 7
      },
      logStatisList: [],
      headerList: [],
      logStatisTotal: 0,
      drawer: false,
      detailsInfo: {}
    }
  },
  computed: {
    ...mapGetters(['realName'])
  },
  created() {
    // 穿个参代表是初始化的时候执行的，用于初始化选中第一个日志
    this.getSecretLog_logList(0)
    this.getlogStatisList()
    this.getUserList()
  },
  methods: {
    async getUserList() {
      const { data } = await getList({ pageNum: 1, pageSize: 100, organizationId: 56642510 })
      this.userList = data.list
    },
    load() {
      if (this.list.length === this.total) {
        return
      } else {
        this.queryInfo.pageSize += 1
        this.getSecretLog_logList()
      }
    },

    tabClick(val) {
      if (val.label === '日志记录') {
        this.getSecretLog_logList()
      } else {
        this.getlogStatisList()
      }
    },
    //   获取日志记录
    async getSecretLog_logList(type) {
      const { data } = await secretLog_logList(this.queryInfo)
      this.list = data.list
      this.total = data.total
      if (type === 0) {
        this.logId = this.list[0].logId
        this.goDetails(this.list[0])
      }
      console.log(data)
    },
    // 获取日志统计
    async getlogStatisList() {
      const { data } = await secretLog_logStatis(this.logStatisInfo)
      this.logStatisList = data.list
      this.logStatisTotal = parseInt(data.total)
      const list = this.logStatisList[0]
      this.headerList = [
        {
          label: list.monKey,
          value: 'monValue'
        },
        {
          label: list.tueKey,
          value: 'tueValue'
        },
        {
          label: list.wedKey,
          value: 'wedValue'
        },
        {
          label: list.thuKey,
          value: 'thuValue'
        },
        {
          label: list.friKey,
          value: 'friValue'
        },
        {
          label: list.satKey,
          value: 'satValue'
        },
        {
          label: list.sunKey,
          value: 'sunValue'
        }
      ]
      console.log(this.headerList)
    },
    // 时间搜索的格式化
    datePickerChange(val) {
      if (val) {
        this.queryInfo.startTime = formatDate(val[0])
        this.queryInfo.endTime = formatDate(val[1], 'yyyy-MM-dd') + ' 23:59:59'
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getSecretLog_logList()
    },
    // 重置搜索
    reset() {
      this.queryInfo = {
        customerName: null,
        startTime: null,
        endTime: null,
        createUser: null,
        userId: null,
        pageNum: 1,
        pageSize: 10
      }
      this.date = null
      this.logStatisInfo = {
        time: null,
        pageNum: 1,
        pageSize: 10
      }
      this.getSecretLog_logList()
      this.getlogStatisList()
    },
    personSearch(item) {
      this.queryInfo.userId = item.userId
      this.getSecretLog_logList()
    },
    // 写日志dialog -  关闭事件
    addDialogClose() {
      this.formInfo = {
        name: null,
        time: null,
        visitContent: null,
        visitResult: null,
        planContent: null,
        remark: null,
        contactsIds: []
      }
      this.time = null
      this.trackCustomer = []
      this.$refs['form'].resetFields()
    },
    goDetails(item) {
      this.logId = item.logId
      this.detailsInfo = { ...item }
      // this.drawer = true
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  background: #e8eaed;
  min-height: 100%;
  padding-bottom: 10px;
  .log_container {
    display: flex;
    flex-direction: column;
    width: 1754px;
    height: 860px;
    background: #fff;
    border-radius: 8px 8px 8px 8px;
    .log_header {
      height: 56px;
      padding-top: 18px;
      background: url('../../assets/secretLog/secretLog_header.png') no-repeat;
      background-size: 100%;
      span {
        display: inline-block;
        width: 88px;
        height: 30px;
        margin-left: 30px;
        line-height: 30px;
        border-radius: 4px;
        background: rgba($color: #fff, $alpha: 0.2);
        font-size: 15px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        font-weight: bold;
        color: #ffffff;
        text-align: center;
        cursor: pointer;
      }
      .checked {
        background: #fff;
        color: #0b1a44;
      }
    }
    .log_body {
      flex: 1;
      height: calc(100% - 56px);
      overflow: hidden;

      .log_body_top {
        .search {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-right: 37px;
          .searchForm {
            padding-top: 30px;
            padding-left: 20px;
            .customerName {
              width: 172px;
              height: 36px;
              ::v-deep {
                .el-input__inner {
                  width: 172px;
                  height: 36px;
                  background: #ffffff;
                  border: 1px solid #d8dbe1;
                }
              }
            }
            .date {
              ::v-deep {
                width: 240px;
                height: 36px;
                background: #ffffff;
                border-radius: 4px 4px 4px 4px;
                border: 1px solid #d8dbe1;
                // .el-range__icon{
                //   display: none;
                // }
                .el-range__close-icon {
                  line-height: 28px;
                }
              }
            }
            ::v-deep {
              .el-form-item {
                .el-form-item__label {
                  font-size: 14px;
                  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                  font-weight: 400;
                  color: #0b1a44;
                }
              }
            }
          }
        }
        .person {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          padding-left: 20px;
          padding-top: 8px;
          border-top: 1px solid #eeeeef;
          & > div {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 16px;
            margin-bottom: 8px;
            width: 128px;
            height: 42px;
            border-radius: 4px 4px 4px 4px;
            border: 1px solid #eeeeef;
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #0b1a44;
            cursor: pointer;
            img {
              margin-right: 8px;
              width: 28px;
              height: 28px;
              border-radius: 50%;
              object-fit: cover;
            }
          }
          .checkedPerson {
            background: #e9ebef;
            color: #3464e0;
          }
        }
      }

      .log_content {
        display: flex;
        justify-content: space-between;
        height: 690px;
        .log_content_left {
          height: 100%;
          padding-bottom: 10px;
          width: 574px;
          background: #f5f5f5;
          overflow: auto;
          /* 设置滚动条的样式 */
          &::-webkit-scrollbar {
            width: 3px;
          }
          &::-webkit-scrollbar-thumb {
            background-color: #3465df;
            border-radius: 30px;
          }
          ul {
            li {
              display: flex;
              justify-content: space-between;
              width: 100%;
              .list_left {
                padding-left: 33px;
                padding-top: 38px;
                padding-right: 8px;
                box-sizing: border-box;
                text-align: right;

                & > span:first-of-type {
                  display: inline-block;
                  width: 67px;
                  font-size: 12px;
                  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                  font-weight: 400;
                  color: #0b1a44;
                }
                & > span:last-of-type {
                  font-size: 12px;
                  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                  font-weight: 400;
                  color: #868b9f;
                }
              }
              .list_right {
                width: 466px;
                min-height: 118px;

                padding: 19px 10px 19px 20px;
                background: #ffffff;
                border: 1px solid #eeeeef;
                border-bottom: none;
                cursor: pointer;
                line-height: 18px;
                & > div:first-of-type {
                  font-size: 14px;
                  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
                  font-weight: bold;
                  color: #0b1a44;
                }
                & > div:nth-of-type(n + 2) {
                  font-size: 12px;
                  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                  font-weight: 400;
                  color: #657081;
                }
              }
              .checked {
                position: relative;
                background: #f1f3fb;
                &::before {
                  content: '';
                  position: absolute;
                  left: 0;
                  top: 0;
                  width: 4px;
                  height: 100%;
                  background: #3465df;
                }
              }
              &:last-of-type {
                .list_right {
                  border-bottom: 1px solid #eeeeef;
                }
              }
            }
          }
        }
        .log_content_right {
          flex: 1;
          padding-left: 40px;
          padding-top: 24px;
          padding-right: 100px;
          ::v-deep {
            .el-descriptions__title {
              font-size: 15px;
              font-family: Microsoft YaHei-Bold, Microsoft YaHei;
              font-weight: bold;
              color: #3a5aae;
            }
            .el-descriptions-item__label {
              font-size: 14px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #868b9f;
            }
            .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
              padding-bottom: 32px;
            }
            .planContent {
              font-size: 14px;
              font-family: Microsoft YaHei-Bold, Microsoft YaHei;
              font-weight: bold;
              color: #0b1a44;
            }
            // 修改表格内的文字
            .el-table__body {
              .el-table__row {
                .cell {
                  font-size: 12px;
                  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
                  font-weight: bold;
                  color: #0b1a44;
                }
              }
            }
            .name {
              font-size: 14px;
              font-family: Microsoft YaHei-Bold, Microsoft YaHei;
              font-weight: bold;
              color: #0b1a44;
            }
          }
        }
      }
      .logStatistics {
        border-top: 1px solid #eeeeef;
        padding-top: 24px;
        padding-left: 136px;
        padding-right: 222px;
        .el-table {
          height: 608px;
          &::before {
            display: none;
          }
        }
        ::v-deep {
          .success {
            width: 179px;
            height: 76px;
            padding: 0;
            background: #d3ecd3;
            border: 1px solid rgba(255, 255, 255, 0.64);
            font-size: 14px;
            font-family: Microsoft YaHei-Bold, Microsoft YaHei;
            font-weight: bold;
            color: #1d7d37;
            .cell {
              padding: 0;
              width: 100%;
              height: 100%;
              line-height: 74px;
              span {
                display: block;
                width: 100%;
                height: 100%;
                text-align: center;
              }
              .error {
                color: #dc4f3f !important;
                background: #ecd8d3 !important;
              }
            }
          }
          .realName {
            width: 150px;
            height: 76px;
            background: #eeeeef;
            border: 1px solid #ffffff;
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #0b1a44;
          }
          .table_header {
            width: 179px;
            height: 66px;
            background: #eeeeef;
            border: 1px solid #ffffff;
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #0b1a44;
            &:first-of-type {
              width: 150px;
              height: 66px;
              background: linear-gradient(136deg, #5f80b1 0%, #647591 100%);
              border: 1px solid rgba(255, 255, 255, 0.56);
              font-size: 14px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #ffffff;
            }
          }
        }
      }
    }
  }
}
::v-deep {
  .el-dialog__body {
    padding: 20px 50px;
  }
  .el-drawer__header {
    margin-bottom: 0;
    padding-bottom: 20px;
    border-bottom: 1px solid #e6e6e6;
  }
  .el-drawer__header > :first-child {
    color: #000;
    font-weight: bold;
  }
  .el-drawer__body {
    padding: 0 15px;
    padding-top: 15px;
    .tbody {
      margin-top: 25px;
    }
    .el-descriptions-item__label {
      font-size: 16px;
      font-weight: bold;
      color: #000;
    }
  }
}
</style>
