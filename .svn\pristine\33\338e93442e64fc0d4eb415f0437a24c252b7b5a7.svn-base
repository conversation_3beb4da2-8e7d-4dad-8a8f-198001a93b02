<template>
  <div class="app-container">
    <el-form ref="queryInfo" :model="queryInfo" label-width="80px" inline>
      <el-form-item label="标题:">
        <el-input v-model="queryInfo.name" placeholder="请输入标题" size="small"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="success" size="small" @click="getList">查询</el-button>
        <el-button plain type="primary" size="small" @click="reset">重置</el-button>
        <el-button type="primary" size="small" @click="addPlan"> 新增方案 </el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" style="width: 100%" border>
      <el-table-column align="center" prop="name" label="标题" width="width"> </el-table-column>
      <el-table-column align="center" prop="realName" label="创建人" width="width"> </el-table-column>
      <el-table-column align="center" prop="createTime" label="创建时间" width="width"> </el-table-column>
      <el-table-column align="center" prop="remark" label="备注" width="width"> </el-table-column>
      <el-table-column align="center" label="操作" width="width">
        <template v-slot="{ row }">
          <el-button size="small" type="primary" @click="details(row)">详情</el-button>

          <el-button size="small" type="warning" @click="edit(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="del(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-pagination
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="[10, 15, 20, 30]"
      :total="total"
      :page-size.sync="queryInfo.pageSize"
      :current-page.sync="queryInfo.pageNum"
      style="text-align: center; margin-top: 15px"
      @size-change="getList"
      @current-change="getList"
    >
    </el-pagination>
    <!-- 新增/修改方案 -->
    <AddPlan ref="AddPlan" :show-dialog.sync="showDialog" @success="getList" />
    <!-- 详情 -->
    <PlanDetails ref="PlanDetails" />
  </div>
</template>
<script>
import { tPlanShareList, tPlanShareRemove } from '@/api/tPlanShare'
import AddPlan from '@/views/tPlanShare/addPlan'
import PlanDetails from '@/views/tPlanShare/planDetails'
export default {
  name: '',
  components: {
    AddPlan,
    PlanDetails
  },
  data() {
    return {
      queryInfo: {
        name: null,
        pageNum: 1,
        pageSize: 10
      },
      list: [],
      total: 0,
      showDialog: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    async getList() {
      const { data } = await tPlanShareList(this.queryInfo)
      this.list = data.list
      this.total = data.total
    },
    reset() {
      this.queryInfo = {
        name: null,
        pageNum: 1,
        pageSize: 10
      }
      this.getList()
    },
    addPlan() {
      this.showDialog = true
    },
    details(row) {
      this.$refs['PlanDetails'].openDialog(row)
    },
    edit(row) {
      this.$refs['AddPlan'].showEdit(row)
      this.showDialog = true
    },
    del(row) {
      this.$confirm('确定要删除该方案吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await tPlanShareRemove({ id: row.planShareId })
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    }
  }
}
</script>
<style scoped lang="scss"></style>
