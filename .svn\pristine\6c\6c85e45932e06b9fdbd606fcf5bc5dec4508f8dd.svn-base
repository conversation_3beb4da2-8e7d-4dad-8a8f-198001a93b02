<template>
  <div class="Home">
    <div class="top">
      <div class="left">
        <img src="@/assets/login/logo.png" alt="" />
        <img src="@/assets/Home/text.png" alt="" />
      </div>
      <div class="right">
        <iframe scrolling="no" src="https://tianqiapi.com/api.php?style=ty&skin=durian&color=fff" frameborder="0" width="200" height="75" allowtransparency="true" class="weather"></iframe>
        <span
          class="time"
        >{{ year }} <span style="margin: 0 5px 0 5px">{{ Day }}</span> {{ time }}</span>
        <el-popover placement="top" trigger="click" popper-class="meesage_popover">
          <template v-slot:reference>
            <div class="messageHint">
              <i class="el-icon-message-solid"></i>
              <i class="el-icon-caret-bottom"></i>
            </div>
          </template>
          <template>
            <el-row class="meesage_top" type="flex" justify="space-between" align="middle">
              <span>最新通知(0)</span>
              <span>清空</span>
            </el-row>
            <el-row style="margin-top: 14px">
              <img src="@/assets/personCenter/message_bg.png" alt="" />
            </el-row>
            <div class="no_message">
              <img src="@/assets/personCenter/no_message.png" alt="" />
              <div>暂无最新消息</div>
            </div>
            <el-row class="meesage_info">
              <span></span>
              <span>您有一场会议即将在01-01 10:30开始，请您准时参加</span>
            </el-row>
            <div class="lookAll">查看全部</div>
          </template>
        </el-popover>
        <span class="realName">{{ realName }}</span>
        <el-dropdown class="avatar-container" trigger="click">
          <div class="avatar-wrapper">
            <el-avatar shape="circle" :size="48" fit="cover" :src="avatar" @error="true">
              <img src="@/assets/login/logo.png" />
            </el-avatar>
            <i class="el-icon-caret-bottom" style="color: #fff" />
          </div>
          <el-dropdown-menu slot="dropdown" class="user-dropdown">
            <el-dropdown-item @click.native="changePassword"> 修改密码 </el-dropdown-item>
            <el-dropdown-item divided @click.native="logout"> 退出系统 </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <div class="center">
      <div class="title">系统入口</div>
      <div ref="contentBox" class="contentBox">
        <div class="content" style="margin-right: 700px">
          <!-- 基础信息管理 -->
          <img v-if="keyList.includes('basicInfo')" class="module" src="@/assets/Home/basicInfo.png" alt="" @click="checkedMode(1)" />
          <!-- 项目管理 -->
          <img v-if="keyList.includes('project_outer')" class="module" src="@/assets/Home/project_outer.png" alt="" @click="checkedMode(4)" />
          <!-- 流程管理 -->
          <img v-if="keyList.includes('process')" class="module" src="@/assets/Home/process.png" alt="" @click="checkedMode(2)" />
          <!-- 小微秘 -->
          <img v-if="keyList.includes('secret')" class="module" src="@/assets/Home/xiaoWeiMi.png" alt="" @click="checkedMode(7)" />
          <!-- 会议管理 -->
          <img v-if="keyList.includes('meeting')" class="module" src="@/assets/Home/meeting.png" alt="" @click="checkedMode(3)" />
          <!-- 知识库管理 -->
          <img v-if="keyList.includes('training_repository')" class="module" src="@/assets/Home/training_repository.png" alt="" @click="checkedMode(5)" />
          <!-- 制度管理 -->
          <img v-if="keyList.includes('institution')" class="module" src="@/assets/Home/system.png" alt="" @click="checkedMode(6)" />
          <!-- 任务管理 -->
          <img v-if="keyList.includes('task')" class="module" src="@/assets/Home/task.png" alt="" @click="goTask" />
          <!-- 合同管理 -->
          <img v-if="keyList.includes('contract')" class="module" src="@/assets/Home/contract.png" alt="" @click="checkedMode(8)" />
          <!-- bug测试 -->
          <img v-if="keyList.includes('bug')" class="module" src="@/assets/Home/bug.png" alt="" @click="checkedMode(9)" />
          <!-- 资源库管理 -->
          <img v-if="keyList.includes('library')" class="module" src="@/assets/Home/library.png" alt="" @click="checkedMode(10)" />
          <!-- 绩效管理 -->
          <img v-if="keyList.includes('performance')" class="module" src="@/assets/Home/performance.png" alt="" @click="checkedMode(11)" />
        </div>
        <div v-if="showPage" class="content">
          <img v-if="keyList.includes('contract')" class="module" src="@/assets/Home/contract.png" alt="" @click="checkedMode(8)" />
          <img v-if="keyList.includes('bug')" class="module" src="@/assets/Home/bug.png" alt="" @click="checkedMode(9)" />
          <img v-if="keyList.includes('library')" class="module" src="@/assets/Home/library.png" alt="" @click="checkedMode(10)" />
          <img v-if="keyList.includes('performance')" class="module" src="@/assets/Home/performance.png" alt="" @click="checkedMode(11)" />
        </div>
      </div>
      <template v-if="showPage">
        <div v-show="currentPage === 1" class="goPage" @click="goPage('down')">下一页</div>
        <div v-show="currentPage === 2" class="goPage" @click="goPage('up')">上一页</div>
      </template>
    </div>
    <el-dialog title="修改密码" :visible.sync="changePasswordDialog" width="420px" append-to-body>
      <div>
        <el-form ref="passwordForm" :model="changePasswordInfo" label-width="80px" :rules="passwordRules">
          <el-form-item label="原密码" prop="oldPassword">
            <el-input v-model="changePasswordInfo.oldPassword" type="password"></el-input>
          </el-form-item>
          <el-form-item label="新密码" prop="newPassword">
            <el-input v-model="changePasswordInfo.newPassword" type="password"></el-input>
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input v-model="changePasswordInfo.confirmPassword" type="password"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="changePasswordDialog = false">取 消</el-button>
        <el-button type="primary" @click="onClickPassword">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { formatDate } from '@/filters'
import { changePassword } from '@/api/systemUser'
import { getPassword } from '@/utils/auth'
// import { resetRouter } from '@/router'

export default {
  name: '',
  data() {
    return {
      year: null,
      time: null,
      Day: null,
      times: null,
      changePasswordDialog: false,
      changePasswordInfo: {
        oldPassword: null,
        newPassword: null,
        confirmPassword: null
      },
      passwordRules: {
        oldPassword: [{ required: true, message: '请输入原密码', trigger: 'blur' }],
        newPassword: [{ required: true, message: '请输入新密码', trigger: 'blur' }],
        confirmPassword: [{ required: true, message: '请确认新密码', trigger: 'blur' }]
      },
      currentPage: 1,
      firstList: [],
      showPage: false,
      surplusNum: 0 // 首页剩余的位置
    }
  },
  computed: {
    ...mapGetters(['keyList', 'realName', 'avatar', 'routes', 'userId'])
  },
  mounted() {
    this.getTime()
    this.judgePage()
  },
  beforeDestroy() {
    clearInterval(this.times)
    this.times = null
  },
  methods: {
    checkedMode(type) {
      if (type === 1) {
        this.$store.commit('checkedData/set_data_type', type)
        this.$nextTick(() => {
          this.$router.push('/basicInfo/home')
        })
      } else if (type === 2) {
        this.$store.commit('checkedData/set_data_type', type)
        this.$nextTick(() => {
          this.$router.push('/process/management')
        })
      } else if (type === 3) {
        this.$store.commit('checkedData/set_data_type', type)
        this.$nextTick(() => {
          this.$router.push('/meeting')
        })
      } else if (type === 4) {
        this.$store.commit('checkedData/set_data_type', type)
        this.$nextTick(() => {
          if (this.keyList.includes('project')) {
            this.$router.push('/project')
          } else {
            this.$router.push('/aftermarket')
          }
        })
      } else if (type === 5) {
        this.$store.commit('checkedData/set_data_type', type)
        this.$nextTick(() => {
          if (this.keyList.includes('training')) {
            this.$router.push('/training')
          } else {
            this.$router.push('/repository')
          }
        })
      } else if (type === 6) {
        this.$store.commit('checkedData/set_data_type', type)
        this.$nextTick(() => {
          this.$router.push('/institution')
        })
      } else if (type === 7) {
        this.$store.commit('checkedData/set_data_type', type)
        this.$nextTick(() => {
          const route = this.routes.filter((item) => {
            if (item.meta && item.meta.type === 7) {
              return item
            }
          })
          this.$router.push(route[0].path)
        })
      } else if (type === 8) {
        this.$store.commit('checkedData/set_data_type', type)
        this.$nextTick(() => {
          this.$router.push('/contract')
        })
      } else if (type === 9) {
        this.$store.commit('checkedData/set_data_type', type)
        this.$nextTick(() => {
          this.$router.push('/bug')
        })
      } else if (type === 10) {
        this.$store.commit('checkedData/set_data_type', type)
        this.$nextTick(() => {
          this.$router.push('/library')
        })
      } else if (type === 11) {
        this.$store.commit('checkedData/set_data_type', type)
        this.$nextTick(() => {
          this.$router.push('/performance')
        })
      }
    },
    getTime() {
      this.year = formatDate(new Date(), 'yyyy/MM/dd')
      const wk = new Date().getDay()
      const weeks = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
      this.Day = weeks[wk]
      this.times = setInterval(() => {
        this.time = formatDate(new Date(), 'hh:mm:ss')
      }, 1000)
    },
    changePassword() {
      this.changePasswordDialog = true
    },
    onClickPassword() {
      this.$refs['passwordForm'].validate(async (val) => {
        if (val) {
          if (this.changePasswordInfo.oldPassword === getPassword()) {
            if (this.changePasswordInfo.confirmPassword === this.changePasswordInfo.newPassword) {
              // 通过
              await changePassword({
                userId: this.userId,
                newPassword: this.changePasswordInfo.newPassword,
                oldPassword: this.changePasswordInfo.oldPassword
              })
              this.$message.success('修改密码成功')
              this.changePasswordDialog = false
              // 修改密码后退出登录
              this.$nextTick(() => {
                this.logout()

                this.$message.warning('请重新登录')
              })
            } else {
              this.$message.warning('两次密码不一致')
            }
          } else {
            this.$message.warning('原密码不正确')
          }
        }
      })
    },
    goTask() {
      window.open('http://192.168.1.103:2222/manage/dashboard', 'blank')
    },
    async logout() {
      await this.$store.dispatch('user/logout')
      this.$router.push(`/login`)
    },
    goPage(type) {
      this.$refs['contentBox'].style = type === 'down' ? 'transform: translateX(-2224px)' : 'transform: translateX(0)'
      this.currentPage = type === 'down' ? 2 : 1
    },
    judge(type) {
      let num = 0
      const HomePage = ['basicInfo', 'project_outer', 'process', 'secret', 'meeting', 'training_repository', 'institution', 'task']
      HomePage.forEach((item) => {
        if (!this.keyList.includes(item)) {
          num++
        }
      })
      this.surplusNum = num
    },

    judgePage() {
      const moduleList = document.querySelectorAll('.module')
      if (moduleList.length > 8) {
        this.showPage = true
      } else {
        this.showPage = false
      }
    }
  }
}
</script>

<style scoped lang="scss">
.Home {
  width: 100%;
  height: 100%;
  padding-top: 65px;
  padding-left: 72px;
  padding-right: 72px;
  background: url('../assets/Home/bg.png') no-repeat;
  background-size: cover;
  overflow: hidden;
  .top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      img {
        &:first-of-type {
          width: 50px;
          height: 50px;
          margin-right: 15px;
        }
      }
    }
    .right {
      display: flex;
      align-items: center;
      .time {
        margin-right: 30px;
        font-size: 18px;
        font-family: D-DIN Exp-DINExp-Bold, D-DIN Exp-DINExp;
        // font-weight: bold;
        color: #ffffff;
      }
      .realName {
        font-size: 16px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #ffffff;
        margin-right: 16px;
      }
    }
  }
  .center {
    position: relative;
    max-width: calc(1452px - 28px);
    min-width: 1240px;
    height: 590px;
    margin: 0 auto;
    margin-top: 166px;
    .title {
      margin-bottom: 27px;
      font-size: 22px;
      font-weight: bold;
      background: linear-gradient(180deg, #e5e5e5 0%, #ffffff 41%, #d2d2d2 100%);
      -webkit-background-clip: text;
      color: transparent;
    }
    .contentBox {
      display: flex;
      width: 3748px;
      transition: all 0.3s;
      transition-timing-function: ease-in-out;
      .content {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        width: 1600px;
        height: 536px;
        overflow: hidden;
        img {
          max-width: 332px;
          max-height: 212px;
          margin-bottom: 56px;
          margin-right: 32px;
          cursor: pointer;
        }
        // max-width: 1600px;
        // margin: 0 auto;
        // margin-top: 300px;
      }
    }

    .goPage {
      position: absolute;
      left: 50%;
      bottom: 0;
      transform: translateX(-50%);
      width: 70px;
      height: 30px;
      line-height: 30px;
      background: #fff;
      color: #4783ed;
      text-align: center;
      border-radius: 15px;
      cursor: pointer;
    }
  }
}
.messageHint {
  display: flex;
  align-items: center;
  margin-right: 35px;
  color: #fff;
  cursor: pointer;

  .el-icon-message-solid {
    position: relative;
    font-size: 21px;
    &::after {
      display: none;
      position: absolute;
      top: 0;
      right: 3px;
      z-index: 2;
      width: 5px;
      height: 5px;
      border-radius: 50%;
      background: #eb6557;
      content: '';
    }
  }
  .el-icon-caret-bottom {
    color: #fff;
    font-size: 12px;
  }
}
.weather {
  ::v-deep {
    color: #fff !important;
  }
}
@media screen and (min-width: 1240px) and (max-width: 1585px) {
  .Home {
    padding-top: 30px;
    .center {
      margin-top: 50px !important;
      .content {
        img {
          margin-bottom: 30px;
          &:nth-of-type(3n) {
            margin-right: 0;
          }
        }
      }
    }
  }
}
@media screen and (min-width: 1585px) {
  .center {
    .content {
      img {
        &:nth-of-type(4n) {
          margin-right: 0;
        }
        &:nth-of-type(3n) {
          margin-right: 32px;
        }
      }
    }
  }
}
</style>
