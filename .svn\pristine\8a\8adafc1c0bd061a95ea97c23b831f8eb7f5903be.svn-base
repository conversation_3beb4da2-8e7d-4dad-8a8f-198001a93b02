<template>
  <div class="app-container">
    <div class="bidDetails_top">
      <el-button type="primary" size="small" icon="el-icon-back" @click="$router.push('/bid')">返回</el-button>
      <el-button v-if="bidDetails.isOneself === 1 && isEdit === false" type="primary" size="small" @click="edit">编辑</el-button>
      <el-button v-if="isEdit === true" type="primary" size="small" plain @click="submitEdit">保存</el-button>
    </div>
    <el-card v-if="JSON.stringify(bidDetails) !== '{}'">
      <div slot="header">
        <span style="font-weight: 600">基本信息</span>
      </div>
      <el-form v-if="isEdit" ref="form" :model="formInfo" label-width="90px" style="width: 1200px; margin: 0 auto" :rules="rules">
        <el-form-item label="项目编号:" prop="bidCode" class="name_input">
          <el-input v-model="formInfo.bidCode" placeholder="请输入项目编号" size="small" maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="项目名称:" prop="bidName" class="name_input">
          <el-input v-model="formInfo.bidName" placeholder="请输入项目名称" size="small" maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="客户名称:" prop="customerId" class="level">
          <el-select v-model="formInfo.customerId" placeholder="请选择客户名称" size="small" @focus="getSecretCustomerCustomerList">
            <el-option v-for="item in allCustomer" :key="item.customerId" :value="item.customerId" :label="item.customerName"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="负责人:" prop="userId" class="level">
          <el-select v-model="formInfo.userId" placeholder="请选择客户名称" size="small" @focus="getUserList">
            <el-option v-for="item in userList" :key="item.userId" :label="item.realName" :value="item.userId"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注:" class="name_input">
          <el-input v-model="formInfo.remark" placeholder="请输入备注" type="textarea" size="small" maxlength="20"></el-input>
        </el-form-item>
      </el-form>
      <el-descriptions v-else :title="`项目名称:${bidDetails.bidName}(${bidDetails.bidCode})`">
        <el-descriptions-item label="客户名称">
          <span>{{ bidDetails.customerName }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="负责人">{{ bidDetails.realName }}</el-descriptions-item>
        <el-descriptions-item label="创建人">{{ bidDetails.createUserName }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ bidDetails.createTime }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ bidDetails.updateTime }}</el-descriptions-item>
        <el-descriptions-item label="备注">{{ bidDetails.remark }}</el-descriptions-item>
      </el-descriptions>
    </el-card>
    <el-card v-if="JSON.stringify(bidDetails) !== '{}'" style="margin-top: 15px">
      <div slot="header">
        <div class="bidDetailsFiles_top">
          <span style="font-weight: 600">附件信息</span>
          <el-button type="primary" size="small" icon="el-icon-plus" @click="uploadingFileDialog = true">上传附件</el-button>
        </div>
      </div>
      <el-table :data="bidDetails.fileDtos" style="width: 100%" border>
        <el-table-column align="center" label="序号" width="width" type="index"> </el-table-column>
        <el-table-column align="center" prop="fileName" label="文件名称" width="width"> </el-table-column>
        <el-table-column align="center" prop="fileSize" label="文件大小" width="width">
          <template v-slot="{ row }">
            <span>{{ row.fileSize }}KB</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="realName" label="上传人" width="width"> </el-table-column>
        <el-table-column align="center" prop="createTime" label="上传时间" width="width"> </el-table-column>
        <el-table-column align="center" prop="prop" label="操作" width="width">
          <template v-slot="{ row }">
            <el-button type="primary" size="small" @click="downloadFile(row)">下载</el-button>
            <el-button type="primary" size="small" @click="rechristen(row)">重命名</el-button>
            <el-button type="danger" size="small" @click="delFile(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!-- 客户详情dialog -->
    <el-dialog title="客户详情" :visible.sync="customerDetailsDialog" width="450px">
      <el-descriptions v-if="customerDetailsDialog" :column="1" class="clientInfo_center">
        <el-descriptions-item label="客户名称">
          <span>{{ clientInfo.customerName }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="所属区域">
          <span>{{ clientInfo.name }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="客户来源">
          <span>{{ clientInfo.source | source }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="客户级别">
          <span>{{ clientInfo.level | level }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="客户负责人">
          <span>{{ clientInfo.realName }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="联系方式">
          <span>{{ clientInfo.phone }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="联系地址">
          <span>{{ clientInfo.address }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="邮箱">
          <span>{{ clientInfo.email }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          <span>{{ clientInfo.createTime }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          <span>{{ clientInfo.updateTime }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="备注">
          <span>{{ clientInfo.remark }}</span>
        </el-descriptions-item>
      </el-descriptions>
      <div slot="footer">
        <!-- <el-button size="small" @click="customerDetailsDialog = false">取 消</el-button> -->
        <el-button type="primary" size="small" @click="customerDetailsDialog = false">关 闭</el-button>
      </div>
    </el-dialog>
    <!-- 上传附件的dialog -->
    <el-dialog title="上传附件" :visible.sync="uploadingFileDialog" width="450px" :close-on-click-modal="false">
      <div style="width: 360px; margin: 0 auto">
        <el-upload class="upload-demo" drag :action="action" :headers="header" :file-list="fileList" multiple :before-upload="beforeUpload" :on-success="uploadSuccess" :on-remove="uploadRemove">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div slot="tip" class="el-upload__tip" style="color: red; line-height: 15px">只能上传.doc .docx .xls .xlsx .pdf .jpg .png .zip格式的附件，且大小不超过50MB</div>
        </el-upload>
      </div>
      <div slot="footer">
        <el-button @click="uploadingFileDialog = false">取 消</el-button>
        <el-button type="primary" @click="uploadingFile">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 文件重命名dialog -->
    <el-dialog title="文件重命名" :visible.sync="rechristenDialog" width="400px">
      <div style="width: 300px; margin: 0 auto">
        <el-input v-model="fileName" placeholder="请输入文件名称" clearable></el-input>
      </div>
      <div slot="footer">
        <el-button @click="rechristenDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirmRechristen">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { secretCustomeAllCustomer, secretCustomerCustomerDetails } from '@/api/clientele'
import { bidSecretBidDetails, bidSecretBidUpdate } from '@/api/bid'
import { meetingSaveFile, meetingRenameFile, meetingUpdateFileName, meetingDeleteFile } from '@/api/meeting'
import { getList } from '@/api/systemUser'

import { mapGetters } from 'vuex'
export default {
  name: '',
  data() {
    return {
      bidDetails: {},
      isEdit: false,
      clientInfo: {}, // 客户详情
      customerDetailsDialog: false,
      allCustomer: [],
      uploadingFileDialog: false,
      action: window.config.VUE_APP_BASE_API + '/system/upload/file',
      header: {
        Authorization: null
      },
      formInfo: {
        bidId: this.$route.params.id,
        bidCode: null,
        bidName: null,
        customerId: null,
        userId: null,
        type: null,
        remark: null
      },
      userList: [],
      rules: {
        chanceName: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
        customerId: [{ required: true, message: '请选择客户名称', trigger: 'change', type: 'number' }],
        type: [{ required: true, message: '请选择商机阶段', trigger: 'change', type: 'number' }]
      },
      fileList: [],
      rechristenDialog: false,
      fileName: null,
      oldPath: null,
      fileId: null
    }
  },
  computed: {
    ...mapGetters(['token'])
  },
  created() {
    this.getBidSecretBidDetails()
  },
  methods: {
    async getBidSecretBidDetails() {
      const { data } = await bidSecretBidDetails({ bidId: this.$route.params.id })
      this.bidDetails = data
      console.log(data)
    },
    edit() {
      console.log(this.bidDetails)
      this.formInfo.bidCode = this.bidDetails.bidCode
      this.formInfo.bidName = this.bidDetails.bidName
      this.formInfo.customerId = parseInt(this.bidDetails.customerId)
      this.formInfo.userId = this.bidDetails.userId
      this.formInfo.remark = this.bidDetails.remark
      this.isEdit = true
      this.getSecretCustomerCustomerList()
      this.getUserList()
    },
    // 查询所有客户
    async getSecretCustomerCustomerList() {
      const { data } = await secretCustomeAllCustomer()
      this.allCustomer = data
      console.log(data)
    },
    // 查询所有销售人员
    async getUserList() {
      const { data } = await getList({ pageNum: 1, pageSize: 200, organizationId: 56642510 })
      this.userList = data.list
    },
    // 客户详情
    async customerDetails() {
      const { data } = await secretCustomerCustomerDetails({ customerId: this.bidDetails.customerId })
      this.clientInfo = data
      console.log(this.clientInfo)
      this.customerDetailsDialog = true
    },
    // 提交修改
    submitEdit() {
      this.$refs['form'].validate(async (val) => {
        if (!val) return
        await bidSecretBidUpdate(this.formInfo)
        this.$message.success('修改成功!')
        this.getBidSecretBidDetails()
        this.isEdit = false
      })
    },
    beforeUpload(file) {
      this.header.Authorization = `Bearer ${this.token}`
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isDOC = file.type === 'application/msword'
      const isDOCX = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      const isXLS = file.type === 'application/vnd.ms-excel'
      const isXLSX = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      const isPDF = file.type === 'application/pdf'
      const isZIP = file.type === 'application/x-zip-compressed'
      const isLt2M = file.size / 1024 / 1024 < 50
      if (!isLt2M) {
        this.$message.error('上传附件大小不能超过 50MB!')
      }
      if (!isJPG && !isPNG && !isDOC && !isDOCX && !isXLS && !isXLSX && !isPDF && !isZIP) {
        this.$message.warning('只能上传.doc .docx .xls .xlsx .pdf .jpg .png .zip 格式的附件')
      }
      return (isJPG || isPNG || isDOC || isDOCX || isXLS || isXLSX || isPDF || isZIP) && isLt2M
    },
    uploadSuccess(response, file, fileList) {
      this.fileList = fileList
    },
    uploadRemove(file, fileList) {
      this.fileList = fileList
    },
    async uploadingFile() {
      const list = []
      this.fileList.forEach((item) => {
        list.push({
          fileName: item.name,
          fileSize: item.size / 1024,
          fileUrl: item.response.data[0],
          meetingId: this.$route.params.id,
          belongType: 7
        })
      })
      await meetingSaveFile(list)
      this.$message.success('上传附件成功')
      this.uploadingFileDialog = false
      this.fileList = []
      this.getBidSecretBidDetails()
    },
    // 下载文件
    downloadFile(row) {
      window.open(row.fileUrl, '_blank')
    },
    // 文件重命名
    rechristen(row) {
      this.fileName = row.fileName
      this.oldPath = row.fileUrl.split('workmanage/')[1]
      this.fileId = row.fileId
      this.rechristenDialog = true
    },
    // 确定重命名
    async confirmRechristen() {
      const { data } = await meetingRenameFile({ oldPath: this.oldPath, newPath: this.fileName })
      await meetingUpdateFileName({ fileId: this.fileId, fileName: this.fileName, fileUrl: data })
      this.$message.success('文件重命名成功')
      this.rechristenDialog = false
      this.getBidSecretBidDetails()
    },
    // 删除附件
    delFile(row) {
      this.$confirm('确认要删除该文件吗, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await meetingDeleteFile({ fileId: row.fileId })
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getBidSecretBidDetails()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    }
  }
}
</script>

<style scoped lang="scss">
.bidDetails_top {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}
.bidDetailsFiles_top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
::v-deep {
  .name_input {
    .el-input__inner {
      width: 1200px;
    }
    .el-textarea__inner {
      width: 1200px;
    }
  }
}
</style>
