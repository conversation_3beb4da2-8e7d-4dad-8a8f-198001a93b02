<template>
  <div class="app-container">
    <el-row class="top">
      <el-col :span="22">
        <i class="el-icon-location"></i>
        <span>当前位置：<router-link to="/training">培训管理</router-link> /</span>
        <span>查看培训</span>
      </el-col>

    </el-row>
    <div class="box">
      <div class="title">查看培训</div>
      <el-descriptions v-if="JSON.stringify(meetingDetails) !== `{}`" :column="1" style="margin: 0 auto">
        <el-descriptions-item label="培训标题">
          <span v-if="meetingDetails.trainName" class="trainName">
            {{ meetingDetails.trainName }}
          </span>
          <span v-else class="noData">暂无</span>
        </el-descriptions-item>
        <el-descriptions-item label="培训内容">
          <span v-if="meetingDetails.remark" class="universal">{{ meetingDetails.remark }}</span>
          <span v-else class="noData">暂无</span>
        </el-descriptions-item>
        <el-descriptions-item label="培训状态">
          <span v-if="meetingDetails.trainStatus" class="universal" style="color: #3464e0"> {{ meetingDetails.trainStatus | trainStatus }}</span>
          <span v-else class="noData">暂无</span>
        </el-descriptions-item>
        <el-descriptions-item label="培训时间段">
          <span v-if="meetingDetails.startTime && meetingDetails.endTime" class="universal">{{ meetingDetails.startTime }} 至 {{ meetingDetails.endTime }}</span>
          <span v-else class="noData">暂无</span>
        </el-descriptions-item>
        <el-descriptions-item label="主讲人">
          <span v-if="meetingDetails.trainUserName" class="universal">
            {{ meetingDetails.trainUserName }}
          </span>
          <span v-else class="noData">暂无</span>
        </el-descriptions-item>
        <el-descriptions-item label="预定人">
          <span v-if="meetingDetails.realName" class="universal">
            {{ meetingDetails.realName }}
          </span>
          <span v-else class="noData">暂无</span>
        </el-descriptions-item>
        <el-descriptions-item label="培训地点">
          <span v-if="meetingDetails.meetingRoomName" :class="[{ first: meetingDetails.meetingRoomName === '第一会议室', second: meetingDetails.meetingRoomName === '第二会议室' }, 'meetingRoomName']">{{ meetingDetails.meetingRoomName }}</span>
          <span v-else class="noData">暂无</span>
        </el-descriptions-item>
        <el-descriptions-item label="参会人员">
          <div class="checkPerson">
            <div v-for="item in meetingDetails.userDtos" :key="item.userId">
              <div>
                <span>{{ item.realName }}</span>
                <img v-if="item.sex === 'F'" src="@/assets/meeting/wuman.png" alt="" />
                <img v-else src="@/assets/meeting/man.png" alt="" />
              </div>
              <span>{{ item.organizationName }}-{{ item.jobName }}</span>
              <p v-if="item.signTime">{{ item.signTime }} 签到</p>
              <p v-else style="color: red">没有签到</p>
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="上传附件">
          <ul v-if="meetingDetails.fileDtos" class="fileList">
            <li v-for="item in meetingDetails.fileDtos" :key="item.fileId">
              <div>
                <div class="fileImg">
                  <img src="@/assets/meeting/file.png" alt="" />
                  <span v-if="item.fileSize">{{ item.fileSize }}KB</span>
                </div>
                <div>
                  <el-tooltip class="item" effect="dark" :content="item.fileName" placement="top">
                    <span>{{ item.fileName }}</span>
                  </el-tooltip>
                </div>
              </div>
              <div v-if="item.percentage && item.percentage !== 100">
                <el-progress :percentage="item.percentage"></el-progress>
              </div>
              <div>
                <span v-if="item.realName && item.createTime">{{ item.realName }}上传于{{ item.createTime }}</span>
                <i class="el-icon-download" @click="downloadFile(item)"></i>
              </div>
            </li>
          </ul>
        </el-descriptions-item>
      </el-descriptions>
      <div v-if="(meetingDetails.isOneself === 0 && meetingDetails.isFeedback === 1) || (meetingDetails.feedbackeDtos && meetingDetails.feedbackeDtos.length >= 1)" class="feedback">
        <div class="feedback_title">
          <span>培训心得</span>
          <!--
            <el-row type="flex" justify="center" style="margin-top: 15px">
              <el-col :span="6">
                <el-button type="primary" size="mini" @click="getTrainFeedback">提交</el-button>
              </el-col>
            </el-row> -->
          <span v-if="meetingDetails.isOneself !== 1" slot="reference" class="addButton" @click="dialogVisible = true">+培训心得</span>
          <!-- <span slot="reference" class="addButton" @click="dialogVisible = true">+意见反馈</span> -->
        </div>
        <div class="feedback_content">
          <span v-if="feedbackList.length === 0">暂无培训心得</span>
          <div v-for="(item, index) in feedbackList" :key="index" class="feedback_list">
            <div class="name">
              <span>{{ item.realName }}</span>
              <img v-if="item.sex === 'F'" src="@/assets/meeting/wuman.png" alt="" />
              <img v-else src="@/assets/meeting/man.png" alt="" />
              <span class="time">{{ item.feedbackTime }}</span>
            </div>
            <span class="jobName">{{ item.organizationName }}-{{ item.jobName }}</span>

            <el-tooltip class="item" effect="dark" :content="item.feedback" placement="left">
              <span class="text">{{ item.feedback.length >= 50 ? (item.feedback = item.feedback.substring(0, 50) + '...') : item.feedback }}</span>
            </el-tooltip>
          </div>
          <el-pagination v-if="feedbackList.length > 0" style="text-align: center; margin-top: 15px" layout="total, prev, pager, next" :page-sizes="[5, 10, 15, 20]" background :total="total" :page-size.sync="pageSize" :current-page.sync="pageNum" @size-change="getDetails" @current-change="getDetails" />
        </div>
      </div>
      <el-row type="flex" justify="center">
        <el-button type="primary" size="small" icon="el-icon-back" @click="$router.push('/training')">返回</el-button>
      </el-row>
    </div>

    <el-dialog :visible.sync="dialogVisible" width="733px" @close="feedback = null">
      <el-input v-model="feedback" class="feedback" placeholder="请输入培训心得" type="textarea" show-word-limit maxlength="200"></el-input>
      <template v-slot:footer>
        <span class="footer_button" @click="dialogVisible = false">关闭</span>
        <span class="footer_button" style="background: #3464e0; color: #fff; margin-left: 32px" @click="getTrainFeedback">提交</span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { trainDetails, trainFeedback, trainFeedDetails } from '@/api/training.js'

export default {
  name: '',
  data() {
    return {
      meetingDetails: [],
      feedback: null,
      dialogVisible: false,
      pageNum: 1,
      pageSize: 5,
      feedbackList: [],
      total: null
    }
  },
  created() {
    this.getDetails()
  },
  methods: {
    async getDetails() {
      const { data } = await trainDetails({ trainId: this.$route.params.trainId, belongType: 2 })
      const res = await trainFeedDetails({ trainId: this.$route.params.trainId, pageNum: this.pageNum, pageSize: this.pageSize })
      this.feedbackList = res.data.list
      this.total = res.data.total
      this.meetingDetails = data
    },
    async getTrainFeedback() {
      if (this.feedback) {
        await trainFeedback({ trainId: this.$route.params.trainId, feedback: this.feedback })
        this.dialogVisible = false
      }
      this.getDetails()
    },
    // 下载文件
    downloadFile(row) {
      if (row.fileId) {
        window.open(row.fileUrl, '_blank')
      } else {
        window.open(row.response.data[0], '_blank')
      }
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  position: relative;
  padding: 0;
  padding-right: 40px;
  padding-bottom: 30px;
  background-color: #e8eaed;
  min-height: 100%;
  padding-top: 58px;
}
.top {
  position: absolute;
  top: 0px;
  width: 100%;
  background-color: #e8eaed;
  padding: 24px 0 16px 0;
  z-index: 999;
  .el-col {
    i {
      font-size: 14px;
      color: #657081;
    }
    span {
      &:first-of-type {
        margin: 0 5px;
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #657081;
      }
      &:last-of-type {
        font-size: 14px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        font-weight: bold;
        color: #0b1a44;
      }
    }
  }
}
.box {
  position: relative;
  width: 1754px;
  // min-height: 600px;
  background: #ffffff;
  border-radius: 8px;
  padding-bottom: 10px;
  opacity: 1;
  .title {
    padding: 24px 0 10px 56px;
    margin-bottom: 24px;
    border-bottom: 1px solid #eeeeef;
    font-size: 16px;
    font-family: Microsoft YaHei-Bold, Microsoft YaHei;
    font-weight: bold;
    color: #0b1a44;
  }
  .el-descriptions {
    position: relative;
    width: 1200px;
    margin: 0 !important;
    .noData {
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #b1bac7;
    }
    .trainName {
      font-size: 14px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #0b1a44;
    }
    .universal {
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      color: #0b1a44;
    }
    .meetingRoomName {
      display: block;
      width: 128px;
      height: 94px;
      line-height: 94px;
      background: #565555;
      border-radius: 8px 8px 8px 8px;
      text-align: center;
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      color: #ffffff;
    }
    // 会议室背景图片
  .first {
    background: url('../../../assets/meeting/firstMeeting.png') no-repeat;
    background-size: 100% 100%;
  }
  .second {
    background: url('../../../assets/meeting/secondMeeting.png') no-repeat;
    background-size: 100% 100%;
  }
  // 会议室背景图片 over
    .checkPerson {
      display: flex;
      flex-wrap: wrap;
      & > div {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        position: relative;
        width: 174px;
        height: 76px;
        margin-right: 18px;
        margin-bottom: 18px;
        padding: 8px 14px;
        background: #f5f5f5;
        border-radius: 4px 4px 4px 4px;
        box-sizing: border-box;
        & > div {
          display: flex;
          align-items: center;
          span {
            padding: 0;
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #0b1a44;
          }
          img {
            margin-left: 5px;
            width: initial;
            height: initial;
          }
        }
        span {
          font-size: 12px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          color: #a3a8bb;
          text-overflow: ellipsis; /*文字隐藏后添加省略号*/
          white-space: nowrap; /*强制不换行*/
        }
        p {
          font-size: 12px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          color: #868b9f;
        }
      }
    }
    .fileList {
      display: flex;
      flex-wrap: wrap;
      li {
        margin-right: 32px;
        margin-bottom: 32px;
        padding: 14px 10px 20px 10px;
        width: 268px;
        height: 124px;
        background: #f9f9f9;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #eeeeef;
        box-sizing: border-box;
        &:nth-of-type(3n){
          margin-right: 0;
        }
        & > div {
          &:first-of-type {
            display: flex;
            justify-content: flex-start;
            .fileImg {
              display: flex;
              flex-direction: column;
              // justify-content: center;
              align-items: center;
              img {
                width: 46px;
                height: 38px;
              }
              span {
                font-size: 12px;
                font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                font-weight: 400;
                color: #868b9f;
                line-height: initial;
              }
            }
            & > div {
              margin-left: 3px;
              line-height: 40px;
              &:last-of-type {
                span {
                  display: inline-block;
                  width: 180px;
                  font-size: 14px;
                  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                  font-weight: 400;
                  color: #0b1a44;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  -o-text-overflow: ellipsis;
                }
                i {
                  margin-left: 5px;
                  color: #ff7e26;
                  font-size: 18px;
                  cursor: pointer;
                }
              }
            }
          }
          &:last-of-type {
            margin-top: 18px;
            font-size: 12px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #a3a8bb;
            line-height: initial;
            i {
              font-size: 18px;
              float: right;
              margin-left: 15px;
              cursor: pointer;
            }
            .el-icon-download:hover {
              color: #3464e0;
            }
            .el-icon-delete:hover {
              color: #eb6557;
            }
          }
        }
      }
    }
  }
  .feedback {
    position: absolute;
    top: 74px;
    right: 16px;
    width: 496px;
    // height: 832px;
    padding: 18px 8px 8px 8px;
    background: #f9f3ea;
    box-shadow: inset 0px 3px 0px 0px #f3c057;
    border-radius: 8px;
    .feedback_title {
      padding-left: 22px;
      font-size: 14px;
      font-weight: bold;
      color: #0b1a44;
      .addButton {
        display: inline-block;
        width: 80px;
        height: 28px;
        margin-left: 22px;
        line-height: 28px;
        background: #3464e0;
        border-radius: 4px 4px 4px 4px;
        color: #ffffff;
        text-align: center;
        cursor: pointer;
      }
    }
    .feedback_content {
      overflow: hidden;
      & > span {
        display: block;
        width: 100px;
        margin: 0 auto;
        margin-top: 15px;
        text-align: center;
      }
      margin-top: 8px;
      padding: 0 25px;
      padding-bottom: 15px;
      height: auto;
      background: #ffffff;
      border-radius: 6px;
      .feedback_list {
        padding-top: 25px;
        padding-bottom: 15px;
        border-bottom: 1px solid #e9ebef;

        .name {
          position: relative;
          display: flex;
          align-items: center;
          span {
            padding: 0;
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #0b1a44;
          }
          img {
            margin-left: 5px;
            width: initial;
            height: initial;
          }
          .time {
            position: absolute;
            bottom: -35%;
            right: 0;
            font-size: 12px;
            color: #868b9f;
          }
        }
        .jobName {
          display: block;
          margin-top: 5px;
          font-size: 12px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          color: #a3a8bb;
          text-overflow: ellipsis; /*文字隐藏后添加省略号*/
          white-space: nowrap; /*强制不换行*/
        }
        .text {
          display: block;
          margin-top: 10px;
          font-size: 14px;
          color: #0b1a44;
          line-height: 22px;
        }
      }
    }
  }
}

::v-deep {
  .el-descriptions__header {
    padding: 24px 0 10px 56px;
    border-bottom: 1px solid #eeeeef;
    .el-descriptions__title {
      font-size: 16px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #0b1a44;
    }
  }
  .el-descriptions__body {
    padding-left: 72px;
    .el-descriptions-item {
      padding-bottom: 28px;
      line-height: initial;
      .el-descriptions-item__container {
        align-items: center;
        .el-descriptions-item__label {
          min-width: 75px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #868b9f;
        }
      }
    }
  }
  .el-dialog__header {
    height: 68px;
    background: url('../../../assets/training/dialogHeder_bg.png') no-repeat;
    background-size: 100%;
    .el-dialog__headerbtn .el-dialog__close {
      color: #fff;
      font-size: 24px;
    }
  }
  .el-dialog__body {
    padding: 40px 55px;
    padding-bottom: 32px;
    .feedback {
      .el-textarea__inner {
        height: 347px;
      }
    }
  }
  .el-dialog__footer {
    display: flex;
    justify-content: center;
    .footer_button {
      display: inline-block;
      width: 114px;
      height: 38px;
      line-height: 38px;
      background: #f2f4ff;
      border-radius: 4px;
      font-size: 14px;
      font-weight: bold;
      color: #868b9f;
      text-align: center;
      cursor: pointer;
    }
  }
}
</style>
