<template>
  <div class="app-container">
    <el-descriptions v-if="info" :column="6" border title="基本信息">
      <el-descriptions-item label="实验名称">{{ info.title }}</el-descriptions-item>
      <el-descriptions-item label="实验状态">
        <el-tag :type="info.status === 1 ? 'success' : ''">{{ info.status === 1 ? '完成' : '未完成' }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="平均用时">{{ info.avgTime ? info.avgTime : 0 }}</el-descriptions-item>
      <el-descriptions-item label="平均分数">{{ info.avgScore ? info.avgScore : 0 }}</el-descriptions-item>
      <el-descriptions-item label="用户姓名">{{ info.realname }}</el-descriptions-item>
      <el-descriptions-item label="创建时间">
        {{ info.createTime }}
      </el-descriptions-item>
    </el-descriptions>

    <el-card v-if="info.stepReqs" class="box-card">
      <div slot="header" class="clearfix">
        <span>最近一次实验考试详情（本次实验共{{ info.stepReqs.length }}步,总用时: <i class="time"> {{ totalTime }}</i> , 总得分: <i class="score">{{ info.score }}</i>分 ）</span>
      </div>
      <el-table :data="info.stepReqs" style="width: 100%" border>
        <el-table-column align="center" prop="seq" label="步骤序号" width="60"> </el-table-column>
        <el-table-column align="center" prop="title" label="步骤名称" width="width"> </el-table-column>
        <el-table-column align="center" prop="startTime" label="操作时间" width="150">
          <template v-slot="{ row }">
            <span>{{ row.startTime }}-{{ row.endTime }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="用时/规定用时" width="150">
          <template v-slot="{ row }">
            <span>{{ row.timeUsed }}/{{ row.expectTime }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="expectTime" label="得分/满分" width="150">
          <template v-slot="{ row }">
            <span>{{ row.score }}/{{ row.maxScore }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="repeatCount" label="操作次数" width="80"> </el-table-column>
        <el-table-column align="center" prop="evaluation" label="步骤评价" width="width"> </el-table-column>
        <el-table-column align="center" prop="scoringModel" label="赋分模型" width="width"> </el-table-column>
        <el-table-column align="center" prop="remarks" label="备注" width="width"> </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { emulationGetRecord } from '@/api/emulation'
export default {
  name: '',
  data() {
    return {
      info: null
    }
  },
  computed: {
    totalTime() {
      if (this.info && this.info.timeUsed) {
        const minute = parseInt(this.info.timeUsed / 60)
        const s = this.info.timeUsed % 60
        return `${minute}分钟${s}秒`
      } else {
        return '0分钟'
      }
    }
  },
  created() {
    this.getRecord()
  },
  methods: {
    async getRecord() {
      const { data } = await emulationGetRecord({ emulationId: this.$route.params.id })
      this.info = data
    }
  }
}
</script>

<style scoped lang="scss">
.box-card {
  margin-top: 30px;
  .clearfix {
    font-size: 16px;
  }
  .time,
  .score {
    font-style: normal;
    color: #006eff;
  }
  .score {
    color: #c72424;
  }
}
</style>
