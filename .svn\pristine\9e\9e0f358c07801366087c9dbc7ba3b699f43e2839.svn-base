<template>
  <div class="">
    <el-dialog :title="showTitle" :visible="showDialog" width="420px" :close-on-click-modal="false" @close="close">
      <el-form ref="form" :model="role" label-width="80px" :rules="rules">
        <el-form-item label="角色标识:" label-width="130px" prop="roleKey"><el-input v-model="role.roleKey" maxlength="20" placeholder="请输入角色标识" :disabled="role.roleId ? true : false"></el-input></el-form-item>
        <el-form-item label="角色名称:" label-width="130px" prop="roleName"><el-input v-model="role.roleName" maxlength="20" placeholder="请输入角色名称"></el-input></el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="confirmOnClick">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { roleSave, roleUpdate } from '@/api/role'
export default {
  name: 'RoleDialog',
  props: {
    showDialog: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      role: {
        roleName: null,
        roleKey: null
      },
      rules: {
        roleName: [{ required: true, tiggle: 'blur', message: '请输入角色名称 ' }],
        roleKey: [{ required: true, tiggle: 'blur', message: '请输入角色标识 ' }]
      }
    }
  },
  computed: {
    showTitle() {
      return this.role.roleId ? '修改角色' : '添加角色'
    }
  },
  methods: {
    showData(row) {
      this.role = { ...row }
      console.log(row)
    },
    close() {
      this.role = {
        roleName: null,
        roleKey: null
      }
      this.$refs['form'].resetFields()
      this.$emit('update:showDialog', false)
    },
    // 点击确认后触发的事件
    confirmOnClick() {
      this.$refs['form'].validate(async (val) => {
        if (val) {
          if (this.role.roleId) {
            // 有id代表修改
            await roleUpdate(this.role)
            console.log('修改操作')
            this.$message.success('修改成功')
            this.$emit('addRoleSuccess')

            this.close()
          } else {
            roleSave(this.role)
              .then((res) => {
                console.log(res)
                this.$message.success('添加成功')
                this.$refs['form'].resetFields()
                this.$emit('addRoleSuccess')
                this.close()
              })
              .catch((err) => {
                console.log(err)
              })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
