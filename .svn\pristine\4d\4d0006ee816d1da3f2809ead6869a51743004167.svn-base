<template>
  <div class="virtual">
    <div class="box">
      <img class="logo animate__animated animate__zoomIn" src="@/assets/virtual/logo.png" alt="" />
      <img class="light" src="@/assets/virtual/light.png" alt="" />
      <div class="loading_bg">
        <div class="progress_bar" :style="{ '--width': width + '%' }"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: '',
  data() {
    return {
      show: false,
      width: 1
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      const timer = setInterval(() => {
        this.width++
        if (this.width >= 97) {
          clearInterval(timer)
        }
      }, 100)
    }
  }
}
</script>

<style scoped lang="scss">
.virtual {
  position: relative;
  width: 100%;
  height: 100%;
  background: url('~@/assets/virtual/bg.png') no-repeat;
  .box {
    position: absolute;
    top: 284px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    .logo {
      width: 342px;
      height: 154px;
    }
    .light {
      position: absolute;
      left: 170px;
      top: 98px;
      width: 23px;
      height: 75px;
      visibility: hidden;
      animation: move linear 10s infinite 2s;
    }

    @keyframes move {
      0% {
        transform: translateX(0);
        visibility: visible;
      }
      88% {
        visibility: visible;
      }
      90% {
        transform: translateX(338px);
        visibility: hidden;
      }
    }
    .loading_bg {
      position: relative;
      width: 706px;
      height: 48px;
      margin-top: 46px;
      background: url('~@/assets/virtual/loading_bg.png') no-repeat;
      background-size: cover;
      .progress_bar {
        position: absolute;
        left: 10px;
        top: 44%;
        transform: translateY(-50%);
        height: 28px;
        width: var(--width);
        background: linear-gradient(180deg, #48f2ff 0%, #003c8b 100%);
        border-radius: 11px;
        transition: all  0.3s;
      }
    }
  }
}
</style>
