<template>
  <div class="app-container">
    <!-- 搜索查询 -->
    <el-row style="min-width: 1200px; margin-bottom: 20px">
      <el-col :span="24">
        <el-form ref="form" :model="searchForm" label-width="80px" inline>
          <el-form-item label="字典类型" label-width="80px">
            <el-input v-model="searchForm.dictType" size="small" placeholder="请输入字典类型" clearable></el-input>
          </el-form-item>
          <el-form-item label="字典名称" label-width="70px">
            <el-input v-model="searchForm.dictName" size="small" placeholder="请输入字典名称" clearable></el-input>
          </el-form-item>
          <el-form-item label="字典状态" label-width="70px">
            <el-select v-model="searchForm.status" placeholder="请选择状态" size="small">
              <el-option v-for="item in state" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="success" size="small" @click="getdictTypeList">查询</el-button>
            <el-button type="primary" size="small" @click="addDictType">新增数据字典</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <!-- 列表展示 -->
    <el-table v-loading="listLoading" :data="dictTypeList" element-loading-text="Loading" border fit highlight-current-row>
      <el-table-column label="序号" align="center" width="120" type="index"> </el-table-column>
      <el-table-column label="字典类型" align="center" prop="dictType"> </el-table-column>
      <el-table-column label="字典名称" align="center" prop="dictName"> </el-table-column>
      <el-table-column label="字典状态" align="center" prop="status">
        <template v-slot="{ row }">
          <span>{{ row.status | stateHandle }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template v-slot="scope">
          <el-button size="small" type="warning" @click="editDictType(scope.row)">修改字典</el-button>
          <el-button size="small" type="danger" @click="delDictType(scope.row)">删除字典</el-button>
          <el-button size="small" type="info" @click="lookDictType(scope.row)">查看字典数据</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-pagination style="text-align: center; margin-top: 15px" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 15, 20, 30]" :total="total" :page-size.sync="searchForm.pageSize" :current-page.sync="searchForm.pageNum" @size-change="getdictTypeList" @current-change="getdictTypeList"> </el-pagination>
    <!-- 添加和修改考生的弹出框 -->
    <improvement ref="dictTypeDialog" :show-dialog.sync="showDialog" @addStudentSuccess="getdictTypeList" />
  </div>
</template>

<script>
import { getdictTypeList, deleteDictType } from '@/api/dictionaries'
import improvement from './components/improvement.vue'
export default {
  name: 'Dictionaries',
  components: {
    improvement
  },
  data() {
    return {
      listLoading: true,
      dictTypeList: [],
      total: 0,
      searchForm: {
        dictType: null,
        dictName: null,
        status: null,
        pageNum: 1,
        pageSize: 10
      },
      showDialog: false,
      state: [
        {
          label: '启用',
          value: 1
        },
        {
          label: '禁用',
          value: 0
        }
      ]
    }
  },
  created() {
    this.getdictTypeList()
  },
  methods: {
    async getdictTypeList() {
      const { data } = await getdictTypeList(this.searchForm)
      this.total = data.total
      this.dictTypeList = data.list
      this.listLoading = false
    },
    addDictType() {
      this.showDialog = true
    },
    editDictType(row) {
      console.log(row)
      this.$refs['dictTypeDialog'].showData(row)
      this.showDialog = true
    },
    async delDictType(row) {
      try {
        await this.$confirm('确定要删除该字典吗, 是否继续?', '删除字典', {
          type: 'warning'
        })
        await deleteDictType({ dictType: row.dictType })
        this.getdictTypeList()
        this.$message.success('删除成功')
      } catch (err) {
        return new Error(err)
      }
      console.log(row)
    },
    lookDictType(row) {
      if (row.status) {
        this.$router.push(`/dictionaries/data/${row.dictType}`)
      } else {
        this.$message.warning('当前状态已禁用，请启用后再进行查看')
      }
    }
  }
}
</script>

<style scoped lang="sass"></style>
