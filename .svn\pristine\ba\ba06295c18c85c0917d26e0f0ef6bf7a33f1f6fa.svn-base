<template>
  <div class="">
    <el-dialog title="迁移数据" :visible.sync="dialogVisible" width="1200px" top="5vh" custom-class="transfer-data-dialog">
      <el-steps :active="active" finish-status="success" align-center>
        <el-step title="选择迁出人员及区域"> </el-step>
        <el-step title="选择迁入人员"></el-step>
        <el-step title="确认迁入数据"></el-step>
        <el-step title="迁入数据成功"></el-step>
      </el-steps>
      <!-- 迁出人员及区域 -->
      <div v-show="active === 0" class="transfer-user-area">
        <div class="transfer-user">
          <el-form ref="form" inline :model="queryInfo" label-width="80px">
            <el-form-item>
              <el-input v-model="queryInfo.name" placeholder="请输入用户名或姓名" style="width: 220px" clearable @clear="getTransferUserList" @keyup.enter.native="getTransferUserList" />
            </el-form-item>
            <el-form-item>
              <el-select v-model="queryInfo.status" placeholder="请选择状态" style="width: 160px" clearable @change="getTransferUserList">
                <el-option label="在职" :value="1"></el-option>
                <el-option label="离职" :value="0"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-select v-model="queryInfo.sex" placeholder="请选择性别" clearable @change="getTransferUserList">
                <el-option label="男" value="M"></el-option>
                <el-option label="女" value="F"></el-option>
              </el-select>
            </el-form-item>
          </el-form>

          <div class="user-list">
            <el-table :data="transferUserList" style="width: 100%" max-height="500" border highlight-current-row @row-click="handleRowClick">
              <el-table-column align="center" prop="username" label="用户名" width="width"></el-table-column>
              <el-table-column align="center" prop="realName" label="姓名" width="width"></el-table-column>
              <el-table-column align="center" prop="sex" label="性别" width="width">
                <template slot-scope="scope">
                  {{ scope.row.sex | sexHandle }}
                </template>
              </el-table-column>
              <el-table-column align="center" prop="status" label="状态" width="width">
                <template slot-scope="scope">
                  <el-tag v-if="scope.row.status == 1" type="success">在职</el-tag>
                  <el-tag v-else type="danger">离职</el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="transfer-area">
          <div class="transfer-area-title">负责的区域</div>
          <el-empty v-if="!transferAreaList.length" description="暂无数据">
            <template v-slot:image>
              <img src="@/assets/transfer/empty.png" alt="暂无数据" />
            </template>
            <template v-slot:description>
              <div>暂无负责的区域</div>
            </template>
          </el-empty>
          <template v-else>
            <div class="transfer-area-tree">
              <el-checkbox v-model="isAllChecked" @change="handleCheckAllChange">全选</el-checkbox>
              <el-tree
                ref="tree"
                :data="transferAreaList"
                :props="defaultProps"
                show-checkbox
                check-on-click-node
                node-key="id"
                :default-checked-keys="defaultCheckedKeys"
                @check="handleTreeCheck"
              ></el-tree>
            </div>
          </template>
        </div>
      </div>
      <!-- 迁入人员 -->
      <div v-show="active === 1" class="transfer-enter-user">
        <div class="transfer-user">
          <el-form ref="form" inline :model="queryInfo" label-width="80px">
            <el-form-item>
              <el-input v-model="queryInfo.name" placeholder="请输入用户名或姓名" style="width: 220px" clearable @clear="getTransferUserList" @keyup.enter.native="getTransferUserList" />
            </el-form-item>
            <el-form-item>
              <el-select v-model="queryInfo.status" placeholder="请选择状态" clearable style="width: 160px" @change="getTransferUserList">
                <el-option label="在职" :value="1"></el-option>
                <el-option label="离职" :value="0"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-select v-model="queryInfo.sex" placeholder="请选择性别" clearable @change="getTransferUserList">
                <el-option label="男" value="M"></el-option>
                <el-option label="女" value="F"></el-option>
              </el-select>
            </el-form-item>
          </el-form>

          <div class="user-list">
            <el-table :data="transferUserList" style="width: 100%" max-height="500" border highlight-current-row @row-click="handleRowClick_enter">
              <el-table-column align="center" prop="username" label="用户名" width="width"></el-table-column>
              <el-table-column align="center" prop="realName" label="姓名" width="width"></el-table-column>
              <el-table-column align="center" prop="sex" label="性别" width="width">
                <template slot-scope="scope">
                  {{ scope.row.sex | sexHandle }}
                </template>
              </el-table-column>
              <el-table-column align="center" prop="status" label="状态" width="width">
                <template slot-scope="scope">
                  <el-tag v-if="scope.row.status == 1" type="success">在职</el-tag>
                  <el-tag v-else type="danger">离职</el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="transfer-area">
          <div class="transfer-area-title">迁移给</div>
          <el-empty v-if="!currentRow_enter" description="暂无数据">
            <template v-slot:image>
              <img src="@/assets/transfer/empty.png" alt="暂无数据" />
            </template>
            <template v-slot:description>
              <div>请选择迁入人员</div>
            </template>
          </el-empty>

          <div v-else class="transfer-area-content">
            <el-descriptions :column="4" border align="center" direction="vertical" :label-style="{ textAlign: 'center' }" :content-style="{ textAlign: 'center' }">
              <el-descriptions-item label="用户名">{{ currentRow_enter.username }}</el-descriptions-item>
              <el-descriptions-item label="姓名">{{ currentRow_enter.realName }}</el-descriptions-item>
              <el-descriptions-item label="性别">{{ currentRow_enter.sex | sexHandle }}</el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag v-if="currentRow_enter.status == 1" type="success">在职</el-tag>
                <el-tag v-else type="danger">离职</el-tag>
              </el-descriptions-item>
            </el-descriptions>

            <div class="transfer-area-region-name">迁移区域:{{ regionName }}</div>
          </div>
        </div>
      </div>

      <!-- 确认迁入 -->
      <div v-show="active === 2" class="transfer-confirm-enter">
        <div class="transfer-confirm-enter-title">迁移信息确认<i>(如有需要可点击上一步返回修改)</i></div>
        <el-descriptions class="confirm-info" :column="1" border>
          <el-descriptions-item label="原负责人">
            <div class="info-item">
              <div class="info-content">
                <div class="name">{{ currentRow ? currentRow.realName : '' }}</div>
              </div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="新负责人">
            <div class="info-item">
              <div class="info-content">
                <div class="name">{{ currentRow_enter ? currentRow_enter.realName : '' }}</div>
              </div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="迁移区域">
            <div v-if="regionName" class="region-list">
              <el-tag v-for="(region, index) in regionName.split(',')" :key="index" type="" class="region-tag">
                {{ region }}
              </el-tag>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <!-- 迁入数据成功 -->
      <div v-show="active === 3" class="transfer-success">
        <el-result icon="success" title="数据迁移成功">
          <template slot="subTitle"> 数据迁移已完成，请前往数据迁移模块的用户列表，<a @click="goDetail">点击查看详情</a> </template>
          <template slot="extra">
            <el-button type="primary" @click="continueTransfer">继续迁移数据</el-button>
            <el-button type="primary" plain @click="closeDialog">关 闭</el-button>
          </template>
        </el-result>
      </div>

      <div v-show="active !== 3" slot="footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button v-if="active > 0" type="primary" plain @click="prevStep">上一步</el-button>
        <el-button v-if="active <= 1" type="primary" @click="nextStep">下一步</el-button>
        <el-button v-else type="primary" @click="confirmChange">确认变更</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { transfer_transferUserList, transfer_regionByUserId, transfer_dataChange } from '@/api/transfer'
export default {
  name: '',
  data() {
    return {
      dialogVisible: false,
      active: 0,
      queryInfo: {
        name: null,
        status: null,
        sex: null
      },
      transferUserList: [],
      currentRow: null, // 当前选中行
      currentRow_enter: null, // 迁入人员当前选中行
      transferAreaList: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      defaultCheckedKeys: [],
      isAllChecked: false,
      regionName: '', // 迁出区域名称
      submitData: {
        outUserId: null, // 迁出人员id
        regions: [], // 迁出区域id
        enterUserId: null // 迁入人员id
      }
    }
  },
  watch: {
    submitData: {
      handler(val) {
        console.log('实时检测需要提交的数据', val)
      },
      deep: true
    }
  },
  created() {},

  methods: {
    open() {
      this.dialogVisible = true
      this.getTransferUserList()
    },
    async getTransferUserList() {
      const { data } = await transfer_transferUserList(this.queryInfo)
      this.transferUserList = data
    },
    // 新增行点击事件处理
    handleRowClick(row) {
      this.currentRow = row
      this.submitData.outUserId = row.userId
      this.getTransferAreaList()
    },
    handleRowClick_enter(row) {
      this.currentRow_enter = row
      this.submitData.enterUserId = row.userId
    },
    async getTransferAreaList() {
      const { data } = await transfer_regionByUserId({ userId: this.currentRow.userId })
      this.transferAreaList = data
      // 获取所有节点的id作为默认选中的key
      this.defaultCheckedKeys = this.getAllNodeIds(data)
      this.isAllChecked = true
    },
    // 递归获取所有节点的id
    getAllNodeIds(data) {
      let ids = []
      data.forEach((node) => {
        ids.push(node.id)
        if (node.children && node.children.length) {
          ids = ids.concat(this.getAllNodeIds(node.children))
        }
      })
      return ids
    },
    // 处理全选/取消全选
    handleCheckAllChange(val) {
      if (val) {
        this.$refs.tree.setCheckedKeys(this.defaultCheckedKeys)
      } else {
        this.$refs.tree.setCheckedKeys([])
      }
    },
    // 处理树节点选中状态变化
    handleTreeCheck() {
      const checkedKeys = this.$refs.tree.getCheckedKeys()
      this.isAllChecked = checkedKeys.length === this.defaultCheckedKeys.length
    },
    // 上一步
    prevStep() {
      this.active--
    },
    // 下一步
    nextStep() {
      if (this.active === 0) {
        if (!this.currentRow) {
          this.$message.warning('请选择迁出人员')
          return
        }
        if (this.transferAreaList.length === 0 || this.$refs.tree.getCheckedKeys(true).length === 0) {
          this.$message.warning('请选择迁出区域')
          return
        }
        this.submitData.regions = this.$refs.tree.getCheckedKeys(true)
        this.regionName = this.$refs.tree
          .getCheckedNodes()
          .map((item) => {
            return item.name
          })
          .join(',')
      }
      if (this.active === 1) {
        if (!this.currentRow_enter) {
          this.$message.warning('请选择迁入人员')
          return
        }
      }
      this.active++
    },
    async confirmChange() {
      const loading = this.$loading({
        lock: true,
        text: '迁移中...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      try {
        await transfer_dataChange(this.submitData)
        this.$message.success('迁移成功')
        this.active++
      } catch (error) {
        this.$message.error('迁移失败')
      } finally {
        loading.close()
      }
    },
    goDetail() {
      this.$router.push(`/transfer/intro/${this.currentRow_enter.userId}`)
    },
    continueTransfer() {
      this.active = 0
      this.submitData = {
        outUserId: null, // 迁出人员id
        regions: [], // 迁出区域id
        enterUserId: null // 迁入人员id
      }
      this.currentRow = null
      this.currentRow_enter = null
      this.regionName = ''
      this.defaultCheckedKeys = []
      this.isAllChecked = false
      this.transferAreaList = []
      this.getTransferUserList()
    },
    closeDialog() {
      this.dialogVisible = false
      this.$emit('refresh')
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep {
  .transfer-data-dialog {
    .el-steps {
      padding-bottom: 15px;
      border-bottom: 1px solid #e5e5e5;
    }
    .transfer-user-area,
    .transfer-enter-user {
      display: flex;
      justify-content: space-between;
      .transfer-user {
        width: 680px;
        padding-right: 10px;
        border-right: 1px solid #e5e5e5;
        .el-form {
          margin-top: 15px;
          .el-form-item {
            &:last-of-type {
              margin-right: 0;
            }
          }
        }
      }
      .transfer-area {
        flex: 1;
        .transfer-area-title {
          padding: 15px 25px;
          font-size: 18px;
          font-weight: bold;
        }
        .transfer-area-tree {
          padding-left: 40px;
        }
        .transfer-area-content {
          padding-left: 20px;
          height: 500px;
          .transfer-area-region-name {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e5e5;
            padding-left: 20px;
            color: #333;
            font-size: 18px;
            font-weight: bold;
          }
        }

        .el-empty {
          .el-empty__image {
            width: 350px;
            height: 350px;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .el-empty__description {
            font-size: 18px;
            font-family: Microsoft YaHei-Bold, Microsoft YaHei;
            font-weight: bold;
            color: #000;
            .el-button {
              margin-top: 15px;
            }
          }
        }
      }
    }
    .el-dialog__footer {
      padding: 10px 20px;
    }

    // 新增选中行样式
    .el-table__body tr.current-row > td {
      background-color: #e6f7ff !important;
      color: #1890ff;
    }

    // 新增鼠标滑过样式
    .el-table__body tr:hover > td {
      cursor: pointer;
    }

    .tree-header {
      padding: 0 25px 10px;
      border-bottom: 1px solid #e5e5e5;
      margin-bottom: 10px;

      .el-checkbox {
        font-size: 16px;
        font-weight: 500;
      }
    }

    .transfer-area-tree {
      .el-checkbox {
        margin-right: 8px;
        margin-bottom: 5px;
        .el-checkbox__input {
          width: 20px;
          height: 20px;
          .el-checkbox__inner {
            width: 100%;
            height: 100%;
            &::after {
              height: 10px;
              width: 5px;
              left: 6px;
            }
          }
        }
        .el-checkbox__label {
          font-size: 18px;
        }
      }

      .el-tree {
        padding: 0 25px;
        padding-left: 0;
        font-size: 16px;
        color: #333;

        .el-tree-node__content {
          height: 40px;
          line-height: 40px;
          margin: 5px 0;

          &:hover {
            background-color: #f5f7fa;
          }
        }

        .el-tree-node__label {
          font-weight: 500;
          font-size: 18px;
        }

        .el-tree-node.is-current > .el-tree-node__content {
          background-color: #e6f7ff;
          color: #1890ff;
        }
      }
    }

    .transfer-confirm-enter {
      padding: 20px;

      .transfer-confirm-enter-title {
        font-size: 20px;
        font-weight: bold;
        color: #333;
        margin-bottom: 30px;
        text-align: center;
        i {
          font-style: normal;
          font-size: 16px;
          font-weight: normal;
          color: #646662;
        }
      }

      .confirm-info {
        max-width: 800px;
        margin: 0 auto;

        .el-descriptions__label {
          width: 100px;
          font-size: 18px;
          color: #666;
        }

        .el-descriptions__content {
          font-size: 18px;
        }

        .info-item {
          display: flex;
          align-items: center;
          padding: 10px 0;

          .info-content {
            .name {
              font-size: 16px;
              font-weight: 500;
              color: #333;
            }
          }
        }

        .region-list {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
          padding: 10px 0;

          .region-tag {
            font-size: 14px;
            // padding: 8px 12px;
            border-radius: 4px;
          }
        }
      }
    }
    .transfer-success {
      padding: 20px;
      .el-result__title {
        font-size: 24px;
        font-weight: bold;
      }
      .el-result__subtitle {
        margin-bottom: 20px;
        margin-top: 20px;
        font-size: 20px;
        a {
          color: #1890ff;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
