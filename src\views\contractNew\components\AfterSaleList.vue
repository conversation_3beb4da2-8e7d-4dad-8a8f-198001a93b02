<template>
  <div class="app-container">
    <!-- 售后服务列表表格 -->
    <el-table v-loading="loading" :data="afterSaleList" element-loading-text="加载中..." border stripe style="width: 100%">
      <!-- 售后ID -->
      <el-table-column prop="saleId" label="售后ID" width="80" align="center" />

      <!-- 负责人 -->
      <el-table-column prop="realName" label="负责人" width="100" align="center" />

      <!-- 客户名称 -->
      <el-table-column prop="customerName" label="客户名称" width="120" show-overflow-tooltip />

      <!-- 合同名称 -->
      <el-table-column prop="contractName" label="合同名称" min-width="200" show-overflow-tooltip />

      <!-- 联系人 -->
      <el-table-column prop="contactsName" label="联系人" width="100" align="center" />

      <!-- 服务内容 -->
      <el-table-column prop="content" label="服务内容" min-width="150" show-overflow-tooltip />

      <!-- 服务方式 -->
      <el-table-column prop="saleWay" label="服务方式" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.saleWay === 1 ? 'success' : 'info'">
            {{ getSaleWayText(scope.row.saleWay) }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 开始时间 -->
      <el-table-column prop="startTime" label="开始时间" width="110" align="center" />

      <!-- 结束时间 -->
      <el-table-column prop="endTime" label="结束时间" width="110" align="center" />

      <!-- 附件 -->
      <el-table-column label="附件" width="80" align="center">
        <template slot-scope="scope">
          <el-button v-if="scope.row.files && scope.row.files.length > 0" type="text" size="small" @click="viewFiles(scope.row)"> {{ scope.row.files.length }}个文件 </el-button>
          <span v-else class="text-muted">无</span>
        </template>
      </el-table-column>

      <!-- 操作 -->
      <el-table-column label="操作" width="120" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="viewDetail(scope.row)"> 查看详情 </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 详情对话框 -->
    <el-dialog title="售后服务详情" :visible.sync="dialogVisible" width="60%" :close-on-click-modal="false">
      <div v-if="currentRow" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="售后ID">{{ currentRow.saleId }}</el-descriptions-item>
          <el-descriptions-item label="负责人">{{ currentRow.realName }}</el-descriptions-item>
          <el-descriptions-item label="客户名称">{{ currentRow.customerName }}</el-descriptions-item>
          <el-descriptions-item label="联系人">{{ currentRow.contactsName }}</el-descriptions-item>
          <el-descriptions-item label="服务方式">
            <el-tag :type="currentRow.saleWay === 1 ? 'success' : 'info'">
              {{ getSaleWayText(currentRow.saleWay) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="服务时间"> {{ currentRow.startTime }} 至 {{ currentRow.endTime }} </el-descriptions-item>
          <el-descriptions-item label="合同名称" :span="2">{{ currentRow.contractName }}</el-descriptions-item>
          <el-descriptions-item label="服务内容" :span="2">{{ currentRow.content }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ currentRow.remark || '无' }}</el-descriptions-item>
        </el-descriptions>

        <!-- 附件列表 -->
        <div v-if="currentRow.files && currentRow.files.length > 0" class="files-section">
          <h4>相关附件</h4>
          <el-table :data="currentRow.files" border size="small">
            <el-table-column prop="fileName" label="文件名" show-overflow-tooltip />
            <el-table-column prop="fileSize" label="文件大小" width="100" align="center">
              <template slot-scope="scope">
                {{ formatFileSize(scope.row.fileSize) }}
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="上传时间" width="180" align="center">
              <template slot-scope="scope">
                {{ formatDateTime(scope.row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100" align="center">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="downloadFile(scope.row)"> 下载 </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { contractAfterAllList } from '@/api/contractNew'
export default {
  name: 'AfterSaleList',
  data() {
    return {
      dialogVisible: false,
      loading: false,
      afterSaleList: [],
      currentRow: null
    }
  },
  created() {
    this.getAfterSaleList()
  },
  methods: {
    async getAfterSaleList() {
      this.loading = true
      try {
        const { data } = await contractAfterAllList()
        console.log(data)
        this.afterSaleList = data || []

        // 测试数据格式示例:
        const testData = {
          saleId: 24,
          realName: '石刚锋',
          customerName: '西京学院',
          contractName: '西京学院医学院虚拟仿真及模拟教学系统采购合同',
          contactsName: '张自航',
          content: '服务器硬盘重新分区，远程部署',
          saleWay: 1,
          remark: '艾总联系售后，安装部署完成后，明确告知客户张直航，这次免费部署，下次要收费',
          startTime: '2025-07-22',
          endTime: '2025-07-23',
          files: [
            {
              fileId: 34459,
              fileName: '2025-7-23 影像服务器远程重新部署.png',
              meetingId: 24,
              fileSize: 297,
              createUserId: '03040668381334',
              belongType: 26,
              createTime: '2025-07-23T05:11:02.000+00:00',
              fileUrl: 'http://work.sdzft.com/cloudFile/workmanage/contract/_17532473984532025-7-23 影像服务器远程重新部署.png',
              status: 1
            }
          ]
        }

        // 如果没有数据，使用测试数据进行展示
        if (this.afterSaleList.length === 0) {
          this.afterSaleList = [testData]
        }
      } catch (error) {
        console.error('获取售后服务列表失败:', error)
        this.$message.error('获取售后服务列表失败')
      } finally {
        this.loading = false
      }
    },

    // 获取服务方式文本
    getSaleWayText(saleWay) {
      const saleWayMap = {
        1: '远程服务',
        2: '现场服务',
        3: '电话服务'
      }
      return saleWayMap[saleWay] || '未知'
    },

    // 查看详情
    viewDetail(row) {
      this.currentRow = row
      this.dialogVisible = true
    },

    // 查看附件
    viewFiles(row) {
      this.viewDetail(row)
    },

    // 格式化文件大小
    formatFileSize(size) {
      if (!size) return '0 KB'
      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0
      let fileSize = size

      while (fileSize >= 1024 && index < units.length - 1) {
        fileSize /= 1024
        index++
      }

      return `${fileSize.toFixed(1)} ${units[index]}`
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return ''
      const date = new Date(dateTime)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    // 下载文件
    downloadFile(file) {
      if (file.fileUrl) {
        window.open(file.fileUrl, '_blank')
      } else {
        this.$message.warning('文件链接不存在')
      }
    }
  }
}
</script>
<style scoped lang="scss">
.app-container {
  padding: 20px;
}

.detail-content {
  .el-descriptions {
    margin-bottom: 20px;
  }

  .files-section {
    margin-top: 20px;

    h4 {
      margin-bottom: 10px;
      color: #303133;
      font-weight: 600;
    }
  }
}

.text-muted {
  color: #909399;
  font-size: 12px;
}

.dialog-footer {
  text-align: right;
}

// 表格样式优化
.el-table {
  .el-table__header {
    th {
      background-color: #f5f7fa;
      color: #606266;
      font-weight: 600;
    }
  }

  .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}

// 标签样式
.el-tag {
  font-size: 12px;
}
</style>
