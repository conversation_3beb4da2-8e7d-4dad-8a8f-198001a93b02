<template>
  <div class="">
    <el-dialog :title="showTitle" :visible="showDialog" width="860px" :close-on-click-modal="false" @close="close">
      <div>
        <el-steps :active="active" align-center>
          <el-step title="会议信息"> </el-step>
          <el-step title="参会人员"></el-step>
          <el-step title="上传附件"></el-step>
        </el-steps>
      </div>
      <!-- 1. 会议信息 -->
      <div v-if="active === 0" style="margin-top: 30px">
        <el-form ref="form" :model="formInfo" label-width="100px" :rules="rules" inline>
          <!-- <el-form-item label="会议编号:" prop="meetingCode">
            <el-input v-model="formInfo.meetingCode" placeholder="请输入会议编号,20字以内" maxlength="20" size="small" oninput="value = value.replace(/[\u4E00-\u9FA5]/g,'')" clearable></el-input>
          </el-form-item> -->
          <el-form-item label="会议室名称:" prop="meetingRoomName">
            <el-select v-model="formInfo.meetingRoomName" placeholder="请选择实验室" size="small" :disabled="formInfo.meetingStatus === 2" clearable>
              <el-option label="第一会议室" value="第一会议室"> </el-option>
              <el-option label="第二会议室" value="第二会议室"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="会议主题:" prop="meetingName">
            <el-input v-model="formInfo.meetingName" placeholder="请输入会议主题,20字以内" maxlength="20" size="small" :disabled="formInfo.meetingStatus === 2" clearable></el-input>
          </el-form-item>
          <el-form-item label="会议状态:" prop="meetingStatus">
            <el-select v-model="formInfo.meetingStatus" placeholder="请选择会议状态" size="small" clearable>
              <el-option label="未开始" :value="1"> </el-option>
              <el-option label="进行中" :value="2"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="会议备注:">
            <el-input v-model="formInfo.remark" placeholder="请输入会议备注,200字以内" type="textarea" maxlength="200" size="small" clearable></el-input>
          </el-form-item>
          <el-form-item label="预定时间段:" prop="startTime">
            <el-date-picker v-model="value1" size="small" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" clearable :disabled="formInfo.meetingStatus === 2" @change="pickerChange"> </el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <!-- 2.会议人员 -->
      <div v-if="active === 1">
        <el-row :gutter="10" type="flex" justify="end" style="margin-top: 30px; margin-bottom: 15px">
          <el-col :span="3.5">
            <el-button type="primary" size="small" :disabled="formInfo.meetingStatus === 2" @click="addPersonDialog = true">添加参会人员</el-button>
          </el-col>
        </el-row>
        <el-table :data="checkedTableData ? checkedTableData.slice((pagination.page - 1) * pagination.size, (pagination.page - 1) * pagination.size + pagination.size) : checkedTableData" style="width: 100%" border>
          <el-table-column type="index" label="序号" width="width" align="center"> </el-table-column>
          <el-table-column prop="realName" label="姓名" width="width" align="center"> </el-table-column>
          <el-table-column prop="organizationName" label="部门" width="width" align="center"> </el-table-column>
          <el-table-column prop="jobName" label="岗位" width="width" align="center"> </el-table-column>
          <el-table-column label="操作" width="width" align="center">
            <template v-slot="{ row }">
              <el-button type="danger" size="mini" :disabled="formInfo.meetingStatus === 2" @click="removeData(row)">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination :page-size.sync="pagination.size" :current-page.sync="pagination.page" :total="checkedTableData ? checkedTableData.length : pagination.total" :page-sizes="[5, 10, 15, 20]" layout=" total,prev,pager,next,sizes,jumper" style="text-align: center; margin-top: 15px" @size-change="handleSizeChange" @current-change="handleCurrentChange"> </el-pagination>
      </div>
      <!-- 3.上传附件 -->
      <div v-if="active === 2" style="margin-top: 30px">
        <el-row :gutter="10" type="flex" justify="space-between" align="middle" style="margin-bottom: 30px">
          <el-col :span="21"> 支持扩展名： .doc .docx .xls .xlsx .pdf .jpg文件，大小不超过50M </el-col>
          <el-col :span="2.5">
            <el-button type="primary" size="small" @click="uploadingFileDialog = true">上传附件</el-button>
          </el-col>
        </el-row>
        <el-table :data="fileList" style="width: 100%" border>
          <el-table-column type="index" label="序号" width="width" align="center"> </el-table-column>
          <el-table-column prop="name" label="文件名称" width="width" align="center">
            <template v-slot="{ row }">
              <span>{{ row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="size" label="文件大小" width="100" align="center">
            <template v-slot="{ row }">
              <span>{{ row.fileId ? row.fileSize * 1024 : row.size | formattingFileSize }}</span>
            </template>
          </el-table-column>
          <el-table-column label="上传人" width="100" align="center">
            <template>
              <span>{{ realName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="上传时间" width="width" align="center">
            <template>
              <span>{{ new Date() | formatDate }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="250" align="center">
            <template v-slot="{ row }">
              <el-button type="primary" size="mini" @click="downloadFile(row)">下载</el-button>
              <el-button type="warning" size="mini" @click="rechristen(row)">重命名</el-button>
              <el-button type="danger" size="mini" @click="delFile(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 底部按钮 -->
      <el-divider></el-divider>
      <el-row :gutter="10" type="flex" justify="center">
        <el-col :span="active > 0 ? 5 : 2">
          <el-button v-if="active > 0" type="primary" size="small" plain @click="lastStep">上一步</el-button>
          <el-button v-if="active < 2" type="primary" size="small" @click="next">下一步</el-button>
          <el-button v-if="active === 2" type="primary" size="small" @click="submitData">提 交</el-button>
        </el-col>
      </el-row>

      <!-- <div slot="footer">
        <el-button @click="showDialog = false">取 消</el-button>
        <el-button type="primary" @click="showDialog = false">确 定</el-button>
      </div> -->
    </el-dialog>
    <!-- 添加参会人员dialog -->
    <el-dialog v-if="showDialog" title="选择参会人员" :visible.sync="addPersonDialog" width="width">
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form ref="addPersonForm" :model="addPersonFormInfo" label-width="50px" inline>
            <el-form-item label="姓名:">
              <el-input v-model="addPersonFormInfo.realName" style="width: 200px" size="small" clearable placeholder="请输入姓名" maxlength="20"></el-input>
            </el-form-item>
            <el-form-item label="部门:" label-width="80px">
              <el-select ref="selecteltree" v-model="addPersonFormInfo.organizationName" class="select" size="small" clearable placeholder="请选择部门" @clear="onSelectClear" @focus="getMenuTreeOfParent">
                <el-option v-for="item in menu" :key="item.id" :label="item.organizationName" :value="item.id" style="display: none" />
                <el-tree style="padding-left: 15px" :data="menu" node-key="id" empty-text="暂无菜单" highlight-current :expand-on-click-node="false" :props="defaultProps" current-node-key="id" default-expand-all @node-click="handleNodeClick" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="success" size="small" @click="getPersonList">查询</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <el-table :data="tableData" style="width: 100%" border :row-key="getRowKeys" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="width" :reserve-selection="true"> </el-table-column>
        <el-table-column prop="realName" label="姓名" width="width" align="center"> </el-table-column>
        <el-table-column prop="organizationName" label="部门" width="width" align="center"> </el-table-column>
        <el-table-column prop="jobName" label="岗位" width="width" align="center"> </el-table-column>
      </el-table>
      <el-pagination style="text-align: center; margin-top: 15px" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 15, 20, 30]" :total="total" :page-size.sync="addPersonFormInfo.pageSize" :current-page.sync="addPersonFormInfo.pageNum" @size-change="getPersonList" @current-change="getPersonList" />

      <div slot="footer">
        <el-button @click="addPersonDialog = false">取 消</el-button>
        <el-button type="primary" @click="addPerson">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 上传附件dialog -->
    <el-dialog title="上传附件" :visible.sync="uploadingFileDialog" width="450px">
      <div style="width: 360px; margin: 0 auto">
        <el-upload class="upload-demo" drag action="http://************:8701/system/upload/file" :headers="header" :file-list="fileList" multiple :before-upload="beforeUpload" :on-success="uploadSuccess" :on-remove="uploadRemove">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div slot="tip" class="el-upload__tip" style="color: red; line-height: 15px">只能上传.doc .docx .xls .xlsx .pdf .jpg .png 格式的附件，且大小不超过50MB</div>
        </el-upload>
      </div>
      <div slot="footer">
        <el-button @click="uploadingFileDialog = false">取 消</el-button>
        <el-button type="primary" @click="uploadingFileDialog = false">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 文件重命名dialog -->
    <el-dialog title="文件重命名" :visible.sync="rechristenDialog" width="400px">
      <div style="width: 300px; margin: 0 auto">
        <el-input v-model="fileName" placeholder="请输入文件名称" clearable></el-input>
      </div>
      <div slot="footer">
        <el-button @click="rechristenDialog = false">取 消</el-button>
        <el-button type="primary" @click="uploadingConfirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { meetingJudgeTime, meetingSave, meetingUpdate } from '@/api/meeting'

import { getList } from '@/api/systemUser'
import { getOrganizationTree } from '@/api/organization'

import { formatDate } from '@/filters'
import { mapGetters } from 'vuex'

export default {
  name: 'MeetingDialog',
  props: {
    showDialog: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      active: 0,
      formInfo: {
        meetingCode: null,
        meetingRoomName: null,
        meetingName: null,
        meetingStatus: 1,
        remark: null,
        startTime: null,
        endTime: null,
        users: [],
        meetingFileReqs: []
      },
      rules: {
        meetingRoomName: [
          {
            required: true,
            tigger: 'chagne',
            message: '请选择会议室名称'
          }
        ],
        meetingName: [
          {
            required: true,
            tigger: 'blur',
            message: '请输入会议主题'
          }
        ],
        meetingStatus: [
          {
            required: true,
            tigger: 'chagne',
            message: '请选择会议状态',
            type: 'number'
          }
        ],
        startTime: [
          {
            required: true,
            tigger: 'blur',
            message: '请选择预约时间段'
          }
        ]
      },
      value1: null,
      /** 2.会议人员 */
      checkedTableData: [], // 确定的人员列表
      tableData: [], // 人员列表
      selectData: [], // 多选框选中的数据
      addPersonDialog: false,
      addPersonFormInfo: {
        realName: null,
        organizationName: null,
        organizationId: null,
        pageNum: 1,
        pageSize: 5
      },
      defaultProps: {
        label: 'organizationName',
        children: 'children'
      },
      menu: [],
      total: 0,
      uploadingFileDialog: false,
      // 添加人员的假数据分页
      pagination: {
        size: 5,
        page: 1,
        total: 0
      },
      /** 3.上传附件 */
      header: {
        Authorization: null
      },
      fileList: [],
      rechristenDialog: false,
      fileName: null,
      uid: null
    }
  },
  computed: {
    ...mapGetters(['name', 'token', 'realName']),
    showTitle() {
      return this.formInfo.meetingId ? '修改会议' : '新增会议'
    }
  },

  methods: {
    pickerChange(val) {
      this.formInfo.startTime = formatDate(val[0], 'yyyy-MM-dd hh:mm:ss')
      this.formInfo.endTime = formatDate(val[1], 'yyyy-MM-dd hh:mm:ss')
      return val
    },
    // 上一步
    lastStep() {
      this.active--
    },
    // 下一步
    async next() {
      const info = {
        startTime: this.formInfo.startTime,
        endTime: this.formInfo.endTime,
        meetingRoomName: this.formInfo.meetingRoomName,
        userIds: []
      }
      if (this.active === 0) {
        this.$refs['form'].validate(async (val) => {
          if (val) {
            if (this.formInfo.meetingId && this.formInfo.meetingStatus === 2) {
              this.active = 1
              // this.getPersonList()
            } else {
              const { data } = await meetingJudgeTime(info)
              if (data === 1) {
                this.active = 1
                this.getPersonList()
              } else {
                const message = data.join(',')
                console.log(message)
                this.$message.warning(`当前时间段的${this.formInfo.meetingRoomName}已被占用`)
              }
            }
          }
        })
      } else if (this.active === 1) {
        info.userIds = this.checkedTableData.map((item) => item.userId)
        this.formInfo.users = [...info.userIds]
        if (info.userIds.length < 1) {
          return this.$message.warning('请选择参会人员')
        }
        if (this.formInfo.meetingId && this.formInfo.meetingStatus === 2) {
          this.active = 2
        } else {
          const { data } = await meetingJudgeTime(info)
          if (data === 1) {
            this.active = 2
          } else {
            const message = data.join(',')
            this.$message.warning(`${message}会议时间与当前会议冲突，请重新选择人员或重新选择时间`)
          }
        }
      }
    },
    /** 2.会议人员 */
    // 获取人员列表
    async getPersonList() {
      console.log(this.name)
      const { data } = await getList(this.addPersonFormInfo)
      this.tableData = data.list
      this.total = data.total
      if (this.checkedTableData.length < 1) {
        // this.checkedTableData = this.tableData.filter((item) => {
        //   return item.username === this.name
        // })
        console.log(this.checkedTableData)
      }
    },
    // 确认添加的人员
    addPerson() {
      console.log(this.checkedTableData)
      this.checkedTableData.push(...this.selectData)
      this.checkedTableData = this.deWeight(this.checkedTableData)
      console.log(this.checkedTableData)
      this.addPersonDialog = false
    },
    deWeight(arr) {
      for (var i = 0; i < arr.length - 1; i++) {
        for (var j = i + 1; j < arr.length; j++) {
          if (arr[i].userId === arr[j].userId) {
            // id为需要去重字段
            arr.splice(j, 1)
            // 因为数组长度减小1，所以直接 j++ 会漏掉一个元素，所以要 j--
            j--
          }
        }
      }
      return arr
    },

    // 会议人员列表移除按钮触发
    removeData(row) {
      this.checkedTableData = this.checkedTableData.filter((item) => item.userId !== row.userId)
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectData = val
    },
    getRowKeys(row) {
      return row.userId
    },
    // 选择部门 单选模式下用户点击清空按钮时触发
    onSelectClear() {
      this.addPersonFormInfo.organizationId = null
    },
    // 获取部门树
    getMenuTreeOfParent() {
      getOrganizationTree().then((res) => {
        this.menu = res.data
        console.log(this.menu)
      })
    },
    // 树形节点被点击触发的事件
    handleNodeClick(node) {
      this.addPersonFormInfo.organizationId = node.id
      this.addPersonFormInfo.organizationName = node.organizationName
      this.$refs['selecteltree'].blur()
      console.log(node)
    },
    // 人员列表的假分页
    handleSizeChange(val) {
      this.pagination.size = val
    },
    handleCurrentChange(val) {
      this.pagination.page = val
    },
    /** 2.会议人员  结束 */

    /** 3. 上传附件 */
    beforeUpload(file) {
      this.header.Authorization = `Bearer ${this.token}`
      console.log(file.type)
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isDOC = file.type === 'application/msword'
      const isDOCX = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      const isXLS = file.type === 'application/vnd.ms-excel'
      const isXLSX = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      const isPDF = file.type === 'application/pdf'
      const isLt2M = file.size / 1024 / 1024 < 50
      if (!isLt2M) {
        this.$message.error('上传附件大小不能超过 50MB!')
      }
      if (!isJPG && !isPNG && !isDOC && !isDOCX && !isXLS && !isXLSX && !isPDF) {
        this.$message.warning('只能上传.doc .docx .xls .xlsx .pdf .jpg .png 格式的附件')
      }
      return (isJPG || isPNG || isDOC || isDOCX || isXLS || isXLSX || isPDF) && isLt2M
    },
    // 上传成功回调
    uploadSuccess(response, file, fileList) {
      this.fileList = fileList
    },
    // 文件列表移除文件时的钩子
    uploadRemove(file, fileList) {
      this.fileList = fileList
    },
    // 下载文件
    downloadFile(row) {
      if (row.fileId) {
        window.open(row.fileUrl, '_blank')
      } else {
        window.open(row.response.data[0], '_blank')
      }
    },
    // 删除文件
    delFile(row) {
      console.log(row)
      this.fileList = this.fileList.filter((item) => item.uid !== row.uid)
    },
    // 重命名
    rechristen(row) {
      console.log(row)
      this.fileName = row.name
      this.uid = row.uid
      this.rechristenDialog = true
    },
    // 重命名dialog确定按钮触发事件
    async uploadingConfirm() {
      this.fileList.forEach((item) => {
        if (item.uid === this.uid) {
          item.name = this.fileName
        }
      })
      this.rechristenDialog = false
    },
    // 提交数据
    async submitData() {
      if (this.formInfo.meetingId) {
        this.$set(this.formInfo, 'meetingFileReqs', [])
        if (this.fileList.length >= 1) {
          this.fileList.forEach((item) => {
            this.formInfo.meetingFileReqs.push({
              fileName: item.name,
              fileSize: item.fileSize || item.size / 1024,
              fileUrl: item.fileUrl || item.response.data[0]
            })
          })
        }
        console.log(this.formInfo)
        await meetingUpdate(this.formInfo)
        this.$message.success('修改会议成功')
        this.$emit('update:showDialog', false)
      } else {
        if (this.fileList.length >= 1) {
          this.fileList.forEach((item) => {
            this.formInfo.meetingFileReqs.push({
              fileName: item.name,
              fileSize: item.size / 1024,
              fileUrl: item.response.data[0]
            })
          })
        }
        await meetingSave(this.formInfo)
        this.$message.success('添加会议成功')
        this.$emit('update:showDialog', false)
      }
    },
    // 修改数据回显
    edit(data) {
      this.formInfo = { ...data }
      this.value1 = [data.startTime, data.endTime]
      this.checkedTableData = data.userDtos
      this.fileList = data.fileDtos
      this.fileList.forEach((item, index) => {
        this.$set(this.fileList[index], 'name', item.fileName)
        item['uid'] = item.fileId
      })
    },
    close() {
      this.active = 0
      /** 1.会议信息数据重置 */
      this.formInfo = {
        meetingCode: null,
        meetingRoomName: null,
        meetingName: null,
        meetingStatus: 1,
        remark: null,
        startTime: null,
        endTime: null,
        users: [],
        meetingFileReqs: []
      }
      this.value1 = null
      /** 2.会议人员数据重置 */
      this.checkedTableData = []
      this.tableData = [] // 人员列表
      this.selectData = [] // 多选框选中的数据
      this.addPersonFormInfo = {
        realName: null,
        organizationName: null,
        organizationId: null,
        pageNum: 1,
        pageSize: 5
      }
      this.isDestory = true
      this.menu = []
      /** 3.上传附件数据重置 */
      this.fileList = []
      this.fileName = null
      this.uid = null
      this.$emit('update:showDialog', false)

      this.$emit('success')
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep {
  // .el-date-editor {
  //   width: 350px;
  // }
  .el-input {
    width: 300px;
  }
  .el-textarea {
    width: 300px;
  }
  .select {
    .el-input {
      width: 200px;
    }
  }
  .el-pagination__jump {
    .el-input {
      width: 50px;
    }
  }
  .el-button--primary.is-plain:focus,
  .el-button--primary.is-plain:hover {
    color: #409eff;
    background: #ecf5ff;
    border-color: #b3d8ff;
  }
}
</style>
