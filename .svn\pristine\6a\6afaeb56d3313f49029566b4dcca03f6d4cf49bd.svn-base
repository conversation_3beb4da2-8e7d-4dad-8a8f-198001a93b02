<template>
  <div class="">
    <el-dialog :title="showTitle" :visible="organizationDialog" width="350px" :close-on-click-modal="false" @close="close">
      <el-form ref="form" :model="organizationForm" label-width="90px" :rules="rules">
        <el-form-item v-if="showTitle !== '修改部门'" label="部门id:" prop="organizationId">
          <el-input v-model.number="organizationForm.organizationId" type="number" min="1" oninput="if(value.length > 11) value=value.slice(0, 11)" placeholder="请输入部门id" />
        </el-form-item>
        <el-form-item label="部门名称:" prop="organizationName"><el-input v-model="organizationForm.organizationName" maxlength="20" placeholder="请输入部门名称" /></el-form-item>
        <el-form-item label="部门性质:" prop="type">
          <el-radio-group v-model="organizationForm.type">
            <el-radio :label="1">职能部门</el-radio>
            <el-radio :label="2">研发部门</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序:" prop="sort">
          <el-input
            v-model.number="organizationForm.sort"
            :disabled="organizationForm.parentId === 0"
            oninput="if(value.length > 4) value=value.slice(0, 4)"
            min="1"
            placeholder="请输入排序编号"
            type="number"
          />
        </el-form-item>
        <el-form-item label="上级菜单:" prop="value">
          <el-select ref="selecteltree" v-model="organizationForm.value" :disabled="organizationForm.parentId === 0" @focus="getMenuTreeOfParent">
            <el-option v-for="item in menu" :key="item.id" :label="item.organizationName" :value="item.id" style="display: none" />
            <el-tree
              style="padding-left: 15px"
              :data="menu"
              node-key="id"
              empty-text="暂无菜单"
              highlight-current
              :expand-on-click-node="false"
              :props="defaultProps"
              current-node-key="id"
              default-expand-all
              @node-click="handleNodeClick"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="confirmOnClick">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addOrganization, editOrganization, getOrganizationTree } from '@/api/organization'

export default {
  name: 'OrganizationDialog',
  props: {
    organizationDialog: {
      type: Boolean,
      default: false
    },
    isShowSelect: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      organizationForm: {
        organizationName: null,
        sort: null,
        parentId: null,
        type: null,
        value: null
      },

      rules: {
        organizationId: [{ required: true, tiggle: 'blur', message: '请输入部门id', type: 'number' }],
        organizationName: [{ required: true, tiggle: 'blur', message: '请输入部门名称 ' }],
        type: [{ required: true, tiggle: 'change', message: '请选择部门性质', type: 'number' }],

        value: [{ required: true, tiggle: 'blur', message: '请选择上级菜单' }],
        sort: [
          { required: true, tiggle: 'blur', message: '请填写排序', type: 'number' },
          { min: 0, tiggle: 'blur', message: '排序不得小于0', type: 'number' }
        ]
      },
      menu: [],
      defaultProps: {
        label: 'organizationName',
        children: 'children'
      },
      showTitle: '添加部门'
    }
  },
  created() {
    // this.getMenuTreeOfParent()
  },
  methods: {
    showData(row) {
      this.organizationForm = row
      this.showTitle = '修改部门'
      getOrganizationTree().then((res) => {
        const data = this.TreeToFlat(res.data)
        data.forEach((item) => {
          if (item.id === this.organizationForm.parentId) {
            this.$nextTick(() => {
              this.organizationForm['value'] = item.organizationName
              console.log(this.organizationForm['value'])
            })
          }
        })
      })
      console.log(row)
    },
    close() {
      this.organizationForm = {
        organizationName: null,
        type: null,
        sort: null,
        parentId: null,
        value: null
      }
      this.showTitle = '添加部门'
      this.$refs['form'].resetFields()
      this.$emit('update:organizationDialog', false)
    },
    // 点击确认后触发的事件
    confirmOnClick() {
      if (this.organizationForm.parentId === 0) {
        this.organizationForm.value = '0'
      }
      this.$refs['form'].validate(async (val) => {
        if (val) {
          if (this.showTitle === '修改部门') {
            // 有id代表修改
            const res = await editOrganization(this.organizationForm)
            console.log(res)
            this.$message.success('修改成功')
            this.$emit('addSuccess')

            this.close()
          } else {
            const sort = parseInt(this.organizationForm.sort)
            console.log(typeof sort)
            addOrganization({ ...this.organizationForm, sort })
              .then((res) => {
                console.log(res)
                this.$message.success('添加成功')
                this.$refs['form'].resetFields()
                this.$emit('addSuccess')
                this.close()
              })
              .catch((err) => {
                console.log(err)
              })
          }
        }
      })
    },
    getMenuTreeOfParent() {
      getOrganizationTree().then((res) => {
        this.menu = res.data
        // this.organizationForm.value = this.menu[0].organizationName
        // this.organizationForm.parentId = this.menu[0].id
        console.log(this.menu)
      })
    },
    handleNodeClick(node) {
      this.organizationForm.parentId = node.id
      this.$nextTick(function () {
        this.organizationForm.value = node.organizationName
      })
      this.$refs['selecteltree'].blur()
      console.log(node)
    },
    // 将树形结构扁平化
    TreeToFlat(data) {
      let formatData = []
      for (var i = 0; i < data.length; i++) {
        formatData.push({
          id: data[i].id,
          organizationName: data[i].organizationName
        })
        if (data[i].children) {
          formatData = formatData.concat(this.TreeToFlat(data[i].children))
        }
      }
      return formatData
    }
  }
}
</script>

<style scoped lang="sass"></style>
