<template>
  <div class="app-container">
    <Navbar></Navbar>
    <div class="personalCenter_container">
      <div class="sideBar">
        <ul>
          <li :class="{ active: activeTab === 0 }" @click="tabsChange(0)">
            <svg-icon icon-class="personData"></svg-icon>
            <span>个人资料</span>
          </li>
          <li :class="{ active: activeTab === 1 }" @click="tabsChange(1)">
            <svg-icon icon-class="contacts"></svg-icon>
            <span>通讯录</span>
          </li>
          <li :class="{ active: activeTab === 2 }" @click="tabsChange(2)">
            <svg-icon icon-class="information"></svg-icon>
            <span>我的消息</span>
          </li>
        </ul>
      </div>
      <div class="content">
        <template v-if="activeTab === 0">
          <!-- 个人资料 -->
          <div v-if="!isEditInfo" class="personData">
            <div class="personData_header">
              <span class="title">个人资料</span>
              <span class="editButton" @click="isEditInfo = true">
                <i class="el-icon-edit"></i>
                修改
              </span>
            </div>
            <div class="personData_body">
              <el-row type="flex" justify="center" style="margin-top: 32px; margin-bottom: 64px">
                <el-avatar :size="100" :src="personInfo.headurl" fit="cover">
                  <img src="https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png" />
                </el-avatar>
              </el-row>
              <el-row style="padding-left: 48px">
                <span class="label"> 账号/用户名</span>
                <span class="label_content">{{ personInfo.username }}</span>
              </el-row>
              <el-row style="padding-left: 48px; margin-top: 38px">
                <span class="label">姓名</span>
                <span class="label_content">{{ personInfo.realName }}</span>
              </el-row>
              <el-row style="padding-left: 48px; margin-top: 38px">
                <span class="label">性别</span>
                <span class="label_content">
                  {{ personInfo.sexText }}
                  <img v-if="personInfo.sex === 'F'" src="@/assets/meeting/wuman.png" alt="" />
                  <img v-else src="@/assets/meeting/man.png" alt="" />
                </span>
              </el-row>
              <el-row style="padding-left: 48px; margin-top: 38px">
                <span class="label">手机号码</span>
                <span class="label_content">{{ personInfo.mobile }}</span>
              </el-row>
              <el-row style="padding-left: 48px; margin-top: 38px">
                <span class="label">部门-岗位-直属上级</span>
                <span class="label_content">{{ personInfo.organizationName }} - {{ personInfo.jobName }} - {{ personInfo.higherUserName }}</span>
              </el-row>
            </div>
          </div>
          <!-- 修改个人资料 -->
          <div v-else class="edit_personData">
            <el-row type="flex" justify="center" style="margin-top: 40px; margin-bottom: 64px">
              <div class="edit_avater">
                <el-avatar :size="100" :src="editFormInfo.headurl" fit="cover" @error="errorHandler">
                  <img src="@/assets/personCenter/avatarPreview.png" />
                </el-avatar>
                <span class="text" @click="editAvatar">修改</span>
              </div>
            </el-row>
            <el-form ref="form" :model="editFormInfo" label-width="80px" label-position="top" class="eidtForm">
              <el-form-item label="账号/用户名">
                <el-input v-model="editFormInfo.username" size="small" disabled></el-input>
              </el-form-item>
              <el-form-item label="姓名">
                <el-input v-model="editFormInfo.realName" size="small" placeholder="请输入姓名" maxlength="20" clearable></el-input>
              </el-form-item>
              <el-form-item label="性别">
                <el-radio-group v-model="editFormInfo.sex" style="margin-top: 15px">
                  <el-radio label="M">男 <img src="@/assets/meeting/man.png" alt="" /></el-radio>
                  <el-radio label="F">女 <img src="@/assets/meeting/wuman.png" alt="" /></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="手机号码">
                <el-input v-model="editFormInfo.mobile" size="small" placeholder="请输入手机号" maxlength="50" clearable></el-input>
              </el-form-item>
              <el-form-item label="部门-岗位-直属上级" class="selectInput">
                <el-select ref="selecteltree" v-model="editFormInfo.organizationName" size="small" clearable @focus="getMenuTreeOfParent">
                  <el-option v-for="item in menu" :key="item.id" :label="item.organizationName" :value="item.id" style="display: none" />
                  <el-tree
                    style="padding-left: 15px"
                    :data="menu"
                    node-key="id"
                    empty-text="暂无菜单"
                    highlight-current
                    :expand-on-click-node="false"
                    :props="defaultProps"
                    current-node-key="id"
                    default-expand-all
                    @node-click="handleNodeClick"
                  />
                </el-select>
                <span style="margin: 0 8px; color: #b1bac7"> - </span>
                <el-input v-model="editFormInfo.jobName" size="small" placeholder="岗位" maxlength="20" clearable></el-input>
                <span style="margin: 0 8px; color: #b1bac7"> - </span>
                <el-select v-model="editFormInfo.higherUserId" placeholder="直属上级" size="small" clearable @focus="getUserList">
                  <el-option v-for="item in userList" :key="item.userId" :label="item.realName" :value="item.userId"> </el-option>
                </el-select>
              </el-form-item>
            </el-form>
            <el-row class="editDataSaveButton">
              <span class="cancelButton" @click="cancel">取消</span>
              <span class="saveButton" @click="save">保存</span>
            </el-row>
          </div>
        </template>
        <template v-if="activeTab === 1">
          <addressBook />
        </template>
        <template v-if="activeTab === 2">
          <message />
        </template>
      </div>
    </div>
    <!-- 修改头像dialog -->
    <el-dialog title="头像上传" :visible.sync="editAvatarDialog" width="602px" class="editAvatarDialog">
      <div class="avatar_center_box">
        <el-upload class="upload-demo" drag :action="`${actionUrl}/system/upload/file`" :headers="header" :before-upload="beforeUpload" :show-file-list="false" :on-success="uploadSuccess">
          <div class="uploadIcon">
            <img src="@/assets/personCenter/editAvatar_icon.png" alt="" />
            <span>选择图片</span>
          </div>
          <div class="el-upload__text">支持.jpg .png 格式,大小不超过3MB</div>
          <div slot="tip" class="el-upload__tip" style="color: red; line-height: 15px"></div>
        </el-upload>
        <div class="avatarPreview">
          <span>头像预览</span>
          <el-avatar v-if="avatarPath" :key="avatarPath" :size="100" :src="avatarPath" style="margin-top: 21px" fit="cover" @error="errorHandler">
            <img src="@/assets/personCenter/avatarPreview.png" />
          </el-avatar>
          <el-avatar v-else :size="100" fit="cover" :src="avatarPreview" style="margin-top: 21px"></el-avatar>
        </div>
      </div>
      <div slot="footer">
        <el-row class="editDataSaveButton">
          <span class="cancelButton" @click="cancelEdit">取消</span>
          <span class="saveButton" @click="saveAvatar">保存</span>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Navbar from '@/layout/components/Navbar.vue'
import { mapGetters } from 'vuex'
import { getOrganizationTree } from '@/api/organization'
import { selectSysUser, getList, updateSysUser } from '@/api/systemUser'
import avatarPreview from '@/assets/personCenter/avatarPreview.png'
import addressBook from '@/views/personalCenter/addressBook'
import message from '@/views/personalCenter/message'
export default {
  name: 'PersonUser',
  components: { Navbar, addressBook, message },
  data() {
    return {
      activeTab: 0,
      personInfo: {},
      isEditInfo: false,
      editFormInfo: {},
      menu: [],
      defaultProps: {
        label: 'organizationName',
        children: 'children'
      },
      userList: [],
      editAvatarDialog: false,
      /** 修改头像*/
      actionUrl: window.config.VUE_APP_BASE_API,
      header: {
        Authorization: null
      },
      avatarPreview,
      avatarPath: null
    }
  },
  computed: {
    ...mapGetters(['userId', 'token'])
  },
  mounted() {
    this.getUserData()
  },

  methods: {
    tabsChange(index) {
      if (index === 0) {
        this.getUserData()
      }
      this.activeTab = index
      window.sessionStorage.setItem('zf_admin_userCenter_activeTab', this.activeTab)
    },
    async getUserData() {
      const { data } = await selectSysUser(this.userId)
      this.personInfo = data
      this.editFormInfo = { ...data }
      const activeTab = window.sessionStorage.getItem('zf_admin_userCenter_activeTab')
      this.activeTab = activeTab ? parseInt(activeTab) : 0
    },
    getMenuTreeOfParent() {
      getOrganizationTree().then((res) => {
        this.menu = res.data
        console.log(this.menu)
      })
    },
    handleNodeClick(node) {
      this.editFormInfo.organizationId = node.id
      this.editFormInfo.organizationName = node.organizationName
      this.$refs['selecteltree'].blur()
      this.$forceUpdate()
    },
    async getUserList() {
      const { data } = await getList({ pageNum: 1, pageSize: 2000 })
      this.userList = data.list
    },
    cancel() {
      this.isEditInfo = false
      this.getUserData()
    },
    async save() {
      await updateSysUser(this.editFormInfo)
      this.$message.success('修改成功')
      this.isEditInfo = false
      this.getUserData()
      this.$store.dispatch('user/getInfo')
    },
    /** 3. 上传附件 */
    beforeUpload(file) {
      this.header.Authorization = `Bearer ${this.token}`
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 3
      if (!isLt2M) {
        this.$message.error('上传头像大小不能超过 3MB!')
      }
      if (!isJPG && !isPNG) {
        this.$message.warning('只能上传 .jpg .png 格式的附件')
      }
      return (isJPG || isPNG) && isLt2M
    },
    // 上传成功回调
    uploadSuccess(response, file, fileList) {
      this.avatarPath = response.data[0]
      console.log(this.avatarPath)
    },
    errorHandler() {
      return true
    },
    editAvatar() {
      this.avatarPath = this.editFormInfo.headurl
      this.editAvatarDialog = true
    },
    cancelEdit() {
      this.editAvatarDialog = false
      this.avatarPath = null
    },
    async saveAvatar() {
      await updateSysUser({ headurl: this.avatarPath, userId: this.userId })
      this.$message.success('头像修改成功')
      this.avatarPath = null
      this.getUserData()
      this.$store.dispatch('user/getInfo')
      this.editAvatarDialog = false
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  padding: 0;
  height: 100%;
}
.personalCenter_container {
  display: flex;
  height: calc(100% - 68px);

  .sideBar {
    width: 200px;
    height: 100%;
    padding-top: 32px;
    background: #ffffff;
    ul {
      li {
        display: flex;
        align-items: center;
        padding-left: 37px;
        width: 200px;
        height: 48px;
        border-left: 2px solid transparent;

        cursor: pointer;
        box-sizing: border-box;
        &:hover {
          background: #ebeff7;
          //   border-left: 2px solid #3464e0;
          ::v-deep {
            .svg-icon {
              color: #3464e0;
            }
          }

          span {
            color: #3464e0;
          }
        }
        border-radius: 0px 0px 0px 0px;
        .svg-icon {
          width: 18px;
          height: 16px;
          margin-right: 5px;
        }
        span {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
        }
      }
      .active {
        background: #ebeff7;
        border-left: 2px solid #3464e0;
        ::v-deep {
          .svg-icon {
            color: #3464e0;
          }
        }
        span {
          color: #3464e0;
        }
      }
    }
  }
  .content {
    position: relative;
    flex: 1;
    height: 100%;
    background: #e8eaed;
    .personData,
    .edit_personData {
      position: absolute;
      top: 69px;
      left: 50%;
      transform: translateX(-50%);
      width: 640px;
      height: 680px;
      padding: 0 24px;
      background: #ffffff;
      border-radius: 8px;
      .personData_header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 40px;
        padding-bottom: 15px;
        padding-left: 48px;
        padding-right: 48px;
        border-bottom: 1px solid #eeeeef;
        .title {
          font-size: 16px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
        }
        .editButton {
          i {
            color: #a3a8bb;
          }
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
          cursor: pointer;
        }
      }
      .personData_body {
        .label {
          display: inline-block;
          width: 143px;
          margin-right: 55px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #868b9f;
        }
        .label_content {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
        }
      }
    }
    .edit_personData {
      padding: 0 56px;
      .edit_avater {
        position: relative;
        height: 100px;
        border-radius: 50%;
        overflow: hidden;
        cursor: pointer;
        &:hover {
          .text {
            display: block;
          }
        }
        .text {
          display: none;
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 132px;
          height: 35px;
          line-height: 30px;
          background: rgba($color: #686868, $alpha: 0.6);
          text-align: center;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #ffffff;
        }
      }
      .eidtForm {
        ::v-deep {
          .el-form-item {
            .el-form-item__label {
              padding-bottom: 0;
              line-height: 0;
              font-size: 14px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #868b9f;
            }
            .el-form-item__content {
              .el-input {
                .el-input__inner {
                  border: none;
                  padding-left: 0;
                  border-radius: 0px;
                  border-bottom: 1px solid #d8dbe1;
                  font-size: 14px;
                  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                  font-weight: 400;
                  color: #0b1a44;
                  &:focus + .el-input__prefix {
                    color: #3464e0;
                  }
                  &:focus {
                    border-bottom: 1px solid #3464e0;
                  }
                }
              }
              .el-radio-group {
                display: flex;
                .el-radio__input.is-checked .el-radio__inner {
                  // background: #3464e0;
                  background: #fff;
                  width: 14px;
                  height: 14px;
                  border-color: #3464e0;
                  // border: none;
                  &::after {
                    background-color: #3464e0;
                    width: 8px;
                    height: 8px;
                  }
                }
                .el-radio__inner {
                  width: 14px;
                  height: 14px;
                }
                .el-radio__input.is-checked + .el-radio__label {
                  color: #0b1a44;
                }
                .el-radio__label {
                  font-size: 14px;
                  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                  font-weight: 400;
                  color: #657081;
                  img {
                    vertical-align: middle;
                  }
                }
              }
            }
          }
          .selectInput {
            .el-form-item__label {
              padding-bottom: 17px;
            }
            .el-form-item__content {
              display: flex;
            }
            .el-input {
              width: 140px;
              height: 32px;

              .el-input__inner {
                width: 140px;
                height: 32px;
                padding-left: 15px !important;
                background: #f5f5f5;
                border-radius: 4px 4px 4px 4px !important;
                border: 1px solid #e9ebef !important;
              }
            }
          }
        }
      }
      .editDataSaveButton {
        display: flex;
        justify-content: center;
        .cancelButton,
        .saveButton {
          width: 250px;
          height: 40px;
          line-height: 40px;
          border: 1px solid #f5f5f5;
          background: #fff;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #a3a8bb;
          text-align: center;
          border-radius: 4px;
          cursor: pointer;
        }
        .cancelButton:hover {
          background: #f5f5f5;
          border-color: #d9dce2;
        }
        .saveButton {
          margin-left: 28px;
          border-color: #3e6ce2;
          color: #3464e0;
        }
        .saveButton:hover {
          background: #f5f5f5;
        }
      }
    }
  }
}
.editAvatarDialog {
  ::v-deep {
    .el-dialog__header {
      padding-top: 24px;
      padding-left: 32px;
      padding-right: 32px;
      padding-bottom: 16px;
      border-bottom: 1px solid #eeeeef;
      .el-dialog__title {
        font-size: 16px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #0b1a44;
      }
      .el-dialog__headerbtn {
        top: 25px;
        .el-icon-close {
          font-size: 16px;
          font-weight: bold;
          color: #657081;
        }
      }
    }
    .el-dialog__body {
      padding-left: 103px;
      padding-right: 119px;
      padding-bottom: 53px;
      .avatar_center_box {
        display: flex;
        justify-content: space-between;
        .el-upload-dragger {
          width: 178px;
          height: 193px;
          background: #f2f6fa;
          border: 1px solid #e9ebef;
        }
        .uploadIcon {
          width: 56px;
          margin: 0 auto;
          margin-top: 43px;
          img {
            display: block;
            margin: 0 auto;
            margin-bottom: 8px;
          }
          span {
            text-align: center;
          }
        }
        .el-upload__text {
          padding: 0 17px;
          font-size: 12px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #b1bac7;
          line-height: 30px;
        }
        .avatarPreview {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
        }
      }
    }
    .el-dialog__footer {
      padding: 0;
      padding-bottom: 70px;
      .editDataSaveButton {
        display: flex;
        justify-content: center;
        .cancelButton,
        .saveButton {
          width: 130px;
          height: 40px;
          line-height: 40px;
          border: 1px solid #f5f5f5;
          background: #fff;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #a3a8bb;
          text-align: center;
          border-radius: 4px;
          cursor: pointer;
        }
        .cancelButton:hover {
          background: #f5f5f5;
          border-color: #d9dce2;
        }
        .saveButton {
          margin-left: 28px;
          border-color: #3e6ce2;
          color: #3464e0;
        }
        .saveButton:hover {
          background: #f5f5f5;
        }
      }
    }
  }
}
</style>
