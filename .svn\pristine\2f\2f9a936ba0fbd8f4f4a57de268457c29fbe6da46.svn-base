import Cookies from 'js-cookie'

const Token<PERSON>ey = 'zfkj_admin_token'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(TokenKey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}
export function setPassword(password) {
  return Cookies.set('zf_password', password)
}
export function getPassword() {
  return Cookies.get('zf_password')
}
export function removePassword() {
  return Cookies.remove('zf_password')
}
