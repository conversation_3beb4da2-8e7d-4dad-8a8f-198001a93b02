import request from '@/utils/request'
// 绩效管理
/** 列表 */
export function tAchievementList(data) {
  return request({
    url: '/tAchievement/list',
    method: 'POST',
    data
  })
}
/** 添加 */
export function tAchievementAdd(data) {
  return request({
    url: '/tAchievement/add',
    method: 'POST',
    data
  })
}
/** 修改 */
export function tAchievementUpdate(data) {
  return request({
    url: '/tAchievement/update',
    method: 'POST',
    data
  })
}
/** 删除 */
export function tAchievementRemove(params) {
  return request({
    url: '/tAchievement/remove',
    method: 'DELETE',
    params
  })
}
/** 退回 */
export function tAchievementReturnAchievement(data) {
  return request({
    url: '/tAchievement/returnAchievement',
    method: 'POST',
    data
  })
}
/** 列表导出 */
export function tAchievementListExport(data) {
  return request({
    url: '/tAchievement/listExport',
    method: 'POST',
    data
  })
}
/** 详情 */
export function tAchievementDetail(params) {
  return request({
    url: '/tAchievement/detail',
    method: 'get',
    params
  })
}
