// 小微秘-销售方案
import request from '@/utils/request'
/** 添加 */
export function tSalesPlanAdd(data) {
  return request({
    url: '/tSalesPlan/add',
    method: 'POST',
    data
  })
}
/** 修改 */
export function tSalesPlanUpdate(data) {
  return request({
    url: '/tSalesPlan/update',
    method: 'POST',
    data
  })
}
/** 删除 */
export function tSalesPlanRemove(params) {
  return request({
    url: '/tSalesPlan/remove',
    method: 'DELETE',
    params
  })
}
/** 列表 */
export function tSalesPlanList(params) {
  return request({
    url: '/tSalesPlan/list',
    method: 'GET',
    params
  })
}
/** 列表导出 */
export function tSalesPlanListExport(params) {
  return request({
    url: '/tSalesPlan/listExport',
    method: 'GET',
    params
  })
}
/** 详情 */
export function tSalesPlanDetail(params) {
  return request({
    url: '/tSalesPlan/detail',
    method: 'GET',
    params
  })
}
/** 软硬件列表 */
export function tSalesPlanProductList(data) {
  return request({
    url: '/tSalesPlan/productList',
    method: 'post',
    data
  })
}
/** 产品列表excel导出 */
export function productExcelExport(params) {
  return request({
    url: '/tSalesPlan/productExcelExport',
    method: 'GET',
    params
  })
}
/** 产品列表word导出 */
export function productWordExport(params) {
  return request({
    url: '/tSalesPlan/productWordExport',
    method: 'GET',
    params
  })
}
/** 添加销售方案备份 */
export function tSalesPlanBackupAdd(data) {
  return request({
    url: '/tSalesPlanBackup/add',
    method: 'post',
    data
  })
}
/** 添加销售方案列表 */
export function tSalesPlanBackupList(params) {
  return request({
    url: '/tSalesPlanBackup/list',
    method: 'get',
    params
  })
}
