// 方案共享
import request from '@/utils/request'
/** 列表 */
export function tPlanShareList(params) {
  return request({
    url: '/tPlanShare/list',
    method: 'GET',
    params
  })
}

/** 添加 */
export function tPlanShareAdd(data) {
  return request({
    url: '/tPlanShare/add',
    method: 'POST',
    data
  })
}
/** 修改 */
export function tPlanShareUpdate(data) {
  return request({
    url: '/tPlanShare/update',
    method: 'POST',
    data
  })
}
/** 删除 */
export function tPlanShareRemove(params) {
  return request({
    url: '/tPlanShare/remove',
    method: 'DELETE',
    params
  })
}

/** 详情 */
export function tPlanShareDetail(params) {
  return request({
    url: '/tPlanShare/detail',
    method: 'GET',
    params
  })
}
