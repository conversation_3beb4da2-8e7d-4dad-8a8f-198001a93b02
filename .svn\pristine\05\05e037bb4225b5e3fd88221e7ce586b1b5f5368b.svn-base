<template>
  <div v-loading.fullscreen="fullscreenLoading" element-loading-text="数据保存中" element-loading-background="rgba(0, 0, 0, 0.8)" class="app-container">
    <el-row :gutter="10" class="top">
      <el-col :span="24">
        <i class="el-icon-location"></i>
        <span>当前位置：销售方案 /</span>
        <span>{{ pageType }}</span>
      </el-col>
    </el-row>
    <div class="box">
      <div class="header">
        <span>{{ pageType }}</span>
        <div>
          <el-button v-if="id !== 0" type="warning" size="small" icon="el-icon-folder-opened" @click="backups_dialog = true">备份</el-button>
          <el-button type="primary" size="small" icon="el-icon-view" @click="isPreview = !isPreview">{{ isPreview ? '关闭预览' : '开启预览' }}</el-button>
        </div>
      </div>
      <template v-if="!isPreview">
        <el-form ref="form" class="addForm" :model="formInfo" :rules="rules" label-width="130px">
          <el-form-item label="方案名称:" prop="name" class="name">
            <el-input v-model="formInfo.name" placeholder="请输入方案名称" maxlength="40"></el-input>
          </el-form-item>
          <el-form-item v-if="organizationId !== 61311410" label="客户名称:" prop="customerId" class="customerId">
            <el-select v-model="formInfo.customerId" placeholder="请选择客户" @focus="getCustomerList" @change="setCustomerName">
              <el-option v-for="item in customerList" :key="item.customerId" :label="item.customerName" :value="item.customerId"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="客户预算（万元）:" prop="budget" class="budget">
            <el-input v-model="formInfo.budget" placeholder="请输入客户预算（万元）" maxlength="40"></el-input>
          </el-form-item>
          <el-form-item label="选择产品:" class="product">
            <div class="addProduct" @click="addProductDialog = true">+添加产品</div>
            <div v-if="formInfo.productReqs.length" class="productList">
              <el-button v-if="id === 0" type="primary" size="small" @click="setIsShow(2)">参数</el-button>
              <el-button v-if="id === 0" type="primary" size="small" @click="setIsShow(1)">简介</el-button>
              <div class="total">合计（万元）: {{ totalPrices }}</div>
              <el-table :data="formInfo.productReqs" style="width: 1164px" border header-cell-class-name="tableHeader">
                <el-table-column align="center" label="序号" width="width" type="index"></el-table-column>
                <el-table-column prop="name" align="center" label="产品名称" width="width"></el-table-column>
                <el-table-column align="center" label="单价(万元)" width="width">
                  <template v-slot="{ row }">
                    <el-input v-model.number="row.offer" type="number" class="offerInput" placeholder="单价(万元)"></el-input>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="单位" width="100">
                  <template v-slot="{ row }">
                    <span>{{ row.unit | softwareUnit }}</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="需求数量" width="width">
                  <template v-slot="{ row }">
                    <el-input v-model.number="row.number" class="numberInput" placeholder="需求数量"></el-input>
                  </template>
                </el-table-column>
                <el-table-column prop="totalOffer" align="center" label="总价(万元)" width="100">
                  <template v-slot="{ row }">
                    <span>{{ Number((row.offer * row.number).toString().match(/^\d+(?:\.\d{0,4})?/)) }}</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="showContent" show-overflow-tooltip :label="showContentLabel" width="width"></el-table-column>
                <el-table-column prop="prop" align="center" label="操作" width="width">
                  <template v-slot="{ row }">
                    <span class="delButton" @click="del(row)">删除</span>
                    <el-button type="text" @click="openRemark(row)"> 备注</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-form-item>
          <el-form-item label="备注:" class="remark">
            <el-input v-model="formInfo.remark" placeholder="请输入产品备注" type="textarea" resize="none" maxlength="500"></el-input>
          </el-form-item>
        </el-form>
        <div class="footer">
          <span @click="$router.push('/tSalesPlan')">取 消</span>
          <span @click="save">保 存</span>
        </div>
      </template>
      <!-- 开启预览 -->
      <template v-if="isPreview">
        <el-descriptions v-if="formInfo" :column="1" style="margin-left: 118px; margin-top: 28px">
          <el-descriptions-item label="方案名称">
            <span class="name">{{ formInfo.name }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="客户名称">
            <span>{{ formInfo.customerName }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="客户预算（万元）">{{ formInfo.budget }}</el-descriptions-item>
          <el-descriptions-item label="产品列表">
            <div v-if="formInfo.productReqs.length" class="productList">
              <div class="total">合计（万元）: {{ totalPrices }}</div>
              <el-table :data="formInfo.productReqs" style="width: 1164px" border header-cell-class-name="tableHeader">
                <el-table-column prop="name" align="center" label="产品名称" width="width"> </el-table-column>
                <el-table-column prop="offer" align="center" label="单价(万元)" width="100"> </el-table-column>
                <el-table-column align="center" label="单位" width="100">
                  <template v-slot="{ row }">
                    <span>{{ row.unit | softwareUnit }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="number" align="center" label="需求数量" width="100"> </el-table-column>
                <el-table-column prop="totalOffer" align="center" label="总价(万元)" width="100">
                  <template v-slot="{ row }">
                    <span>{{ Number((row.offer * row.number).toString().match(/^\d+(?:\.\d{0,4})?/)) }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="showContent" align="center" show-overflow-tooltip :label="showContentLabel" width="width"> </el-table-column>
                <el-table-column prop="remark" align="center" show-overflow-tooltip label="备注" width="width"> </el-table-column>
              </el-table>
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <span slot="label">备 <i v-html="'&emsp;&nbsp;'"></i> 注</span>
            <span>{{ formInfo.remark }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </template>
    </div>

    <!-- 选择产品的弹窗 -->
    <el-dialog title="添加产品" :visible.sync="addProductDialog" custom-class="addProductDialog" top="10vh" width="1000px" @open="getSalesPlanProductList">
      <el-form ref="form" :model="productForm" label-width="80px" inline>
        <el-form-item label="产品名称:">
          <el-input v-model="productForm.name" placeholder="请输入产品名称" maxlength="40"></el-input>
        </el-form-item>
        <el-form-item label="产品类别:" class="type">
          <el-select v-model="productForm.type" placeholder="产品类别">
            <el-option label="软件" :value="1"> </el-option>
            <el-option label="自研硬件" :value="2"> </el-option>
            <el-option label="外购硬件" :value="3"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="productForm.type === 1" label="类别:" class="type">
          <el-select v-model="productForm.softType" placeholder="请选择类别" clearable>
            <el-option label="平台" :value="1"> </el-option>
            <el-option label="系统" :value="2"> </el-option>
            <el-option label="虚拟仿真" :value="3"> </el-option>
            <el-option label="VR/MR" :value="4"> </el-option>
            <el-option label="虚实结合" :value="5"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="productForm.type === 1" label="专业:" class="type">
          <el-select v-model="productForm.majorId" placeholder="请选择专业" clearable @focus="getAllMajor">
            <el-option v-for="item in allMajorList" :key="item.majorId" :label="item.majorName" :value="item.majorId"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <span class="form_button search" @click="getSalesPlanProductList">查询</span>
          <span class="form_button reset" @click="reset">重置</span>
        </el-form-item>
      </el-form>
      <el-table :data="productList" style="width: 100%" border header-cell-class-name="tableHeader" @selection-change="handleSelectionChange">
        <el-table-column align="center" type="selection" width="55"></el-table-column>
        <el-table-column align="center" prop="name" label="产品名称" width="width"></el-table-column>
        <el-table-column align="center" label="产品类型" width="width">
          <template v-slot="{ row }">
            <span>{{ row.type === 1 ? '软件' : row.type === 2 ? '自研硬件' : '外购硬件' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="offer" label="单价（万元）" width="width"></el-table-column>
        <el-table-column align="center" label="单位" width="width">
          <template v-slot="{ row }">
            <span>{{ row.unit | softwareUnit }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" show-overflow-tooltip prop="param" label="参数" width="width"></el-table-column>
        <el-table-column align="center" show-overflow-tooltip prop="description" label="简介" width="width"></el-table-column>
      </el-table>
      <el-pagination
        v-if="productList.length > 0"
        style="margin-top: 32px; text-align: right"
        layout="total,sizes, prev, pager, next"
        background
        :total="total"
        :page-sizes="[6, 12, 24, 48]"
        :page-size.sync="productForm.pageSize"
        :current-page.sync="productForm.pageNum"
        @size-change="getSalesPlanProductList"
        @current-change="getSalesPlanProductList"
      />
      <div slot="footer">
        <el-button @click="addProductDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 备注弹窗 -->
    <el-dialog title="备注" :visible.sync="remarkDialog" width="500px">
      <el-input v-model="remark" type="textarea" placeholder="请输入备注"></el-input>
      <div slot="footer">
        <el-button @click="remarkDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirmRemark">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 备份名称弹窗 -->
    <el-dialog title="备份名称" :visible.sync="backups_dialog" width="300px">
      <el-input v-model="backupName" placeholder="请输入备份名称" maxlength="50"></el-input>
      <div slot="footer">
        <el-button @click="backups_dialog = false">取 消</el-button>
        <el-button type="primary" @click="backups">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { secretCustomeAllCustomer } from '@/api/clientele'
import { tSalesPlanProductList, tSalesPlanAdd, tSalesPlanDetail, tSalesPlanUpdate, tSalesPlanBackupAdd } from '@/api/tSalesPlan'
import { allMajor } from '@/api/specialty'
import { mapGetters } from 'vuex'
export default {
  name: 'AddtSalesPlan',
  data() {
    var checkNum = (rule, value, callback) => {
      if (!value) {
        callback()
      }
      if (!isNaN(parseInt(value))) {
        callback()
      } else {
        return callback(new Error('请输入数字'))
      }
    }
    return {
      fullscreenLoading: false,
      formInfo: {
        name: null, // 方案名称
        customerId: null, // 客户id
        customerName: null, // 客户名称
        budget: null, // 客户预算
        totalMoney: null, // 方案总价
        remark: null,
        productReqs: [], // 产品
        isShow: 1
      },
      rules: {
        name: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
        customerId: [{ required: true, message: '请选择客户', trigger: 'change' }],
        budget: [{ validator: checkNum, trigger: 'blur' }]
      },
      customerList: [],
      addProductDialog: false,
      productForm: {
        name: null, // 产品名称
        type: null, // 1 软件 2 自研硬件 3 外购硬件
        softType: null, // 类别
        majorId: null, // 专业
        pageNum: 1,
        pageSize: 6
      },
      productList: [],
      total: 0,
      selectData: [],
      totalPrices: 0,
      id: 0,
      remarkDialog: false,
      remark: null,
      oldProductId: null, // 软硬件id
      isPreview: false,
      allMajorList: [], // 所有专业列表
      backups_dialog: false,
      backupName: null
    }
  },
  computed: {
    pageType() {
      return this.id === 0 ? '添加方案' : '修改方案'
    },
    showContentLabel() {
      return this.formInfo.isShow === 1 ? '简介' : '参数'
    },
    ...mapGetters(['organizationId'])
  },
  watch: {
    'formInfo.productReqs': {
      deep: true,
      handler(val) {
        if (val) {
          let prices = 0
          val.forEach((item) => {
            item.totalOffer = item.offer * item.number
            prices += parseFloat(item.totalOffer)
          })
          this.totalPrices = Number(prices.toString().match(/^\d+(?:\.\d{0,4})?/))
          this.formInfo.totalMoney = Number(prices.toString().match(/^\d+(?:\.\d{0,4})?/))
        }
      }
    },
    'formInfo.totalMoney': {
      handler(val) {
        if (this.formInfo.budget) {
          if (val > parseFloat(this.formInfo.budget)) {
            this.$message.warning('产品总价已超出客户预算')
          }
        }
      }
    }
  },
  created() {
    this.id = parseInt(this.$route.params.id)
    if (this.id !== 0) {
      this.getDetails()
    }
  },
  methods: {
    async getCustomerList() {
      const { data } = await secretCustomeAllCustomer()
      this.customerList = data
      console.log(data)
    },
    async getDetails() {
      const { data } = await tSalesPlanDetail({ id: this.id })
      this.formInfo = { ...data, productReqs: data.productDetailDtos }
      this.getCustomerList()
    },

    save() {
      this.$refs['form'].validate((val) => {
        if (val) {
          this.fullscreenLoading = true
          // 删除参数和简介字段
          this.formInfo.productReqs.forEach((item) => {
            delete item['param']
            delete item['description']
          })
          if (this.id !== 0) {
            tSalesPlanUpdate(this.formInfo)
              .then((res) => {
                this.fullscreenLoading = false
                this.$message.success('修改方案成功!')
                this.$router.push('/tSalesPlan')
              })
              .catch(() => {
                this.fullscreenLoading = false
              })
          } else {
            tSalesPlanAdd(this.formInfo)
              .then((res) => {
                this.fullscreenLoading = false
                this.$message.success('添加方案成功!')
                this.$router.push('/tSalesPlan')
              })
              .catch(() => {
                this.fullscreenLoading = false
              })
          }
        }
      })
    },
    // 设置简介或参数
    setIsShow(type) {
      this.formInfo.isShow = type
      if (type === 1) {
        this.formInfo.productReqs.forEach((item) => {
          item.showContent = item.description
        })
      } else {
        this.formInfo.productReqs.forEach((item) => {
          item.showContent = item.param
        })
      }
    },
    // 选择产品 ----------------
    async getSalesPlanProductList() {
      const msg = this.formInfo.productReqs.map((item) => {
        return {
          oldType: item.type,
          oldProductId: item.oldProductId
        }
      })
      const { data } = await tSalesPlanProductList({ ...this.productForm, msg: JSON.stringify(msg) })
      this.productList = data.list
      this.total = data.total
    },
    reset() {
      this.productForm = {
        name: null, // 产品名称
        type: null, // 1 软件 2 硬件
        pageNum: 1,
        pageSize: 6
      }
      this.getSalesPlanProductList()
    },
    handleSelectionChange(val) {
      this.selectData = val
    },
    confirm() {
      if (!this.formInfo.productReqs.length) {
        this.formInfo.productReqs = [...this.selectData]
        this.formInfo.productReqs.forEach((item) => {
          this.$set(item, 'number', 1)
          this.$set(item, 'totalOffer', item.offer)
          this.$set(item, 'remark', '')
          this.$set(item, 'showContent', this.formInfo.isShow === 1 ? item.description : item.param)
        })
      } else {
        const productKeys = this.formInfo.productReqs.map((item) => item.oldProductId)
        this.selectData.forEach((item) => {
          if (!productKeys.includes(item.oldProductId)) {
            const showContent = this.formInfo.isShow === 1 ? item.description : item.param
            this.formInfo.productReqs.push({ ...item, number: 1, totalOffer: item.offer, showContent, remark: '' })
          }
        })
      }
      this.addProductDialog = false
    },
    del(row) {
      console.log(this.formInfo.productReqs)
      this.formInfo.productReqs = this.formInfo.productReqs.filter((item) => item.oldProductId !== row.oldProductId)
      this.$message.success('删除成功')
      console.log(row)
    },
    // 选择产品 ---------------- over

    // 备注
    openRemark(row) {
      this.remark = row.remark
      this.oldProductId = row.oldProductId
      this.remarkDialog = true
    },
    confirmRemark() {
      this.formInfo.productReqs.forEach((item) => {
        if (item.oldProductId === this.oldProductId) {
          item.remark = this.remark
        }
      })
      this.$message.success('备注成功')
      this.remarkDialog = false
      this.remark = null
      this.oldProductId = null
    },
    // 备注 --- over

    // 备份
    async backups() {
      if (!this.backupName) return this.$message.error('备份名称不能为空')
      await tSalesPlanBackupAdd({ ...this.formInfo, productDetailDtos: this.formInfo.productReqs, backupName: this.backupName })
      this.$message.success('备份成功')
      this.backups_dialog = false
      this.backupName = null
    },
    // 获取所有专业
    async getAllMajor() {
      const { data } = await allMajor()
      this.allMajorList = data
    },
    // 设置客户名称
    setCustomerName(val) {
      if (!val) return false
      this.formInfo.customerName = this.customerList.find((item) => item.customerId === val).customerName
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  position: relative;
  padding: 0;
  background-color: #e8eaed;
  min-height: 100%;
  padding-top: 58px;
  padding-bottom: 10px;

  .top {
    position: absolute;
    top: 0px;
    width: 100%;
    background-color: #e8eaed;
    padding: 24px 0 16px 0;
    z-index: 999;

    .el-col {
      i {
        font-size: 14px;
        color: #657081;
      }

      span {
        &:first-of-type {
          margin: 0 5px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #657081;
        }

        &:last-of-type {
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
      }
    }
  }

  .box {
    width: 1754px;
    max-height: 791px;
    background: #ffffff;
    padding-bottom: 40px;
    border-radius: 8px 8px 8px 8px;
    overflow: auto;

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24px 48px 11px 48px;
      border-bottom: 1px solid #eeeeef;
      font-size: 16px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #0b1a44;
    }
  }

  ::v-deep {
    .addForm {
      padding-left: 188px;
      padding-top: 24px;
      border-bottom: 1px solid #eeeeef;

      .el-form-item {
        margin-bottom: 28px;
      }
    }
    .el-form {
      .el-input__inner {
        height: 40px;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #d8dbe1;
        &::placeholder {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #b1bac7;
        }
      }
    }
    .el-form-item__label {
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #0b1a44;
    }

    .name {
      .el-input__inner {
        width: 436px;
      }
    }

    .customerId,
    .budget {
      .el-input__inner {
        width: 318px;
      }
    }

    .remark {
      .el-textarea__inner {
        width: 682px;
        height: 138px;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #d8dbe1;
      }
    }

    .addProduct {
      width: 116px;
      height: 38px;
      line-height: 38px;
      background: #3464e0;
      font-size: 16px;
      color: #ffffff;
      text-align: center;
      border-radius: 4px;
      cursor: pointer;

      &:hover {
        background: #355fce;
      }
    }

    .el-radio-group {
      .el-radio__input.is-checked .el-radio__inner {
        // background: #3464e0;
        background: #fff;
        width: 18px;
        height: 18px;
        border-color: #3464e0;

        // border: none;
        &::after {
          background-color: #3464e0;
          width: 8px;
          height: 8px;
        }
      }

      .el-radio__inner {
        width: 18px;
        height: 18px;
      }

      .el-radio__input.is-checked + .el-radio__label {
        color: #0b1a44;
      }

      .el-radio__label {
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #0b1a44;
      }
    }

    .form_button {
      display: inline-block;
      margin-right: 16px;
      width: 68px;
      height: 28px;
      line-height: 28px;
      border-radius: 4px 4px 4px 4px;
      font-size: 14px;
      font-weight: 400;
      text-align: center;
      cursor: pointer;
    }

    .search {
      margin-left: 25px;
      background: #3464e0;
      color: #fff;
    }

    .reset {
      color: #b1bac7;
      border: 1px solid #b1bac7;
    }

    .tableHeader {
      background: #f0f0f0;
      font-size: 14px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #0b1a44;
    }

    .el-table td.el-table__cell,
    .el-table th.el-table__cell.is-leaf {
      border-color: #a3a8bb;
    }

    .el-table--border,
    .el-table--group {
      border-color: #a3a8bb;
    }

    .el-table--border::after,
    .el-table--group::after,
    .el-table::before {
      background-color: #a3a8bb;
    }

    .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
      background: #eff1f3;
    }

    .productList {
      width: 1164px;
      text-align: right;

      .total {
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #0b1a44;
      }

      .offerInput,
      .numberInput {
        .el-input__inner {
          width: 120px;
          height: 32px;
          background: #f5f5f5;
          border: 1px solid #d8dbe1;
          color: #0b1a44;
          text-align: center;
        }
      }

      .delButton {
        margin-right: 15px;
        font-size: 14px;
        color: #eb6557;
        cursor: pointer;
      }
    }
    .el-descriptions-item__label {
      width: 128px;
      text-align: right;
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #868b9f;
    }
    .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
      padding-bottom: 28px;
    }
    .el-descriptions-item__content {
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #0b1a44;
    }
  }

  .footer {
    display: flex;
    justify-content: center;
    margin-top: 40px;

    span {
      display: inline-block;
      width: 260px;
      height: 46px;
      line-height: 46px;
      font-size: 14px;
      font-weight: 400;
      border-radius: 4px 4px 4px 4px;
      text-align: center;
      cursor: pointer;
    }

    & > span:first-of-type {
      background: #ffffff;
      border: 1px solid #d8dbe1;
      color: #a3a8bb;
    }

    & > span:last-of-type {
      margin-left: 32px;
      background: #3464e0;
      font-weight: bold;
      color: #ffffff;
    }
  }
}

::v-deep {
  .addProductDialog {
    max-height: 760px;
    overflow: auto;
    .el-form {
      .el-input {
        .el-input__inner {
          width: 202px;
        }
      }
    }
  }
  .offerInput input::-webkit-outer-spin-button,
  .offerInput input::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }
  .offerInput input[type='number'] {
    -moz-appearance: textfield;
  }

  .offerInput inpit {
    border: none;
  }
}
</style>
