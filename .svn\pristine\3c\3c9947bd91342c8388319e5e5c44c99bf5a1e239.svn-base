// 小微秘 招投标
import request from '@/utils/request'
/** 添加招投标 */
export function bidSave(data) {
  return request({
    url: '/secret/bid/save',
    method: 'POST',
    data
  })
}
/** 修改招投标 */
export function bidSecretBidUpdate(data) {
  return request({
    url: '/secret/bid/secretBidUpdate',
    method: 'POST',
    data
  })
}

/** 招投标列表 */
export function bidSecretBidList(params) {
  return request({
    url: '/secret/bid/secretBidList',
    method: 'GET',
    params
  })
}

/** 招投标详情 */
export function bidSecretBidDetails(params) {
  return request({
    url: '/secret/bid/secretBidDetails',
    method: 'GET',
    params
  })
}

