<template>
  <div class="app-container">
    <el-row type="flex" justify="space-between">
      <el-button type="primary" icon="el-icon-back" size="small" @click="$router.push('/contract')">返回</el-button>
      <!-- <el-button type="primary" size="small">编辑</el-button> -->
    </el-row>
    <el-card style="margin-top: 15px">
      <div slot="header" class="header">合同信息</div>
      <el-descriptions :title="`合同名称：${detailsInfo.contractName}`" :column="2">
        <el-descriptions-item label="客户名称">{{ detailsInfo.customerName }}</el-descriptions-item>
        <el-descriptions-item label="签订日期">{{ detailsInfo.signingTime }}</el-descriptions-item>
        <el-descriptions-item label="验收日期">{{ detailsInfo.acceptanceTime }}</el-descriptions-item>
        <el-descriptions-item label="质保日期">{{ detailsInfo.warrantyTime }}</el-descriptions-item>
        <el-descriptions-item label="退质保金日期">{{ detailsInfo.refundTime }}</el-descriptions-item>
        <el-descriptions-item label="实际退质保金日期">{{ detailsInfo.realityRefundTime }}</el-descriptions-item>
        <el-descriptions-item label="经销商名称">{{ detailsInfo.dealerName }}</el-descriptions-item>
        <el-descriptions-item label="产品内容">{{ detailsInfo.remark }}</el-descriptions-item>
        <el-descriptions-item label="创建人">{{ detailsInfo.realName }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ detailsInfo.createTime }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ detailsInfo.updateTime }}</el-descriptions-item>
        <el-descriptions-item label="合同">
          <span v-if="detailsInfo.fileDtos && detailsInfo.fileDtos.length" class="fileName" @click="lookDetails(detailsInfo.fileDtos[0])">
            {{ detailsInfo.fileDtos[0].fileName }}
          </span>
          <span v-else>暂无</span>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
  </div>
</template>

<script>
import { contract_contractDetails } from '@/api/contract'
export default {
  name: '',
  data() {
    return {
      detailsInfo: {},
      imgList: [],
      fileList: []
    }
  },
  created() {
    this.getContract_contractDetails()
  },
  methods: {
    async getContract_contractDetails() {
      const { data } = await contract_contractDetails({ contractId: this.$route.params.id })
      console.log(data)
      this.detailsInfo = data
    },
    lookDetails(item) {
      window.open(item.fileUrl, '_blank')
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep {
  .el-descriptions__header {
    .el-descriptions__title {
      font-size: 22px;
    }
  }
  .el-descriptions-item__container {
    span {
      color: #1a1a1a;
      font-size: 18px;
    }
  }
}
.header {
  font-size: 24px;
  font-weight: 600;
}
.fileName {
  color: #3465df;
  text-decoration: underline;
  cursor: pointer;
}
</style>
