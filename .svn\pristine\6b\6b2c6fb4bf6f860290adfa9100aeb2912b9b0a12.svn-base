<template>
  <div class="remove-list">
    <el-dialog title="删除列表" :visible.sync="dialogVisible" width="1400px" @close="close">
      <el-table :data="list" style="width: 100%" border>
        <el-table-column type="index" width="50" align="center" label="序号"> </el-table-column>
        <el-table-column prop="userRealName" label="商机负责人" width="150" align="center"> </el-table-column>
        <el-table-column prop="customerName" label="客户" width="150" align="center"> </el-table-column>
        <el-table-column prop="content" label="项目内容" width="width" align="center"> </el-table-column>
        <el-table-column prop="rank" label="项目等级" width="120" align="center">
          <template v-slot="{ row }">
            <el-tag v-if="row.rank" :type="row.rank === 'A' ? 'success' : row.rank === 'B' ? 'warning' : ''">{{ row.rank }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="money" label="项目金额(万元)" width="width" align="center"> </el-table-column>
        <el-table-column prop="current" label="项目当前情况" width="width" align="center"> </el-table-column>
        <el-table-column label="操作" width="150" fixed="right" align="center">
          <template v-slot="{ row }">
            <el-button type="danger" size="small" @click.stop="deleteChance(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { chanceDelList, chanceRemove } from '@/api/chance'
export default {
  name: '',
  data() {
    return {
      pageNum: 1,
      pageSize: 10,
      dialogVisible: false,
      list: []
    }
  },
  created() {},
  methods: {
    open() {
      this.dialogVisible = true
      this.getChanceList()
    },
    close() {
      this.$emit('refresh')
    },
    getChanceList() {
      chanceDelList({ pageNum: this.pageNum, pageSize: this.pageSize }).then(({ data }) => {
        this.list = data.list
        console.log(this.list)
      })
    },
    deleteChance(row) {
      chanceRemove({ id: row.chanceId }).then(({ data }) => {
        console.log(data)
        this.$message.success('删除成功')
        this.getChanceList()
      })
    }
  }
}
</script>
<style scoped lang="scss"></style>
