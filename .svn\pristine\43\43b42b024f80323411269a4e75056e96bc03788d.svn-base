<template>
  <div class="app-container">
    <el-dialog title="方案详情" :visible.sync="dialogVisible" width="600px">
      <el-descriptions v-if="info" :column="1" border>
        <el-descriptions-item label="标题">{{ info.name }}</el-descriptions-item>
        <el-descriptions-item label="创建人">{{ info.realName }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ info.createTime }}</el-descriptions-item>
        <el-descriptions-item label="备注"> {{ info.remark }} </el-descriptions-item>
        <el-descriptions-item label="文件列表">
          <template v-if="info.fileDtos.length">
            <el-table :data="info.fileDtos" style="width: 100%" border>
              <el-table-column align="center" prop="fileName" label="文件名称" width="width"> </el-table-column>
              <el-table-column align="center" label="操作" width="width">
                <template v-slot="{ row }">
                  <el-button type="primary" plain size="small" @click="preview(row)">预览</el-button>
                  <el-button type="primary" size="small" @click="down(row)">下载</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!-- <span v-for="item in info.fileDtos" :key="item.fileId" class="file" @click="down(item)">{{ item.fileName }}</span> -->
          </template>
        </el-descriptions-item>
      </el-descriptions>
      <div slot="footer">
        <el-button type="primary" @click="dialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
    <el-dialog custom-class="previewDialog" :visible.sync="previewDialog" width="100%" top="0">
      <VueOfficeDocx :src="docUrl" />
      <div slot="footer">
        <el-button type="primary" @click="previewDialog = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { tPlanShareDetail } from '@/api/tPlanShare'
import { downUrl } from '@/utils'
import VueOfficeDocx from '@vue-office/docx'
import '@vue-office/docx/lib/index.css'
export default {
  name: '',
  components: {
    VueOfficeDocx
  },
  data() {
    return {
      dialogVisible: false,
      info: null,
      docUrl: null,
      previewDialog: false
    }
  },
  created() {},
  methods: {
    async openDialog(val) {
      const { data } = await tPlanShareDetail({ id: val.planShareId })
      this.info = { ...data }
      this.dialogVisible = true
    },
    down(item) {
      downUrl(item.fileName, item.fileUrl)
    },
    preview(item) {
      const type = item.fileName.split('.')[item.fileName.split('.').length - 1]
      if (type === 'docx') {
        this.docUrl = item.fileUrl
        this.previewDialog = true
      } else {
        window.open(item.fileUrl)
      }
    }
  }
}
</script>
<style scoped lang="scss">
.file {
  font-size: 16px;
  color: #355fce;
  cursor: pointer;
  &::after {
    content: '、';
  }
  &:last-of-type {
    &::after {
      content: '';
    }
  }
}
::v-deep {
  .previewDialog {
    .el-dialog__header {
      padding: 0;
      .el-dialog__close {
        font-size: 40px;
        color: #fff;
      }
    }
    .el-dialog__body {
      padding: 0 0;
    }
  }
}
</style>
