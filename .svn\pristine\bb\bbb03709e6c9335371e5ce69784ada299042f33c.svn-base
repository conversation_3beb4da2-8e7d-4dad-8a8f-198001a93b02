<template>
  <div class="app-container">
    <div class="performance">
      <div class="header">
        <div class="add" @click="add"></div>
      </div>
      <div class="searchBox">
        <el-form ref="queryInfo" :model="queryInfo" label-width="80px" inline>
          <el-form-item label="姓名:">
            <el-input v-model="queryInfo.realName" maxlength="40" placeholder="请输入姓名"></el-input>
          </el-form-item>
          <el-form-item label="部门:" class="orgs">
            <el-select v-model="queryInfo.orgs" placeholder="请选择部门" multiple collapse-tags @focus="getDepartmentList">
              <el-option v-for="item in departmentList" :key="item.organizationId" :label="item.organizationName" :value="item.organizationId"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="考核时间:">
            <el-date-picker v-model="date" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd" @change="dateChange">
            </el-date-picker>
          </el-form-item>
          <el-form-item style="margin-left: 15px">
            <el-button type="primary" size="small" @click="getList">查询</el-button>
            <el-button type="primary" plain size="small" @click="reset">重置</el-button>
            <el-button type="info" plain size="small" @click="exportList">批量导出</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="content">
        <el-table :data="list" style="width: 100%" stripe header-cell-class-name="tableHeader_cell" cell-class-name="table_cell" @sort-change="sortChange">
          <el-table-column prop="realName" label="姓名" width="width" align="center">
            <template v-slot="{ row }">
              <div class="realName">
                <img :src="row.headurl" alt="" />
                <span>{{ row.realName }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="organizationName" label="部门" width="width" align="center"> </el-table-column>
          <el-table-column prop="jobName" label="职位" width="100px" align="center"> </el-table-column>
          <el-table-column prop="time" label="考核时间" width="width" align="center" sortable="custom"> </el-table-column>
          <el-table-column prop="totalSelfScore" label="自评分" width="80px" align="center">
            <template v-slot="{ row }">
              <div class="totalSelfScore">
                {{ row.totalSelfScore }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="totalLastScore" label="最终分值" width="80px" align="center">
            <template v-slot="{ row }">
              <div class="totalLastScore">
                {{ row.totalLastScore }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="updateTime" label="更新时间" width="width" align="center"> </el-table-column>
          <el-table-column prop="createTime" label="提交时间" width="width" align="center"> </el-table-column>
          <el-table-column prop="state" label="状态" width="width" align="center" sortable="custom">
            <template v-slot="{ row }">
              <div v-if="row.state === 1" class="state state1">
                <img src="@/assets/performance/state1.png" alt="" />
                <span>待评分</span>
              </div>
              <div v-if="row.state === 2" class="state state2">
                <img src="@/assets/performance/state2.png" alt="" />
                <span>已评分</span>
              </div>
              <div v-if="row.state === 3" class="state state3">
                <el-tooltip effect="dark" :content="row.returnRemark" placement="top" popper-class="stateTooltip">
                  <img src="@/assets/performance/state3.png" alt="" />
                </el-tooltip>
                <span>已退回</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="width" align="center">
            <template v-slot="{ row }">
              <template v-if="row.state === 1 && (duty === 1 || duty === 2) && !row.isOneself">
                <el-tooltip effect="dark" content="评分" placement="top" popper-class="stateTooltip">
                  <img class="operateButton" src="@/assets/performance/score.png" alt="" @click="score(row)" />
                </el-tooltip>
                <el-tooltip effect="dark" content="退回" placement="top" popper-class="stateTooltip">
                  <img class="operateButton" src="@/assets/performance/goBack.png" alt="" @click="goBack(row)" />
                </el-tooltip>
              </template>
              <template v-if="row.state !== 2 && row.isOneself">
                <el-tooltip effect="dark" content="编辑" placement="top" popper-class="stateTooltip">
                  <img class="operateButton" src="@/assets/meeting/edit.png" alt="" @click="edit(row)" />
                </el-tooltip>
                <el-tooltip effect="dark" content="删除" placement="top" popper-class="stateTooltip">
                  <img class="operateButton" src="@/assets/meeting/del.png" alt="" @click="del(row)" />
                </el-tooltip>
              </template>
              <template v-if="row.state === 2 || row.state === 3">
                <el-tooltip effect="dark" content="详情" placement="top" popper-class="stateTooltip">
                  <img class="operateButton" src="@/assets/performance/details_list.png" alt="" @click="details(row)" />
                </el-tooltip>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          v-if="list.length > 0"
          style="margin-top: 32px; text-align: right"
          layout="total,  prev, pager, next"
          background
          :total="total"
          :page-size.sync="queryInfo.pageSize"
          :current-page.sync="queryInfo.pageNum"
          @size-change="getList"
          @current-change="getList"
        />
      </div>
    </div>
    <!-- 删除弹窗 -->
    <el-dialog :visible.sync="delDialog" width="413px" top="30vh" custom-class="delDialog">
      <template v-slot:title>
        <img src="@/assets/library/hint.png" alt="" class="hint" />
        <span class="hintText">提示</span>
      </template>
      <div class="delText">确定删除该资源吗？</div>
      <div class="operate">
        <span class="closeButton" @click="delDialog = false">取 消</span>
        <span class="confirmButton" @click="confirmDel">确 定</span>
      </div>
    </el-dialog>
    <!-- 退回弹窗 -->
    <el-dialog :visible.sync="goBackDialog" width="596px" custom-class="goBackDialog" top="25vh">
      <template v-slot:title>
        <img src="@/assets/performance/goBackDialogHeader.png" alt="" />
      </template>
      <el-input v-model="remark" type="textarea" placeholder="请输入退回原因" maxlength="300" resize="none"></el-input>
      <div class="operate">
        <span class="closeButton" @click="goBackDialog = false">关闭</span>
        <span class="confirmButton" @click="confirmGoBack">提交</span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { tAchievementList, tAchievementRemove, tAchievementReturnAchievement, tAchievementListExport } from '@/api/tAchievement'
import { allOrganization } from '@/api/organization'
import { mapGetters } from 'vuex'

export default {
  name: '',
  data() {
    return {
      queryInfo: {
        startTime: null, // 考核开始时间
        endTime: null, // 考核结束时间
        realName: null, // 姓名
        orgs: [], // 部门ids
        orderField: null, // 排序字段 1 考核时间 2 状态
        order: null, // 排序顺序 1 正序 2 倒叙
        pageNum: 1,
        pageSize: 5
      },
      date: null,
      list: [],
      total: 0,
      departmentList: [],
      delDialog: false,
      achievementId: null,
      goBackDialog: false,
      remark: null
    }
  },
  computed: {
    ...mapGetters(['duty'])
  },
  created() {
    this.getList()
  },

  methods: {
    async getList() {
      const { data } = await tAchievementList(this.queryInfo)
      this.list = data.list
      this.total = data.total
    },
    async getDepartmentList() {
      const { data } = await allOrganization()
      this.departmentList = data
    },
    dateChange(val) {
      if (val) {
        this.queryInfo.startTime = val[0]
        this.queryInfo.endTime = val[1]
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getList()
    },
    reset() {
      this.queryInfo = {
        startTime: null, // 考核开始时间
        endTime: null, // 考核结束时间
        realName: null, // 姓名
        orgs: [], // 部门ids
        orderField: null, // 排序字段 1 考核时间 2 状态
        order: null, // 排序顺序 1 正序 2 倒叙
        pageNum: 1,
        pageSize: 5
      }
      this.getList()
    },
    sortChange(val) {
      this.queryInfo.orderField = val.prop === 'time' ? 1 : 2
      this.queryInfo.order = val.order === 'descending' ? 2 : 1
      this.getList()
    },
    add() {
      this.$router.push('/performance/add/add/0')
    },
    details(row) {
      this.$router.push(`/performance/add/details/${row.achievementId}`)
    },
    edit(row) {
      this.$router.push(`/performance/add/edit/${row.achievementId}`)
    },
    del(row) {
      this.achievementId = row.achievementId
      this.delDialog = true
    },
    confirmDel() {
      const loading = this.$loading({
        text: '正在删除，请稍后',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      tAchievementRemove({ id: this.achievementId })
        .then(() => {
          loading.close()
          this.$message.success('删除成功')
          this.getList()
          this.delDialog = false
        })
        .catch(() => {
          loading.close()
          this.$message.error('删除失败,请重试')
        })
    },
    score(row) {
      this.$router.push(`/performance/add/score/${row.achievementId}`)
    },
    goBack(row) {
      this.achievementId = row.achievementId
      this.goBackDialog = true
    },
    confirmGoBack() {
      const loading = this.$loading({
        text: '正在提交中，请稍后',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      tAchievementReturnAchievement({ achievementId: this.achievementId, remark: this.remark })
        .then(() => {
          loading.close()
          this.$message.success('提交完成')
          this.getList()
          this.goBackDialog = false
        })
        .catch(() => {
          loading.close()
          this.$message.error('提交失败,请重试')
        })
    },
    async exportList() {
      const { data } = await tAchievementListExport(this.queryInfo)
      const headers = {
        姓名: 'realName',
        部门: 'organizationName',
        职位: 'jobName',
        考核时间: 'time',
        实验名称: 'name',
        自评分: 'totalSelfScore',
        最终评分: 'totalLastScore',
        更新时间: 'updateTime',
        提交时间: 'createTime',
        状态: 'state'
      }
      const res = this.formatJson(headers, data)
      import('@/vendor/Export2Excel').then((excel) => {
        excel.export_json_to_excel({
          header: Object.keys(headers), // 表头 必填
          data: res, // 具体数据 必填
          filename: '绩效表' // 非必填
        })
      })
    },
    formatJson(headers, rows) {
      return rows.map((item) => {
        return Object.keys(headers).map((key) => {
          if (headers[key] === 'state') {
            if (item[headers[key]] === 1) {
              return '待评分'
            } else if (item[headers[key]] === 2) {
              return '已评分'
            } else {
              return '已退回'
            }
          } else {
            return item[headers[key]]
          }
        })
      })
    }
  }
}
</script>
<style lang="scss">
.stateTooltip {
  max-width: 216px;
  background: #0d1c46 !important;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #f5f5f5 !important;
  line-height: 20px;
}
</style>

<style scoped lang="scss">
.app-container {
  padding: 0;
  background: #e8eaed;
  height: 100%;
  .performance {
    width: 1754px;
    height: 850px;
    border-radius: 4px 4px 4px 4px;
    background: #f5f5f5;
    .header {
      position: relative;
      width: 100%;
      height: 88px;
      background: url('~@/assets/performance/header.png') no-repeat;
      background-size: cover;
      .add {
        position: absolute;
        left: 193px;
        top: 24px;
        width: 116px;
        height: 42px;
        background: url('~@/assets/performance/add.png') no-repeat;
        background-size: cover;
        cursor: pointer;
        &:hover {
          background: url('~@/assets/performance/add_hover.png') no-repeat;
          background-size: cover;
        }
      }
    }
    .searchBox {
      display: flex;
      align-items: center;
      height: 88px;
      background: #fff;
      ::v-deep {
        .el-form-item {
          margin-bottom: 0;
        }
        .el-form-item__label {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
        }
        .el-input__inner {
          width: 284px;
          height: 36px;
          border: 1px solid #eeeeef;
        }
        .orgs {
          .el-input__inner {
            width: 264px;
          }
        }
      }
    }
    .content {
      padding-top: 28px;
      padding-left: 40px;
      padding-right: 40px;
      ::v-deep {
        .el-table {
          // 去掉边框
          &::before {
            display: none;
          }
          .el-table__row {
            // cursor: pointer;
          }
          .el-table__header {
            border-radius: 8px 8px 0 0;
            overflow: hidden;
          }
          .table_cell {
            height: 96px;
            font-size: 15px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #0b1a44;
          }
          // 表格头部样式
          .tableHeader_cell {
            padding: 10px 0;
            background: #657081;
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #e8ce98;
          }
        }
        .operateButton {
          cursor: pointer;
        }
        // 去掉边框
        .el-table td.el-table__cell,
        .el-table th.el-table__cell.is-leaf {
          border: none;
        }
        // 修改表格斑马纹样式
        .el-table__row--striped {
          .table_cell {
            background: #eeeeef;
          }
        }
        // 修改排序样式
        .el-table .ascending .sort-caret.ascending {
          border-bottom-color: #e8ce98;
        }
        .el-table .descending .sort-caret.descending {
          border-top-color: #e8ce98;
        }

        .realName {
          display: flex;
          align-items: center;
          margin-left: 40px;
          img {
            margin-right: 20px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
          }
        }
        .totalSelfScore {
          color: #3464e0;
        }
        .totalLastScore {
          color: #eb6557;
        }
        .state {
          display: flex;
          align-items: center;
          justify-content: center;
          img {
            margin-right: 3px;
            width: 18px;
            height: 18px;
          }
          span {
            font-size: 15px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #868b9f;
          }
        }

        .state2 {
          span {
            color: #23bb87;
          }
        }
        .state3 {
          span {
            color: #eb6557;
          }
        }
      }
    }
  }
  ::v-deep {
    .delDialog {
      height: 206px;
      border-radius: 8px !important;
      .el-dialog__header {
        display: flex;
        align-items: center;
        padding-top: 12px;
        padding-left: 24px;
        padding-right: 20px;
        padding-bottom: 8px;
        border-bottom: 1px solid #eeeeef;
        .hint {
          width: 30px;
          height: 30px;
        }
        .hintText {
          margin-left: 6px;
          font-size: 16px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #303a40;
        }
        .el-dialog__headerbtn {
          right: 20px;
          top: 20px;
          .el-dialog__close {
            font-weight: bold;
          }
        }
      }
      .delText {
        text-align: center;
        font-size: 16px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #303a40;
      }
      .operate {
        display: flex;
        justify-content: center;
        margin-top: 48px;
        .closeButton {
          width: 114px;
          height: 38px;
          line-height: 36px;
          background: #ffffff;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #d8dbe1;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          color: #a3a8bb;
          text-align: center;
          cursor: pointer;
          &:hover {
            background: #f5f5f5;
          }
        }
        .confirmButton {
          margin-left: 32px;
          width: 114px;
          height: 38px;
          line-height: 36px;
          background: #3464e0;
          border-radius: 4px 4px 4px 4px;
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #ffffff;
          text-align: center;
          cursor: pointer;
          &:hover {
            background: #355fce;
          }
        }
      }
    }
    .goBackDialog {
      height: 380px;
      border-radius: 4px;
      .el-dialog__header {
        position: relative;
        padding: 0;
        height: 57px;
        .el-dialog__headerbtn {
          right: 24px;
          top: 24px;
          .el-dialog__close {
            font-weight: bold;
            font-size: 14px;
            color: #ffffff;
          }
        }
      }
      .el-dialog__body {
        padding: 24px 32px;
        padding-bottom: 0;
        .el-textarea {
          .el-textarea__inner {
            width: 100%;
            height: 216px;
          }
        }
        .operate {
          display: flex;
          justify-content: center;
          margin-top: 24px;
          margin-bottom: 24px;
          .closeButton {
            width: 114px;
            height: 38px;
            line-height: 36px;
            background: #ffffff;
            border-radius: 4px 4px 4px 4px;
            border: 1px solid #d8dbe1;
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            color: #a3a8bb;
            text-align: center;
            cursor: pointer;
            &:hover {
              background: #f5f5f5;
            }
          }
          .confirmButton {
            margin-left: 32px;
            width: 114px;
            height: 38px;
            line-height: 36px;
            background: #3464e0;
            border-radius: 4px 4px 4px 4px;
            font-size: 14px;
            font-family: Microsoft YaHei-Bold, Microsoft YaHei;
            font-weight: bold;
            color: #ffffff;
            text-align: center;
            cursor: pointer;
            &:hover {
              background: #355fce;
            }
          }
        }
      }
    }
  }
}
::v-deep {
  .el-pagination.is-background .btn-next,
  .el-pagination.is-background .btn-prev,
  .el-pagination.is-background .el-pager li {
    background: #fff;
  }
}
</style>
