<template>
  <div style="width: 100%; height: 100%">
    <div id="trainStatisEchart" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script>
import resize from '../mixins/resize'
export default {
  name: '',
  mixins: [resize],
  data() {
    return {
      chart: null
    }
  },
  methods: {
    init(data) {
      this.chart = this.$echarts.init(document.getElementById('trainStatisEchart'))
      this.setOpiton(data)
    },
    setOpiton(data) {
      const that = this
      this.chart.setOption({
        title: {
          text: '培训情况统计',
          textStyle: {
            color: '#0B1A44',
            fontSize: 14
          }
        },
        legend: {
          x: 'right' // 可设定图例在左、右、居中

        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: 'rgba(0,0,0,0.7)',
          borderColor: 'gray',
          textStyle: {
            fontSize: 12,
            color: '#fff'
          },
          padding: [12, 16, 16, 16]
        },
        xAxis: {
          type: 'category',
          data: data.x
        },
        yAxis: {
          name: '流程条数（条）',
          type: 'value'
        },
        grid: {
          //   top: '0',
          left: '5%',
          right: '0',
          bottom: '0',
          containLabel: true
        },
        series: [
          {
            name: '部门内训',
            data: data.department,
            type: 'bar',
            barWidth: 17,
            stack: 'Ad',
            itemStyle: {
              color: '#6970ff'
            }
          },
          {
            name: '公司培训',
            data: data.company,
            type: 'bar',
            barWidth: 17,
            stack: 'Ad',
            itemStyle: {
              color: '#3762e4'
            }
          },
          {
            name: '外部培训',
            data: data.external,
            type: 'bar',
            barWidth: 17,
            stack: 'Ad',
            itemStyle: {
              color: '#37b3d2'
            }
          }
        ]
      })
      this.chart.on('click', function (params) {
        that.$emit('jumpPage')
      })
    }
  }
}
</script>

<style scoped lang="scss">
</style>
