<template>
  <div class="app-container">
    <div v-if="info" class="content">
      <el-row>
        <el-button type="primary" plain icon="el-icon-arrow-left" @click="goBack">返回</el-button>
        <template v-if="info">
          <el-button type="primary" :disabled="info.state !== 1" @click="confirm(info)">确认</el-button>
          <el-button type="success" :disabled="info.state !== 2" @click="solve()">解决</el-button>
          <el-button type="info" :disabled="info.state !== 3" @click="close()">关闭</el-button>
        </template>
      </el-row>
      <div v-if="info" class="box">
        <el-card>
          <div class="title">{{ info.title }}</div>
          <div class="description" v-html="info.description"></div>

          <!-- <el-descriptions :column="1">
            <el-descriptions-item label="bug标题" label-class-name="title">
            </el-descriptions-item>
            <el-descriptions-item label="bug描述">
            </el-descriptions-item>
          </el-descriptions> -->
        </el-card>
        <el-card>
          <el-descriptions title="基本信息" :column="1">
            <el-descriptions-item label="bug状态">
              <el-tag :type="bugType" size="small">{{ info.state | bugState }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="所属产品">{{ info.productName }}</el-descriptions-item>
            <el-descriptions-item label="实验名称">{{ info.name }}</el-descriptions-item>
            <el-descriptions-item label="所属专业">{{ info.majorName }}</el-descriptions-item>
            <el-descriptions-item label="优先级">
              <el-tag type="success" size="small">
                {{ info.priority === 1 ? '低' : info.priority === 2 ? '中' : '高' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="反馈人">{{ info.feedbackUser }}</el-descriptions-item>
            <el-descriptions-item label="反馈时间">{{ info.feedbackTime }}</el-descriptions-item>
            <el-descriptions-item label="解决人">{{ info.repairUser }}</el-descriptions-item>
            <el-descriptions-item label="解决时间">{{ info.repairTime }}</el-descriptions-item>
            <el-descriptions-item v-if="info.state === 4" label="关闭人">{{ info.createUserName }}</el-descriptions-item>
            <el-descriptions-item v-if="info.state === 4" label="关闭时间">{{ info.closeTime }}</el-descriptions-item>
          </el-descriptions>
          <div class="record">
            <div v-for="item in logRecord" :key="item.logId">
              {{ item.createTime }}, 由{{ item.createUserName }}{{ item.type === 1 ? '创建' : item.type === 2 ? '确认' : item.type === 3 ? '解决' : item.type === 4 ? '关闭' : '' }}
            </div>
          </div>
        </el-card>
      </div>
    </div>
    <!-- bug确认 -->
    <el-dialog title="bug确认" :visible.sync="confirmDialog" width="600px">
      <div>
        <el-form ref="confirmForm" :model="confirmForm" label-width="90px" :rules="confirmFormRules">
          <el-form-item label="解决人:" prop="repairUser">
            <el-select v-model="confirmForm.repairUser" filterable placeholder="请选择解决人" @focus="getUserList">
              <el-option v-for="item in userList" :key="item.userId" :label="item.realName" :value="item.realName"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="优先级:" prop="priority">
            <el-radio-group v-model="confirmForm.priority">
              <el-radio :label="1">低</el-radio>
              <el-radio :label="2">中</el-radio>
              <el-radio :label="3">高</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="备注:">
            <el-input v-model="confirmForm.remark" placeholder="请输入备注" maxlength="300" type="textarea" :autosize="{ minRows: 3, maxRows: 8 }"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div class="record">
        <div v-for="item in logRecord" :key="item.logId">
          {{ item.createTime }}, 由{{ item.createUserName }}{{ item.type === 1 ? '创建' : item.type === 2 ? '确认' : item.type === 3 ? '解决' : '关闭' }}
        </div>
      </div>
      <div slot="footer">
        <el-button @click="confirmBugClose">取 消</el-button>
        <el-button type="primary" @click="confirmBug">确 定</el-button>
      </div>
    </el-dialog>
    <!-- bug解决 -->
    <el-dialog title="bug解决" :visible.sync="solveDialog" width="1000px">
      <div>
        <el-form ref="solveForm" :model="solveForm" label-width="90px" :rules="solveRules">
          <el-form-item label="解决人:" prop="repairUser">
            <el-select v-model="solveForm.repairUser" filterable placeholder="请选择解决人" disabled @focus="getUserList">
              <el-option v-for="item in userList" :key="item.userId" :label="item.realName" :value="item.realName"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="解决日期:" prop="repairTime">
            <el-date-picker v-model="solveForm.repairTime" type="datetime" placeholder="选择解决日期" value-format="yyyy-MM-dd HH:mm:ss"> </el-date-picker>
          </el-form-item>
          <el-form-item label="解决方案:" prop="repairPlan">
            <vueQuillEditor ref="contentQuill" @change="solveForm.repairPlan = $event.html" />
          </el-form-item>
        </el-form>
      </div>
      <div class="record">
        <div v-for="item in logRecord" :key="item.logId">
          {{ item.createTime }}, 由{{ item.createUserName }}{{ item.type === 1 ? '创建' : item.type === 2 ? '确认' : item.type === 3 ? '解决' : '关闭' }}
        </div>
      </div>
      <div slot="footer">
        <el-button @click="solveDialog = false">取 消</el-button>
        <el-button type="primary" @click="solveBug">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { tBugDetail, tBugBugLogRecord, tBugUpdate, tBugConfirm } from '@/api/bug'
import { getList } from '@/api/systemUser'
import { formatDate } from '@/filters'
import vueQuillEditor from '@/components/vueQuillEditor'
export default {
  name: 'BugDetails',
  components: {
    vueQuillEditor
  },
  data() {
    return {
      info: null,
      logRecord: [],
      confirmDialog: false,
      confirmForm: {
        bugId: null,
        repairUser: null,
        priority: null,
        remark: null
      },
      confirmFormRules: {
        repairUser: [{ required: true, message: '请选择解决人', trigger: 'change' }],
        priority: [{ required: true, message: '请选择优先级', trigger: 'change' }]
      },
      userList: [],
      solveDialog: false,
      solveForm: {},
      solveRules: {
        repairUser: [{ required: true, message: '请选择解决人', trigger: 'change' }],
        repairTime: [{ required: true, message: '请选择反馈时间', trigger: 'change' }],
        repairPlan: [{ required: true, message: '请输入解决方案', trigger: 'blur' }]
      }
    }
  },
  computed: {
    bugType() {
      return this.info.state === 1 ? 'info' : this.info.state === '1' ? 'warning' : this.info.state === 2 ? 'success' : ''
    }
  },
  created() {
    this.getDetails()
    this.getRecord()
  },
  methods: {
    goBack() {
      this.$router.push('/bug')
    },
    async getDetails() {
      const { data } = await tBugDetail({ id: this.$route.params.id })
      this.info = data
    },
    async getRecord() {
      const { data } = await tBugBugLogRecord({ bugId: this.$route.params.id })
      this.logRecord = data
    },
    async confirm() {
      this.confirmForm.bugId = this.info.bugId
      this.confirmDialog = true
      // const { data } = await tBugBugLogRecord({ bugId: row.bugId })
      // this.logRecord = data
    },

    async getUserList() {
      const { data } = await getList({ pageNum: 1, pageSize: 2000, organizationId: this.organizationId })
      this.userList = data.list
    },
    confirmBug() {
      this.$refs['confirmForm'].validate(async (val) => {
        if (val) {
          await tBugConfirm(this.confirmForm)
          this.$message.success('bug确认成功')
          this.confirmBugClose()
          this.getDetails()
          this.getRecord()
        }
      })
    },
    confirmBugClose() {
      this.confirmForm = {
        bugId: null,
        repairUser: null,
        priority: null,
        remark: null
      }
      this.$refs['confirmForm'].resetFields()
      this.confirmDialog = false
    },
    async solve() {
      const time = formatDate(new Date(), 'yyyy-MM-dd hh:mm:ss')
      this.solveForm = { ...this.info }
      this.solveForm.repairTime = time
      this.$set(this.solveForm, 'repairPlan', null)
      this.solveDialog = true
      this.$nextTick(() => {
        const quill = document.querySelector('.ql-blank')
        quill.style.minHeight = '400px'
      })
    },
    solveBug() {
      this.$refs['solveForm'].validate(async (val) => {
        if (val) {
          await tBugUpdate({ ...this.solveForm, state: 3 })
          this.$message.success('解决成功!')
          this.getDetails()
          this.getRecord()
          this.solveBugClose()
        }
      })
    },
    solveBugClose() {
      this.solveForm = {}
      this.$refs['solveForm'].resetFields()
      this.solveDialog = false
    },
    close() {
      this.$confirm('确定要关闭该bug吗?', '关闭bug', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await tBugUpdate({ ...this.info, state: 4 })
          this.$message({
            type: 'success',
            message: '关闭成功!'
          })
          this.getDetails()
          this.getRecord()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消关闭'
          })
        })
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  width: 100%;
  height: 100%;
  background: #f2f2f2;
  .content {
    width: 1600px;
    height: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #fff;
    .box {
      display: flex;
      padding-top: 20px;
      .el-card:first-of-type {
        flex: 1;
        max-height: 700px;
        overflow: auto;
        &::-webkit-scrollbar {
          width: 5px;
          border-radius: 5px;
          background: rgba($color: #3464e0, $alpha: 0.3);
        }
        &::-webkit-scrollbar-thumb {
          background: #3464e0;
          border-radius: 5px;
        }
        ::v-deep {
          .title {
            font-size: 22px;
            font-weight: bold;
            color: #000;
          }
        }

        .description {
          margin-top: 30px;
          ::v-deep {
            img {
              max-width: 1000px;
            }
          }
        }
      }
      .el-card:last-of-type {
        width: 400px;
        margin-left: 15px;
        max-height: 700px;
        overflow: auto;
        &::-webkit-scrollbar {
          width: 5px;
          border-radius: 5px;
          background: rgba($color: #3464e0, $alpha: 0.3);
        }
        &::-webkit-scrollbar-thumb {
          background: #3464e0;
          border-radius: 5px;
        }
        ::v-deep {
          .el-card__body {
            padding: 0;
          }
          .el-descriptions {
            padding: 20px;
          }
        }
        .record {
          padding: 20px;
          border-top: 15px solid #f2f2f2;
          font-size: 14px;
          div {
            margin-bottom: 5px;
          }
        }
      }
    }
  }
  ::v-deep {
    .el-dialog {
      max-height: 750px;
      overflow: auto;
      &::-webkit-scrollbar {
        width: 3px;
        background: rgb(167, 167, 167);
      }
      &::-webkit-scrollbar-thumb {
        color: #000;
      }
      .record {
        border-top: 1px solid blue;
        padding-top: 15px;
        & > div {
          font-size: 15px;
          color: #000;
        }
      }
    }
  }
}
</style>
