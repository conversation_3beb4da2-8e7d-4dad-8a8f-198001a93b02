<template>
  <div class="">
    <!-- 日志 -->
    <!-- 搜索查询 -->
    <el-row style="min-width: 1200px; margin-bottom: 20px">
      <el-col :span="24">
        <el-form ref="form" :model="modeLogInfo" label-width="80px" inline>
          <el-form-item label="用户名:" label-width="60px">
            <el-input v-model="modeLogInfo.userName" size="small" placeholder="用户名"></el-input>
          </el-form-item>
          <el-form-item label="开始时间:">
            <el-date-picker v-model="stateTime" size="small" align="right" :picker-options="pickerOptions0" placeholder="选择日期"> </el-date-picker>
          </el-form-item>
          <el-form-item label="结束时间:">
            <el-date-picker v-model="endTime" size="small" align="right" :picker-options="pickerOptions0" placeholder="选择日期"> </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="success" size="small" @click="serachClick">查询</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-table
      :data="modeLogList"
      style="width: 100%"
      border
    ><el-table-column align="center" label="序号" width="width" type="index"> </el-table-column>
      <el-table-column align="center" prop="username" label="用户名" width="width"> </el-table-column>
      <el-table-column align="center" prop="loginTime" label="操作时间" width="width">
        <template v-slot="{ row }">
          <span>{{ row.operationTime | formatDate }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="module" label="模块" width="width"> </el-table-column>
      <el-table-column align="center" prop="businessType" label="业务类型" width="width"> </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-pagination layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 15, 20, 30]" :total="total" :page-size.sync="modeLogInfo.pageSize" :current-page.sync="modeLogInfo.pageNum" style="text-align: center; margin-top: 15px" @size-change="getModelLogList" @current-change="getModelLogList"> </el-pagination>
  </div>
</template>

<script>
import { getModelLogList } from '@/api/log.js'
import { formatDate } from '@/filters'
export default {
  name: 'MoudeLog',
  data() {
    return {
      modeLogInfo: { pageNum: 1, pageSize: 10, userName: null, startDate: null, endDate: null, module: null },
      stateTime: null,
      endTime: null,
      modeLogList: [],
      total: 0,
      pickerOptions0: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 8.64e6
        }
      }
    }
  },
  created() {
    this.getModelLogList()
  },
  methods: {
    async getModelLogList() {
      const { data } = await getModelLogList(this.modeLogInfo)
      console.log(data)
      this.modeLogList = data.list
      this.total = data.total
    },
    serachClick() {
      this.stateTime ? this.modeLogInfo.startDate = formatDate(this.stateTime, 'yyyy-MM-dd') : this.modeLogInfo.startDate = null
      this.endTime ? this.modeLogInfo.endDate = formatDate(this.endTime, 'yyyy-MM-dd') : this.modeLogInfo.endDate = null
      this.getModelLogList()

      // const time = new Date(this.logInfo.startDate)
      // const currentData = new Date()
      // if (time.getTime() < currentData.getTime()) {
      // } else {
      //   this.$message.warning('您选择的开始时间不能是今天以后的时间')
      // }
      console.log(this.time)
      console.log(this.modeLogInfo)
    }
  }
}
</script>

<style scoped lang="sass"></style>
