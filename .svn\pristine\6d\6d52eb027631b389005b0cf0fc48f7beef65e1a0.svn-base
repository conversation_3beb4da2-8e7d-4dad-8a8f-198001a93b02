<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="viewport"
    content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1, user-scalable=no">
  <link rel="icon" href="../favicon.ico">
  <title>中飞虚拟仿真教学平台</title>
  <link rel="shortcut icon" href="./TemplateData/favicon.ico">
  <link rel="stylesheet" href="./TemplateData/style.css">
  <link rel="stylesheet" href="./TemplateData/animate.min.css">
  <!-- <script src="../ipconfig.js" type="text/javascript" charset="utf-8"></script> -->
  <script src="../jquery-3.7.0.min.js" type="text/javascript" charset="utf-8"></script>
  <script src="./TemplateData/UnityLoader.js"></script>
  <script src="./TemplateData/UnityProgress.js"></script>
  <script type="text/javascript">
    // var baseUrl = window.config.VUE_APP_BASE_API;
    // var labUrl = window.config.VUE_FICTITIOUS_URL;
    // var islocal = window.config.VUE_IS_LOCAL;
    // var token = window.localStorage.getItem('token');
    // var name = window.localStorage.getItem('name');
    // var pid = getQueryVariable('pid'); //试卷id
    // var resourceId = getQueryVariable('resourceId'); //资源id
    // var vrDetailId = getQueryVariable('vrDetailId'); //实验id
    if (window.localStorage.getItem('emulationUrl')) {
      // const durl = /https?:\/\/[\w.:\-_%]*\/[\w.\-_%]*\/[\w.\-_%]*\/[\w.:\-_%]*\//i
      const webGlUrl = window.localStorage.getItem('emulationUrl')
      const index = window.localStorage.getItem('emulationUrl').lastIndexOf('Build')
      const jurl = webGlUrl.substring(index, webGlUrl.length)
      const serverURL = webGlUrl.replace(jurl, '')
      $(function () {
        getLib();
      })


      function getLib() {
        screenFull();
        //   var jsonUrl = 'https://' + window.localStorage.getItem('webGlUrl')
        var jsonUrl = webGlUrl
        console.log(jsonUrl);
        //   var jsonUrl = labUrl + 'Build/' + res.data.attachPrefix + '.json';

        gameInstance = UnityLoader.instantiate("gameContainer", jsonUrl, {
          onProgress: UnityProgress
        });
      }

      function loadComplete() {
        // $.ajax({
        //   type: "get",
        //   url: baseUrl + "/api/getPaper",
        //   headers: {
        //     'token': token
        //   },
        //   data: {
        //     id: pid,
        //     pid: pid,
        //   },
        //   success: function (res) {
        //     if (res.msg) {
        //       alert(res.msg);
        //     } else {
        //       res = JSON.parse(res);
        //       res.data.id = pid;
        //       res = JSON.stringify(res);
        //       gameInstance.SendMessage("Jia", "init", res);
        //     }
        //   }
        // });
      }

      function close(flag) {
        // $.ajax({
        // 	type: "put",
        // 	url: baseUrl + "/simulation/record/" + recordId,
        // 	headers: {
        // 		'token': token
        // 	}
        // });
            
        window.evt = document.createEvent('Event');
        window.evt.initEvent('myEvent', true, true);
        window.dispatchEvent(window.evt); //让自定义事件触发

      }

      function closeWindow() {
        // $.ajax({
        // 	type: "put",
        // 	url: baseUrl + "/simulation/record/" + recordId,
        // 	headers: {
        // 		'token': token
        // 	}
        // });
        window.evt = document.createEvent('Event');
        window.evt.initEvent('myEvent', true, true);
        window.dispatchEvent(window.evt); //让自定义事件触发
      }

      function netError() {
        var netConfirm = confirm("网络连接中断，是否刷新？");
        if (netConfirm == true) {
          location.reload()
        } else {
          closeWindow()
        }
      }

      function setResDir() {
        console.log('164 ===================================');
        $('.webgl_infobox').addClass('webgl_infohidden');
        $('.webgl_infotips').addClass('webgl_infohidden');
        gameInstance.SendMessage("ServerURL", "SetServerURL", serverURL);
      }

      function screenFull() {
        var height = document.documentElement.clientHeight - 50;
        var width = document.documentElement.clientWidth - 10;
        var w = height / 9 * 16;
        if (w > width) {
          height = width / 16 * 9;
          w = width;
        }
        document.getElementById('gameContainer').style.height = height + 'px'
        document.getElementById('gameContainer').style.width = w + 'px'
      }

      function getQueryVariable(variable) {
        if (window.location.href.indexOf('?') > 0) {
          var query = window.location.href.split('?')[1];
          var vars = query.split("&");
          for (var i = 0; i < vars.length; i++) {
            var pair = vars[i].split("=");
            if (pair[0] == variable) {
              return pair[1];
            }
          }
        }
        return (false);
      }
    }

  </script>
</head>

<body>
  <div class="webgl_content">
    <div id="gameContainer" class="webgl_container"></div>
    <div class="webgl_virtual">
      <div class="webgl_infobox">
        <img class="webgl_logo animate__animated animate__zoomIn" src="./TemplateData/logo.png" alt="" />
        <img class="webgl_light" src="./TemplateData/light.png" alt="" />
        <div class="webgl_loading_bg">
          <div class="progress_bar"></div>
        </div>
        <div class="webgl_loading_text"></div>
      </div>
    </div>
    <div class="webgl_footfullscreen " onclick="gameInstance.SetFullscreen(1)"></div>
    <!-- <div class="webgl_infotips">
      <div>本实验基于最新的HTML5和WebGL技术开发，使用虚拟3D场景构建。如点击进入实验课程后无法正常显示，建议下载<span
          class="textWarning">谷歌浏览器、火狐浏览器打开（不支持IE浏览器）</span>。如使用其他浏览器（如360、QQ、UC、猎豹、遨游等浏览器）请切换成<span
          class="textSuccess">极速模式</span>，去掉<span class="textSuccess">鼠标手势</span>。</div>
      <div>如遇到加载至99%长时间不进入实验的情况，请清除浏览器缓存，重新加载。如仍未正常显示，请检查电脑是否有<span class="textSuccess">独立显卡</span>，内存是否满足<span
          class="textSuccess">8G内存</span>建议更换电脑重新进入实验。</div>
    </div> -->
    <!-- <div class="webgl_footer webgl_infohidden">
      <div class="webgl_footlogo"></div>
      <div class="webgl_footuser"></div>
      <div class="webgl_foottitle"></div>
    </div> -->
  </div>
</body>

</html>