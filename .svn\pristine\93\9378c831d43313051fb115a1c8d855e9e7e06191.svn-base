<template>
  <el-dialog
    :visible="showRoleDialog"
    title="分配角色"
    width="900px"
    :close-on-click-modal="false"
    @close="btnClose"
  >
    <el-checkbox-group v-model="checkList">
      <el-checkbox
        v-for="item in list"
        :key="item.roleId"
        :label="item.roleId"
      >{{ item.roleName }}</el-checkbox>
    </el-checkbox-group>
    <el-row type="flex" justify="center" style="margin-top: 20px">
      <el-col :span="6">
        <el-button type="primary" @click="allotRole">确定</el-button>
        <el-button @click="btnClose">取消</el-button>
      </el-col>
    </el-row>
  </el-dialog>
</template>

<script>
import { getroleList, roleAll, assignRoles } from '@/api/role'
export default {
  name: 'AllotRole',
  props: {
    showRoleDialog: {
      type: Boolean,
      required: true
    },
    userId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      checkList: [],
      list: []
    }
  },
  created: function() {
    this.getRoleList()
  },
  methods: {
    async getRoleList() {
      const res = await getroleList({ pageNum: 1, pageSize: 200 })
      this.list = res.data.list
      console.log(this.list)
      console.log(res, '全部角色')
    },
    async getUserDetailById(id) {
      const { data } = await roleAll(id)
      const roleId = []
      data.forEach((item) => {
        if (item.checked) {
          roleId.push(item.roleId)
        }
      })
      this.checkList = roleId
      console.log(data, '当前用户所拥有角色')
    },
    async allotRole() {
      const res = await assignRoles({
        userId: this.userId,
        roleIds: this.checkList
      })
      console.log(res)

      this.$message.success('角色分配成功')
      this.$emit('update:showRoleDialog', false)
    },
    btnClose() {
      this.checkList = [] // 清空原来的数组
      this.$emit('update:showRoleDialog', false)
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
