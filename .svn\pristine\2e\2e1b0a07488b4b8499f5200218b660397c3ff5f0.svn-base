<template>
  <div class="">
    <el-dialog :title="showTitle" :visible="showDialog" width="450px" :close-on-click-modal="false" @close="close">
      <el-form ref="form" :model="specialtyForm" label-width="90px" :rules="rules">
        <el-form-item label="专业代码:" prop="majorCode"><el-input v-model="specialtyForm.majorCode" :disabled="specialtyForm.parentId === 0" oninput="value = value.replace(/[\u4E00-\u9FA5]/g,'')" maxlength="50" placeholder="请输入专业代码" /></el-form-item>
        <el-form-item label="专业名称:" prop="majorName"><el-input v-model="specialtyForm.majorName" maxlength="100" placeholder="请输入专业名称" /></el-form-item>
        <el-form-item label="上级专业:" prop="parentId">
          <el-select ref="selecteltree" v-model="value" :disabled="specialtyForm.parentId === 0" placeholder="请选择上级专业" @focus="getMenuTreeOfParent">
            <el-option v-for="item in menu" :key="item.id" :label="item.majorName" :value="item.id" style="display: none" />
            <el-tree style="padding-left: 15px" :data="menu" node-key="id" empty-text="暂无菜单" highlight-current :expand-on-click-node="false" :props="defaultProps" current-node-key="id" default-expand-all @node-click="handleNodeClick" />
          </el-select>
        </el-form-item>
        <el-form-item label="专业描述:"><el-input v-model="specialtyForm.description" maxlength="200" type="textarea" placeholder="请输入专业描述,200字以内" /></el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="confirmOnClick">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { save, majorUpdate, majorTree } from '@/api/specialty'

export default {
  name: 'AddSpecialty',
  props: {
    showDialog: {
      type: Boolean,
      default: false
    },
    isShowSelect: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      specialtyForm: {
        majorName: null,
        majorCode: null,
        description: null,
        parentId: null
      },

      rules: {
        majorName: [{ required: true, tiggle: 'blur', message: '请输入专业名称 ' }],
        majorCode: [{ required: true, tiggle: 'blur', message: '请填写专业代码' }],
        parentId: [{ required: true, tiggle: 'blur', type: 'number', message: '请选择上级专业' }]
      },
      menu: [],
      defaultProps: {
        label: 'majorName',
        children: 'children'
      },
      showTitle: '添加专业',
      value: null
    }
  },
  created() {
    // this.getMenuTreeOfParent()
  },
  methods: {
    showData(row) {
      console.log(row)
      this.specialtyForm = { ...row }
      this.showTitle = '修改专业'
      majorTree().then((res) => {
        const data = this.TreeToFlat(res.data)
        data.forEach((item) => {
          if (item.id === this.specialtyForm.parentId) {
            this.$nextTick(() => {
              this.value = item.majorName
            })
          }
        })
      })
      console.log(row)
    },
    close() {
      this.specialtyForm = {
        majorName: null,
        majorCode: null,
        description: null,
        parentId: null
      }
      this.value = null
      this.showTitle = '添加专业'
      this.$refs['form'].resetFields()
      this.$emit('update:showDialog', false)
    },
    // 点击确认后触发的事件
    confirmOnClick() {
      this.$refs['form'].validate(async (val) => {
        if (val) {
          if (this.showTitle === '修改专业') {
            if (this.specialtyForm.id === this.specialtyForm.parentId) {
              this.$message.warning('不能选择当前菜单为上级菜单')
              return
            }
            if (this.specialtyForm.children && this.specialtyForm.children.length >= 1) {
              const flag = this.specialtyForm.children.some((item) => {
                if (this.specialtyForm.parentId === item.id) {
                  return true
                }
              })
              if (flag) {
                this.$message.warning('不能选择当前菜单的子级为上级菜单')
                return
              }
            }
            // 有id代表修改
            await majorUpdate(this.specialtyForm)
            this.$message.success('修改成功')
            this.$emit('success')
            this.close()
          } else {
            save(this.specialtyForm)
              .then((res) => {
                this.$message.success('添加成功')
                this.$refs['form'].resetFields()
                this.$emit('success')
                this.close()
              })
              .catch((err) => {
                console.log(err)
              })
          }
        }
      })
    },
    getMenuTreeOfParent() {
      majorTree().then((res) => {
        this.menu = res.data
      })
    },
    handleNodeClick(node) {
      this.specialtyForm.parentId = node.id
      this.$nextTick(function () {
        this.value = node.majorName
      })
      this.$refs['selecteltree'].blur()
    },
    // 将树形结构扁平化
    TreeToFlat(data) {
      let formatData = []
      for (var i = 0; i < data.length; i++) {
        formatData.push({
          id: data[i].id,
          majorName: data[i].majorName
        })
        if (data[i].children) {
          formatData = formatData.concat(this.TreeToFlat(data[i].children))
        }
      }
      return formatData
    }
  }
}
</script>

<style scoped lang="sass"></style>
