<template>
  <div class="app-container">
    <el-form ref="form" :model="formInfo" :rules="rules" label-width="90px" inline>
      <el-form-item label="所属产品:" prop="productId">
        <el-select v-model="formInfo.productId" placeholder="请选择所属产品">
          <el-option v-for="item in productList" :key="item.productId" :label="item.productName" :value="item.productId"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="所属专业:" prop="majorId">
        <el-select v-model="formInfo.majorId" placeholder="请选择所属专业">
          <el-option v-for="item in majorList" :key="item.majorId" :label="item.majorName" :value="item.majorId"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="实验名称:" prop="majorId">
        <el-input v-model="formInfo.name" placeholder="请输入实验名称" maxlength="40"></el-input>
      </el-form-item>
      <!-- <el-form-item label="解决人:" prop="repairUser">
        <el-select v-model="formInfo.repairUser" filterable placeholder="请选择解决人" @focus="getUserList">
          <el-option v-for="item in userList" :key="item.userId" :label="item.realName" :value="item.realName"> </el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="bug标题:" prop="title">
        <el-input v-model="formInfo.title" placeholder="请输入bug标题" maxlength="40"></el-input>
      </el-form-item>
      <el-form-item label="优先级:" prop="priority">
        <el-radio-group v-model="formInfo.priority">
          <el-radio :label="1">低</el-radio>
          <el-radio :label="2">中</el-radio>
          <el-radio :label="3">高</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="bug描述:" prop="description">
        <vueQuillEditor ref="contentQuill" @change="formInfo.description = $event.html" />
      </el-form-item>
      <el-form-item label="反馈人:" prop="feedbackUser">
        <el-input v-model="formInfo.feedbackUser" placeholder="请输入反馈人" maxlength="40"></el-input>
      </el-form-item>
      <el-form-item label="反馈时间:" prop="feedbackTime">
        <el-date-picker v-model="formInfo.feedbackTime" type="datetime" placeholder="选择反馈时间" value-format="yyyy-MM-dd HH:mm:ss"> </el-date-picker>
      </el-form-item>
    </el-form>
    <el-row type="flex" justify="center">
      <el-button type="primary" plain @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">保存</el-button>
    </el-row>
  </div>
</template>

<script>
import { getdictTypeList } from '@/api/product'
import { allMajor } from '@/api/specialty'
import { getList } from '@/api/systemUser'
import { tBugAdd, tBugDetail, tBugUpdate } from '@/api/bug'
import { mapGetters } from 'vuex'
import vueQuillEditor from '@/components/vueQuillEditor'

export default {
  name: '',
  components: {
    vueQuillEditor
  },
  data() {
    return {
      formInfo: {
        bugId: null, // 标识id
        title: null, // 标题
        name: null, // 实验名称
        majorId: null, // 专业id
        productId: null, // 产品id
        feedbackUser: null, // 反馈人
        feedbackTime: null, // 反馈时间
        description: null, // 问题描述
        priority: null, // 优先级 1 低 2 中 3 高
        repairPlan: null, // 解决方案
        // repairUser: null, // 解决人
        repairTime: null, // 解决时间
        state: null // 状态 1 待解决 2 解决中 3 已解决 4 已关闭
      },
      rules: {
        productId: [{ required: true, message: '请选择产品', trigger: 'change', type: 'number' }],
        majorId: [{ required: true, message: '请选择专业', trigger: 'change', type: 'number' }],
        name: [{ required: true, message: '请选择实验名称', trigger: 'blur' }],
        // repairUser: [{ required: true, message: '请选择解决人', trigger: 'change' }],
        title: [{ required: true, message: '请输入bug标题', trigger: 'blur' }],
        priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
        description: [{ required: true, message: '请输入bug描述', trigger: 'change' }],
        feedbackUser: [{ required: true, message: '请输入反馈人', trigger: 'blur' }],
        feedbackTime: [{ required: true, message: '请选择反馈时间', trigger: 'change' }]
      },
      productList: [],
      majorList: [],
      userList: []
    }
  },
  computed: {
    ...mapGetters(['organizationId'])
  },
  created() {
    this.getProductList()
    this.getMajorList()
    if (this.$route.params.id !== '0') {
      this.showData()
    }
  },
  mounted() {
    const quill = document.querySelector('.ql-blank')
    quill.style.minHeight = '400px'
  },
  methods: {
    async getProductList() {
      const { data } = await getdictTypeList({ pageNum: 1, pageSize: 200 })
      this.productList = data.list
    },
    async getMajorList() {
      const { data } = await allMajor()
      this.majorList = data
    },
    async getUserList() {
      const { data } = await getList({ pageNum: 1, pageSize: 2000 })
      this.userList = data.list
    },
    close() {
      this.$router.push('/bug')
    },
    confirm() {
      this.$refs['form'].validate((val) => {
        if (val) {
          const loading = this.$loading({
            text: '正在保存中',
            background: 'rgba(0, 0, 0, 0.8)'
          })
          if (this.$route.params.id !== '0') {
            tBugUpdate(this.formInfo)
              .then(() => {
                loading.close()
                this.$message.success('修改成功')
                this.close()
              })
              .catch(() => {
                loading.close()
              })
          } else {
            tBugAdd(this.formInfo)
              .then(() => {
                loading.close()
                this.$message.success('保存成功')
                this.close()
              })
              .catch(() => {
                loading.close()
              })
          }
        }
      })
    },
    async showData() {
      const { data } = await tBugDetail({ id: this.$route.params.id })
      this.formInfo = { ...data }
      this.$refs['contentQuill'].setContent(data.description)
      // this.getUserList()
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  padding-top: 30px;
}
::v-deep {
  .el-form {
    width: 1200px;
    margin: 0 auto;
    .el-form-item {
      margin-bottom: 40px;
    }

    .el-input {
      .el-input__inner {
        width: 300px;
      }
    }
    .el-date-editor{
      .el-input__inner {
        width: 230px;
      }
    }

  }
}
</style>
