import Vue from 'vue'

import 'normalize.css/normalize.css' // A modern alternative to CSS resets

import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
// import locale from 'element-ui/lib/locale/lang/en' // lang i18n

import 'animate.css'

import '@/styles/index.scss' // global css
import 'lib-flexible'
import App from './App'
import store from './store'
import router from './router'

import '@/icons' // icon
import '@/permission' // permission control
import updateLocalStoragefrom from './utils/updateLocalStorage'
Vue.use(updateLocalStoragefrom)

import LightTimeline from 'vue-light-timeline'

Vue.use(LightTimeline)
import VueQuillEditor from 'vue-quill-editor'
import Quill from 'quill'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
import { ImageDrop } from 'quill-image-drop-module'
import imageResize from 'quill-image-resize-module'
Quill.register('modules/imageDrop', ImageDrop)
Quill.register('modules/imageResize', imageResize)
Vue.use(VueQuillEditor)

import WebsocketHeartbeatJs from '@/utils/WebsocketHeartbeatJs'
Vue.prototype.$websocketHeartbeatJs = new WebsocketHeartbeatJs({
  url: window.config.VUE_APP_WS_URL
})
/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */
// if (process.env.NODE_ENV === 'production') {
//   const { mockXHR } = require('../mock')
//   mockXHR()
// }
// 引入jquery
// import $ from 'jquery'
// Vue.prototype.$ = $

// 引入echarts

import * as echarts from 'echarts'
Vue.prototype.$echarts = echarts
// 引入轮播
import VueAwesomeSwiper from 'vue-awesome-swiper'
Vue.use(VueAwesomeSwiper)
// set ElementUI lang to EN
// Vue.use(ElementUI, { locale })
// 如果想要中文版 element-ui，按如下方式声明
Vue.use(ElementUI)
import * as filters from '@/filters'
// 注册全局的过滤器
Object.keys(filters).forEach((key) => {
  Vue.filter(key, filters[key])
})

Vue.config.productionTip = false
new Vue({
  el: '#app',
  router,
  store,
  render: (h) => h(App)
})
