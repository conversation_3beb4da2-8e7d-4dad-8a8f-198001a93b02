import request from '@/utils/request'

/** 版本号 - 列表 */
export function versionNumberList(params) {
  return request({
    url: '/versionNumber/list',
    method: 'GET',
    params
  })
}
/** 版本号 - 添加 */
export function versionNumberAdd(data) {
  return request({
    url: '/versionNumber/add',
    method: 'POST',
    data
  })
}
/** 版本号 - 修改 */
export function versionNumberUpdate(data) {
  return request({
    url: '/versionNumber/update',
    method: 'POST',
    data
  })
}
/** 版本号 - 删除 */
export function versionNumberRemove(params) {
  return request({
    url: '/versionNumber/remove',
    method: 'DELETE',
    params
  })
}

/** 版本号 - 详情 */
export function versionNumberDetail(params) {
  return request({
    url: '/versionNumber/detail',
    method: 'GET',
    params
  })
}

/** 版本详情 - 列表 */
export function versionNumberDetailsList(params) {
  return request({
    url: '/versionNumber/detail/list',
    method: 'GET',
    params
  })
}

/** 版本号 - 添加 */
export function versionNumberDetailAdd(data) {
  return request({
    url: '/versionNumber/detail/add',
    method: 'POST',
    data
  })
}
/** 版本详情 - 修改 */
export function versionNumberDetailUpdate(data) {
  return request({
    url: '/versionNumber/detail/update',
    method: 'POST',
    data
  })
}

/** 版本详情 - 删除 */
export function versionNumberDetailDelete(params) {
  return request({
    url: '/versionNumber/detail/delete',
    method: 'DELETE',
    params
  })
}
