<template>
  <div class="app-container">
    <el-row class="top" type="flex" justify="space-between">
      <el-col :span="22">
        <i class="el-icon-location"></i>
        <span>当前位置：<router-link to="/secretLog">日志管理</router-link> /</span>
        <span>写日志</span>
      </el-col>
      <el-col :span="1">
        <el-button type="primary" size="small" icon="el-icon-back" @click="$router.push('/project')">返回</el-button>
      </el-col>
    </el-row>
    <div class="addLog_container">
      <div class="addLog_content">
        <el-row class="addLog_title" type="flex" justify="space-between" align="middle">
          <span>写日志</span>
          <span>返回</span>
        </el-row>
        <el-form ref="form" :model="formInfo" label-width="85px" :rules="rules" class="addLog_form">
          <el-form-item label="时间:" prop="time">
            <div class="time">
              <span :class="{ checked: time === '今天' }" @click="selectChange('今天')">今天</span>
              <span :class="{ checked: time === '昨天' }" @click="selectChange('昨天')">昨天</span>
            </div>
          </el-form-item>
          <el-form-item label="拜访内容:" prop="visitContent">
            <el-input v-model="formInfo.visitContent" type="textarea" placeholder="请输入拜访内容"></el-input>
          </el-form-item>
          <el-form-item label="拜访结果:">
            <el-input v-model="formInfo.visitResult" type="textarea" placeholder="请输入拜访结果"></el-input>
          </el-form-item>
          <el-form-item label="跟踪客户:" class="client" required>
            <div>
              <div class="allClient">
                <el-row type="flex" justify="center" align="middle" style="margin-bottom: 20px">
                  <div>
                    <el-select v-model="customerName" placeholder="请选择客户" style="margin-right: 24px" size="small" clearable @focus="getSecretCustomerCustomerList" @change="selectCustomerChange">
                      <el-option v-for="item in allCustomer" :key="item.customerId" :value="item.customerId" :label="item.customerName"> </el-option>
                    </el-select>
                    <el-select v-model="selectRealName" placeholder="请选择联系人" size="small" clearable @focus="getUserList" @change="contactsChange">
                      <el-option v-for="item in userList" :key="item.contactsId" :label="item.contactsName" :value="item.contactsId"> </el-option>
                    </el-select>
                  </div>

                  <el-button type="primary" size="small" style="margin-left: 15px" @click="addConfirm">添加</el-button>
                </el-row>
                <el-table :data="userList" style="width: 100%" @selection-change="handleSelectionChange">
                  <el-table-column align="center" type="selection" label="" width="width"> </el-table-column>
                  <el-table-column align="center" prop="customerName" label="" width="width"> </el-table-column>
                  <el-table-column align="center" prop="contactsName" label="" width="width"> </el-table-column>
                  <el-table-column align="center" prop="majorName" label="" width="width"> </el-table-column>
                  <el-table-column align="center" prop="phone" label="" width="width"> </el-table-column>
                </el-table>
              </div>
              <div class="checkedClient">
                <el-row>
                  <span class="title">已选择的客户</span>
                </el-row>
                <el-table :data="confirmSelectData" style="width: 100%" border header-cell-class-name="checkedClient_tableHeader" cell-class-name="table_cell">
                  <el-table-column align="center" prop="customerName" label="客户名称" width="width"> </el-table-column>
                  <el-table-column align="center" prop="contactsName" label="客户联系人" width="width"> </el-table-column>
                  <el-table-column align="center" prop="majorName" label="所属专业" width="width"> </el-table-column>
                  <el-table-column align="center" prop="phone" label="联系方式" width="width"> </el-table-column>
                  <el-table-column align="center" label="操作" width="width">
                    <template v-slot="{ row, $index }">
                      <span style="color: #3464e0; cursor: pointer" @click="delCustomer(row, $index)">移除</span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="明日计划:">
            <el-input v-model="formInfo.planContent" type="textarea" placeholder="请输入明日计划"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div class="addLog_footer">
        <span @click="$router.push('/secretLog')"><i class="el-icon-error"></i> 取消</span>
        <span @click="submitLog"><i class="el-icon-success"></i> 提交</span>
      </div>
    </div>
  </div>
</template>

<script>
import { secretCustomeAllCustomer, secretCustomeAllContacts } from '@/api/clientele'
import { secretLog_saveLog } from '@/api/secretLog'
import { formatDate } from '@/filters'
import { mapGetters } from 'vuex'

export default {
  name: '',
  data() {
    return {
      time: null,
      formInfo: {
        name: null,
        time: null,
        visitContent: null,
        visitResult: null,
        planContent: null,
        remark: null,
        contactsIds: []
      },
      customerName: null,
      customerId: null,
      selectRealName: null,
      allCustomer: [],
      userList: [],
      confirmSelectData: [], // 确定选择的人员
      selectData: [],
      rules: {
        time: [{ required: true, message: '请选择时间', trigger: 'change' }],
        visitContent: [{ required: true, message: '请输入拜访内容', trigger: 'blur' }]
      }
    }
  },
  computed: {
    ...mapGetters(['realName'])
  },
  created() {
    this.getUserList()
  },
  methods: {
    selectChange(val) {
      this.time = val
      let time = null
      if (val === '今天') {
        time = formatDate(new Date(), 'yyyy-MM-dd')
      } else {
        const dateTime = new Date()
        dateTime.setDate(dateTime.getDate() - 1)
        time = formatDate(dateTime, 'yyyy-MM-dd')
      }
      this.formInfo.time = time
      this.formInfo.name = `${this.realName}的工作日志(${time})`
    },
    // 提交日志
    submitLog() {
      if (!this.confirmSelectData.length) {
        return this.$message.error('跟踪客户不能为空')
      }
      this.formInfo.contactsIds = this.confirmSelectData.map((item) => item.contactsId)
      this.$refs['form'].validate(async (val) => {
        if (!val) return
        await secretLog_saveLog(this.formInfo)
        this.$message.success('日志提交成功!')
        this.$router.push('/secretLog')
      })
    },
    // 获取全部客户
    async getSecretCustomerCustomerList() {
      const { data } = await secretCustomeAllCustomer(this.customerInfo)
      this.allCustomer = data
    },
    // 选择客户后赋值
    selectCustomerChange(val) {
      if (this.selectRealName) {
        this.selectRealName = null
      }
      this.customerId = val
      this.getUserList()
    },
    // 获取全部联系人
    async getUserList() {
      //   if (!this.customerId) return
      const { data } = await secretCustomeAllContacts({ customerId: this.customerId })
      this.userList = data
      if (this.confirmSelectData.length > 0) {
        const ids = this.confirmSelectData.map((item) => item.contactsId)
        this.userList = this.userList.filter((item) => {
          return !ids.includes(item.contactsId)
        })
      }
      console.log(data)
    },
    //  选择联系人后赋值
    contactsChange(contactsId) {
      if (contactsId) {
        this.userList = this.userList.filter((item) => item.contactsId === contactsId)
        console.log(this.userList)
      } else {
        this.getUserList()
      }
    },
    // 选中的客户
    handleSelectionChange(val) {
      this.selectData = val
      console.log(val)
    },
    // 确定选择的客户
    addConfirm() {
      if (this.confirmSelectData.length === 0) {
        this.confirmSelectData = this.selectData
      } else {
        const ids = this.confirmSelectData.map((item) => item.contactsId)
        const data = this.selectData.filter((item) => {
          return !ids.includes(item.contactsId)
        })
        this.confirmSelectData.push(...data)
      }
      this.selectData = []
      const ids = this.confirmSelectData.map((item) => item.contactsId)
      this.userList = this.userList.filter((item) => {
        return !ids.includes(item.contactsId)
      })
    },
    // 写日志dialog - 删除客户
    delCustomer(row, index) {
      this.confirmSelectData.splice(index, 1)
      this.getUserList()
      console.log(row, index)
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  position: relative;
  padding: 0;
  background-color: #e8eaed;
  min-height: 100%;
  padding-top: 58px;
  padding-bottom: 30px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  .top {
    position: absolute;
    top: 0px;
    width: 100%;
    background-color: #e8eaed;
    padding: 24px 39px 0 0;
    z-index: 999;

    .el-col {
      i {
        font-size: 14px;
        color: #657081;
      }
      span {
        &:first-of-type {
          margin: 0 5px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #657081;
        }
        &:last-of-type {
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
      }
    }
  }
}
.addLog_container {
  width: 1754px;
  padding: 16px;
  margin-top: 16px;
  background: #f5f5f5;
  border-radius: 8px 8px 8px 8px;
  overflow: hidden;
  .addLog_content {
    width: 100%;
    padding-top: 36px;
    padding-left: 74px;
    padding-right: 67px;
    padding-bottom: 15px;
    height: 100%;
    background: #ffffff;
    .addLog_title {
      background: #fafafa;
      padding: 10px 0;
      padding-left: 16px;
      padding-right: 8px;
      border-left: 2px solid #3464e0;
      & > span:first-of-type {
        font-size: 16px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        font-weight: bold;
        color: #0b1a44;
      }
      & > span:last-of-type {
        width: 62px;
        height: 30px;
        line-height: 30px;
        border: 1px solid #d8dbe1;
        border-radius: 4px;
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #868b9f;
        text-align: center;
        cursor: pointer;
      }
    }
    .addLog_form {
      margin-top: 15px;
      padding-left: 80px;
      .time {
        span {
          display: inline-block;
          width: 80px;
          height: 30px;
          margin-left: 16px;
          line-height: 30px;
          background: #f5f5f5;
          border-radius: 4px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
          text-align: center;
          cursor: pointer;
        }
        .checked {
          color: #fff;
          background: #3464e0;
        }
      }
      .client {
        .el-form-item__content > div {
          display: flex;
          .allClient,
          .checkedClient {
            width: 602px;
            height: 300px;
            background: #ffffff;
            border-radius: 4px 4px 4px 4px;
            opacity: 1;
            border: 1px solid #d8dbe1;
            /* 设置滚动条的样式 */
            &::-webkit-scrollbar {
              width: 3px;
            }
            &::-webkit-scrollbar-thumb {
              background-color: #eceef1;
              border-radius: 30px;
            }
          }
          .allClient {
            margin-right: 7px;
            padding-top: 24px;
            overflow: auto;
            ::v-deep {
              .el-select {
                width: 233px;
                height: 30px;
                .el-input {
                  width: 100%;
                  height: 100%;
                  .el-input__inner {
                    width: 100%;
                    height: 100%;
                    background: #f2f4f7;
                    border-color: #e9ebef;
                    font-size: 12px;
                    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                    font-weight: 400;
                    color: #0b1a44;
                  }
                }

                .el-input__icon {
                  line-height: 30px;
                }
              }
              .el-table__header-wrapper {
                display: none;
              }
            }
          }
          .checkedClient {
            width: 674px;
            padding: 0 16px;
            .title {
              font-size: 12px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #868b9f;
            }
            ::v-deep {
              .table_cell {
                height: 42px;
                border: 1px solid #e9ebef;
                font-size: 12px;
                font-family: Microsoft YaHei-Bold, Microsoft YaHei;
                font-weight: bold;
                color: #0b1a44;
              }
              .checkedClient_tableHeader {
                height: 42px;
                padding: 0;
                background: #fafafa;
                border: 1px solid #e9ebef;
                font-size: 12px;
                font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                font-weight: 400;
                color: #657081;
              }
            }
          }
        }
      }

      ::v-deep {
        //   修改label样式
        .el-form-item__label {
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
        // 修改文本域的样式
        .el-textarea {
          .el-textarea__inner {
            width: 1284px;
            height: 140px;
            background: #ffffff;
            border-radius: 4px 4px 4px 4px;
            border: 1px solid #d8dbe1;
          }
        }
      }
    }
  }
  .addLog_footer {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1722px;
    height: 80px;
    margin-top: 16px;
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    & > span:first-of-type {
      display: inline-block;
      margin-right: 32px;
      width: 114px !important;
      height: 38px;
      line-height: 38px;
      width: 28px;
      background: #f2f4ff;
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #868b9f;
      text-align: center;
      border-radius: 6px;
      cursor: pointer;
    }
    & > span:last-of-type {
      display: inline-block;
      width: 114px !important;
      height: 38px;
      line-height: 38px;
      width: 28px;
      background: #3464e0;
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #fff;
      text-align: center;
      border-radius: 6px;
      cursor: pointer;
    }
  }
}
</style>
