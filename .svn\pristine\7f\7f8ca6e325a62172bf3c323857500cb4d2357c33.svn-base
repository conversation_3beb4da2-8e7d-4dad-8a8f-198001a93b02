<template>
  <div class="">
    <el-dialog title="分享" :visible="showDialog" width="400px" @open="getUser" @close="close">
      <el-select v-model="userIds" multiple filterable placeholder="请选择要分享的用户">
        <el-option v-for="item in userList" :key="item.userId" :label="item.realName" :value="item.userId"> </el-option>
      </el-select>
      <div slot="footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getList } from '@/api/systemUser'
import { tSalesPlanAdd } from '@/api/tSalesPlan'
export default {
  name: '',
  props: {
    showDialog: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      info: {},
      userIds: [],
      userList: []
    }
  },
  methods: {
    close() {
      this.userIds = []
      this.$emit('update:showDialog', false)
    },
    async getUser() {
      const { data } = await getList({ organizationId: 56642510 })
      this.userList = data.list
    },
    confirm() {
      var loading = this.$loading({
        text: '分享中，请稍后',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      tSalesPlanAdd({ ...this.info, userId: this.userIds.join(',') })
        .then((res) => {
          loading.close()
          this.$message.success('分享成功!')
          this.$router.push('/tSalesPlan')
          this.close()
        })
        .catch(() => {
          loading.close()
        })
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep {
  .el-select {
    width: 360px;
  }
}
</style>
