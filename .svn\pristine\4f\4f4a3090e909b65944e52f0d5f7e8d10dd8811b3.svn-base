// import parseTime, formatTime and set to filter

/**
 * Show plural label if time is plural number
 * @param {number} time
 * @param {string} label
 * @return {string}
 */
function pluralize(time, label) {
  if (time === 1) {
    return time + label
  }
  return time + label + 's'
}

/**
 * @param {number} time
 */
export function timeAgo(time) {
  const between = Date.now() / 1000 - Number(time)
  if (between < 3600) {
    return pluralize(~~(between / 60), ' minute')
  } else if (between < 86400) {
    return pluralize(~~(between / 3600), ' hour')
  } else {
    return pluralize(~~(between / 86400), ' day')
  }
}

/**
 * Number formatting
 * like 10000 => 10k
 * @param {number} num
 * @param {number} digits
 */
export function numberFormatter(num, digits) {
  const si = [
    { value: 1e18, symbol: 'E' },
    { value: 1e15, symbol: 'P' },
    { value: 1e12, symbol: 'T' },
    { value: 1e9, symbol: 'G' },
    { value: 1e6, symbol: 'M' },
    { value: 1e3, symbol: 'k' }
  ]
  for (let i = 0; i < si.length; i++) {
    if (num >= si[i].value) {
      return (num / si[i].value).toFixed(digits).replace(/\.0+$|(\.[0-9]*[1-9])0+$/, '$1') + si[i].symbol
    }
  }
  return num.toString()
}

/**
 * 10000 => "10,000"
 * @param {number} num
 */
export function toThousandFilter(num) {
  return (+num || 0).toString().replace(/^-?\d+/g, (m) => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
}

/**
 * Upper case first char
 * @param {String} string
 */
export function uppercaseFirst(string) {
  return string.charAt(0).toUpperCase() + string.slice(1)
}

export function parseTime(time, cFormat) {
  if (arguments.length === 0) {
    return null
  }

  if ((time + '').length === 10) {
    time = +time * 1000
  }

  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    date = new Date(parseInt(time))
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const timeStr = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    if (key === 'a') {
      return ['一', '二', '三', '四', '五', '六', '日'][value - 1]
    }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  return timeStr
}

export function formatTime(time, option) {
  time = +time * 1000
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return d.getMonth() + 1 + '月' + d.getDate() + '日' + d.getHours() + '时' + d.getMinutes() + '分'
  }
}
export function getNowFormatDate() {
  var date = new Date()
  var seperator1 = '-'
  var year = date.getFullYear()
  var month = date.getMonth() + 1
  var strDate = date.getDate()
  if (month >= 1 && month <= 9) {
    month = '0' + month
  }
  if (strDate >= 0 && strDate <= 9) {
    strDate = '0' + strDate
  }
  var currentdate = year + seperator1 + month + seperator1 + strDate
  return currentdate
}
/* 数字 格式化 */
export function nFormatter(num, digits) {
  const si = [
    {
      value: 1e18,
      symbol: 'E'
    },
    {
      value: 1e15,
      symbol: 'P'
    },
    {
      value: 1e12,
      symbol: 'T'
    },
    {
      value: 1e9,
      symbol: 'G'
    },
    {
      value: 1e6,
      symbol: 'M'
    },
    {
      value: 1e3,
      symbol: 'k'
    }
  ]
  for (let i = 0; i < si.length; i++) {
    if (num >= si[i].value) {
      return (num / si[i].value + 0.1).toFixed(digits).replace(/\.0+$|(\.[0-9]*[1-9])0+$/, '$1') + si[i].symbol
    }
  }
  return num.toString()
}

export function html2Text(val) {
  const div = document.createElement('div')
  div.innerHTML = val
  return div.textContent || div.innerText
}

export function toThousandslsFilter(num) {
  return (+num || 0).toString().replace(/^-?\d+/g, (m) => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
}
// 验证手机号
export function checkPhone(rule, value, callback) {
  const reg = /^1[3|4|5|7|8][0-9]\d{8}$/
  if (reg.test(value)) {
    callback()
  } else if (!value) {
    return callback()
  } else {
    return callback(new Error('请输入正确的手机号'))
  }
}
export function checkPassword(rule, value, callback) {
  if (!value) {
    return callback(new Error('密码不能为空'))
  } else if (value.length < 6) {
    callback(new Error('请至少输入 6 个字符。请不要使用容易被猜到的密码'))
  } else {
    callback()
  }
}
// 手机号证验证
export function checkTel(rule, value, callback) {
  if (!value) {
    return callback()
  } else {
    const reg = /^1[3|4|5|6|7|8][0-9]\d{8}$/
    if (reg.test(value)) {
      callback()
    } else {
      return callback(new Error('请输入正确的手机号'))
    }
  }
}
// 身份证验证
export function checkiDNumber(value, callback) {
  var reg = /\d{17}[\d|x]|\d{15}/
  return reg.test(value)
}
// 身份证验证
export function checkEmails(rule, value, callback) {
  if (!value) {
    return callback(new Error('身份证不能为空'))
  } else {
    var reg = /(^\d{15}$)|(^\d{17}([0-9]|X)$)/
    if (reg.test(value)) {
      callback()
    } else {
      return callback(new Error('请输入正确的身份证号'))
    }
  }
}
// 邮箱验证
export function checkEmail(rule, value, callback) {
  if (!value) {
    return callback(new Error('邮箱不能为空'))
  } else {
    var reg = /^[A-Za-zd]+([-_.][A-Za-zd]+)*@([A-Za-zd]+[-.])+[A-Za-zd]{2,5}$/
    if (reg.test(value)) {
      callback()
    } else {
      return callback(new Error('请输入正确的邮箱'))
    }
  }
}
// 英文验证
export function checkCode(value, callback) {
  var reg = /^[A-Za-z]+$/g
  return reg.test(value)
}
// qq验证
export function checkQq(value, callback) {
  var reg = /^[0-9]+$/g
  return reg.test(value)
}
// 银行卡号
export function formatBankNo(BankNo, callback) {
  var strBin = '10,18,30,35,37,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,58,60,62,65,68,69,84,87,88,94,95,98,99'
  return strBin
}
export function getStrleng(str, max) {
  var myLen = 0
  for (var i = 0; i < str.length && myLen <= max * 2; i++) {
    if (str.charCodeAt(i) > 0 && str.charCodeAt(i) < 128) {
      myLen++
    } else myLen += 2
  }
  return myLen
}
// 上传图片格式控制
export function updatedImg(file, obj, callback, func) {
  if (file.size < ********) {
    var fileName = file.name
    var suffix = fileName.substring(fileName.lastIndexOf('.') + 1).toUpperCase()
    if (suffix === 'PDF' || suffix === 'JPG' || suffix === 'JPEG' || suffix === 'PNG' || suffix === 'GIF') {
      return true
    } else {
      var tipType = '文件类型不正确,请重新上传'
      callback(tipType)
      return false
    }
  } else {
    var tipSize = '文件大小超过5M,请重新上传'
    callback(tipSize)
    return false
  }
}
// 上传文档格式控制
export function updatedFile(file, obj, callback, func) {
  if (file.size < ********) {
    var fileName = file.name
    var suffix = fileName.substring(fileName.lastIndexOf('.') + 1).toUpperCase()
    if (suffix === 'DOC' || suffix === 'DOCX' || suffix === 'XLS' || suffix === 'XLSX' || suffix === 'PDF' || suffix === 'ZIP' || suffix === 'RAR') {
      return true
    } else {
      var tipType = '文件类型不正确,请重新上传'
      callback(tipType)
      return false
    }
  } else {
    var tipSize = '文件大小超过5M,请重新上传'
    callback(tipSize)
    return false
  }
}
export function importFile(file, obj, callback, func) {
  if (file.size < ********) {
    var fileName = file.name
    var suffix = fileName.substring(fileName.lastIndexOf('.') + 1).toUpperCase()
    if (suffix === 'XLS' || suffix === 'XLSX') {
      return true
    } else {
      var tipType = '文件类型不正确,请重新上传'
      callback(tipType)
      return false
    }
  } else {
    var tipSize = '文件大小超过10M,请重新上传'
    callback(tipSize)
    return false
  }
}
export function minHeight(resfile) {
  return document.body.clientHeight - 180 + 'px'
}

export function formatDate(date, fmt = 'yyyy-MM-dd hh:mm') {
  if (!(date instanceof Array)) {
    date = new Date(date)
  }
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  const o = {
    'M+': date.getMonth() + 1,
    'd+': date.getDate(),
    'h+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds()
  }
  for (const k in o) {
    if (new RegExp(`(${k})`).test(fmt)) {
      const str = o[k] + ''
      fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? str : padLeftZero(str))
    }
  }
  return fmt
}

function padLeftZero(str) {
  return ('00' + str).substr(str.length)
}
export function getBlob(response) {
  const blob = new Blob([response.data], {
    type: 'application/vnd.ms-excel'
  })
  const link = document.createElement('a')
  link.href = window.URL.createObjectURL(blob)
  var filename = decodeURI(response.headers.filename)
  // link.download = filename + '.xls'
  link.download = filename
  link.click()
}
// 图片 blob 流转化为可用 src
export function imgHandle(obj) {
  return window.URL.createObjectURL(obj)
}
export function sexHandle(sex) {
  const sexOptions = [
    {
      label: '',
      value: null
    },
    {
      label: '男',
      value: 'M'
    },
    {
      label: '女',
      value: 'F'
    }
  ]
  const [{ label }] = sexOptions.filter((item) => item.value === sex)
  return label
}
// 停用/ 启用
export function stateHandle(state) {
  if (!state) {
    return '禁用'
  }
  const states = [
    {
      label: '启用',
      value: 1
    },
    {
      label: '禁用',
      value: 0
    }
  ]
  const [{ label }] = states.filter((item) => item.value === state)
  return label
}
// 产品类型
export function formattingProductType(state) {
  const states = [
    {
      label: '软件',
      value: 1
    },
    {
      label: '硬件',
      value: 2
    },
    {
      label: '虚拟仿真',
      value: 3
    }
  ]
  const [{ label }] = states.filter((item) => item.value === parseInt(state))
  return label
}

/** 审批状态*/
export function processStatus(state) {
  const states = [
    {
      label: '新创建 ',
      value: 'NEW'
    },
    {
      label: '审批中',
      value: 'RUNNING'
    },
    {
      label: '被终止',
      value: 'TERMINATED'
    },
    {
      label: '完成',
      value: 'COMPLETED'
    },
    {
      label: '取消',
      value: 'CANCELED'
    }
  ]
  const [{ label }] = states.filter((item) => item.value === state)

  return label
}
/** 当前审批状态*/
export function currentTaskStatus(state) {
  const states = [
    {
      label: '未启动',
      value: 'NEW'
    },
    {
      label: '处理中',
      value: 'RUNNING'
    },
    {
      label: '暂停',
      value: 'PAUSED'
    },
    {
      label: '取消',
      value: 'CANCELED'
    },
    {
      label: '完成',
      value: 'COMPLETED'
    },
    {
      label: '终止',
      value: 'TERMINATED'
    }
  ]
  const [{ label }] = states.filter((item) => item.value === state)

  return label
}
/** 审批类型*/
export function processType(state) {
  const states = [
    {
      label: '发起',
      value: 1
    },
    {
      label: '抄送',
      value: 2
    },
    {
      label: '审批',
      value: 3
    }
  ]
  const [{ label }] = states.filter((item) => item.value === parseInt(state))

  return label
}

/** 审批结果*/
export function processResult(state) {
  const states = [
    {
      label: '',
      value: 'NONE'
    },
    {
      label: '同意',
      value: 'AGREE'
    },
    {
      label: '拒绝',
      value: 'REFUSE'
    },
    {
      label: '转交',
      value: 'REDIRECTED'
    }
  ]
  const [{ label }] = states.filter((item) => item.value === state)
  return label
}
/** 会议状态*/
export function meetingStatus(state) {
  const states = [
    {
      label: '未开始',
      value: 1
    },
    {
      label: '进行中',
      value: 2
    },
    {
      label: '已结束',
      value: 3
    },
    {
      label: '已归档',
      value: 4
    }
  ]
  const [{ label }] = states.filter((item) => item.value === parseInt(state))

  return label
}
/** 项目阶段*/
export function projectStage(state) {
  const states = [
    {
      label: '未立项',
      value: 1
    },
    {
      label: '调研中',
      value: 2
    },
    {
      label: '研发中',
      value: 3
    },
    {
      label: '交付中',
      value: 4
    },
    {
      label: '已交付',
      value: 5
    }
  ]
  if (state === null) {
    return ''
  }
  const [{ label }] = states.filter((item) => item.value === parseInt(state))

  return label
}

/** 施工阶段*/
export function buildStage(state) {
  const states = [
    {
      label: '施工准备中',
      value: 1
    },
    {
      label: '施工中',
      value: 2
    },
    {
      label: '竣工',
      value: 3
    }
  ]
  if (state === null) {
    return ''
  }
  const [{ label }] = states.filter((item) => item.value === parseInt(state))

  return label
}
/** 培训状态格式化*/
export function trainStatus(state) {
  const states = [
    {
      label: '未开始',
      value: 1
    },
    {
      label: '进行中',
      value: 2
    },
    {
      label: '已结束',
      value: 3
    },
    {
      label: '已终止',
      value: 4
    }
  ]
  if (state === null) {
    return ''
  }
  const [{ label }] = states.filter((item) => item.value === parseInt(state))

  return label
}
/** 培训状态格式化*/
export function powerType(state) {
  const states = [
    {
      label: ' 部门内部',
      value: 1
    },
    {
      label: ' 公司培训',
      value: 2
    },
    {
      label: '外部培训',
      value: 3
    }
  ]
  if (state === null) {
    return ''
  }
  const [{ label }] = states.filter((item) => item.value === parseInt(state))

  return label
}
// 格式化文件大小
export function formattingFileSize(size) {
  const MB = parseInt(size / 1024 / 1024)
  const KB = parseInt(size / 1024)
  return MB > 0 ? `${MB}MB` : `${KB}KB`
  // return `${KB}KB`
}
/** 格式化知识库类型 */
export function repository_type(state) {
  const states = [
    {
      label: ' 技术类 ',
      value: 1
    },
    {
      label: ' 专业类 ',
      value: 2
    },
    {
      label: '商务类',
      value: 3
    },
    {
      label: '其他',
      value: 4
    }
  ]
  if (state === null) {
    return ''
  }
  const [{ label }] = states.filter((item) => item.value === parseInt(state))

  return label
}

export function timeago(time) {
  var data = new Date(time)
  var dateTimeStamp = data.getTime()
  var minute = 1000 * 60 // 把分，时，天，周，半个月，一个月用毫秒表示
  var hour = minute * 60
  var day = hour * 24
  var week = day * 7
  var month = day * 30
  var year = month * 12
  var now = new Date().getTime() // 获取当前时间毫秒
  var diffValue = now - dateTimeStamp // 时间差

  var result = ''
  if (diffValue < 0) {
    result = '' + '未来'
  }
  var minC = diffValue / minute // 计算时间差的分，时，天，周，月
  var hourC = diffValue / hour
  var dayC = diffValue / day
  var weekC = diffValue / week
  var monthC = diffValue / month
  var yearC = diffValue / year

  if (yearC >= 1) {
    result = ' ' + parseInt(yearC) + '年前'
  } else if (monthC >= 1 && monthC < 12) {
    result = ' ' + parseInt(monthC) + '月前'
  } else if (weekC >= 1 && weekC < 5 && dayC > 6 && monthC < 1) {
    result = ' ' + parseInt(weekC) + '周前'
  } else if (dayC >= 1 && dayC <= 6) {
    result = ' ' + parseInt(dayC) + '天前'
  } else if (hourC >= 1 && hourC <= 23) {
    result = ' ' + parseInt(hourC) + '小时前'
  } else if (minC >= 1 && minC <= 59) {
    result = ' ' + parseInt(minC) + '分钟前'
  } else if (diffValue >= 0 && diffValue <= minute) {
    result = '刚刚'
  }

  return result
}
