<template>
  <div class="app-container">
    <el-row class="top" type="flex" justify="space-between">
      <el-col :span="22">
        <i class="el-icon-location"></i>
        <span>当前位置：<router-link to="/project">项目管理</router-link> /</span>
        <span>项目详情</span>
      </el-col>
      <el-col :span="1">
        <el-button type="primary" size="small" icon="el-icon-back" @click="$router.push('/project')">返回</el-button>
      </el-col>
    </el-row>

    <section class="project_details_box">
      <el-row class="top_nav" type="flex" justify="space-between" align="bottom">
        <el-col :span="22">
          <div class="tabs">
            <span :class="{ checkedTab: activeName === 1 }" @click="activeName = 1">基本信息</span>
            <span :class="{ checkedTab: activeName === 2 }" @click="activeName = 2">成员列表</span>
            <span :class="{ checkedTab: activeName === 3 }" @click="activeName = 3">项目资料</span>
            <span :class="{ checkedTab: activeName === 4 }" @click="activeName = 4">版本记录</span>
            <span :class="{ checkedTab: activeName === 5 }" @click="activeName = 5">施工记录</span>
          </div>
        </el-col>
        <el-col :span="2">
          <div v-if="activeName === 1 && $route.params.type === '1'" class="saveButton" @click="edit">保存</div>
        </el-col>
      </el-row>
      <!-- 详情进入展示——基本信息展示 -->
      <el-card v-show="activeName === 1 && $route.params.type === '0'" class="basicInfo_details">
        <div>
          <div class="name"><img src="@/assets/project/table_name_icon.png" alt="" /> {{ addFormInfo.projectName }}</div>
          <el-row class="descriptionList">
            <el-col :span="3">
              <span>负责部门：</span>
              <span>{{ addFormInfo.organizationName }}</span>
            </el-col>
            <el-col :span="12">
              <span>项目负责人：</span>
              <span>{{ addFormInfo.realName }}</span>
            </el-col>
          </el-row>
          <el-row class="descriptionList">
            <el-col :span="3">
              <span>项目编号：</span>
              <span>{{ addFormInfo.code }}</span>
            </el-col>
            <el-col :span="12">
              <span>客户名称：</span>
              <span>{{ addFormInfo.customerName }}</span>
            </el-col>
          </el-row>
          <el-row class="descriptionList">
            <el-col :span="24">
              <span>项目描述：</span>
              <span>{{ addFormInfo.description }}</span>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <!--修改进入——基本信息 -->
      <el-card v-show="activeName === 1 && $route.params.type === '1'" class="basicInfo">
        <div class="ProjectDetails">
          <div class="code"><img src="@/assets/project/table_code_icon.png" alt="" style="margin-right: 8px" />项目编号:{{ addFormInfo.code }}</div>
          <el-form ref="addFormRef" label-position="left" :model="addFormInfo" label-width="100px" :rules="rules" inline>
            <el-form-item label="项目名称:" prop="projectName">
              <el-input v-model="addFormInfo.projectName" size="small" placeholder="请输入项目名称,20字以内" maxlength="20"></el-input>
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
              <el-input v-model="addFormInfo.customerName" size="small" placeholder="请输入客户名称,20字以内" maxlength="20"></el-input>
            </el-form-item>
            <el-form-item label="负责部门:">
              <el-input v-model="addFormInfo.organizationName" size="small" placeholder="请输入客户名称,20字以内" maxlength="20" disabled></el-input>
            </el-form-item>
            <!-- <el-form-item label="项目类型:" prop="state">
            <el-radio v-model="addFormInfo.state" :label="1">研发</el-radio>
          </el-form-item> -->
            <el-form-item label="项目负责人:" prop="userId">
              <el-select v-model="addFormInfo.userId" placeholder="请选择项目负责人" size="small" @focus="getUserList">
                <el-option v-for="item in userList" :key="item.userId" :label="item.realName" :value="item.userId"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="项目描述:">
              <el-input v-model="addFormInfo.description" size="small" type="textarea" placeholder="请输入项目描述,500字以内" maxlength="500"></el-input>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
      <!-- 成员列表 -->
      <el-card v-show="activeName === 2" class="memberList">
        <div slot="header">
          <el-row :gutter="10" type="flex" justify="space-between" align="middle">
            <el-col :span="6">
              <strong class="header_title">成员列表</strong>
            </el-col>
            <el-col :span="5.5">
              <el-input v-model="usersDetailsInfo.name" style="width: 300px; margin-right: 30px" placeholder="请输入姓名" size="small" suffix-icon="el-icon-search" clearable></el-input>
              <el-button type="primary" plain size="small" @click="getProjectUsersDetails">查询</el-button>
              <el-button v-if="$route.params.type !== '0'" type="primary" size="small" @click="addUser">添加</el-button>
            </el-col>
          </el-row>
        </div>
        <div>
          <el-table :data="usersDetailsList" style="width: 100%" border :header-cell-style="{ background: '#eeeeef', fontSize: '14px', fontWeight: 400, color: '#0B1A44' }">
            <el-table-column prop="headurl" label="头像" width="width" align="center">
              <template v-slot="{ row }">
                <el-avatar shape="circle" :size="65" fit="cover" :src="row.headurl" @error="return true">
                  <img src="https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png" />
                </el-avatar>
              </template>
            </el-table-column>
            <el-table-column prop="realName" label="姓名" width="width" align="center"> </el-table-column>
            <el-table-column prop="organizationName" label="部门" width="width" align="center"> </el-table-column>
            <el-table-column prop="jobName" label="岗位" width="width" align="center"> </el-table-column>
            <el-table-column label="操作" width="width" align="center">
              <template v-slot="{ row }">
                <el-button type="text" size="small" :disabled="$route.params.type === '0'" @click="delUser(row)">移出</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination background style="text-align: right; margin-top: 15px" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 15, 20, 30]" :total="usersDetailsTotal" :page-size.sync="usersDetailsInfo.pageSize" :current-page.sync="usersDetailsInfo.pageNum" @size-change="getProjectUsersDetails" @current-change="getProjectUsersDetails" />
        </div>
      </el-card>
      <!-- 项目资料 -->
      <el-card v-show="activeName === 3" class="projectMaterial memberList">
        <div slot="header">
          <el-row :gutter="10" type="flex" justify="space-between" align="middle">
            <el-col :span="6">
              <strong class="header_title">项目资料</strong>
            </el-col>
            <el-col :span="5.5">
              <el-button v-if="$route.params.type !== '0'" type="primary" size="small" @click="uploadingFileDialog = true">上传附件</el-button>
            </el-col>
          </el-row>
        </div>
        <div>
          <ul class="fileList">
            <li v-for="item in filesDetailsList" :key="item.name">
              <div>
                <div class="fileImg">
                  <img src="@/assets/meeting/file.png" alt="" />
                  <span>{{ item.fileSize }}KB</span>
                </div>
                <div>
                  <el-tooltip class="item" effect="dark" :content="item.fileName" placement="top">
                    <span>{{ item.fileName }}</span>
                  </el-tooltip>
                  <i v-if="$route.params.type !== '0'" class="el-icon-edit" @click="rechristen(item)"></i>
                </div>
              </div>
              <div v-if="item.percentage && item.percentage !== 100">
                <el-progress :percentage="item.percentage"></el-progress>
              </div>
              <div>
                <span>{{ item.realName }}上传于{{ item.createTime | formatDate }}</span>
                <i v-if="$route.params.type !== '0'" class="el-icon-download" @click="downloadFile(item)"></i>
                <i v-if="$route.params.type !== '0'" class="el-icon-delete" @click="delFile(item)"></i>
              </div>
            </li>
          </ul>
        </div>
        <div>
          <!-- <el-table :data="filesDetailsList" style="width: 100%" border>
            <el-table-column label="序号" type="index" width="width" align="center"> </el-table-column>
            <el-table-column prop="fileName" label="文件名称" width="width" align="center"> </el-table-column>
            <el-table-column prop="fileSize" label="文件大小" width="width" align="center">
              <template v-slot="{ row }">
                <span>{{ row.fileSize | formattingFileSize_KB }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="realName" label="上传人" width="width" align="center"> </el-table-column>
            <el-table-column prop="createTime" label="上传时间" width="width" align="center"> </el-table-column>
            <el-table-column label="操作" width="width" align="center">
              <template v-slot="{ row }">
                <el-button type="primary" size="small" @click="downloadFile(row)">下载</el-button>
                <el-button type="warning" size="small" @click="rechristen(row)">重命名</el-button>
                <el-button type="danger" size="small" @click="delFile(row)">删除</el-button>
              </template> </el-table-column>image.png
          </el-table> -->
          <el-pagination background style="text-align: right; margin-top: 15px" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 15, 20, 30]" :total="filesDetailsTotal" :page-size.sync="filesDetailsInfo.pageSize" :current-page.sync="filesDetailsInfo.pageNum" @size-change="getProjectFilesDetails" @current-change="getProjectFilesDetails" />
        </div>
      </el-card>
      <!-- 版本记录 -->
      <el-card v-show="activeName === 4" class="memberList editionRecord">
        <div slot="header">
          <el-row :gutter="10" type="flex" justify="space-between" align="middle">
            <el-col :span="6">
              <strong class="header_title">版本记录</strong>
            </el-col>
            <el-col :span="5.5">
              <el-button v-if="$route.params.type !== '0'" type="primary" size="small" @click="addVersion">添加</el-button>
            </el-col>
          </el-row>
        </div>
        <div>
          <el-table :data="versionDetailsList" border style="width: 100%" :header-cell-style="{ background: '#eeeeef', fontSize: '14px', fontWeight: 400, color: '#0B1A44' }">
            <el-table-column prop="version" label="版本号" width="width" align="center"> </el-table-column>
            <el-table-column prop="versionStage" label="版本阶段" width="width" align="center">
              <template v-slot="{ row }">
                <span>{{ row.versionStage | projectStage }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="svnUrl" label="SVN地址" width="width" align="center"> </el-table-column>
            <el-table-column prop="description" label="修订内容" width="width" align="center"> </el-table-column>
            <el-table-column prop="createTime" label="更新时间" width="width" align="center"> </el-table-column>
            <el-table-column label="操作" width="width" align="center">
              <template v-slot="{ row }">
                <el-tooltip class="item" effect="light" content="详情" placement="top">
                  <img style="cursor: pointer" src="@/assets/repository/details_icon.png" alt="" @click="showVersionDetails(row)" />
                </el-tooltip>
                <el-tooltip class="item" effect="light" content="修改" placement="top">
                  <img v-if="$route.params.type !== '0'" style="cursor: pointer" src="@/assets/meeting/edit.png" alt="" @click="editVersion(row)" />
                </el-tooltip>
                <el-tooltip class="item" effect="light" content="删除" placement="top">
                  <img v-if="$route.params.type !== '0'" style="cursor: pointer" src="@/assets/meeting/del.png" alt="" @click="delVersion(row)" />
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination background style="text-align: right; margin-top: 15px" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 15, 20, 30]" :total="versionDetailsTotal" :page-size.sync="versionDetailsInfo.pageSize" :current-page.sync="versionDetailsInfo.pageNum" @size-change="getProjectVersionDetails" @current-change="getProjectVersionDetails" />
        </div>
      </el-card>

      <!-- 施工记录 -->
      <el-card v-show="activeName === 5" class="memberList editionRecord">
        <div slot="header">
          <el-row :gutter="10" type="flex" justify="space-between" align="middle">
            <el-col :span="6">
              <strong class="header_title">施工记录</strong>
            </el-col>
          </el-row>
        </div>
        <div>
          <el-table :data="buildDetailsList" border style="width: 100%" :header-cell-style="{ background: '#eeeeef', fontSize: '14px', fontWeight: 400, color: '#0B1A44' }">
            <el-table-column prop="address" label="施工地址" width="width" align="center"> </el-table-column>
            <el-table-column prop="time" label="施工日期" width="width" align="center"> </el-table-column>
            <el-table-column prop="realName" label="施工人员" width="width" align="center"> </el-table-column>
            <el-table-column prop="customerUserName" label="客户方联系人" width="width" align="center"> </el-table-column>
            <el-table-column prop="customerUserPhone" label="客户方联系方式" width="width" align="center"> </el-table-column>
            <el-table-column prop="prop" label="施工阶段" width="width" align="center">
              <template v-slot="{ row }">
                <span>{{ row.buildStage | buildStage }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="ipAddress" label="ip地址" width="width" align="center"> </el-table-column>
            <el-table-column prop="description" label="施工内容" width="width" align="center"> </el-table-column>
            <el-table-column label="操作" width="width" align="center">
              <template v-slot="{ row }">
                <el-tooltip class="item" effect="light" content="详情" placement="top">
                  <img style="cursor: pointer" src="@/assets/repository/details_icon.png" alt="" @click="showProjectBuild(row)" />
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination background style="text-align: right; margin-top: 15px" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 15, 20, 30]" :total="buildDetailsTotal" :page-size.sync="buildDetailsInfo.pageSize" :current-page.sync="buildDetailsInfo.pageNum" @size-change="getProjectBuildDetails" @current-change="getProjectBuildDetails" />
        </div>
      </el-card>
    </section>

    <!-- 添加人员dialog -->
    <el-dialog v-if="addUserDialog" title="选择人员" :visible.sync="addUserDialog" width="672px" class="addUserDialog">
      <el-row style="margin-bottom: 12px">
        <el-col :span="24">
          <el-form ref="addPersonForm" :model="addPersonFormInfo" label-width="50px" inline>
            <el-input v-model="addPersonFormInfo.realName" class="realNameSearch" style="width: 200px" size="small" clearable placeholder="请输入姓名" maxlength="20" @keyup.enter.native="getPersonList">
              <template v-slot:suffix>
                <i class="el-icon-search" @click="getPersonList"></i>
              </template>
            </el-input>
            <el-select ref="selecteltree" v-model="addPersonFormInfo.organizationName" class="select" size="small" clearable placeholder="请选择部门" @clear="onSelectClear" @focus="getMenuTreeOfParent">
              <el-option v-for="item in menu" :key="item.id" :label="item.organizationName" :value="item.id" style="display: none" />
              <el-tree style="padding-left: 15px" :data="menu" node-key="id" empty-text="暂无菜单" highlight-current :expand-on-click-node="false" :props="defaultProps" current-node-key="id" default-expand-all @node-click="handleNodeClick" />
            </el-select>
            <!-- <el-button type="success" size="small" @click="getPersonList">查询</el-button> -->
          </el-form>
        </el-col>
      </el-row>
      <el-table :data="tableData" style="width: 100%" border :row-key="getRowKeys" :header-cell-style="{ background: '#eeeeef', fontFamily: 'Microsoft YaHei-Bold, Microsoft YaHei', fontSize: 'bold', color: '#0B1A44' }" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="width" :reserve-selection="true" align="center"> </el-table-column>
        <el-table-column prop="realName" label="姓名" width="width" align="center"> </el-table-column>
        <el-table-column prop="organizationName" label="部门" width="width" align="center"> </el-table-column>
        <el-table-column prop="jobName" label="岗位" width="width" align="center"> </el-table-column>
      </el-table>
      <div slot="footer">
        <el-row type="flex" justify="space-between">
          <el-pagination layout="prev, pager, next" background :page-sizes="[5, 10, 15, 20]" :total="total" :page-size.sync="addPersonFormInfo.pageSize" :current-page.sync="addPersonFormInfo.pageNum" @size-change="getPersonList" @current-change="getPersonList" />
          <div>
            <el-button size="small" plain @click="addUserDialog = false">取 消</el-button>
            <el-button type="primary" size="small" @click="addPerson">确 定</el-button>
          </div>
        </el-row>
      </div>
    </el-dialog>
    <!-- 上传附件 -->
    <el-dialog title="上传附件" :visible.sync="uploadingFileDialog" width="450px" :close-on-click-modal="false">
      <div style="width: 360px; margin: 0 auto">
        <el-upload class="upload-demo" drag :action="action" :headers="header" :file-list="fileList" multiple :before-upload="beforeUpload" :on-success="uploadSuccess" :on-remove="uploadRemove">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div slot="tip" class="el-upload__tip" style="color: red; line-height: 15px">只能上传.doc .docx .xls .xlsx .pdf .jpg .png .zip格式的附件，且大小不超过50MB</div>
        </el-upload>
      </div>
      <div slot="footer">
        <el-button @click="uploadingFileDialog = false">取 消</el-button>
        <el-button type="primary" @click="uploadingFile">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 文件重命名dialog -->
    <el-dialog title="文件重命名" :visible.sync="rechristenDialog" width="400px">
      <div style="width: 300px; margin: 0 auto">
        <el-input v-model="fileName" placeholder="请输入文件名称" clearable></el-input>
      </div>
      <div slot="footer">
        <el-button @click="rechristenDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirmRechristen">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 添加和修改版本记录dialog -->
    <el-dialog :title="versionDialogTitle" :visible.sync="versionDetailsDialog" class="versionDialog" width="622px" :close-on-click-modal="false" @close="versionDetailsDialogClose">
      <div>
        <el-form ref="versionDetailsForm" class="versionDetailsForm" :model="versionDetailsFormInfo" :rules="versionDetailsRules" label-width="90px">
          <el-form-item label="版本号:" prop="version" class="versionCode">
            <el-input v-model="versionDetailsFormInfo.version" size="small" placeholder="请输入版本号" maxlength="20" oninput="value = value.replace(/[\u4E00-\u9FA5]/g,'')"></el-input>
          </el-form-item>
          <el-form-item label="版本阶段:" prop="versionStage" class="versionStage">
            <el-select v-model="versionDetailsFormInfo.versionStage" placeholder="请选择版本阶段" size="small">
              <el-option v-for="item in stageOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="SVN地址:" prop="svnUrl" class="svnUrl">
            <el-input v-model="versionDetailsFormInfo.svnUrl" size="small" placeholder="请输入SVN地址"></el-input>
          </el-form-item>
          <el-form-item label="修订内容:" class="description">
            <el-input v-model="versionDetailsFormInfo.description" placeholder="请输入修订内容,200字以内" maxlength="200" type="textarea"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-row type="flex" justify="center">
          <el-button @click="versionDetailsDialog = false">取 消</el-button>
          <el-button type="primary" class="versionDialog_footer_subButton" @click="confirmAddVersion">提 交</el-button>
        </el-row>
      </div>
    </el-dialog>
    <!-- 版本详情 -->
    <el-dialog title="版本详情" :visible.sync="VersionDetailsDialog" width="591px" :close-on-click-modal="false" class="showProjectBuildDialog">
      <div>
        <el-descriptions :column="1">
          <el-descriptions-item
            label="版本号"
          ><span style="font-weight: bold;">{{ VersionDetails.version }}</span></el-descriptions-item>
          <el-descriptions-item
            label="版本阶段"
          ><span>{{ VersionDetails.versionStage }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label="SVN地址"
          ><span>{{ VersionDetails.svnUrl }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label="修订内容"
          ><span>{{ VersionDetails.description }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label="创建时间"
          ><span>{{ VersionDetails.createTime }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div slot="footer">
        <el-row :gutter="10" type="flex" justify="center">
          <el-col :span="6">
            <el-button type="primary" @click="VersionDetailsDialog = false">关 闭</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
    <!-- 添加和修改施工记录dialog -->
    <el-dialog :title="projectBuildTitle" :visible.sync="addBuildRecordDialog" width="672px" :close-on-click-modal="false" @close="ProjectBuildClose">
      <div>
        <el-form ref="buildDetailsForm" class="buildDetailsForm" :model="buildDetailsFormInfo" label-width="120px" inline :rules="buildDetailsFormRules">
          <el-form-item label="施工地址:" prop="address">
            <el-input v-model="buildDetailsFormInfo.address" size="small" placeholder="请输入施工地址" maxlength="50" clearable></el-input>
          </el-form-item>
          <el-form-item label="施工阶段:" prop="buildStage">
            <el-select v-model="buildDetailsFormInfo.buildStage" size="small" placeholder="请选择施工阶段" clearable>
              <el-option v-for="item in stageOptions_after" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="施工人员:" prop="userId">
            <el-select v-model="buildDetailsFormInfo.userId" placeholder="请选择施工人员" size="small" clearable @focus="getUserList">
              <el-option v-for="item in userList" :key="item.userId" :label="item.realName" :value="item.userId"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="客户方联系人:">
            <el-input v-model="buildDetailsFormInfo.customerUserName" size="small" placeholder="请输入客户方联系人" maxlength="50" clearable></el-input>
          </el-form-item>
          <el-form-item label="客户方联系方式:">
            <el-input v-model="buildDetailsFormInfo.customerUserPhone" size="small" placeholder="请输入客户方联系方式" maxlength="50" oninput="value = value.replace(/[\u4E00-\u9FA5]/g,'')" clearable></el-input>
          </el-form-item>
          <el-form-item label="施工日期:">
            <el-date-picker v-model="buildDetailsFormInfo.time" type="datetime" size="small" placeholder="选择施工日期" clearable> </el-date-picker>
          </el-form-item>
          <el-form-item label="ip地址:">
            <el-input v-model="buildDetailsFormInfo.ipAddress" size="small" placeholder="请输入ip地址" maxlength="50" oninput="value = value.replace(/[\u4E00-\u9FA5]/g,'')" clearable></el-input>
          </el-form-item>
          <el-form-item label="施工内容:">
            <el-input v-model="buildDetailsFormInfo.description" size="small" type="textarea" placeholder="请输入施工内容" maxlength="200" clearable></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="addBuildRecordDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirmAddProjectBuild">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 施工记录详情 -->
    <el-dialog title="施工记录详情" :visible.sync="showProjectBuildDialog" width="622px" class="showProjectBuildDialog">
      <div v-if="showProjectBuildDialog">
        <el-descriptions :column="1">
          <el-descriptions-item label="施工地址"><span>{{ ProjectBuildDetailsInfo.address }}</span></el-descriptions-item>
          <el-descriptions-item label="施工日期">  <span>{{ ProjectBuildDetailsInfo.time }}</span> </el-descriptions-item>
          <el-descriptions-item label="施工人员"> <span>{{ ProjectBuildDetailsInfo.realName }}</span> </el-descriptions-item>
          <el-descriptions-item label="客户方联系人"> <span>{{ ProjectBuildDetailsInfo.description }}</span>   </el-descriptions-item>
          <el-descriptions-item label="客户方联系方式"> <span>{{ ProjectBuildDetailsInfo.createTime }}</span> </el-descriptions-item>
          <el-descriptions-item label="施工阶段"> <span>{{ ProjectBuildDetailsInfo.buildStage | buildStage }}</span> </el-descriptions-item>
          <el-descriptions-item label="IP地址"> <span>{{ ProjectBuildDetailsInfo.ipAddress }}</span> </el-descriptions-item>
          <el-descriptions-item label="施工内容"> <span>{{ ProjectBuildDetailsInfo.description }}</span> </el-descriptions-item>
        </el-descriptions>
      </div>
      <div slot="footer">
        <el-row :gutter="10" type="flex" justify="center">
          <el-col :span="6">
            <el-button type="primary" @click="showProjectBuildDialog = false">关 闭</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { projectDetails, projectUpdateProject, projectUsersDetails, projectSaveUser, projectRemoveUser, projectFilesDetails, projectSaveFile, projectVersionDetails, projectSaveVersion, projectUpdateVersion, projectDeleteVersion, projectBuildDetails, projectBuildSaveBuild, projectBuildUpdateBuild, projectBuildDeleteBuild } from '@/api/project'
import { getList } from '@/api/systemUser'
import { getOrganizationTree } from '@/api/organization'
import { meetingUpdateFileName, meetingDeleteFile, meetingRenameFile } from '@/api/meeting'
import { formatDate } from '@/filters'
import { mapGetters } from 'vuex'
export default {
  name: 'ProjectDetails',
  data() {
    return {
      activeName: 1,
      addFormInfo: {
        projectName: null, // 项目名称
        customerName: null, // 客户名称
        userId: null, // 负责人id
        state: 1, // 项目状态 1 研发 2 施工
        description: null, // 描述
        relationId: null, // 关联研发项目id
        type: 1 // 类型 1 项目管理 2 售后记录
      },
      rules: {
        projectName: [
          {
            required: true,
            tigger: 'blur',
            message: '项目名称不能为空'
          }
        ],
        customerName: [
          {
            required: true,
            tigger: 'blur',
            message: '客户名称不能为空'
          }
        ],
        userId: [
          {
            required: true,
            tigger: 'change',
            message: '负责人不能为空',
            type: 'string' || 'number'
          }
        ],
        state: [
          {
            required: true,
            tigger: 'change',
            message: '项目类型不能为空',
            type: 'number'
          }
        ]
      },
      userList: [],
      /** 成员列表部分 */
      usersDetailsInfo: {
        id: this.$route.params.id,
        type: 1,
        name: null,
        pageNum: 1,
        pageSize: 10
      },
      usersDetailsList: [],
      //  成员列表
      addUserDialog: false,
      addPersonFormInfo: {
        realName: null,
        organizationName: null,
        organizationId: null,
        pageNum: 1,
        pageSize: 5
      },
      usersDetailsTotal: null,
      menu: [],
      defaultProps: {
        label: 'organizationName',
        children: 'children'
      },
      selectData: [],
      tableData: [],
      total: null,
      type: 1,
      /** 项目资料 */
      filesDetailsInfo: {
        id: this.$route.params.id,
        belongType: 4,
        pageNum: 1,
        pageSize: 10
      },
      filesDetailsList: [],
      filesDetailsTotal: null,
      uploadingFileDialog: false,
      fileList: [],
      action: window.config.VUE_APP_BASE_API + '/system/upload/file',
      header: {
        Authorization: null
      },
      rechristenDialog: false, // 文件重命名
      fileName: null,
      fileId: null,
      /** 版本记录 */
      versionDetailsInfo: {
        id: this.$route.params.id,
        type: 1,
        pageNum: 1,
        pageSize: 10
      },
      versionDetailsList: [],
      versionDetailsTotal: null,
      versionDetailsDialog: false,

      versionDetailsFormInfo: {
        projectId: this.$route.params.id,
        svnUrl: null, // svn地址
        description: null, // 修订内容
        version: null, // 版本号
        versionStage: null // 版本阶段 1 未立项 2 调研中 3 研发中 4 交付中 5 已交付
      },
      stageOptions: [
        {
          label: '未立项',
          value: 1
        },
        {
          label: '调研中',
          value: 2
        },
        {
          label: '研发中',
          value: 3
        },
        {
          label: '已归档',
          value: 4
        },
        {
          label: '已交付',
          value: 5
        }
      ],
      versionDetailsRules: {
        version: [
          {
            required: true,
            tigger: 'blur',
            message: '请输入版本号'
          }
        ],
        versionStage: [
          {
            required: true,
            tigger: 'change',
            message: '请选择版本阶段',
            type: 'number'
          }
        ],
        svnUrl: [
          {
            required: true,
            tigger: 'blur',
            message: '请输入SVN地址'
          }
        ]
      },
      VersionDetailsDialog: false, // 版本详情dialog
      VersionDetails: {},
      /** 施工记录*/
      buildDetailsInfo: {
        id: this.$route.params.id,
        type: 1,
        pageNum: 1,
        pageSize: 10
      },
      buildDetailsList: [],
      buildDetailsTotal: null,
      addBuildRecordDialog: false,
      buildDetailsFormInfo: {
        projectId: this.$route.params.id, // 售后记录id
        address: null, // 施工地址
        time: null, // 施工日期
        userId: null, // 施工人员
        customerUserName: null, // 客户方联系人
        customerUserPhone: null, // 客户方联系方式
        buildStage: null, // 施工阶段 1 施工准备中 2 施工中 3 竣工
        ipAddress: null, // ip地址
        description: null // 施工内容
      },
      stageOptions_after: [
        {
          label: '施工准备中',
          value: 1
        },
        {
          label: '施工中',
          value: 2
        },
        {
          label: '竣工',
          value: 3
        }
      ],
      buildDetailsFormRules: {
        address: [
          {
            required: true,
            tigger: 'blur',
            message: '请输入施工地址'
          }
        ],
        buildStage: [
          {
            required: true,
            tigger: 'change',
            message: '请选择施工阶段',
            type: 'number'
          }
        ],
        userId: [
          {
            required: true,
            tigger: 'change',
            message: '请选择施工人员'
          }
        ]
      },
      showProjectBuildDialog: false,
      ProjectBuildDetailsInfo: {},
      oldPath: null
    }
  },

  computed: {
    ...mapGetters(['organizationId', 'token', 'keyList']),
    versionDialogTitle() {
      return this.versionDetailsFormInfo.versionId ? '修改版本记录' : '添加版本记录'
    },
    projectBuildTitle() {
      return this.buildDetailsFormInfo.buildId ? '修改施工记录' : '添加施工记录'
    }
  },
  created() {
    //  获取详情信息
    this.getProjectDetails()
    // 获取人员列表
    this.getProjectUsersDetails()
    // 获取项目资料
    this.getProjectFilesDetails()
    // 获取版本资料
    this.getProjectVersionDetails()
    // 获取施工记录
    this.getProjectBuildDetails()
  },
  methods: {
    //  获取详情信息
    async getProjectDetails() {
      const { data } = await projectDetails({ id: this.$route.params.id })
      this.addFormInfo = { ...data }
      this.addFormInfo.state = 1
      this.getUserList()
    },
    // 获取当前部门下的用户
    async getUserList() {
      const { data } = await getList({ pageNum: 1, pageSize: 200, organizationId: this.organizationId })
      this.userList = data.list
    },
    // 修改项目
    async edit() {
      this.$refs['addFormRef'].validate(async (val) => {
        if (val) {
          const info = {
            id: this.addFormInfo.id,
            name: this.addFormInfo.projectName,
            customerName: this.addFormInfo.customerName,
            userId: this.addFormInfo.userId,
            description: this.addFormInfo.description
          }
          await projectUpdateProject(info)
          this.$message.success('修改项目成功')
          this.getProjectDetails()
        }
      })
    },
    /** 成员列表部分 */

    // 获取人员列表
    async getProjectUsersDetails() {
      const { data } = await projectUsersDetails(this.usersDetailsInfo)
      this.usersDetailsList = data.list
      this.usersDetailsTotal = data.total
      console.log(data)
    },
    addUser() {
      this.getPersonList()
      this.addUserDialog = true
    },
    // 获取所有用户列表
    async getPersonList() {
      const { data } = await getList(this.addPersonFormInfo)
      this.tableData = data.list
      this.total = data.total
    },
    onSelectClear() {
      this.addPersonFormInfo.organizationId = null
      this.getPersonList()
    },
    getMenuTreeOfParent() {
      getOrganizationTree().then((res) => {
        this.menu = res.data
      })
    },
    handleNodeClick(node) {
      this.addPersonFormInfo.organizationId = node.id
      this.addPersonFormInfo.organizationName = node.organizationName
      this.$refs['selecteltree'].blur()
      this.getPersonList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectData = val
    },
    getRowKeys(row) {
      return row.userId
    },
    // 添加人员
    async addPerson() {
      const list = []
      this.selectData.forEach((item) => {
        list.push({
          userId: item.userId,
          pId: this.addFormInfo.id,
          type: this.type
        })
      })
      await projectSaveUser(list)
      this.$message.success('添加人员成功')
      this.addUserDialog = false
      this.getProjectUsersDetails()
    },
    // 删除人员
    delUser(row) {
      console.log(row)
      this.$confirm('确认要删除该人员吗, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const queryInfo = {
            userId: row.userId,
            pId: this.$route.params.id,
            type: this.type
          }
          await projectRemoveUser(queryInfo)
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getProjectUsersDetails()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    /** 项目资料部分 */

    async getProjectFilesDetails() {
      const { data } = await projectFilesDetails(this.filesDetailsInfo)
      this.filesDetailsList = data.list
      this.filesDetailsTotal = data.total
      console.log(data.list)
    },
    beforeUpload(file) {
      this.header.Authorization = `Bearer ${this.token}`
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isDOC = file.type === 'application/msword'
      const isDOCX = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      const isXLS = file.type === 'application/vnd.ms-excel'
      const isXLSX = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      const isPDF = file.type === 'application/pdf'
      const isZIP = file.type === 'application/x-zip-compressed'
      const isLt2M = file.size / 1024 / 1024 < 50
      if (!isLt2M) {
        this.$message.error('上传附件大小不能超过 50MB!')
      }
      if (!isJPG && !isPNG && !isDOC && !isDOCX && !isXLS && !isXLSX && !isPDF && !isZIP) {
        this.$message.warning('只能上传.doc .docx .xls .xlsx .pdf .jpg .png .zip 格式的附件')
      }
      return (isJPG || isPNG || isDOC || isDOCX || isXLS || isXLSX || isPDF || isZIP) && isLt2M
    },
    uploadSuccess(response, file, fileList) {
      this.fileList = fileList
    },
    uploadRemove(file, fileList) {
      this.fileList = fileList
    },
    // 确定上传
    async uploadingFile() {
      const list = []
      this.fileList.forEach((item) => {
        list.push({
          fileName: item.name,
          fileSize: item.size / 1024,
          fileUrl: item.response.data[0],
          meetingId: this.addFormInfo.id,
          type: this.type
        })
      })
      await projectSaveFile(list)
      this.$message.success('上传附件成功')
      this.uploadingFileDialog = false
      this.fileList = []
      this.getProjectFilesDetails()
    },
    // 下载文件
    downloadFile(row) {
      console.log(row)
      window.open(row.fileUrl, '_blank')
    },
    // 文件重命名
    rechristen(row) {
      console.log(row)
      this.fileName = row.fileName
      this.oldPath = row.fileUrl.split('workmanage/')[1]
      this.fileId = row.fileId
      this.rechristenDialog = true
    },
    // 确定重命名
    async confirmRechristen() {
      const { data } = await meetingRenameFile({ oldPath: this.oldPath, newPath: this.fileName })
      await meetingUpdateFileName({ fileId: this.fileId, fileName: this.fileName, fileUrl: data })
      this.$message.success('文件重命名成功')
      this.rechristenDialog = false
      this.getProjectFilesDetails()
    },
    // 删除附件
    delFile(row) {
      this.$confirm('确认要删除该文件吗, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await meetingDeleteFile({ fileId: row.fileId })
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getProjectFilesDetails()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    /** 版本记录 */
    async getProjectVersionDetails() {
      const { data } = await projectVersionDetails(this.versionDetailsInfo)
      this.versionDetailsList = data.list
      this.versionDetailsTotal = data.total
      console.log(data)
    },
    addVersion() {
      this.versionDetailsDialog = true
    },
    editVersion(row) {
      console.log(row)
      this.versionDetailsFormInfo = { ...row }
      this.versionDetailsDialog = true
    },
    delVersion(row) {
      this.$confirm('确认要删除该记录吗, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await projectDeleteVersion({ versionId: row.versionId })
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getProjectVersionDetails()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    confirmAddVersion() {
      this.$refs['versionDetailsForm'].validate(async (val) => {
        if (val) {
          if (this.versionDetailsFormInfo.versionId) {
            await projectUpdateVersion(this.versionDetailsFormInfo)
            this.$message.success('修改版本记录成功')
          } else {
            await projectSaveVersion(this.versionDetailsFormInfo)
            this.$message.success('添加版本记录成功')
          }
          this.versionDetailsDialog = false
        }
      })
    },
    // 上传版本记录对话框关闭事件
    versionDetailsDialogClose() {
      this.versionDetailsFormInfo = {
        projectId: this.$route.params.id,
        svnUrl: null, // svn地址
        description: null, // 修订内容
        version: null, // 版本号
        versionStage: null // 版本阶段 1 未立项 2 调研中 3 研发中 4 交付中 5 已交付
      }
      this.$refs['versionDetailsForm'].resetFields()
      this.getProjectVersionDetails()
    },
    // 查看版本记录详情
    showVersionDetails(row) {
      console.log(row)
      this.VersionDetails = { ...row }
      this.VersionDetailsDialog = true
    },
    /** 施工记录*/
    async getProjectBuildDetails() {
      const { data } = await projectBuildDetails(this.buildDetailsInfo)
      this.buildDetailsList = data.list
      this.buildDetailsTotal = data.total
    },
    // 查看记录详情
    showProjectBuild(row) {
      console.log(row)
      this.ProjectBuildDetailsInfo = { ...row }
      this.showProjectBuildDialog = true
    },
    // 删除记录
    delProjectBuild(row) {
      this.$confirm('确认要删除该记录吗, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await projectBuildDeleteBuild({ buildId: row.buildId })
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getProjectBuildDetails()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 修改记录修改
    editProjectBuildDetails(row) {
      console.log(row)
      this.buildDetailsFormInfo = { ...row }
      this.addBuildRecordDialog = true
    },
    // 确定添加或修改施工记录
    confirmAddProjectBuild() {
      this.$refs['buildDetailsForm'].validate(async (val) => {
        if (val) {
          if (this.buildDetailsFormInfo.time) {
            this.buildDetailsInfo.time = formatDate(this.buildDetailsInfo.time, 'yyyy-MM-dd')
          }
          if (this.buildDetailsFormInfo.buildId) {
            await projectBuildUpdateBuild(this.buildDetailsFormInfo)
            this.$message.success('修改施工记录成功')
          } else {
            await projectBuildSaveBuild(this.buildDetailsFormInfo)
            this.$message.success('添加施工记录成功')
          }
          this.addBuildRecordDialog = false
        }
      })
    },
    // 添加和修改施工记录dialog关闭事件
    ProjectBuildClose() {
      this.buildDetailsFormInfo = {
        projectId: this.$route.params.id, // 售后记录id
        address: null, // 施工地址
        time: null, // 施工日期
        userId: null, // 施工人员
        customerUserName: null, // 客户方联系人
        customerUserPhone: null, // 客户方联系方式
        buildStage: null, // 施工阶段 1 施工准备中 2 施工中 3 竣工
        ipAddress: null, // ip地址
        description: null // 施工内容
      }
      this.$refs['buildDetailsForm'].resetFields()
      this.getProjectBuildDetails()
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  position: relative;
  padding: 0;
  background-color: #e8eaed;
  min-height: 100%;
  padding-top: 58px;
  padding-bottom: 30px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;

  .top {
    position: absolute;
    top: 0px;
    width: 100%;
    background-color: #e8eaed;
    padding: 24px 39px 16px 0;
    z-index: 999;
    .el-col {
      i {
        font-size: 14px;
        color: #657081;
      }
      span {
        &:first-of-type {
          margin: 0 5px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #657081;
        }
        &:last-of-type {
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
      }
    }
  }
  .project_details_box {
    width: 1754px;
    padding: 24px 16px;
    // height: 2072px;
    background: #f5f5f5;
    border-radius: 8px 8px 0px 0px;
    .top_nav {
      .tabs {
        display: flex;
        span {
          width: 74px;
          margin-left: 93px;
          padding-bottom: 8px;
          font-size: 16px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
          cursor: pointer;
          text-align: center;
        }
        .checkedTab {
          width: 74px;
          color: #3464e0;
          border-bottom: 2px solid #3464e0;
          // padding-bottom: 8px;
          transition: all 0.1s ease;
        }
      }
      .saveButton {
        width: 114px;
        height: 38px;
        margin-bottom: 10px;
        line-height: 38px;
        background: #3464e0;
        border-radius: 4px 4px 4px 4px;
        font-size: 16px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        font-weight: bold;
        color: #ffffff;
        text-align: center;
        cursor: pointer;
        &:hover {
          background: #355fce;
        }
      }
    }
  }
}
// 基本信息
.basicInfo {
  padding-left: 82px;
  .ProjectDetails {
    .code {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #0b1a44;
    }
    ::v-deep {
      .el-form-item {
        margin-right: 65px;
      }
      .el-input {
        width: 445px;
        height: 36px;
        .el-input__inner {
          height: 36px;
          border: 1px solid #d8dbe1;
          border-radius: 4px 4px 4px 4px;
        }
      }
      .el-textarea {
        width: 1231px;
        .el-textarea__inner {
          border-radius: 4px 4px 4px 4px;
          height: 87px;
          border: 1px solid #d8dbe1;
        }
      }
    }
  }
}
// 基本信息——详情
.basicInfo_details {
  padding-left: 70px;

  .name {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-family: Microsoft YaHei-Bold, Microsoft YaHei;
    font-weight: bold;
    color: #0b1a44;
    img {
      margin-right: 5px;
    }
  }
  .descriptionList {
    padding-left: 27px;
    margin-top: 20px;
    .el-col {
      span {
        &:first-of-type {
          font-size: 14px;
          font-weight: 400;
          color: #868b9f;
        }
        &:last-of-type {
          font-size: 14px;
          font-weight: 400;
          color: #0b1a44;
        }
      }
    }
  }
}
// 成员列表
.memberList {
  padding-left: 82px;
  .header_title {
    position: relative;
    padding-left: 8px;
    font-size: 15px;
    font-family: Microsoft YaHei-Bold, Microsoft YaHei;
    font-weight: bold;
    color: #0b1a44;

    &::before {
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      content: '';
      width: 4px;
      height: 14px;
      background: #3464e0;
      border-radius: 2px 2px 2px 2px;
    }
  }
  ::v-deep {
    .el-card__header {
      border: none;
      // padding-top: 24px;
      padding-bottom: 0;
    }
    .el-table td.el-table__cell div {
      font-size: 14px;
      font-weight: 400;
      color: #0b1a44;
    }
    .el-button--text {
      font-size: 14px;
      font-weight: 400;
      color: #eb6557;
    }
  }
}
// 项目资料
.projectMaterial {
  .fileList {
    display: flex;
    flex-wrap: wrap;
    li {
      margin-right: 14px;
      margin-bottom: 14px;
      padding: 14px 10px 20px 10px;
      width: 268px;
      height: 124px;
      background: #f9f9f9;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #eeeeef;
      box-sizing: border-box;
      width: 19.3%;
      &:nth-of-type(5n) {
        margin-right: 0;
      }
      & > div {
        &:first-of-type {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          .fileImg {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            img {
              width: 46px;
              height: 38px;
            }
            span {
              font-size: 12px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #868b9f;
              line-height: initial;
            }
          }
          & > div {
            margin-left: 3px;
            line-height: initial;
            .el-textarea {
              width: initial;
              .el-textarea__inner {
                width: 100%;
              }
            }

            &:last-of-type {
              span {
                display: inline-block;
                max-width: 210px;
                font-size: 14px;
                font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                font-weight: 400;
                color: #0b1a44;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
              i {
                margin-left: 5px;
                color: #ff7e26;
                font-size: 18px;
                cursor: pointer;
              }
            }
          }
        }
        &:last-of-type {
          margin-top: 18px;
          font-size: 12px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #a3a8bb;
          line-height: initial;
          i {
            font-size: 18px;
            float: right;
            margin-left: 15px;
            cursor: pointer;
          }
          .el-icon-download:hover {
            color: #3464e0;
          }
          .el-icon-delete:hover {
            color: #eb6557;
          }
        }
      }
    }
  }
}
// 版本记录
.editionRecord {
  ::v-deep {
    // 去掉单元格边框
    .el-table td.el-table__cell,
    .el-table th.el-table__cell.is-leaf {
      border: none;
    }
    // 每行鼠标经过得样式
    .el-table__body tr:hover > td {
      background-color: #f5f5f5;
    }
    .el-table__body tr.current-row > td {
      background-color: #f5f5f5;
    }
  }
}
.versionDetailsForm {
  ::v-deep {
    .el-input {
      width: 300px;
    }
    .el-textarea {
      width: 300px;
    }
  }
}
.buildDetailsForm {
  ::v-deep {
    .el-input {
      width: 300px;
    }
    .el-textarea {
      width: 300px;
    }
  }
}

::v-deep {
  .el-card {
    box-shadow: none;
    border: none;
    border-radius: 8px;
  }
  .addUserDialog,
  .versionDialog,
  .showProjectBuildDialog {
    .el-dialog__header {
      border-bottom: 1px solid #eeeeef;
      .el-dialog__title {
        font-size: 18px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        font-weight: bold;
        color: #0b1a44;
      }
      .el-dialog__headerbtn {
        .el-icon-close {
          font-size: 20px;
          font-weight: bold;
        }
      }
    }
    .el-dialog__body {
      padding: 30px 60px;
      .el-checkbox__input.is-checked .el-checkbox__inner,
      .el-checkbox__input.is-indeterminate .el-checkbox__inner {
        background-color: #3464e0;
        border-color: #3464e0;
      }
      .el-checkbox__inner:hover {
        border-color: #3464e0;
      }
      .realNameSearch {
        width: 244px !important;
        .el-input__inner {
          border-color: #d8dbe1 !important;
          border-radius: 4px !important;
        }
        margin-right: 24px;
        .el-input__suffix {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          margin-right: 5px;

          .el-icon-search {
            font-size: 16px;
            cursor: pointer;
          }
        }
      }
      .select {
        width: 244px !important;
        .el-input__inner {
          border-color: #d8dbe1 !important;
          border-radius: 4px !important;
        }
      }
    }
    .el-dialog__footer {
      padding-left: 60px;
      padding-right: 60px;
    }
  }
  .versionDialog {
    .el-form-item__label {
      padding-right: 16px !important;
    }
    .versionCode,
    .svnUrl,
    .description {
      .el-input {
        width: 430px;
        height: 40px;
        .el-input__inner {
          width: 100%;
          height: 100%;
          border: 1px solid #d8dbe1;
        }
      }
    }
    .versionStage {
      .el-input {
        width: 192px;
        height: 40px;
        .el-input__inner {
          height: 100%;
          width: 100%;
          background: #f5f5f5;
        }
      }
      .el-select .el-input.is-focus .el-input__inner {
        border-color: #e9ebef !important;
      }
    }
    .description {
      .el-textarea {
        width: 431px;
        height: 194px;
        .el-textarea__inner {
          width: 100%;
          height: 100%;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #d8dbe1;
        }
      }
    }
    .el-dialog__footer {
      .versionDialog_footer_subButton {
        background: #3464e0;
      }
      .el-button {
        width: 114px;
        height: 38px;
        border-radius: 4px 4px 4px 4px;
      }
    }
  }
  .showProjectBuildDialog {
    .el-dialog__body {
      padding: 30px 57px;
      .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
        padding-bottom: 28px;
      }
      .el-descriptions-item__content {
        font-size: 14px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;

        color: #0b1a44;
      }
      .el-descriptions-item__label {
        padding-right: 16px;
        font-size: 14px;
        font-weight: 400;
        color: #868b9f;
      }
    }
    .el-dialog__footer {
      padding-left: 57px;
      padding-right: 57px;
    }
  }
}
</style>
