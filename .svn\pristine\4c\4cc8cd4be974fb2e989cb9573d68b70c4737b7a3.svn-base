// 虚拟仿真
import request from '@/utils/requestLibrary'

/** 虚拟仿真列表 */
export function emulationList(params) {
  return request({
    url: '/emulation/list',
    method: 'GET',
    params
  })
}
/** 添加虚拟仿真 */
export function emulationAdd(data) {
  return request({
    url: '/emulation/add',
    method: 'POST',
    data
  })
}
/** 修改虚拟仿真 */
export function emulationUpdate(data) {
  return request({
    url: '/emulation/update',
    method: 'POST',
    data
  })
}
/** 删除虚拟仿真 */
export function emulationRemove(params) {
  return request({
    url: '/emulation/remove',
    method: 'DELETE',
    params
  })
}
/** 虚拟仿真详情 */
export function emulationDetail(params) {
  return request({
    url: '/emulation/detail',
    method: 'GET',
    params
  })
}
/** 反馈列表添加 */
export function emulationAddFeedback(data) {
  return request({
    url: '/emulation/addFeedback',
    method: 'POST',
    data
  })
}
/** 反馈列表列表 */
export function emulationFeedbackList(params) {
  return request({
    url: '/emulation/feedbackList',
    method: 'GET',
    params
  })
}
/** 赋分模型添加 */
export function emulationAddModel(data) {
  return request({
    url: '/emulation/addModel',
    method: 'POST',
    data
  })
}
/** 赋分模型修改 */
export function emulationUpdateModel(data) {
  return request({
    url: '/emulation/updateModel',
    method: 'POST',
    data
  })
}
/** 赋分模型列表 */
export function emulationModelList(params) {
  return request({
    url: '/emulation/modelList',
    method: 'GET',
    params
  })
}
/** 根据虚拟仿真id查询全部赋分模型 */
export function emulationGetAllModels(params) {
  return request({
    url: '/emulation/getAllModels',
    method: 'GET',
    params
  })
}
/** 赋分模型删除 */
export function emulationModeRemove(params) {
  return request({
    url: '/emulation/modeRemove',
    method: 'DELETE',
    params
  })
}

/** 查询考试记录 */
export function emulationGetRecord(params) {
  return request({
    url: '/emulation/getRecord',
    method: 'GET',
    params
  })
}

/** 查询所有专业 */
export function allMajor(params) {
  return request({
    url: '/major/allMajor',
    method: 'GET',
    params
  })
}
