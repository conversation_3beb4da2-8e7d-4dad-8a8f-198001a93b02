/**
 *  name 模块名称
 *  permission 权限标识
 *  url 首页卡片图片地址
 *  navBarImgUrl 顶部导航栏的图片地址
 *  routePath 路由地址
 *  moduleType 模块类型
 * */

const moduleList = [
  {
    name: '基础数据管理',
    permission: 'basicInfo', // 基础信息
    url: require('@/assets/Home/basicInfo.png'),
    navBarImgUrl: require('@/assets/navBar/basicInfo.png'),
    routePath: '/basicInfo/home',
    moduleType: 1
  },
  {
    name: '流程管理',
    permission: 'process', // 流程管理
    url: require('@/assets/Home/process.png'),
    navBarImgUrl: require('@/assets/navBar/process.png'),
    routePath: '/process/management',
    moduleType: 2
  },
  {
    name: '会议管理',
    permission: 'meeting', // 会议管理
    url: require('@/assets/Home/meeting.png'),
    navBarImgUrl: require('@/assets/navBar/meeting.png'),
    routePath: '/meeting',
    moduleType: 3
  },
  {
    name: '项目管理',
    permission: 'project_outer', // 项目管理
    url: require('@/assets/Home/project_outer.png'),
    navBarImgUrl: require('@/assets/navBar/process.png'),
    routePath: '/project',
    moduleType: 4
  },
  {
    name: '培训/知识库管理',
    permission: 'training_repository', // 培训/知识库管理
    navBarImgUrl: require('@/assets/navBar/training_repository.png'),
    url: require('@/assets/Home/training_repository.png'),
    moduleType: 5
  },
  {
    name: '制度管理',
    permission: 'institution', // 制度管理
    url: require('@/assets/Home/system.png'),
    navBarImgUrl: require('@/assets/navBar/institution.png'),
    routePath: '/institution',
    moduleType: 6
  },
  {
    name: '小微秘',
    permission: 'secret', // 小微秘
    url: require('@/assets/Home/xiaoWeiMi.png'),
    navBarImgUrl: require('@/assets/navBar/secret.png'),
    moduleType: 7
  },
  {
    name: '任务管理',
    permission: 'task', // 任务
    url: require('@/assets/Home/task.png'),
    navBarImgUrl: require('@/assets/navBar/task.png'),
    moduleType: 'task' // 这个模块单独跳转
  },
  {
    name: '合同管理',
    permission: 'contract', // 合同管理
    url: require('@/assets/Home/contract.png'),
    navBarImgUrl: require('@/assets/navBar/contract.png'),
    routePath: '/contract',
    moduleType: 8
  },
  {
    name: 'bug管理',
    permission: 'bug', // bug测试
    url: require('@/assets/Home/bug.png'),
    navBarImgUrl: require('@/assets/navBar/bug.png'),
    routePath: '/bug',
    moduleType: 9
  },
  {
    name: '资源库管理',
    permission: 'library', // 资源库管理
    url: require('@/assets/Home/library.png'),
    navBarImgUrl: require('@/assets/navBar/library.png'),
    routePath: '/library',
    moduleType: 10
  },
  {
    name: '绩效管理',
    permission: 'performance', // 绩效管理
    url: require('@/assets/Home/performance.png'),
    navBarImgUrl: require('@/assets/navBar/performance.png'),

    routePath: '/performance',
    moduleType: 11
  },
  {
    name: '产品库',
    permission: 'productLibrary', // 产品库
    url: require('@/assets/Home/productLibrary.png'),
    navBarImgUrl: require('@/assets/navBar/productLibrary.png'),
    moduleType: 12
  },
  {
    name: '虚拟仿真',
    permission: 'emulation', // 虚拟仿真
    url: require('@/assets/Home/emulation.png'),
    navBarImgUrl: require('@/assets/navBar/emulation.png'),
    routePath: '/emulation',
    moduleType: 13
  },
  {
    name: '方案共享',
    permission: 'tPlanShare', // 方案共享
    url: require('@/assets/Home/tPlanShare.png'),
    navBarImgUrl: require('@/assets/navBar/tPlanShare.png'),
    routePath: '/tPlanShare',
    moduleType: 14
  },
  {
    name: '新合同管理',
    permission: 'contract_new', // 合同管理 新
    url: require('@/assets/Home/contractNew.png'),
    navBarImgUrl: require('@/assets/navBar/contractNew.png'),
    routePath: '/newContract',
    moduleType: 15
  },
  {
    name: '产品发布',
    permission: 'productRelease', // 产品发布
    url: require('@/assets/Home/productRelease.png'),
    navBarImgUrl: require('@/assets/navBar/productRelease.png'),
    routePath: '/productRelease',
    moduleType: 16
  },
  {
    name: '问题跟踪',
    permission: 'workTrack', // 问题工作跟踪
    url: require('@/assets/Home/workTrack.png'),
    navBarImgUrl: require('@/assets/navBar/workTrack.png'),
    routePath: '/workTrack',
    moduleType: 17
  },
  {
    name: '版本号管理',
    permission: 'versionNumber', // 版本号管理
    url: require('@/assets/Home/versionNumber.png'),
    navBarImgUrl: require('@/assets/navBar/versionNumber.png'),
    routePath: '/versionNumber',
    moduleType: 18
  }
]

export default moduleList
