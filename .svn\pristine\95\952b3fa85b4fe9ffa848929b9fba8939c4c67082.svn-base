// 产品管理
import request from '@/utils/request'
/** 产品列表 */
export function getdictTypeList(params) {
  return request({
    url: '/product/productList',
    method: 'GET',
    params
  })
}
/** 添加产品 */
export function saveProduct(data) {
  return request({
    url: '/product/saveProduct',
    method: 'POST',
    data
  })
}
/** 修改产品 */
export function productUpdate(data) {
  return request({
    url: '/product/productUpdate',
    method: 'POST',
    data
  })
}
/** 删除产品 */
export function productRemove(params) {
  return request({
    url: '/product/productRemove',
    method: 'DELETE',
    params
  })
}
/** 添加产品迭代信息 */
export function saveProductIteration(data) {
  return request({
    url: '/product/saveProductIteration',
    method: 'POST',
    data
  })
}
/** 修改产品迭代信息 */
export function updateProductIteration(data) {
  return request({
    url: '/product/updateProductIteration',
    method: 'POST',
    data
  })
}
/** 产品列表详情 */
export function productDetails(params) {
  return request({
    url: '/product/productDetails',
    method: 'GET',
    params
  })
}

