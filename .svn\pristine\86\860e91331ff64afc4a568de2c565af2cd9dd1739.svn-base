<template>
  <div class="navbar">
    <el-row :gutter="50" type="flex" justify="space-between" align="middle">
      <el-col :span="5" style="min-width: 360px">
        <div class="left_title">
          <router-link to="/">
            <img src="@/assets/Home/logo.png" alt="" class="logo" />
            <img src="@/assets/Home/title.png" alt="" class="title" />
          </router-link>
        </div>
      </el-col>
      <el-col :span="18">
        <div class="right-menu">
          <div class="fastTab" style="margin-right: 30px">
            <el-dropdown trigger="click" placement="bottom">
              <div class="cut">
                <img src="@/assets/Home/fastTab.png" alt="" />
                <span v-if="type == 1" style="margin: 0 5px">基础数据管理</span>
                <span v-if="type == 2" style="margin: 0 5px">流程管理</span>
                <span v-if="type == 3" style="margin: 0 5px">会议管理</span>
                <span v-if="type == 4" style="margin: 0 5px">项目管理</span>
                <span v-if="type == 5" style="margin: 0 5px">培训/知识库管理</span>
                <span v-if="type == 6" style="margin: 0 5px">制度管理</span>
                <span v-if="type == 7" style="margin: 0 5px">小微秘</span>
                <span v-if="type == 8" style="margin: 0 5px">合同管理</span>
                <span v-if="type == 9" style="margin: 0 5px">bug管理</span>
                <i class="el-icon-caret-bottom" style="font-size: 12px" />
              </div>
              <el-dropdown-menu slot="dropdown" size="small" class="navBar_dropdown-menu">
                <el-dropdown-item style="width: 100%">
                  <img src="@/assets/dashboard/navBar_tabs_img.png" alt="" />
                </el-dropdown-item>
                <template v-if="currentPage === 1">
                  <el-dropdown-item v-for="item in mode" v-if="keyList.includes(item.flag)" :key="item.path" @click.native="checkTab(item)">
                    <div>
                      <img :src="item.url" alt="" style="margin-top: 7px" />
                      <span>{{ item.name }}</span>
                    </div>
                  </el-dropdown-item>
                </template>
                <template v-else>
                  <el-dropdown-item v-for="item in modeTwo" v-if="keyList.includes(item.flag)" :key="item.path" @click.native="checkTab(item)">
                    <div>
                      <img :src="item.url" alt="" style="margin-top: 7px" />
                      <span>{{ item.name }}</span>
                    </div>
                  </el-dropdown-item>
                </template>

                <div v-show="currentPage === 1" class="goPage" @click="goPage('down')">下一页</div>
                <div v-show="currentPage === 2" class="goPage" @click="goPage('up')">上一页</div>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <el-popover placement="top" trigger="click" popper-class="meesage_popover">
            <template v-slot:reference>
              <div class="messageHint">
                <i class="el-icon-message-solid"></i>
                <i class="el-icon-caret-bottom"></i>
              </div>
            </template>
            <template>
              <el-row class="meesage_top" type="flex" justify="space-between" align="middle">
                <span>最新通知(0)</span>
                <span>清空</span>
              </el-row>
              <el-row style="margin-top: 14px">
                <img src="@/assets/personCenter/message_bg.png" alt="" />
              </el-row>
              <div class="no_message">
                <img src="@/assets/personCenter/no_message.png" alt="" />
                <div>暂无最新消息</div>
              </div>
              <el-row class="meesage_info">
                <span></span>
                <span>您有一场会议即将在01-01 10:30开始，请您准时参加</span>
              </el-row>
              <div class="lookAll">查看全部</div>
            </template>
          </el-popover>
          <el-dropdown class="avatar-container" trigger="click">
            <div class="cut">
              <el-avatar :src="avatar" class="user-avatar"></el-avatar>
              <span class="user_realName">{{ realName }}</span>
              <i class="el-icon-caret-bottom" />
            </div>
            <el-dropdown-menu slot="dropdown" class="user-dropdown">
              <router-link to="/">
                <el-dropdown-item>
                  <span style="display: block">首页</span>
                </el-dropdown-item>
              </router-link>
              <router-link to="/personal/center">
                <el-dropdown-item>
                  <span style="display: block">个人中心</span>
                </el-dropdown-item>
              </router-link>
              <el-dropdown-item @click.native="changePassword"> 修改密码 </el-dropdown-item>

              <el-dropdown-item divided @click.native="logout">
                <span style="display: block">退出登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </el-col>
    </el-row>
    <el-dialog title="修改密码" :visible.sync="changePasswordDialog" width="420px" append-to-body>
      <div>
        <el-form ref="passwordForm" :model="changePasswordInfo" label-width="80px" :rules="passwordRules">
          <el-form-item label="原密码" prop="oldPassword">
            <el-input v-model="changePasswordInfo.oldPassword" type="password"></el-input>
          </el-form-item>
          <el-form-item label="新密码" prop="newPassword">
            <el-input v-model="changePasswordInfo.newPassword" type="password"></el-input>
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input v-model="changePasswordInfo.confirmPassword" type="password"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="changePasswordDialog = false">取 消</el-button>
        <el-button type="primary" @click="onClickPassword">确 定</el-button>
      </div>
    </el-dialog>
    <!-- <hamburger :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" /> -->

    <!-- <breadcrumb class="breadcrumb-container" /> -->
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { changePassword } from '@/api/systemUser'
import { getPassword } from '@/utils/auth'
import basicInfo from '@/assets/navBar/basicInfo.png'
import institution from '@/assets/navBar/institution.png'
import meeting from '@/assets/navBar/meeting.png'
import process from '@/assets/navBar/process.png'
import project from '@/assets/navBar/process.png'
import secret from '@/assets/navBar/secret.png'
import task from '@/assets/navBar/task.png'
import contract from '@/assets/navBar/contract.png'
import training_repository from '@/assets/navBar/training_repository.png'
import bug from '@/assets/navBar/bug.png'

// import Breadcrumb from '@/components/Breadcrumb'
// import Hamburger from '@/components/Hamburger'
export default {
  name: 'NavBar',
  components: {
    // Breadcrumb
    // Hamburger
  },
  data() {
    return {
      mode: [
        {
          name: '基础信息管理',
          path: '/basicInfo/home',
          type: 1,
          url: basicInfo,
          flag: 'basicInfo',
          icon: 'dashboard'
        },
        {
          name: '流程管理',
          path: '/process/management',
          type: 2,
          url: process,
          flag: 'process',
          icon: 'process'
        },
        {
          name: '会议管理',
          path: '/meeting',
          type: 3,
          url: meeting,
          flag: 'meeting',
          icon: 'meeting'
        },
        {
          name: '项目管理',
          path: '/project',
          type: 4,
          url: project,
          flag: 'project_outer',
          icon: 'project'
        },
        {
          name: '培训/知识库管理',
          path: '/training',
          type: 5,
          url: training_repository,
          flag: 'training_repository',
          icon: 'training'
        },
        {
          name: '制度管理',
          path: '/institution',
          type: 6,
          url: institution,
          flag: 'institution',
          icon: 'institution'
        },
        {
          name: '小微秘',
          path: '/area',
          type: 7,
          url: secret,
          flag: 'secret',
          icon: 'secret'
        },
        {
          name: '任务管理',
          path: 'http://192.168.1.103:2222/manage/dashboard',
          type: null,
          url: task,
          flag: 'task',
          icon: 'task'
        },
        {
          name: '合同管理',
          path: '/contract',
          type: 8,
          url: contract,
          flag: 'contract',
          icon: 'contract'
        }
      ],
      modeTwo: [
        {
          name: 'bug管理',
          path: '/bug',
          type: 9,
          url: bug,
          flag: 'bug',
          icon: 'bug'
        }
      ],
      type: 1,
      changePasswordDialog: false,
      changePasswordInfo: {
        oldPassword: null,
        newPassword: null,
        confirmPassword: null
      },
      passwordRules: {
        oldPassword: [{ required: true, message: '请输入原密码', trigger: 'blur' }],
        newPassword: [{ required: true, message: '请输入新密码', trigger: 'blur' }],
        confirmPassword: [{ required: true, message: '请确认新密码', trigger: 'blur' }]
      },
      currentPage: 1
    }
  },
  computed: {
    ...mapGetters(['sidebar', 'avatar', 'keyList', 'routes', 'realName', 'userId'])
  },
  created() {
    this.type = window.localStorage.getItem('zf_oa_type')
  },
  mounted() {
    const that = this
    // 根据自己需要来监听对应的key
    window.addEventListener('setItemEvent', function (e) {
      // e.key : 是值发生变化的key
      // 例如 e.key==="token";
      // e.newValue : 是可以对应的新值
      if (e.key === 'zf_oa_type') {
        that.$nextTick(() => {
          that.type = e.newValue
        })
      }
    })
  },
  methods: {
    toggleSideBar() {
      // this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
      await this.$store.dispatch('user/logout')
      this.$router.push(`/login?redirect=${this.$route.fullPath}`)
    },
    checkTab(item) {
      if (item.type) {
        this.type = item.type
        this.$store.commit('checkedData/set_data_type', item.type)
      }

      this.$nextTick(() => {
        if (item.type === 4) {
          if (this.keyList.includes('project')) {
            this.$router.push(item.path)
          } else {
            this.$router.push('/aftermarket')
          }
        } else if (item.type === 5) {
          if (this.keyList.includes('training')) {
            this.$router.push(item.path)
          } else {
            this.$router.push('/repository')
          }
        } else if (item.type === 7) {
          const route = this.routes.filter((item) => {
            if (item.meta && item.meta.type === 7) {
              return item
            }
          })
          this.$router.push(route[0].path)
        } else {
          if (item.flag === 'task') {
            window.open(item.path, 'blank')
          } else {
            this.$router.push(item.path)
          }
        }
      })
    },
    changePassword() {
      this.changePasswordDialog = true
    },
    onClickPassword() {
      this.$refs['passwordForm'].validate(async (val) => {
        if (val) {
          if (this.changePasswordInfo.oldPassword === getPassword()) {
            if (this.changePasswordInfo.confirmPassword === this.changePasswordInfo.newPassword) {
              // 通过
              await changePassword({
                userId: this.userId,
                newPassword: this.changePasswordInfo.newPassword,
                oldPassword: this.changePasswordInfo.oldPassword
              })
              this.$message.success('修改密码成功')
              this.changePasswordDialog = false
              // 修改密码后退出登录
              this.$nextTick(() => {
                this.logout()

                this.$message.warning('请重新登录')
              })
            } else {
              this.$message.warning('两次密码不一致')
            }
          } else {
            this.$message.warning('原密码不正确')
          }
        }
      })
    },
    goPage(type) {
      // this.$refs['contentBox'].style = type === 'down' ? 'transform: translateX(-2224px)' : 'transform: translateX(0)'
      this.currentPage = type === 'down' ? 2 : 1
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 68px;
  width: 100%;
  overflow: hidden;
  position: relative;
  // background: #fff;
  background: url('../../assets/Home/nav_bg.png');
  background-size: cover;
  // box-shadow: 0 1px 4px rgba(0,21,41,.08);

  // .hamburger-container {
  //   line-height: 46px;
  //   height: 100%;
  //   float: left;
  //   cursor: pointer;
  //   transition: background 0.3s;
  //   -webkit-tap-highlight-color: transparent;

  //   &:hover {
  //     background: rgba(0, 0, 0, 0.025);
  //   }
  // }

  // .breadcrumb-container {
  //   float: left;
  // }

  .left_title {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    .logo {
      width: 48px;
      height: 38px;
    }
    .title {
      width: 240px;
      height: 25px;
      padding-left: 8px;
    }
  }
  .right-menu {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-right: 40px;
    height: 100%;
    line-height: 50px;
    &:focus {
      outline: none;
    }
    .cut {
      display: flex;
      align-items: center;
      height: 100%;
      font-size: 16px;
      cursor: pointer;
      img {
        width: 16px;
        height: 16px;
      }
    }

    .messageHint {
      display: flex;
      align-items: center;
      margin-right: 30px;
      height: 100%;
      cursor: pointer;
      .el-icon-message-solid {
        position: relative;
        font-size: 21px;
        &::after {
          display: none;
          position: absolute;
          top: 0;
          right: 3px;
          z-index: 2;
          width: 5px;
          height: 5px;
          border-radius: 50%;
          background: #eb6557;
          content: '';
        }
      }
      .el-icon-caret-bottom {
        color: #606266;
        font-size: 12px;
      }
    }

    .avatar-container {
      margin-right: 30px;

      .cut {
        // margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 50%;
        }
        .user_realName {
          padding: 0 0px 0 10px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
        }
        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 30px;
          font-size: 12px;
        }
      }
    }
  }
}
.el-row {
  height: 100%;
  .el-col {
    height: 100%;
  }
}
.el-dropdown {
  height: 100%;
}
</style>

<style lang="scss">
.navBar_dropdown-menu {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
  padding: 21px 19px;
  padding-bottom: 40px;
  top: 70px !important;
  width: 418px;
  .el-dropdown-menu__item {
    width: 98px;
    height: 82px;
    margin-right: 42px;
    margin-bottom: 24px;

    padding: 0;
    border-radius: 10px 10px 10px 10px;

    &:nth-of-type(1),
    &:nth-of-type(4),
    &:nth-of-type(7),
    &:nth-of-type(10) {
      margin-right: 0;
    }
    &:nth-of-type(1) {
      height: 96px;
      margin-bottom: 24px;
    }
    & > div {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 98px;
      height: 82px;
      background: #f2f7ff;
      border-radius: 10px 10px 10px 10px;
      text-align: center;
      span {
        font-size: 12px;
        // font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        color: #0b1a44;
      }
    }
  }
  .goPage {
    position: absolute;
    left: 50%;
    bottom: 10px;
    transform: translateX(-50%);
    width: 70px;
    height: 30px;
    line-height: 30px;
    background: #f2f7ff;
    font-size: 14px;
    color: #4783ed;
    text-align: center;
    border-radius: 15px;
    cursor: pointer;
  }
}
</style>

<style lang="scss">
// 消息通知弹出框中的样式
.meesage_popover {
  position: relative;
  padding-bottom: 16px;
  &::after {
    content: '';
    position: absolute;
    bottom: 51px;
    left: 0;
    width: 100%;
    height: 1px;
    background: #eeeeef;
  }
  .meesage_top {
    & > span:first-of-type {
      font-size: 16px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #0b1a44;
      cursor: pointer;
    }
    & > span:last-of-type {
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #3464e0;
      cursor: pointer;
    }
  }
  .no_message {
    margin-top: 39px;
    text-align: center;
    & > div {
      margin-top: 10px;
      font-size: 14px;
      color: #b1bac7;
    }
  }
  .meesage_info {
    display: flex;
    align-items: center;
    margin-top: 12px;
    display: none;
    &:last-of-type {
      margin-bottom: 24px;
    }
    & > span:first-of-type {
      display: inline-block;
      width: 4px;
      height: 4px;
      background: #eb6557;
      margin: 0 27px;
      margin-right: 10px;
      border-radius: 50%;
    }
    & > span:last-of-type {
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #657081;
    }
  }
  .lookAll {
    margin-top: 40px;
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #0b1a44;
    text-align: center;
    cursor: pointer;
  }
}
</style>
