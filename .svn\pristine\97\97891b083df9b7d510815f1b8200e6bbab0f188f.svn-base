<template>
  <div class="app-container">
    <el-form ref="form" :model="queryInfo" label-width="80px" inline>
      <el-form-item label="用户名:">
        <el-input v-model="queryInfo.username" placeholder="请输入用户名" style="width: 200px; font-size: 15px" maxlength="30"></el-input>
      </el-form-item>
      <el-form-item label="姓名:">
        <el-input v-model="queryInfo.realName" placeholder="请输入姓名" style="width: 200px; font-size: 15px" maxlength="30"></el-input>
      </el-form-item>
      <el-form-item label="状态:">
        <el-radio-group v-model="queryInfo.status" @change="getTransferUserList">
          <el-radio label="">全部</el-radio>
          <el-radio :label="1">在职</el-radio>
          <el-radio :label="0">离职</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="性别:">
        <el-radio-group v-model="queryInfo.sex" @change="getTransferUserList">
          <el-radio label="">全部</el-radio>
          <el-radio label="M">男</el-radio>
          <el-radio label="F">女</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button type="success" @click="getTransferUserList">查询</el-button>
        <el-button type="primary" plain @click="resetForm">重置</el-button>
        <el-button type="primary" @click="transferData">迁移数据</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="listLoading" :data="list" element-loading-text="Loading" border fit highlight-current-row @row-click="handleRowClick">
      <el-table-column align="center" label="序号" type="index" width="120" />
      <el-table-column align="center" label="用户名" prop="username" width="150" />
      <el-table-column align="center" label="姓名" prop="realName" width="150" />
      <el-table-column align="center" label="性别" prop="sex" :formatter="sexFormatter" width="120" />
      <el-table-column align="center" label="联系方式" prop="mobile" width="150" />
      <el-table-column align="center" label="负责区域" prop="regionNames">
        <template v-slot="{ row }">
          <el-tag v-for="(region, index) in row.regionNames" :key="index" type="" class="region-tag" style="margin-right: 5px; font-size: 14px">
            {{ region }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="状态" prop="status" width="120">
        <template v-slot="{ row }">
          <el-tag :type="row.status === 1 ? 'success' : 'danger'">{{ row.status === 1 ? '在职' : '离职' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="区域变更时间" prop="regionUpdateTime" width="180" />
    </el-table>
    <!-- 分页 -->
    <el-pagination
      style="text-align: center; margin-top: 15px"
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="[10, 15, 20, 30]"
      :total="total"
      :page-size.sync="queryInfo.pageSize"
      :current-page.sync="queryInfo.pageNum"
      @size-change="getTransferUserList()"
      @current-change="getTransferUserList()"
    />
    <TransferData ref="transferData" @refresh="getTransferUserList()" />
  </div>
</template>
<script>
import { transfer_UserList } from '@/api/transfer'
import TransferData from './components/TransferData.vue'
export default {
  name: 'Transfer',
  components: {
    TransferData
  },
  data() {
    return {
      queryInfo: {
        pageNum: 1,
        pageSize: 10,
        username: '',
        realName: '',
        status: '',
        sex: ''
      },
      list: [],
      total: 0,
      listLoading: false
    }
  },
  created() {
    this.getTransferUserList()
  },
  methods: {
    async getTransferUserList() {
      this.listLoading = true
      const { data } = await transfer_UserList(this.queryInfo)
      this.list = data.list
      this.total = data.total
      this.listLoading = false
    },
    resetForm() {
      this.queryInfo = {
        pageNum: 1,
        pageSize: 10,
        username: '',
        realName: '',
        status: '',
        sex: ''
      }
      this.getTransferUserList()
    },
    sexFormatter(row) {
      return row.sex === 'M' ? '男' : '女'
    },
    transferData() {
      this.$refs.transferData.open()
    },
    handleRowClick(row) {
      this.$router.push(`/transfer/intro/${row.userId}`)
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .el-form-item {
    margin-right: 20px;
    .el-form-item__label {
      font-size: 15px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #0b1a44;
    }
    .el-button {
      font-size: 15px;
      padding: 10px 15px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
    }
  }
  .el-radio-group {
    .el-radio__input.is-checked .el-radio__inner {
      // background: #3464e0;
      background: #fff;
      width: 18px;
      height: 18px;
      border-color: #3464e0;
      // border: none;
      &::after {
        background-color: #3464e0;
        width: 8px;
        height: 8px;
      }
    }
    .el-radio__inner {
      width: 18px;
      height: 18px;
    }
    .el-radio__input.is-checked + .el-radio__label {
      color: #0b1a44;
    }
    .el-radio__label {
      font-size: 15px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #0b1a44;
    }
  }
  .el-table {
    .el-table__body {
      tr {
        cursor: pointer;

        &:hover {
          td {
            background-color: #f5f7fa;
          }
        }
      }
    }
  }
}
</style>
