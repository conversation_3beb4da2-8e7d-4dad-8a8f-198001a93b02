<template>
  <div class="virtual">
    <template v-if="webglUrl && version">
      <iframe v-if="version === 'Unity2018'" ref="iframe" :src="webglUrl" allowfullscreen frameborder="0" class="iframe" @load="iframeLoad"></iframe>
      <iframe v-else ref="iframe" :src="newWebglUrl" allowfullscreen frameborder="0" class="iframe" @load="iframeLoad"></iframe>
    </template>
  </div>
</template>
<script>
import { releaseProductDetail } from '@/api/productRelease'
import { mapGetters } from 'vuex'

export default {
  name: '',
  data() {
    return {
      webglUrl: window.config.VUE_APP_WEBGLURL,
      newWebglUrl: './webgl/newData.html',
      fileDatas: null
    }
  },
  computed: {
    ...mapGetters(['userId']),
    version() {
      return this.$route.params.version
    }
  },
  mounted() {
    window.addEventListener('message', function (e) {
      if (e.data === 'close') {
        window.close()
      }
    })
  },
  methods: {
    async iframeLoad() {
      const { id, state } = this.$route.params

      const { data } = await releaseProductDetail({ productId: id, state })
      // this.webglUrl = '/public/webgl/index.html'
      this.fileDatas = data.meetingFileReqs
      if (data.version === 'Unity2018') {
        const jsonData = data.meetingFileReqs.find((item) => {
          return item.fileName.split('.')[item.fileName.split('.').length - 1] === 'json'
        })
        this.info = {
          emulationUrl: jsonData.fileUrl,
          userId: this.userId,
          emulationId: this.$route.params.id
        }
        this.$refs['iframe'].contentWindow.postMessage(JSON.stringify(this.info), this.webglUrl)
      } else {
        const relativePath = this.fileDatas
          .find((item) => {
            if (item.fileUrl.includes('/Build')) {
              return item
            }
          })
          .fileUrl.split('/Build')[0]

        const info = {
          directoryUrl: relativePath + '/',
          loaderUrl: this.getUrl('.loader.js'),
          config: {
            dataUrl: this.getUrl('.data'),
            frameworkUrl: this.getUrl('.framework.js'),
            codeUrl: this.getUrl('.wasm')
          }
        }
        this.$refs['iframe'].contentWindow.postMessage(JSON.stringify(info), '*')
      }
    },
    getUrl(target) {
      const targetData = this.fileDatas.find((item) => {
        return item.fileName.includes(target)
      })
      return targetData.fileUrl
    }
  }
}
</script>
<style scoped lang="scss">
.virtual {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .iframe {
    width: 100%;
    // height: calc(100% - 64px);
    height: 100%;
  }
}
</style>
