<template>
  <div class="app-container">
    <div class="content">
      <el-row>
        <el-button type="primary" icon="el-icon-arrow-left" @click="goBack">返回</el-button>
      </el-row>
      <div v-if="info" class="box">
        <el-card>
          <el-descriptions :column="1">
            <el-descriptions-item label="bug标题" label-class-name="title">
              <div class="title">{{ info.title }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="bug描述">
              <div class="description" v-html="info.description"></div>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        <el-card>
          <el-descriptions title="基本信息" :column="1">
            <el-descriptions-item label="bug状态">
              <el-tag :type="bugType" size="small">{{ info.state | bugState }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="所属产品">{{ info.productName }}</el-descriptions-item>
            <el-descriptions-item label="实验名称">{{ info.name }}</el-descriptions-item>
            <el-descriptions-item label="所属专业">{{ info.majorName }}</el-descriptions-item>
            <el-descriptions-item label="优先级">
              <el-tag type="success" size="small">
                {{ info.priority === 1 ? '低' : info.priority === 2 ? '中' : '高' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="反馈人">{{ info.feedbackUser }}</el-descriptions-item>
            <el-descriptions-item label="反馈时间">{{ info.feedbackTime }}</el-descriptions-item>
            <el-descriptions-item label="解决人">{{ info.repairUser }}</el-descriptions-item>
            <el-descriptions-item label="解决时间">{{ info.repairTime }}</el-descriptions-item>
            <el-descriptions-item label="关闭人">{{ info.createUserName }}</el-descriptions-item>
            <el-descriptions-item label="关闭时间">{{ info.closeTime }}</el-descriptions-item>
          </el-descriptions>
          <div class="record">
            <div v-for="item in logRecord" :key="item.logId">{{ item.createTime }}, 由{{ item.createUserName }}创建, 解决方案为{{ item.remark }}</div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
import { tBugDetail, tBugBugLogRecord } from '@/api/bug'
export default {
  name: 'BugDetails',

  data() {
    return {
      info: null,
      logRecord: []
    }
  },
  computed: {
    bugType() {
      return this.info.state === 1 ? 'info' : this.info.state === '1' ? 'warning' : this.info.state === 2 ? 'success' : ''
    }
  },
  created() {
    this.getDetails()
    this.getRecord()
  },
  methods: {
    goBack() {
      this.$router.push('/bug')
    },
    async getDetails() {
      const { data } = await tBugDetail({ id: this.$route.params.id })
      this.info = data
    },
    async getRecord() {
      const { data } = await tBugBugLogRecord({ bugId: this.$route.params.id })
      this.logRecord = data
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  width: 100%;
  height: 100%;
  background: #f2f2f2;
  .content {
    width: 1600px;
    height: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #fff;
    .box {
      display: flex;
      padding-top: 20px;
      .el-card:first-of-type {
        flex: 1;
        max-height: 700px;
        overflow: auto;
        &::-webkit-scrollbar {
          width: 5px;
          border-radius: 5px;
          background: rgba($color: #3464e0, $alpha: 0.3);
        }
        &::-webkit-scrollbar-thumb {
          background: #3464e0;
          border-radius: 5px;
        }
        ::v-deep {
          .title {
            font-size: 22px;
            font-weight: bold;
            color: #000;
          }
        }

        .description {
          ::v-deep {
            img {
              max-width: 1000px;
            }
          }
        }
      }
      .el-card:last-of-type {
        width: 400px;
        margin-left: 15px;
        max-height: 700px;
        overflow: auto;
        &::-webkit-scrollbar {
          width: 5px;
          border-radius: 5px;
          background: rgba($color: #3464e0, $alpha: 0.3);
        }
        &::-webkit-scrollbar-thumb {
          background: #3464e0;
          border-radius: 5px;
        }
        ::v-deep {
          .el-card__body {
            padding: 0;
          }
          .el-descriptions {
            padding: 20px;
          }
        }
        .record {
          padding: 20px;
          border-top: 15px solid #f2f2f2;
        }
      }
    }
  }
}
</style>
