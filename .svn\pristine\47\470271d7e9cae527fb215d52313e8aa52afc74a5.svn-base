// 制度管理
import request from '@/utils/request'

/** 添加 */
export function noticeSave(data) {
  return request({
    url: '/notice/save',
    method: 'POST',
    data
  })
}
/** 修改制度 */
export function noticeUpdate(data) {
  return request({
    url: '/notice/udpate',
    method: 'POST',
    data
  })
}

/** 列表 */
export function noticeList(params) {
  return request({
    url: '/notice/list',
    method: 'GET',
    params
  })
}

/** 修改状态 */
export function noticeUpdateStatus(params) {
  return request({
    url: '/notice/updateStatus',
    method: 'GET',
    params
  })
}

/** 设置已读 */
export function noticeRead(params) {
  return request({
    url: '/notice/read',
    method: 'GET',
    params
  })
}

/** 添加反馈 */
export function noticeAddFeedback(data) {
  return request({
    url: '/notice/addFeedback',
    method: 'POST',
    data
  })
}
/** 查询已读未读人员列表 */
export function noticeReadList(params) {
  return request({
    url: '/notice/readList',
    method: 'GET',
    params
  })
}

/** 反馈列表 */
export function noticeFeedbacklist(params) {
  return request({
    url: '/notice/feedbacklist',
    method: 'GET',
    params
  })
}
