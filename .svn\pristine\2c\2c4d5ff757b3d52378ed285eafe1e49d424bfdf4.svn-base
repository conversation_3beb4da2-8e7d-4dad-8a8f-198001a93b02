// 小微秘 商机管理
import request from '@/utils/request'
/** 添加商机管理 */
export function chanceSaveChance(data) {
  return request({
    url: '/secret/chance/saveChance',
    method: 'POST',
    data
  })
}
/** 修改商机管理 */
export function chanceUpdateChance(data) {
  return request({
    url: '/secret/chance/updateChance',
    method: 'POST',
    data
  })
}

/** 商机管理列表 */
export function chanceChanceList(params) {
  return request({
    url: '/secret/chance/chanceList',
    method: 'GET',
    params
  })
}

/** 商机管理详情 */
export function chanceChanceDetails(params) {
  return request({
    url: '/secret/chance/chanceDetails',
    method: 'GET',
    params
  })
}
/** 商机管理列表导出 */
export function chanceChanceListExport(params) {
  return request({
    url: '/secret/chance/chanceListExport',
    method: 'GET',
    params
  })
}

/** 添加商机操作记录 */
export function chanceSaveRecord(data) {
  return request({
    url: '/secret/chance/saveRecord',
    method: 'POST',
    data
  })
}

/** 商机操作记录列表 */
export function chanceRecordList(params) {
  return request({
    url: '/secret/chance/recordList',
    method: 'GET',
    params
  })
}

/** 删除申请 */
export function chanceApplyForRemove(params) {
  return request({
    url: '/secret/chance/applyForRemove',
    method: 'DELETE',
    params
  })
}

/** 直接删除(拥有直接删除权限的人或审批通过时调用) */
export function chanceRemove(params) {
  return request({
    url: '/secret/chance/remove',
    method: 'DELETE',
    params
  })
}

/** 删除申请列表 */
export function chanceDelList(params) {
  return request({
    url: '/secret/chance/chanceDelList',
    method: 'GET',
    params
  })
}
