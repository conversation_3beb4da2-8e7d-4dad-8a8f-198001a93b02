<template>
  <div class="">
    <el-drawer title="修改记录" :visible.sync="drawer" direction="rtl">
      <el-timeline>
        <el-timeline-item v-for="item in recordList" :key="item.id" :timestamp="`${item.createTime} ${timeFormat(item.createTime)}`" placement="top">
          <el-card>
            <div class="record_user_info">
              <img :src="item.headurl" :alt="item.realName" />
              <span>{{ item.realName }}</span>
            </div>
            <p v-for="content in item.content" :key="content.id">{{ content.type === 1 ? '新增' : '修改' }}了{{ content.key | formatKey }}：{{ content.value }}</p>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </el-drawer>
  </div>
</template>
<script>
import { chanceRecordList } from '@/api/chance'
export default {
  name: '',
  filters: {
    formatKey(key) {
      const info = {
        content: '项目内容', // 项目内容
        rank: '等级', // 等级 A B C
        money: '项目金额(万元)', // 项目金额(万元)
        competitorInfo: '存在竞争对手情况', // 存在竞争对手情况
        collectType: '采购形式', // 采购形式 1 校内招标 2 公开招标 3 直采
        inviteTime: '招标时间', // 招标时间
        current: '项目当前情况', // 项目当前情况
        remark: '备注' // 备注
      }
      return info[key]
    }
  },
  data() {
    return {
      drawer: false,
      chanceId: null,
      recordList: []
    }
  },
  created() {},
  methods: {
    open(id) {
      console.log('open')
      this.drawer = true
      this.chanceId = id
      this.getRecordList()
    },
    async getRecordList() {
      const { data } = await chanceRecordList({ chanceId: this.chanceId })
      this.recordList = data.list
      this.recordList.forEach((item) => {
        if (item.content) {
          item.content = JSON.parse(item.content)
        }
      })
    },
    timeFormat(time) {
      const week = ['日', '一', '二', '三', '四', '五', '六']
      const date = new Date(time)
      return `周${week[date.getDay()]}`
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .el-drawer__header {
    padding: 15px;
    margin-bottom: 0;
    border-bottom: 1px solid #e5e5e5;
    font-size: 20px;
    color: #000;
  }
  .el-drawer__body {
    padding: 15px;
  }
  .record_user_info {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
    }
    span {
      margin-left: 10px;
      font-size: 16px;
      color: #000;
    }
  }
  p {
    padding: 3px;
    font-size: 15px;
  }
}
</style>
