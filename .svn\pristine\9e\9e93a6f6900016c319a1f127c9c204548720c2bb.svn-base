// 资源库管理

/** 添加资源库 */
import request from '@/utils/requestLibrary'
export function resourceAdd(data) {
  return request({
    url: '/resource/add',
    method: 'POST',
    data
  })
}
/** 修改资源库 */
export function resourceUpdate(data) {
  return request({
    url: '/resource/update',
    method: 'POST',
    data
  })
}
/** 删除资源库 */
export function resourceRemove(params) {
  return request({
    url: '/resource/remove',
    method: 'DELETE',
    params
  })
}
/** 资源库列表 */
export function resourceList(params) {
  return request({
    url: '/resource/list',
    method: 'get',
    params
  })
}
/** 资源库详情 */
export function resourceDetail(params) {
  return request({
    url: '/resource/detail',
    method: 'get',
    params
  })
}
/** 添加资源库日志 */
export function resourceLogAdd(data) {
  return request({
    url: '/resourceLog/add',
    method: 'POST',
    data
  })
}
/** 所有项目列表 */
export function allProjectList(params) {
  return request({
    url: '/project/allProjectList',
    method: 'GET',
    params
  })
}

/** 查询所有专业 */
export function allMajor(params) {
  return request({
    url: '/major/allMajor',
    method: 'GET',
    params
  })
}

/** 查询100的token --- 资源库管理专用 */
export function resourceLogin(data) {
  return request({
    url: '/resource/login',
    method: 'post',
    data
  })
}
