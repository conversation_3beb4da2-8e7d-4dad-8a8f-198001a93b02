<template>
  <div class="app-container">
    <el-row :gutter="50" class="top" type="flex" justify="space-between" align="middle" style="margin-bottom: 16px">
      <el-col :span="6">
        <div class="top_left">
          <span class="meeting_icon">
            <img src="@/assets/repository/repository_icon.png" alt="" />
            知识库管理
          </span>
          <span class="add"> <i class="el-icon-circle-plus-outline"></i> 分享知识</span>
        </div>
      </el-col>
    </el-row>
    <div class="box">
      <div v-if="JSON.stringify(info) !== `{}`" class="content">
        <div class="createTime">{{ info.realName }} 分享于 {{ info.createTime }}</div>
        <div>
          <div class="title">
            {{ info.name }} <span>{{ info.type | repository_type }}</span>
          </div>
          <div class="content_text" v-html="info.content"></div>
          <ul class="file">
            <li v-for="item in info.fileDtos" :key="item.name">
              <div>
                <div class="fileImg">
                  <img src="@/assets/meeting/file.png" alt="" />
                  <span v-if="item.fileSize">{{ item.fileSize }}KB</span>
                  <span v-else>{{ item.size | formattingFileSize }}</span>
                </div>
                <div>
                  <el-tooltip class="item" effect="dark" :content="item.fileName" placement="top">
                    <span v-if="item.fileName">{{ item.fileName }}</span>
                    <span v-else>{{ item.name }}</span>
                  </el-tooltip>
                </div>
              </div>
              <div>
                <span v-if="item.name && item.createTime">{{ realName }}上传于{{ item.createTime | formatDate }}</span>
                <span v-else>{{ realName }}上传于{{ new Date() | formatDate }}</span>
                <i class="el-icon-download" @click="downloadFile(item)"></i>
                <i class="el-icon-delete" @click="delFile(item)"></i>
              </div>
            </li>
          </ul>
          <div class="discuss_top">
            <div class="commend" @click="esayLike(info, 1)">
              <img src="@/assets/repository/commend.png" alt="" />
              <span>赞同</span>
              <span class="commend_num">{{ info.likeCount }}</span>
            </div>
            <div class="discuss_icon">
              <img src="@/assets/repository/discuss.png" alt="" />
              <span>{{ info.commentTotal }}条评论</span>
            </div>
            <div class="unfold">
              <div @click="togglebox">{{ isSpread ? '收起' : '展开' }} <i v-if="isSpread" class="el-icon-caret-top"></i><i v-else class="el-icon-caret-bottom"></i></div>
            </div>
          </div>
          <div class="discuss_center">
            <transition name="mybox">
              <div v-show="boxshow" class="boxtransition">
                <div class="input">
                  <el-popover v-model="emojiShow" placement="bottom" title="表情" width="500" height="700" trigger="click">
                    <img slot="reference" src="@/assets/repository/face.png" alt="" class="face" />
                    <div class="browBox">
                      <el-tabs tab-position="left" @tab-click="tabClick">
                        <!--循环表情包的类型-->
                        <el-tab-pane v-for="(item, index) in faceList" :key="index" :label="item.name">
                          <div class="img-div">
                            <!--循环表情类型的每一个表情包-->
                            <span v-for="(item1, index1) in faceList[tabIndex].iconArr" :key="index1" class="emoji" @click="clickEmoji(item1.icon)">{{ item1.icon }}</span>
                          </div>
                        </el-tab-pane>
                      </el-tabs>
                    </div>
                  </el-popover>
                  <el-input id="input" v-model="text" placeholder="写下你的评论..." type="textarea"></el-input>
                  <span class="submit_button" @click="release"> 发布 </span>
                </div>
                <div class="discuss_title">
                  <span>{{ info.commentTotal }}条评论</span>
                  <span @click="cutSort"><i :style="{ color: sort === 1 ? '#3464E0' : '#D8DBE1' }">正序</i>/<i :style="{ color: sort === 2 ? '#3464E0' : '#D8DBE1' }">倒序</i></span>
                </div>
                <div class="discuss_content">
                  <div v-for="(item, index) in info.comments" :key="item.commentId" class="list" @mouseenter="item.showReply = true" @mouseleave="item.showReply = false">
                    <div style="margin-bottom: 15px">
                      <div class="first_row">
                        <section>
                          <el-image style="width: 26px; height: 26px; border-radius: 4px" :src="item.headurl" fit="cover"></el-image>
                          <span>{{ item.realName }}</span>
                        </section>
                        <span>{{ item.createTime | timeago }}</span>
                      </div>
                      <div class="center_row">
                        {{ item.content }}
                      </div>
                      <div class="last_row">
                        <span class="easyLike" :style="{ color: item.isLike === 1 ? '#3464e0' : '#a3a8bb' }" @click="esayLike(item, 2)"> <i class="iconfont zf-dianzan_kuai"></i>{{ item.likeCount }} </span>
                        <!-- <span v-show="item.showReply" @click="reply(item)">回复</span> -->
                        <span v-show="item.showReply && !item.showReplyText" class="revert" @click="reply(item)">回复</span>
                        <span v-if="item.showReply && item.showReplyText" class="revert" style="margin-right: 15px" @click="cancelRevert(item)">取消回复</span>
                      </div>
                      <div v-if="item.showReplyText" class="replyText">
                        <el-input ref="replyText" v-model="item.replyText" :placeholder="`回复${item.realName}`"></el-input>
                        <span @click="addKnowledgeBaseSaveReply(item)">发布</span>
                      </div>
                    </div>
                    <transition name="mybox">
                      <section v-if="item.replys.length > 0" class="secondLevel">
                        <div v-for="(list, indexs) in item.replys" v-show="indexs < item.showAllReply" :key="list.replyId" class="list" @mouseenter="list.showReply = true" @mouseleave="list.showReply = false">
                          <template>
                            <div class="first_row">
                              <section>
                                <el-image style="width: 26px; height: 26px; border-radius: 4px" :src="list.headurl" fit="cover"></el-image>
                                <span>{{ list.realName }} <i>回复</i>{{ list.replyToRealName }}</span>
                              </section>
                              <span>{{ list.createTime | timeago }}</span>
                            </div>
                            <div class="center_row">
                              {{ list.content }}
                            </div>
                            <div class="last_row">
                              <span class="easyLike" :style="{ color: list.isLike === 1 ? '#3464e0' : '#a3a8bb' }" @click="esayLike(list, 3)"> <i class="iconfont zf-dianzan_kuai"></i>{{ list.likeCount }} </span>
                              <span v-if="list.showReply && list.showReplyText" class="revert" style="margin-right: 15px" @click="cancelRevert(list)">取消回复</span>
                              <span v-show="list.showReply && !list.showReplyText" class="revert" @click="reply(list, index)">回复</span>
                            </div>
                            <div v-if="list.showReplyText" class="replyText">
                              <el-input ref="replyText" v-model="list.replyText" :placeholder="`回复${list.realName}`"></el-input>
                              <span @click="addKnowledgeBaseSaveReply(list, 2)">发布</span>
                            </div>
                          </template>
                        </div>
                        <template v-if="item.replys.length > 1">
                          <span v-if="item.showAllReply === 2 && item.replys.length !== 2" class="lookAll" @click="item.showAllReply = 1000">查看全部 {{ item.replys.length }} 条回复</span>
                          <span v-else-if="item.replys.length !== 2" class="lookAll" @click="item.showAllReply = 2">收起 <i class="el-icon-caret-top"></i></span>
                        </template>
                      </section>
                    </transition>

                    <!-- <el-collapse v-model="activeName" @change="collapseChange">
                  <el-collapse-item>
                    <template slot="title">
                      <span>查看全部 {{ item.replys.length }} 条回复</span>
                    </template>

                  </el-collapse-item>
                </el-collapse> -->
                  </div>
                </div>
              </div>
            </transition>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { knowledgeBaseAddBrowse, knowledgeBaseDetails, knowledgeBaseSaveComment, knowledgeLike, knowledgeBaseSaveReply } from '@/api/repository'
import { meetingDeleteFile } from '@/api/meeting'
import { mapGetters } from 'vuex'
import { emojiArr } from '@/utils/emoji'
export default {
  name: '',
  data() {
    return {
      info: {},
      boxshow: true,
      isSpread: true,
      text: '',
      sort: 1,
      // 表情框是否展示
      emojiShow: false,
      // 表情列表
      faceList: emojiArr,
      // 表情文本
      tabIndex: 0 // 表情包的类型索引
    }
  },
  computed: {
    ...mapGetters(['realName'])
  },

  created() {
    this.addKnowledgeBaseAddBrowse()
    this.getKnowledgeBaseDetails()
  },
  methods: {
    // 添加浏览次数
    async addKnowledgeBaseAddBrowse() {
      await knowledgeBaseAddBrowse({ knowledgeBaseId: this.$route.params.knowledgeBaseId })
    },
    // 获取详情
    async getKnowledgeBaseDetails() {
      const { data } = await knowledgeBaseDetails({ knowledgeBaseId: this.$route.params.knowledgeBaseId, belongType: 3, sort: this.sort })
      this.info = data
      this.info.comments.forEach((item, index) => {
        this.$set(this.info.comments[index], 'showReply', false)
        this.$set(this.info.comments[index], 'showReplyText', false)
        this.$set(this.info.comments[index], 'replyText', '')
        this.$set(this.info.comments[index], 'showAllReply', 2)
        console.log(this.info.comments[index])
        if (this.info.comments[index].replys && this.info.comments[index].replys.length > 0) {
          this.info.comments[index].replys.forEach((item, indexs) => {
            this.$set(this.info.comments[index].replys[indexs], 'showReply', false)
            this.$set(this.info.comments[index].replys[indexs], 'showReplyText', false)
            this.$set(this.info.comments[index].replys[indexs], 'replyText', '')
          })
        }
      })

      console.log(this.info)
    },
    downloadFile(row) {
      if (row.fileId) {
        window.open(row.fileUrl, '_blank')
      } else {
        window.open(row.response.data[0], '_blank')
      }
    },
    async delFile(row) {
      await meetingDeleteFile({ fileId: row.fileId })
      this.$message.success('删除成功')
      this.getKnowledgeBaseDetails()
      // this.info.fileDtos = this.info.fileDtos.filter((item) => item.fileId !== row.fileId)
    },
    // 展开与收起
    togglebox() {
      console.log(this.boxshow, this.isSpread)
      this.boxshow = !this.boxshow
      this.isSpread = !this.isSpread
    },
    // 切换排序
    cutSort() {
      this.sort = this.sort === 1 ? 2 : 1
      this.getKnowledgeBaseDetails()
    },
    clickEmoji(val) {
      // 获取文本输入框元素节点
      const ele = document.querySelector('#input')
      console.log(ele)
      // 获取光标
      const cursor = ele.selectionStart
      // 设置文本内容
      ele.setRangeText(val)
      // 移动光标并聚焦
      ele.selectionStart = cursor + 2
      ele.focus()
      // 使文本输入框的内容等于当前的值
      this.text = ele.value
    },
    // 点击标签页
    tabClick(tab) {
      this.tabIndex = tab.index - 0
    },
    // 发布评论
    async release() {
      await knowledgeBaseSaveComment({ content: this.text, knowledgeBaseId: this.$route.params.knowledgeBaseId })
      this.$message.success('评论成功')
      this.text = ''
      this.getKnowledgeBaseDetails()
    },
    // 对评论点赞
    async esayLike(item, form) {
      console.log(item)
      let likeId = null
      let type = null
      if (form === 2) {
        likeId = item.commentId
        type = item.isLike === 0 ? 1 : 2
      } else if (form === 1) {
        likeId = this.$route.params.knowledgeBaseId
        type = item.isLike === 0 ? 1 : 2
      } else if (form === 3) {
        likeId = item.replyId
        type = item.isLike === 0 ? 1 : 2
      }
      await knowledgeLike({ likeId, type, form })
      this.getKnowledgeBaseDetails()
    },
    // 点击评论回复
    reply(item, index) {
      console.log(index)
      item.replyText = ''
      if (index || index === 0) {
        const isShowReply = this.info.comments[index].replys.some((item) => item.showReplyText === true)
        isShowReply === true ? (item.showReplyText = false) : (item.showReplyText = true)
        console.log(isShowReply)
      } else {
        const isShowReply = this.info.comments.some((item) => item.showReplyText === true)
        isShowReply === true ? (item.showReplyText = false) : (item.showReplyText = true)
      }

      // if (!isShowReply) {
      //   this.$nextTick(() => {
      //     this.$refs.replyText[0].focus()
      //   })
      // }
    },
    // 添加回复
    async addKnowledgeBaseSaveReply(item, type) {
      console.log(item)
      if (type === 2) {
        await knowledgeBaseSaveReply({ commentId: item.commentId, replyTo: item.userId, content: item.replyText })
      } else {
        await knowledgeBaseSaveReply({ commentId: item.commentId, replyTo: item.userId, content: item.replyText })
      }
      this.$message.success('评论成功')
      this.$refs.replyText[0].blur()
      this.getKnowledgeBaseDetails()
    },
    // 取消回复
    cancelRevert(list) {
      console.log(list)
      list.replyText = ''
      list.showReplyText = false
      this.$refs.replyText[0].blur()
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  position: relative;
  background-color: #e8eaed;
  min-height: 100%;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
}
.top {
  .top_left {
    display: flex;
    align-items: center;
    .meeting_icon {
      display: flex;
      align-items: center;
      margin-right: 10px;
      font-size: 16px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #0b1a44;
      img {
        margin-right: 8px;
      }
    }
    .add {
      display: none;
      width: 104px;
      height: 34px;
      line-height: 34px;
      font-size: 16px;
      background-color: #3464e0;
      font-family: Microsoft YaHei;
      color: #fff;
      text-align: center;
      border-radius: 8px;
      cursor: pointer;
    }
  }
}
.box {
  position: relative;
  background: #f5f5f5;
  background-size: 100% cover;
  padding-bottom: 15px;
  &::before {
    content: '';
    position: absolute;
    top: -6px;
    left: 0;
    z-index: 0;
    width: 100%;
    height: 20px;
    border-radius: 20px;
    background: #3465df;
  }
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    border-radius: 10px;
    background: #f5f5f5;
  }
}
.content {
  position: relative;
  z-index: 999;
  padding: 24px 5px 40px 5px;
  width: 1000px;
  background: #ffffff;
  border-radius: 4px;
  margin: 0 auto;
  overflow: hidden;
  .createTime {
    margin-bottom: 20px;
    padding-left: 38px;
    width: 100%;
    height: 33px;
    line-height: 33px;
    background: #f5f5f5;
    font-size: 14px;
    color: #657081;
  }
  & > div {
    padding: 0 35px;
  }
  .title {
    margin-bottom: 24px;
    font-size: 20px;
    font-family: Microsoft YaHei-Bold, Microsoft YaHei;
    font-weight: bold;
    color: #0b1a44;
    span {
      display: inline-block;
      width: 71px;
      height: 28px;
      line-height: 28px;
      background: #ff7e26;
      border-radius: 4px 4px 4px 4px;
      font-size: 16px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #ffffff;
      text-align: center;
    }
  }
  .content_text {
    margin-bottom: 15px;
    ::v-deep {
      * {
        overflow: hidden;
      }
    }
  }
  .file {
    padding-top: 15px;
    border-top: 1px solid #eee;
    li {
      margin-right: 32px;
      padding: 14px 10px 20px 10px;
      width: 268px;
      height: 124px;
      background: #f9f9f9;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #eeeeef;
      box-sizing: border-box;
      & > div {
        &:first-of-type {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          .fileImg {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            img {
              width: 46px;
              height: 38px;
            }
            span {
              font-size: 12px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #868b9f;
              line-height: initial;
            }
          }

          & > div {
            margin-left: 3px;
            line-height: initial;
            .el-textarea {
              width: initial;
              .el-textarea__inner {
                width: 100%;
              }
            }

            &:last-of-type {
              span {
                display: inline-block;
                width: 190px;
                font-size: 14px;
                font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                font-weight: 400;
                color: #0b1a44;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
              i {
                margin-left: 5px;
                color: #ff7e26;
                font-size: 18px;
                cursor: pointer;
              }
            }
          }
        }
        &:last-of-type {
          margin-top: 18px;
          font-size: 12px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #a3a8bb;
          line-height: initial;
          i {
            font-size: 18px;
            float: right;
            margin-left: 15px;
            cursor: pointer;
          }
          .el-icon-download:hover {
            color: #3464e0;
          }
          .el-icon-delete:hover {
            color: #eb6557;
          }
        }
      }
    }
  }
  .discuss_top {
    display: flex;
    align-items: center;
    margin-top: 24px;
    .commend {
      cursor: pointer;
    }
    .commend,
    .discuss_icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 98px;
      height: 32px;
      background: #e6ecfc;
      border-radius: 4px;
      span {
        margin-left: 5px;
        font-size: 14px;
        font-weight: bold;
        color: #3464e0;
      }
    }
    .discuss_icon {
      margin-left: 15px;
      background: #fff;
      span {
        margin-left: 5px;
        color: #868b9f;
      }
    }
    .unfold {
      font-size: 14px;
      font-weight: 400;
      color: #868b9f;
    }
  }
  .discuss_center {
    margin-top: 24px;

    .boxtransition {
      padding-top: 24px;
      background: #ffffff;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #d8dbe1;
      .input {
        display: flex;
        align-items: flex-end;
        position: relative;
        padding-left: 32px;
        padding-right: 32px;
        margin-bottom: 24px;
        .el-textarea {
          min-height: 150px;
          ::v-deep {
            .el-textarea__inner {
              min-height: 150px !important;
            }
          }
        }
        .submit_button {
          display: inline-block;
          margin-left: 25px;
          width: 72px;
          height: 36px;
          line-height: 36px;
          background: #7092e9;
          text-align: center;
          font-size: 14px;
          color: #ffffff;
          border-radius: 4px;
          cursor: pointer;
          &:hover {
            background: #3464e0;
          }
        }
      }
      .discuss_title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-left: 32px;
        padding-right: 32px;
        span {
          &:first-of-type {
            font-size: 14px;
            font-family: Microsoft YaHei-Bold, Microsoft YaHei;
            font-weight: bold;
            color: #0b1a44;
          }
          &:last-of-type {
            font-size: 12px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #b1bac7;
            cursor: pointer;
          }
          i {
            font-style: normal;
          }
        }
      }
      .discuss_content {
        margin-top: 16px;
        border-top: 2px solid #eeeeef;
        padding-left: 32px;
        padding-right: 32px;
        padding-top: 24px;
        padding: 24px;
        & > div {
          padding-top: 16px;
          padding-bottom: 13px;
          border-bottom: 1px solid #eeeeef;
          .first_row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            section {
              display: flex;
              align-items: center;
              span {
                display: inline-block;
                margin: 8px;
                font-size: 14px;
                font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                color: #0b1a44;
              }
            }
            & > span {
              font-size: 14px;
              color: #a3a8bb;
            }
          }
          .center_row {
            margin: 8px 0 10px 0;
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            color: #0b1a44;
            text-indent: 32px;
          }
          .last_row {
            text-indent: 32px;

            .easyLike {
              margin-right: 24px;
              cursor: pointer;
              color: #a3a8bb;
              &:hover {
                color: #3464e0;
              }
              .zf-dianzan_kuai {
                margin-right: 5px;
              }
            }
            .revert {
              font-size: 14px;
              color: #a3a8bb;
              cursor: pointer;
              &:hover {
                color: #3464e0;
              }
            }
          }
          .replyText {
            display: flex;
            margin-top: 16px;
            span {
              display: inline-block;
              margin-left: 25px;
              width: 72px;
              height: 36px;
              line-height: 36px;
              background: #7092e9;
              text-align: center;
              font-size: 14px;
              color: #ffffff;
              border-radius: 4px;
              cursor: pointer;
              &:hover {
                background: #3464e0;
              }
            }
          }
          .secondLevel {
            border-top: 1px solid #eeeeef;
            padding-left: 67px;
            & > div {
              margin-top: 16px;
              padding-bottom: 13px;
              border-bottom: 1px solid #eeeeef;
              .first_row {
                span {
                  i {
                    margin: 0 8px;
                    margin-left: 3px;
                    font-style: normal;
                    color: #a3a8bb;
                  }
                }
              }
            }
            .lookAll {
              display: inline-block;
              padding-top: 16px;
              font-size: 14px;
              font-weight: bold;
              color: #a3a8bb;
              cursor: pointer;
              &:hover {
                color: #3464e0;
              }
            }
          }
        }
      }
    }
  }
}
.face {
  position: absolute;
  bottom: 10px;
  right: 145px;
  z-index: 999;
  cursor: pointer;
}
.emoji {
  display: inline-block;
  margin-left: 10px;
  margin-bottom: 10px;
  cursor: pointer;
}
.mybox-enter-active,
.mybox-leave-active {
  transition: all 0.5s ease;
  transform: translateY(0);
}
.mybox-enter,
.mybox-leave-active {
  opacity: 0;
  transform: translateY(50%);
}
::v-deep {
  .el-collapse {
    border: none;
  }
  .el-collapse-item__header {
    padding-left: 64px;
    border: none;
    font-size: 14px;
    font-family: Microsoft YaHei-Bold, Microsoft YaHei;
    font-weight: bold;
    color: #a3a8bb;
    &:hover {
      color: #3464e0;
    }
  }
}
</style>
