<template>
  <div id="app">
    <keep-alive :include="keepList">
      <router-view />
    </keep-alive>
    <update v-if="showUpdate" ref="update" />
    <!-- <router-view v-if="!$route.meta.keepAlive" /> -->
    <!-- <keep-alive >
      <router-view v-if="$route.meta.keepAlive" />
    </keep-alive>
    <router-view v-if="!$route.meta.keepAlive" /> -->
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex'
import icon from '../public/icon.png'
import update from '@/components/Update'
export default {
  name: 'App',
  components: {
    update
  },
  data() {
    return {
      myNotification: null,
      keepList: [],
      configBaseURL: window.config.VUE_APP_BASE_API,
      showUpdate: false
    }
  },
  computed: {
    ...mapGetters(['userId', 'message'])
  },
  watch: {
    $route(to, form) {
      if (form.name === 'Process' && to.name === 'ProcessDetails') {
        this.keepList = ['Process']
      } else if (form.name === 'Bug' && to.name === 'BugDetails') {
        this.keepList = ['Bug']
      } else if (form.name === 'Software' && to.name === 'SoftwareDetails') {
        this.keepList = ['Software']
      } else if (form.name === 'Hardware' && to.name === 'HardwareDetails') {
        this.keepList = ['Hardware']
      } else if (form.name === 'Contract' && to.name === 'ContractDetails') {
        // 缓存合同首页
        this.keepList = ['Contract']
      } else {
        this.keepList = []
      }
    },
    message: {
      immediate: true,
      handler(val) {
        if (val && val !== 'success') {
          const data = JSON.parse(val)
          const isCurrentUser = data.userIds.find((item) => item === this.userId)
          if (isCurrentUser) {
            if (this.myNotification) {
              this.myNotification.close()
            }
            if (window.electronAPI) {
              this.notification(data.description, data.type)
            }
          }
        }
      }
    }
  },
  mounted() {
    if (window.rendererOperate) {
      this.showUpdate = true
      this.$nextTick(() => {
        this.$refs['update'].CheckUpdate()
      })
    }
  },
  created() {
    const that = this
    this.$websocketHeartbeatJs.onopen = function () {
      console.log('连接成功！')
    }
    this.$websocketHeartbeatJs.onmessage = function (event) {
      that.onMessages(event.data)
    }
    this.$websocketHeartbeatJs.onreconnect = function () {
      console.log('尝试重新连接。。')
    }
    this.$websocketHeartbeatJs.onerror = function () {
      console.log('连接出错。。')
    }
  },
  methods: {
    ...mapActions('websocket', ['onMessages']),
    notification(text, type) {
      this.myNotification = new Notification('消息通知', {
        body: text,
        icon: icon
      })
      window.electronAPI.asynchronousMessage()
      this.myNotification.onclick = () => {
        // 在渲染器进程使用导出的 API
        window.electronAPI.openWindow()
        switch (type) {
          case 1:
            this.$store.commit('checkedData/set_data_type', 9)
            this.$nextTick(() => {
              this.$router.push('/bug')
            })
            break
          case 2:
            this.$store.commit('checkedData/set_data_type', 6)
            this.$nextTick(() => {
              this.$router.push('/institution')
            })
            break
          case 3:
            this.$store.commit('checkedData/set_data_type', 3)
            this.$nextTick(() => {
              this.$router.push('/meeting')
            })
            break
          case 4:
            this.$store.commit('checkedData/set_data_type', 7)
            this.$router.push('/secretLog')
            break
        }
      }
    }
  }
}
</script>
