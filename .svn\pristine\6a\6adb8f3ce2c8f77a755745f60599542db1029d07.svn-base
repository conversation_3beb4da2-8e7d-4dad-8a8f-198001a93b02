<template>
  <div style="width: 100%; height: 100%">
    <div id="processStatisEchart" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script>
import resize from '../mixins/resize'
export default {
  name: '',
  mixins: [resize],
  data() {
    return {
      chart: null
    }
  },
  methods: {
    init(data) {
      this.chart = this.$echarts.init(document.getElementById('processStatisEchart'))
      this.setOpiton(data)
    },
    setOpiton(data) {
      const that = this
      this.chart.setOption({
        title: {
          text: '流程完成情况统计',
          textStyle: {
            color: '#0B1A44',
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: 'rgba(0,0,0,0.7)',
          borderColor: 'gray',
          padding: [12, 16, 16, 16],
          formatter: function (params) {
            let str = `
            <div class="processStatisEchart">
              <span></span>
              <span class="processStatisEchartTitle">${params[0].name}</span>
            </div>
              `
            if (params[0].data.msg && params[0].data.msg.length > 0) {
              if (params[0].data.msg[0].status !== '暂无信息') {
                params[0].data.msg.forEach((item) => {
                  str += `
              <div class="processStatisEchartText"> 
               <span>${item.status}</span>
               <span>${item.cou}</span>
              </div>`
                })
              }
            }

            return str
          }
        },
        xAxis: {
          type: 'category',
          data: data.x
        },
        yAxis: {
          name: '流程条数（条）',
          type: 'value'
        },
        grid: {
          //   top: '0',
          left: '5%',
          right: '0',
          bottom: '0',
          containLabel: true
        },
        series: [
          {
            data: data.y,
            type: 'bar',
            barWidth: 17,
            itemStyle: {
              color: '#6970ff'
            }
          }
        ]
      })
      this.chart.on('click', function (params) {
        console.log(params)
        that.$emit('jumpPage')
      })
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep {
  .processStatisEchart {
    display: flex;
    align-items: center;
    & > span:first-of-type {
      display: inline-block;
      width: 6px;
      height: 6px;
      border-radius: 6px;
      background: #3464e0;
    }
    .processStatisEchartTitle {
      margin-left: 5px;
      font-size: 12px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #ffffff;
    }
  }
  .processStatisEchartText {
    font-size: 12px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #ffffff;
  }
}
</style>
