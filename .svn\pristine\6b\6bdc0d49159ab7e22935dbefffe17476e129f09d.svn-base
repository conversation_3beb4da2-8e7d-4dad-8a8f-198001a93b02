// 新 合同管理
import request from '@/utils/request'
/** 添加 */
export function contractAdd(data) {
  return request({
    url: '/contract/add',
    method: 'POST',
    data
  })
}

/** 列表 */
export function contractList(params) {
  return request({
    url: '/contract/list',
    method: 'GET',
    params
  })
}

/** 修改 */
export function contractUpdate(data) {
  return request({
    url: '/contract/update',
    method: 'POST',
    data
  })
}

/** 删除 */
export function contractRemove(params) {
  return request({
    url: '/contract/remove',
    method: 'DELETE',
    params
  })
}

/** 批量修改销售负责人 */
export function contractUpdateHeadUsers(data) {
  return request({
    url: '/contract/updateHeadUsers',
    method: 'POST',
    data
  })
}

/** 详情 */
export function contractDetail(params) {
  return request({
    url: '/contract/detail',
    method: 'GET',
    params
  })
}

/** 施工信息修改 */
export function contractConstructionUpdate(data) {
  return request({
    url: '/contract/construction/update',
    method: 'POST',
    data
  })
}

/** 合同货款信息修改 */
export function contractMoneyUpdate(data) {
  return request({
    url: '/contract/money/update',
    method: 'POST',
    data
  })
}

/** 收款新增前判断对应款项是否已经设置 */
export function contractReceiveJudgeAdd(params) {
  return request({
    url: '/contract/receive/judgeAdd',
    method: 'GET',
    params
  })
}

/** 收款新增 */
export function contractReceiveAdd(data) {
  return request({
    url: '/contract/receive/add',
    method: 'POST',
    data
  })
}

/** 合同完成判断 */
export function contractReceiveJudgeOver(params) {
  return request({
    url: '/contract/receive/judgeOver',
    method: 'GET',
    params
  })
}

/** 合同完成 */
export function contractReceiveOver(params) {
  return request({
    url: '/contract/receive/over',
    method: 'GET',
    params
  })
}

/** 收款列表 */
export function contractReceiveList(params) {
  return request({
    url: '/contract/receive/list',
    method: 'GET',
    params
  })
}

/** 供货消息通知 */
export function contractMessage(params) {
  return request({
    url: '/contract/message',
    method: 'GET',
    params
  })
}

/** 添加合同日志 */
export function contractLogAdd(params) {
  return request({
    url: '/contract/log/add',
    method: 'GET',
    params
  })
}

/**  日志列表 */
export function contractLogList(params) {
  return request({
    url: '/contract/log/list',
    method: 'GET',
    params
  })
}

/**  添加评论 */
export function contractCommentAdd(params) {
  return request({
    url: '/contract/comment/add',
    method: 'GET',
    params
  })
}

/**   评论列表 */
export function contractCommentList(params) {
  return request({
    url: '/contract/comment/list',
    method: 'GET',
    params
  })
}

/**  附件列表 */
export function contractFileList(params) {
  return request({
    url: '/contract/file/list',
    method: 'GET',
    params
  })
}
