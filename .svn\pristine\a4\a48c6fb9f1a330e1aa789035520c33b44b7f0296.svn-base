<template>
  <div class="app-container">
    <div class="institution_box">
      <div :class="[{ noBoxRight: userId !== '2661693266692916' }, 'box_left']">
        <el-row class="top" type="flex" align="middle">
          <el-col :span="6">
            <div class="top_left">
              <span class="meeting_icon">
                <img src="@/assets/institution/institution_title_icon.png" alt="" />
                制度管理
              </span>
            </div>
          </el-col>
        </el-row>
        <div class="searchArea">
          <el-tabs v-model="queryInfo.form" @tab-click="getNoticeList">
            <el-tab-pane label="我接收的" name="1"></el-tab-pane>
            <el-tab-pane label="我发布的" name="2"></el-tab-pane>
          </el-tabs>
          <div class="searchFrom">
            <el-form ref="form" label-width="80px" inline>
              <el-form-item label="制度类型:" class="type">
                <el-select v-model="queryInfo.type" placeholder="选择类型" size="small" @change="getNoticeList">
                  <el-option label="全部" :value="null"> </el-option>
                  <el-option label="公共" :value="1"> </el-option>
                  <el-option label="部门" :value="2"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item v-if="queryInfo.form === 2" label="状态:" class="type status">
                <el-select v-model="queryInfo.isRelease" placeholder="选择状态" size="small" @change="getNoticeList">
                  <el-option label="全部" :value="null"> </el-option>
                  <el-option label="未发布" :value="0"> </el-option>
                  <el-option label="已发布" :value="1"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="发布时间:" class="time">
                <el-date-picker v-model="queryInfo.startTime" type="date" size="small" prefix-icon="" placeholder="开始时间" @change="getNoticeList"> </el-date-picker>
                至
                <el-date-picker v-model="queryInfo.endTime" type="date" size="small" prefix-icon="" placeholder="结束时间" @change="endTimeChange"> </el-date-picker>
              </el-form-item>
              <el-form-item class="title" label-width="0">
                <el-input v-model="queryInfo.title" size="small" placeholder="查询制度名称/制度人" maxlength="50" clearable>
                  <el-button slot="append" type="primary" icon="el-icon-search" size="small" @click="getNoticeList"></el-button>
                </el-input>
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div class="contentArea">
          <div class="result">找到{{ total }}个结果</div>
          <div class="table">
            <div v-for="item in list" :key="item.id" :class="[{ releaseBox: item.isRelease === 1, no_releaseBox: item.isRelease === 0 }]">
              <div :class="[{ release: item.isRelease === 1 }, 'table_list_top']" :style="{ paddingTop: item.isRelease === 0 ? '0' : '15px' }">
                <el-row type="flex" justify="space-between" align="middle">
                  <el-col :span="14">
                    <span>{{ item.noticeCode }}</span>
                  </el-col>
                  <el-col :span="item.isRelease === 0 ? 9 : queryInfo.form == 1 ? 4 : 7">
                    <template v-if="item.isRelease === 0 && queryInfo.form == 2">
                      <el-tooltip class="item" effect="light" content="修改" placement="top">
                        <img src="@/assets/meeting/edit.png" alt="" @click.stop="edit(item)" />
                      </el-tooltip>
                      <el-tooltip class="item" effect="light" content="删除" placement="top">
                        <img src="@/assets/meeting/del.png" alt="" @click.stop="del(item)" />
                      </el-tooltip>
                    </template>
                    <template v-else>
                      <span v-if="queryInfo.form == 1 && item.isOneself === 0" @click="addFeedback(item)">反馈</span>
                      <span v-if="queryInfo.form == 2" @click="showFeedBack(item)">反馈列表</span>
                    </template>
                  </el-col>
                </el-row>
                <div v-if="item.isRelease === 0" class="publishButton" @click="publish(item)">发布</div>
                <template v-if="item.isRelease === 1">
                  <div class="look_and_repeal">
                    <div class="showButton" @click="look(item)">查看</div>
                    <div v-if="queryInfo.form == 2" class="showButton repeal" @click="revocation(item)">撤销</div>
                  </div>
                </template>
              </div>
              <div class="table_list_center">
                <el-tooltip :content="item.title" placement="top" effect="light">
                  <div class="title">{{ item.title }}</div>
                </el-tooltip>
                <el-row type="flex" justify="space-between" align="middle">
                  <el-col :span="17">
                    <span>{{ item.createTime }}</span>
                  </el-col>
                  <el-col :span="6">
                    <svg-icon icon-class="institution_table_list_icon" :style="{ color: item.isRead === 1 ? '#3464E0' : '#A3A8BB' }"></svg-icon>
                    <span>
                      <span :style="{ color: item.isRead === 1 ? '#3464E0' : '#A3A8BB' }">{{ item.readCount }}</span>
                      /{{ item.allCount }}
                    </span>
                  </el-col>
                </el-row>
              </div>
              <div class="table_list_bottom">
                <el-row type="flex" justify="space-between" align="middle">
                  <el-col :span="9">
                    <i class="el-icon-user-solid"></i>
                    <span>{{ item.realName }}</span>
                  </el-col>
                  <el-col :span="12">
                    <span>{{ item.type === 1 ? '公司制度' : '部门制度' }}</span>
                    <span>|</span>
                    <span :style="{ color: item.isRelease === 0 ? '#FF7E26' : '#A3A8BB' }">{{ item.isRelease === 0 ? '未发布' : '已发布' }}</span>
                  </el-col>
                </el-row>
              </div>
            </div>
          </div>
          <el-pagination
            v-if="list.length > 0"
            layout="total,prev, pager, next"
            :page-sizes="[5, 10, 15, 20]"
            background
            :total="total"
            :page-size.sync="queryInfo.pageSize"
            :current-page.sync="queryInfo.pageNum"
            @size-change="getNoticeList"
            @current-change="getNoticeList"
          />
          <el-empty v-if="list.length <= 0">
            <template v-slot:image>
              <img src="@/assets/institution/noData.png" alt="" />
            </template>
            <template v-slot:description>
              <img src="@/assets/institution/noData_text.png" alt="" />
            </template>
          </el-empty>
        </div>
      </div>
      <!--  -->
      <div v-if="userId === '2661693266692916'" class="box_right">
        <div class="add_top"></div>
        <el-card class="add_center">
          <div slot="header">
            <span>上传文件</span>
          </div>
          <div>
            <el-form ref="form" :model="formInfo">
              <el-form-item class="title">
                <span style="color: #ec6b5d; margin-right: 10px">*</span> <el-input v-model="formInfo.title" size="small" maxlength="100" placeholder="请输入制度名称" show-word-limit></el-input>
              </el-form-item>
              <el-form-item class="title type">
                <span style="color: #ec6b5d; margin-right: 10px">*</span>
                <el-select v-model="formInfo.type" placeholder="选择制度类型" clearable @change="typeChange">
                  <el-option label="公共" :value="1"> </el-option>
                  <el-option label="部门" :value="2"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item class="title type department">
                <el-select
                  ref="selecteltree"
                  v-model="orgNames"
                  size="small"
                  clearable
                  placeholder="请选择部门"
                  class="organization"
                  :disabled="formInfo.type === 1"
                  multiple
                  @clear="onSelectClear"
                  @focus="getMenuTreeOfParent"
                >
                  <el-option v-for="item in menu" :key="item.id" :label="item.organizationName" :value="item.id" style="display: none" />
                  <el-tree
                    style="padding-left: 15px"
                    :data="treeMenu"
                    node-key="id"
                    empty-text="暂无菜单"
                    highlight-current
                    :expand-on-click-node="false"
                    :props="defaultProps"
                    current-node-key="id"
                    default-expand-all
                    @node-click="handleNodeClick"
                  />
                </el-select>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
        <el-card class="add_bottom">
          <div slot="header">
            <span>添加文件</span>
          </div>
          <div class="uploadFile">
            <!-- <ul class="fileList">
              <li v-for="(item, index) in fileList" :key="item.name">
                <div>
                  <div class="fileImg">
                    <img src="@/assets/meeting/file.png" alt="" />
                    <span v-if="item.fileSize">{{ item.fileSize }}KB</span>
                    <span v-else>{{ item.size | formattingFileSize }}</span>
                  </div>
                  <div>
                    <el-tooltip class="item" effect="dark" :content="item.name" placement="top">
                      <span v-if="!item.flag">{{ item.name }}</span>
                    </el-tooltip>
                    <el-input v-if="item.flag" v-model="fileName" size="small" type="textarea" @blur.stop="uploadingConfirm(index)"></el-input>
                    <i v-if="!item.flag" class="el-icon-edit" @click="rechristen(item, index)"></i>
                  </div>
                </div>
                <div v-if="item.percentage && item.percentage !== 100">
                  <el-progress :percentage="item.percentage"></el-progress>
                </div>
                <div>
                  <span v-if="item.name && item.createTime">{{ realName }}上传于{{ item.createTime | formatDate }}</span>
                  <span v-else>{{ realName }}上传于{{ new Date() | formatDate }}</span>
                  <i class="el-icon-download" @click="downloadFile(item)"></i>
                  <i class="el-icon-delete" @click="delFile(item)"></i>
                </div>
              </li>
            </ul> -->
            <template>
              <!-- <span style="color: #ec6b5d; margin-right: 10px">*</span> -->
              <el-upload
                class="upload-demo"
                drag
                :action="`${actionUrl}/system/upload/file`"
                :headers="header"
                :limit="1"
                :file-list="fileList"
                :on-exceed="uploadExceed"
                :before-upload="beforeUpload"
                :on-success="uploadSuccess"
                :on-progress="uploadProgress"
                :on-remove="uploadRemove"
              >
                <div class="uploadIcon">
                  <i class="el-icon-circle-plus-outline"></i>
                  <span style="margin: 5px 0">点击添加文件</span>
                </div>
                <div class="el-upload__text">支持doc /docx /xls /xlsx /pdf /jpg /png 格式,大小不超过50MB</div>
                <div slot="tip" class="el-upload__tip" style="color: red; line-height: 15px"></div>
              </el-upload>
            </template>
          </div>
        </el-card>
        <div class="submit">
          <span @click="add(1)">发布</span>
          <span @click="add(0)">草稿</span>
        </div>
      </div>
    </div>

    <!-- 反馈dialog -->
    <el-dialog :visible.sync="showDialog" width="790px">
      <div slot="title">意见反馈</div>
      <el-input v-model="feedback" class="feedback" placeholder="请输入反馈" type="textarea" show-word-limit maxlength="200"></el-input>
      <template v-slot:footer>
        <span class="footer_button" @click="showDialog = false">关闭</span>
        <span class="footer_button" style="background: #3464e0; color: #fff; margin-left: 32px" @click="getTrainFeedback">提交</span>
      </template>
    </el-dialog>
    <!-- 反馈列表dialog -->
    <el-dialog :visible.sync="feedbackListDialog" width="790px">
      <div slot="title">查看反馈</div>
      <div v-for="item in feedbackList" :key="item.id" class="feedbackList">
        <div class="list_top">
          <span>{{ item.realName }}</span>
          <span>{{ item.createTime }}</span>
        </div>
        <div class="list_center">{{ item.title }}</div>
        <div class="list_bottom">{{ item.feedback }}</div>
      </div>
      <el-pagination
        v-if="feedbackList.length > 0"
        style="text-align: right; margin-top: 15px"
        layout="total,prev, pager, next"
        :page-sizes="[5, 10, 15, 20]"
        background
        :total="feedbackTotal"
        :page-size.sync="feedbackInfo.pageSize"
        :current-page.sync="feedbackInfo.pageNum"
        @size-change="getNoticeFeedbacklist"
        @current-change="getNoticeFeedbacklist"
      />
      <el-empty>
        <img slot="image" src="@/assets/institution/noData_dialog.png" alt="" />
        <span slot="description">暂无数据</span>
      </el-empty>
    </el-dialog>
  </div>
</template>

<script>
import { noticeList, noticeSave, noticeUpdate, noticeUpdateStatus, noticeRead, noticeAddFeedback, noticeFeedbacklist } from '@/api/institution'
import { getOrganizationTree } from '@/api/organization'
import { mapGetters } from 'vuex'
import { formatDate } from '@/filters'

export default {
  name: 'Institution',
  data() {
    return {
      queryInfo: {
        title: null,
        type: null,
        realName: null,
        startTime: null,
        endTime: null,
        isRelease: null,
        form: '1',
        pageNum: 1,
        pageSize: 8
      },
      list: [],
      total: 0,
      formInfo: {
        title: null,
        content: null,
        attachment: null,
        orgIds: null,
        orgNames: null,
        type: null,
        isRelease: null
      },
      orgNames: [],
      value: null,
      menu: [],
      treeMenu: [],
      defaultProps: {
        label: 'organizationName',
        children: 'children'
      },
      /** 3.上传附件 */
      actionUrl: window.config.VUE_APP_BASE_API,
      fileList: [],
      header: {
        Authorization: null
      },
      fileName: null,
      /** 反馈 */
      showDialog: false,
      feedback: null,
      id: null,
      /** 反馈列表 */
      feedbackListDialog: false,
      feedbackInfo: {
        id: null,
        pageNum: 1,
        pageSize: 4
      },
      feedbackList: [],
      feedbackTotal: null
    }
  },
  computed: {
    ...mapGetters(['name', 'token', 'realName', 'userId']),

    showUpload() {
      return !this.fileList.length
    }
  },
  watch: {
    fileList(val) {
      if (val.length > 0) {
        console.log(document.querySelector('.el-upload__input'))
        document.querySelector('.el-upload__input').disabled = true
      } else {
        document.querySelector('.el-upload__input').disabled = false
      }
    }
  },
  created() {
    this.getNoticeList()
  },
  methods: {
    async getNoticeList() {
      if (this.userId !== '2661693266692916') {
        this.queryInfo.pageSize = 10
      }
      const { data } = await noticeList(this.queryInfo)
      this.list = data.list
      this.total = data.total
      console.log(data)
    },
    endTimeChange(val) {
      this.queryInfo.endTime = formatDate(val, 'yyyy-MM-dd')
      this.queryInfo.endTime = this.queryInfo.endTime + ' 23:59:59'
      this.getNoticeList()
    },
    typeChange(val) {
      if (val === 1) {
        this.formInfo.orgIds = null
        this.formInfo.orgNames = null
      }
      this.orgNames = []
    },
    onSelectClear() {
      this.formInfo.orgIds = null
      this.formInfo.orgNames = null
    },
    getMenuTreeOfParent() {
      getOrganizationTree().then((res) => {
        this.treeMenu = res.data[0].children
        this.menu = this.flattenOrganizations(res.data[0].children)
      })
    },
    flattenOrganizations(data) {
      // 创建一个新的空数组，用来存放扁平化后的对象
      const flatArray = []

      // 定义一个内部递归函数来处理每个对象及其子对象
      function processEntry(entry) {
        // 将当前entry的基础属性添加到扁平数组中
        const { children, ...restOfEntry } = entry
        flatArray.push(restOfEntry)

        // 如果存在子对象，递归地处理它们
        if (children && Array.isArray(children)) {
          children.forEach((child) => processEntry(child))
        }
      }

      // 开始处理传入的数据（顶层对象）
      data.forEach((entry) => processEntry(entry))

      // 返回扁平化后的数组
      return flatArray
    },
    handleNodeClick(node) {
      // this.formInfo.orgIds.push(node.id)
      // this.formInfo.orgNames.push(node.organizationName)
      this.orgNames.push(node.id)
      // this.$refs['selecteltree'].blur()
    },

    uploadExceed(files, fileList) {
      console.log(files, fileList)
    },
    beforeUpload(file) {
      this.header.Authorization = `Bearer ${this.token}`
      console.log(file.type)
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isDOC = file.type === 'application/msword'
      const isDOCX = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      const isXLS = file.type === 'application/vnd.ms-excel'
      const isXLSX = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      const isPDF = file.type === 'application/pdf'
      const isLt2M = file.size / 1024 / 1024 < 50
      if (!isLt2M) {
        this.$message.error('上传附件大小不能超过 50MB!')
      }
      if (!isJPG && !isPNG && !isDOC && !isDOCX && !isXLS && !isXLSX && !isPDF) {
        this.$message.warning('只能上传.doc .docx .xls .xlsx .pdf .jpg .png 格式的附件')
      }
      return (isJPG || isPNG || isDOC || isDOCX || isXLS || isXLSX || isPDF) && isLt2M
    },
    // 上传时的钩子
    uploadProgress(event, file, fileList) {
      console.log(event, file, fileList)
    },
    // 上传成功回调
    uploadSuccess(response, file, fileList) {
      this.fileList = fileList
    },
    // 文件列表移除文件时的钩子
    uploadRemove(file, fileList) {
      this.fileList = fileList
    },
    // 下载文件
    downloadFile(row) {
      if (row.fileId) {
        window.open(row.fileUrl, '_blank')
      } else {
        window.open(row.response.data[0], '_blank')
      }
    },
    // 删除文件
    delFile(row) {
      this.fileList = []
    },
    // 重命名
    rechristen(row, index) {
      console.log(row)
      this.$set(this.fileList[index], 'flag', true)
      this.fileName = row.name
      this.uid = row.uid
      // this.rechristenDialog = true
    },
    // 重命名触发事件
    async uploadingConfirm(index) {
      this.fileList.forEach((item) => {
        if (item.uid === this.uid) {
          item.name = this.fileName
        }
      })
      this.fileList[index].flag = false
      // this.rechristenDialog = false
    },
    async add(isRelease) {
      if (this.formInfo.title && this.formInfo.type) {
        if (this.formInfo.type === 2) {
          this.formInfo.orgIds = this.orgNames.join(',')
          this.formInfo.orgNames = this.menu
            .filter((item) => {
              if (this.orgNames.includes(item.id)) return item
            })
            .map((item) => item.organizationName)
            .join(',')
          if (!this.formInfo.orgIds) {
            return this.$message.warning('发布部门不能为空')
          }
        }
        if (this.formInfo.id) {
          this.formInfo.isRelease = isRelease
          if (this.fileList && this.fileList.length === 1) {
            this.formInfo.attachment = this.fileList[0].url || this.fileList[0].response.data[0]
          } else {
            this.formInfo.attachment = null
          }
          await noticeUpdate(this.formInfo)
          this.$message.success('修改成功')
        } else {
          this.formInfo.isRelease = isRelease
          if (this.fileList && this.fileList.length === 1) {
            this.formInfo.attachment = this.fileList[0].response.data[0]
          }
          await noticeSave(this.formInfo)
          this.$message.success('发布成功')
        }
        this.formInfo = {
          title: null,
          content: null,
          attachment: null,
          orgIds: null,
          orgNames: null,
          type: null,
          isRelease: null
        }
        this.fileList = []
        this.orgNames = []
        this.getNoticeList()
      } else {
        if (this.formInfo.title) {
          this.$message.warning('制度类型不能为空')
        } else {
          this.$message.warning('制度名称不能为空')
        }
      }

      console.log(this.formInfo)
    },
    edit(item) {
      this.formInfo = { ...item }
      if (item.attachment) {
        this.fileList = [{ url: item.attachment, name: `${item.title}附件` }]
      }
      if (item.type === 2) {
        this.orgNames = item.orgIds.split(',').map((item) => parseInt(item))
        console.log(this.orgNames)
        this.getMenuTreeOfParent()
      }
    },
    del(item) {
      this.$confirm('确定要删除吗, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await noticeUpdateStatus({ id: item.id, type: 1 })
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getNoticeList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 发布
    async publish(item) {
      console.log(item)
      await noticeUpdateStatus({ id: item.id, type: 3 })
      this.$message({
        type: 'success',
        message: '发布成功!'
      })
      this.getNoticeList()
    },
    // 撤销
    async revocation(item) {
      await noticeUpdateStatus({ id: item.id, type: 2 })
      this.$message({
        type: 'success',
        message: '撤销成功!'
      })
      this.getNoticeList()
    },
    // 查看
    look(item) {
      console.log(item)
      if (item.attachment) {
        window.open(item.attachment, '_blank')
      } else {
        this.$message.warning('暂无文件可查看')
      }
    },
    // 添加反馈
    async addFeedback(item) {
      const { data } = await noticeRead({ id: item.id })
      this.id = data
      this.feedback = item.feedback
      this.showDialog = true
    },
    // 提交反馈
    async getTrainFeedback() {
      console.log(this.id)
      await noticeAddFeedback({ id: this.id, feedback: this.feedback })
      this.$message.success('添加反馈成功')
      this.showDialog = false
      this.getNoticeList()
    },
    async showFeedBack(item) {
      this.feedbackInfo.id = item.id
      await this.getNoticeFeedbacklist()
      this.feedbackListDialog = true
    },
    async getNoticeFeedbacklist() {
      const { data } = await noticeFeedbacklist(this.feedbackInfo)
      this.feedbackList = data.list
      this.feedbackTotal = data.total
      console.log(data)
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  background-color: #e8eaed;
  padding-left: 0px;
  min-height: 100%;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  .institution_box {
    display: flex;
    justify-content: space-between;
    .box_left {
      position: relative;
      width: 1284px;
      // height: 956px;
      background: #f5f5f5;
      border-radius: 8px 8px 8px 8px;
      // 顶部icon
      .top {
        margin-left: 32px !important;
        margin-top: 16px;
        .top_left {
          display: flex;
          align-items: center;
          .meeting_icon {
            display: flex;
            align-items: center;
            margin-right: 10px;
            font-size: 16px;
            font-family: Microsoft YaHei-Bold, Microsoft YaHei;
            font-weight: bold;
            color: #0b1a44;
            img {
              margin-right: 8px;
            }
          }
        }
      }
      // 搜索区域
      .searchArea {
        margin-top: 16px;
        width: 100%;
        background-color: #fff;
        .searchFrom {
          padding-left: 24px;
          .el-form {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .title {
              float: right;
              margin-right: 37px;
              ::v-deep {
                .el-form-item__content {
                  line-height: initial;
                }
                .el-input {
                  width: 244px;
                  height: 30px;
                  .el-input__prefix,
                  .el-input__suffix {
                    top: 1px !important;
                  }
                }
                .el-input__inner {
                  background-color: #fff;
                  height: 30px;
                  border-radius: 4px 0 0px 4px;
                  &::placeholder {
                    font-size: 14px;
                    font-weight: 400;
                    color: #a3a8bb;
                  }
                }
                .el-input__inner::placeholder {
                  color: #a3a8bb;
                }
                .el-button {
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                  padding: 0;
                }
                .el-input-group__append .el-button,
                .el-input-group__append .el-select,
                .el-input-group__prepend .el-button,
                .el-input-group__prepend .el-select {
                  margin: 0;
                }
                .el-input-group__append,
                .el-input-group__prepend {
                  position: relative;
                  background: #3464e0;
                  height: 30px;
                  width: 34px;
                  padding: 0;

                  border-radius: 0;
                  border: none;
                  .el-icon-search {
                    color: #fff;
                    font-size: 16px;
                  }
                }
              }
            }
          }
        }
      }
      // 内容区域
      .contentArea {
        position: relative;
        padding: 16px 0 45px 44px;
        min-height: 630px;
        .result {
          font-size: 12px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
        .table {
          display: flex;
          flex-wrap: wrap;
          margin-top: 15px;
          min-width: 1200px;
          .releaseBox {
            &:hover {
              cursor: pointer;
              .table_list_top {
                position: relative;
                &::before {
                  content: '';
                  position: absolute;
                  left: 0;
                  top: 0;
                  width: 100%;
                  height: 100%;
                  background: rgba($color: #000000, $alpha: 0.5);
                }
                .showButton {
                  display: block;
                }
              }
            }
          }
          .no_releaseBox {
            &:hover {
              cursor: pointer;
              .table_list_top {
                position: relative;
                .publishButton {
                  display: block;
                }
              }
            }
          }
          & > div {
            width: 260px;
            height: 291px;
            margin-right: 40px;
            margin-bottom: 40px;
            background: #fff;
            border-radius: 8px;
            overflow: hidden;
            &:nth-of-type(4n) {
              margin-right: 0;
            }

            .table_list_top {
              width: 100%;
              height: 138px;
              background: url('../../assets/institution/no_release.png') no-repeat;
              background-size: cover;
              .el-row {
                .el-col-14 {
                  padding-left: 16px;

                  span {
                    font-size: 12px;
                    color: #657081;
                  }
                }
                .el-col-9 {
                  display: flex;
                  align-items: center;
                }
              }
              .publishButton {
                display: none;
                width: 79px;
                height: 30px;
                margin: 0 auto;
                margin-top: 34px;
                line-height: 30px;
                background: #3464e0;
                font-size: 14px;
                font-family: Microsoft YaHei-Bold, Microsoft YaHei;
                font-weight: bold;
                color: #ffffff;
                text-align: center;
                border-radius: 30px;
              }
              .look_and_repeal {
                display: flex;
                justify-content: center;
                .showButton {
                  display: none;
                  position: relative;
                  z-index: 999;
                  width: 79px;
                  height: 30px;
                  // margin: 0 auto;
                  margin-top: 38px;
                  line-height: 30px;
                  border: 1px solid rgba(255, 255, 255, 0.6);
                  font-size: 14px;
                  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
                  font-weight: bold;
                  color: #ffffff;
                  text-align: center;
                  border-radius: 30px;
                  cursor: pointer;
                }
                .repeal {
                  margin-left: 16px;
                  border: 1px solid rgba(255, 133, 120, 0.81);
                  color: #ff8578;
                }
              }
            }
            .release {
              background: url('../../assets/institution/release.png') no-repeat;
              background-size: cover;
              .el-row {
                .el-col-14 {
                  padding-left: 16px;
                  span {
                    font-size: 12px;
                    color: #fff;
                  }
                }
                .el-col-4,
                .el-col-7 {
                  span {
                    text-align: right;
                    color: #fff;
                    font-size: 14px;
                    font-weight: 400;
                    cursor: pointer;
                  }
                }
              }
            }
            .table_list_center {
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              padding: 20px 24px;
              min-height: 104px;
              .title {
                font-size: 14px;
                font-family: Microsoft YaHei-Bold, Microsoft YaHei;
                font-weight: bold;
                color: #0b1a44;
                overflow: hidden;
                text-overflow: ellipsis; //溢出用省略号显示
                display: -webkit-box; // 将对象作为弹性伸缩盒子模型显示。
                // 控制行数
                -webkit-line-clamp: 2; //超出两行隐藏
                -webkit-box-orient: vertical; // 从上到下垂直排列子元素
              }
              .el-row {
                margin-top: 16px;
                font-size: 14px;
                font-weight: 400;
                color: #a3a8bb;

                .el-col-6 {
                  display: flex;
                  align-items: center;
                  & > span {
                    margin-left: 5px;
                  }
                }
              }
            }
            .table_list_bottom {
              padding: 13px 24px;
              border-top: 1px solid #eeeeef;
              .el-row {
                .el-col-9 {
                  i {
                    margin-right: 5px;
                    font-size: 15px;
                    color: #b1bac7;
                  }
                  span {
                    font-size: 13px;
                    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                    font-weight: 400;
                    color: #0b1a44;
                  }
                }
                .el-col-12 {
                  font-size: 12px;
                  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                  font-weight: 400;
                  color: #b1bac7;
                  span {
                    &:nth-of-type(2) {
                      padding: 0 8px;
                    }
                  }
                }
              }
            }
          }
        }
      }
      ::v-deep {
        .el-pagination {
          position: absolute;
          right: 60px;
          bottom: 15px;
        }
        .el-empty {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          .el-empty__image {
            width: 182px;
            height: 108px;
            img {
              width: 100%;
              height: 100%;
            }
          }
        }
        // 更改tabs的样式
        .el-tabs__nav-scroll {
          padding-left: 10px;
          padding-top: 10px;
        }
        .el-tabs__item {
          padding: 0 35px;
          font-size: 16px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #868b9f;
        }
        .el-tabs__item.is-active {
          color: #0b1a44;
        }
        .el-tabs__active-bar {
          width: 87px !important;
          height: 2px;
          left: -10px;
          background: #3464e0;
        }
        .el-tabs__nav-wrap::after {
          height: 1px;
          background: #eeeeef;
        }

        .el-form-item__label {
          padding-right: 0;
          font-size: 14px;
          font-weight: 400;
          color: #868b9f;
        }
        // 修改类型下拉框
        .type {
          .el-input {
            width: 75px;
          }
          .el-input__inner {
            width: 100%;
            background: none;
            border: none;
            opacity: 1 !important;
            color: #0b1a44;
            font-weight: bold;
          }
        }

        // 修改状态下拉
        .status {
          .el-input {
            width: 90px;
          }
        }
        // 修改发布时间组件
        .time {
          .el-input {
            width: 150px;
            border-bottom: 1px solid #b1bac7;
          }
          .el-input__prefix {
            display: none;
          }
          .el-input__inner {
            width: 100%;
            background: none;
            border: none;
            opacity: 1 !important;
            color: #0b1a44;
          }
        }
      }
    }
    .noBoxRight {
      width: 100%;
      .contentArea {
        .table {
          max-width: 1700px;
          & > div {
            margin-right: 70px;
            &:nth-of-type(4n) {
              margin-right: 70px;
            }
            &:nth-of-type(5n) {
              margin-right: 0;
            }
          }
        }
      }
    }
    .box_right {
      width: 457px;
      height: 183px;
      border-radius: 8px;
      opacity: 1;
      .add_top {
        width: 100%;
        height: 139px;
        background: url('../../assets/institution/addInstitution.png') no-repeat;
        background-size: cover;
      }
      .add_center {
        .title {
          ::v-deep {
            .el-form-item__content {
              display: flex;
            }
            .el-input__inner {
              height: 34px;
              background: #f5f5f5;
              border-color: #e9ebef;
              color: #0b1a44;
              &::placeholder {
                font-size: 14px;
                font-weight: 400;
                color: #a3a8bb;
              }
            }
            .el-input__count-inner {
              background: transparent;
              font-size: 14px;
              font-weight: 400;
              color: #a3a8bb;
            }
          }
        }
        .type {
          ::v-deep {
            .el-input__inner {
              width: 154px;
              color: #0b1a44;

              &::placeholder {
                font-size: 14px;
                font-weight: 400;
                color: #0b1a44;
              }
            }
            .organization {
              margin-left: 24px;
            }
          }
        }
        .department {
          ::v-deep {
            .el-select {
              width: 100%;
              margin-left: 15px !important;
              .el-input__inner {
                width: 100% !important;
              }
            }
          }
        }
      }
      .add_bottom {
        .uploadFile {
          display: flex;
          .fileList {
            display: flex;
            li {
              margin-right: 32px;
              padding: 14px 10px 20px 10px;
              width: 268px;
              height: 124px;
              background: #f9f9f9;
              border-radius: 4px 4px 4px 4px;
              border: 1px solid #eeeeef;
              box-sizing: border-box;
              & > div {
                &:first-of-type {
                  display: flex;
                  justify-content: flex-start;
                  align-items: center;
                  .fileImg {
                    display: flex;
                    flex-direction: column;
                    justify-content: flex-start;
                    img {
                      width: 46px;
                      height: 38px;
                    }
                    span {
                      font-size: 12px;
                      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                      font-weight: 400;
                      color: #868b9f;
                      line-height: initial;
                    }
                  }
                  & > div {
                    margin-left: 3px;
                    line-height: initial;
                    .el-textarea {
                      width: initial;
                      .el-textarea__inner {
                        width: 100%;
                      }
                    }

                    &:last-of-type {
                      span {
                        display: inline-block;
                        max-width: 160px;
                        font-size: 14px;
                        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                        font-weight: 400;
                        color: #0b1a44;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                      }
                      i {
                        margin-left: 5px;
                        color: #ff7e26;
                        font-size: 18px;
                        cursor: pointer;
                      }
                    }
                  }
                }
                &:last-of-type {
                  margin-top: 18px;
                  font-size: 12px;
                  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                  font-weight: 400;
                  color: #a3a8bb;
                  line-height: initial;
                  i {
                    font-size: 18px;
                    float: right;
                    margin-left: 15px;
                    cursor: pointer;
                  }
                  .el-icon-download:hover {
                    color: #3464e0;
                  }
                  .el-icon-delete:hover {
                    color: #eb6557;
                  }
                }
              }
            }
          }
        }
        ::v-deep {
          .el-upload-dragger {
            width: 218px;
            height: 150px;
            background: #f9f9f9;
            border-radius: 4px 4px 4px 4px;
            border: 1px solid #eeeeef;
          }
          .el-upload-list--text {
            max-block-size: 420px;
          }
          .uploadIcon {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-top: 37px;
            i {
              font-size: 18px;
              margin-right: 5px;
            }
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #3464e0;
          }
          .el-upload {
            order: 999 !important;
          }
          .el-upload__text {
            padding: 0 10px;
            margin: 0 auto;
            line-height: 25px;
            font-size: 12px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #a3a8bb;
          }
        }
      }
      .el-card {
        margin-top: 8px;
        border-radius: 8px;
        box-shadow: none;
        .el-card__header {
          span {
            font-size: 16px;
            font-family: Microsoft YaHei-Bold, Microsoft YaHei;
            font-weight: bold;
            color: #0b1a44;
          }
        }
      }
      .submit {
        width: 284px;
        margin: 0 auto;
        margin-top: 23px;
        & > span {
          display: inline-block;
          cursor: pointer;
          &:first-of-type {
            width: 130px;
            height: 34px;
            line-height: 34px;
            background: #3464e0;
            border-radius: 4px 4px 4px 4px;
            text-align: center;
            font-size: 14px;
            font-family: Microsoft YaHei-Bold, Microsoft YaHei;
            font-weight: bold;
            color: #ffffff;
          }
          &:last-of-type {
            margin-left: 24px;
            width: 130px;
            height: 34px;
            line-height: 34px;
            background: #e8eaed;
            border-radius: 4px 4px 4px 4px;
            border: 1px solid #657081;
            text-align: center;
            font-size: 14px;
            font-family: Microsoft YaHei-Bold, Microsoft YaHei;
            font-weight: bold;
            color: #657081;
          }
        }
      }
    }
  }

  .feedbackList {
    min-height: 109px;
    margin-bottom: 12px;
    background: #f5f5f5;
    .list_top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 0 4px;
      padding: 0 16px;
      padding-top: 10px;
      padding-bottom: 8px;
      border-bottom: 1px solid #eaebef;
      span {
        &:first-of-type {
          font-size: 14px;
          color: #0b1a44;
        }
        &:last-of-type {
          font-size: 12px;
          color: #868b9f;
        }
      }
    }
    .list_center {
      padding: 0 20px;
      padding-top: 17px;
      padding-bottom: 8px;
      font-size: 14px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #0b1a44;
    }
    .list_bottom {
      padding: 0 20px;
      font-size: 14px;
      color: #0b1a44;
    }
  }
  ::v-deep {
    .el-dialog {
      position: relative;
      border-radius: 4px;
      min-height: 560px;
      .el-dialog__header {
        height: 59px;
        background: #3464e0;
        font-size: 18px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        font-weight: bold;
        border-radius: 4px 4px 0 0;

        color: #ffffff;
        .el-dialog__headerbtn .el-dialog__close {
          color: #fff;
        }
      }
      .el-dialog__body {
        padding: 40px 55px;
        padding-bottom: 32px;
        .feedback {
          .el-textarea__inner {
            height: 347px;
          }
        }
        .el-empty {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          .el-empty__image {
            width: 190px;
            height: 112px;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .el-empty__description {
            span {
              margin-left: -10px;
              font-size: 16px;
              font-weight: 400;
              color: #b1bac7;
            }
          }
        }
      }
      .el-dialog__footer {
        display: flex;
        justify-content: center;
        .footer_button {
          display: inline-block;
          width: 114px;
          height: 38px;
          line-height: 38px;
          background: #f2f4ff;
          border-radius: 4px;
          font-size: 14px;
          font-weight: bold;
          color: #868b9f;
          text-align: center;
          cursor: pointer;
        }
      }
    }
  }
}
@media screen and(min-height:955px) {
  .app-container {
    padding: 10px 20px;
  }
  .app-container .institution_box .box_left .contentArea .table > div {
    margin-bottom: 23px !important;
    // margin-right: 15px !important;
  }
  .app-container .institution_box .box_left .contentArea {
    min-height: 717px;
    padding-bottom: 42px;
  }
  .app-container .institution_box .box_left .top {
    margin-top: 10px !important;
  }
  .app-container .institution_box .box_left .searchArea {
    margin-top: 10px !important;
  }
  .el-form-item {
    margin-bottom: 10px;
  }
}
@media screen and(max-height:955px) {
  .app-container {
    padding: 10px 20px;
  }
  .app-container .institution_box .box_left .contentArea .table {
    display: flex;
    // width: 25%;
  }
  .app-container .institution_box .box_left .contentArea .table > div {
    margin-bottom: 20px !important;
    // margin-right: 15px !important;
  }
  .app-container .institution_box .box_left .top {
    margin-top: 10px !important;
  }
  .app-container .institution_box .box_left .searchArea {
    margin-top: 10px !important;
  }
  .app-container .institution_box .box_left .contentArea {
    min-height: 686px;
    padding-bottom: 42px;
  }
  .app-container .institution_box .box_left .contentArea .table > div {
    height: 280px !important;
  }
  .app-container .institution_box .box_left .contentArea .table > div .table_list_center {
    padding: 15px 24px;
    min-height: 100px;
  }
  .app-container .institution_box .box_left .contentArea .table > div .table_list_bottom {
    padding: 10px 24px;
  }
  .el-form-item {
    margin-bottom: 10px;
  }
}
</style>
