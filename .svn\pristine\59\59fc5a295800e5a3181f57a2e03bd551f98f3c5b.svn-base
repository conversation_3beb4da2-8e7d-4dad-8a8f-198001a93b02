import axios from 'axios'
import store from '@/store'

/**
 * 上传
 * @param {String} key 文件key
 * @param {String|File} file 文件
 * @param {Function} progress 进度回调函数
 */
export async function putProgress(key, file, progress, cancel) {
  const CancelToken = axios.CancelToken
  const source = CancelToken.source()
  const param = new FormData()
  param.append('fileName', key)
  // param.append('url', key)
  param.append('file', file)
  cancel(source)
  return axios.post(window.config.VUE_APP_BASE_API + '/system/upload/file', param, {
    headers: {
      'Content-Type': 'multipart/form-data',
      Authorization: `Bearer ${store.getters.token}`
    },
    onUploadProgress: function (progressEvent) {
      const percent = progressEvent.loaded / progressEvent.total
      if (progress) {
        progress(percent)
      }
    },
    cancelToken: source.token
  })
}
