<template>
  <div class="app-container">
    <div class="project_box">
      <el-row>
        <!-- 搜索查询 -->
        <el-col :span="4" class="nav_title">
          <span>售后记录</span>
          <span class="addButton" @click="showDialog = true">
            <img src="@/assets/project/add_icon.png" alt="" />
            添加售后记录
          </span>
        </el-col>
        <el-col :span="20">
          <el-form ref="queryInfo" :model="queryInfo" label-width="80px" inline class="searchForm">
            <el-form-item label="客户名称:" class="title">
              <el-input v-model="queryInfo.customerName" size="small" placeholder="请输入客户名称" clearable></el-input>
            </el-form-item>
            <el-form-item label="合同名称:" class="contractName">
              <el-input v-model="queryInfo.contractName" size="small" placeholder="请输入合同名称" clearable></el-input>
            </el-form-item>
            <!-- <el-form-item label="负责人名称:" label-width="90px">
            <el-input v-model="queryInfo.realName" size="small" placeholder="请输入负责人名称" clearable></el-input>
          </el-form-item> -->
            <el-form-item label="负责部门:" class="responsible_department">
              <el-select ref="selecteltree" v-model="queryInfo.organizationName" size="small" clearable placeholder="请选择部门" @clear="onSelectClear" @focus="getMenuTreeOfParent">
                <el-option v-for="item in menu" :key="item.id" :label="item.organizationName" :value="item.id" style="display: none" />
                <el-tree style="padding-left: 15px" :data="menu" node-key="id" empty-text="暂无菜单" highlight-current :expand-on-click-node="false" :props="defaultProps" current-node-key="id" default-expand-all @node-click="handleNodeClick" />
              </el-select>
            </el-form-item>
            <el-form-item label="开始时间:" class="date">
              <el-date-picker v-model="date" size="small" type="daterange" range-separator="→" start-placeholder="开始日期" end-placeholder="结束日期" @change="datePickerChange"> </el-date-picker>
            </el-form-item>
            <el-form-item label="项目阶段:" class="responsible_department">
              <el-select v-model="queryInfo.stage" placeholder="请选择项目阶段" size="small" clearable>
                <el-option v-for="item in stageOptions_after" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item class="button">
              <el-button type="primary" size="small" @click="getProjectList"> 查询 </el-button>
              <el-button type="primary" size="small" plain @click="reset">重置 </el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <!-- 表格 -->
      <div class="table">
        <el-table :data="projectTable" style="width: 100%" :header-cell-style="{ background: '#3464e0' }" @row-click="rowClick">
          <el-table-column align="center" prop="code" label="售后编号" width="width"> </el-table-column>
          <el-table-column align="center" prop="contractName" label="合同名称" width="width">
            <template v-slot="{ row }">
              <span style="font-weight: bold">{{ row.contractName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="customerName" label="客户名称" width="width"> </el-table-column>
          <el-table-column align="center" prop="organizationName" label="负责部门" width="width"> </el-table-column>
          <el-table-column align="center" prop="realName" label="项目负责人" width="width"> </el-table-column>
          <el-table-column align="center" prop="stage" label="当前施工阶段" width="250">
            <template v-slot="{ row }">
              <div class="stage">
                <el-row type="flex" align="middle" justify="center" style="position: relative">
                  <el-col :span="8">
                    <img v-if="row.stage === 1" src="@/assets/project/stage_icon.png" alt="" />
                    <i v-else class="el-icon-circle-check"></i>
                  </el-col>
                  <el-col :span="8" style="position: relative">
                    <i v-if="row.stage > 2" class="el-icon-circle-check"></i>
                    <img v-if="row.stage === 2" src="@/assets/project/stage_icon.png" alt="" />
                    <i v-if="row.stage < 2" class="round"></i>
                    <i :class="[{ line2: row.stage === 2, line3: row.stage === 3 }, 'line']"></i>
                  </el-col>
                  <el-col :span="8" style="position: relative">
                    <i v-if="row.stage === 3" class="el-icon-circle-check"></i>
                    <i v-else class="round"></i>
                    <i :class="[{ line3: row.stage === 3 }, 'line']"></i>
                  </el-col>
                </el-row>

                <el-row type="flex" align="middle" justify="center">
                  <el-col :span="8">
                    <span class="text">施工准备中</span>
                  </el-col>
                  <el-col :span="8">
                    <span class="text">施工中</span>
                  </el-col>
                  <el-col :span="8">
                    <span class="text">竣工</span>
                  </el-col>
                </el-row>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="updateTime" label="更新时间" width="width"> </el-table-column>
          <el-table-column align="center" prop="prop" label="操作" width="width" class="operate">
            <template v-slot="{ row }">
              <span class="editOperate" @click.stop="edit(row,1)">编辑项目</span>
              <span class="delOperate" @click.stop="del(row)">删除项目</span>
              <!-- <el-button type="warning" size="small" @click="edit(row)">编辑</el-button>
              <el-button type="danger" size="small" @click="del(row)">删除</el-button> -->
            </template>
          </el-table-column>
        </el-table>
        <el-empty v-if="projectTable.length <= 0">
          <template v-slot:image>
            <img src="@/assets/project/noData_bg1.png" alt="" />
          </template>
          <template v-slot:description>
            <img src="@/assets/project/noData_text1.png" alt="" />
          </template>
        </el-empty>
        <el-pagination v-if="projectTable.length > 0" background style="text-align: right" layout="total, prev, pager, next" :page-sizes="[5, 10, 15, 30]" :total="total" :page-size.sync="queryInfo.pageSize" :current-page.sync="queryInfo.pageNum" @size-change="getProjectList" @current-change="getProjectList" />
      </div>
    </div>

    <!-- 添加的dialog -->
    <el-dialog :title="title" :visible.sync="showDialog" width="856px" :close-on-click-modal="false">
      <el-form ref="addFormRef" class="addFormClass" :model="addFormInfo" label-width="90px" :rules="rules">
        <el-form-item label="合同名称:" prop="contractName" class="contractName">
          <el-autocomplete v-if="keyList.includes('Aftermarket') && addFormInfo.state == 2" v-model="addFormInfo.contractName" placeholder="请输入合同名称,20字以内" :maxlength="20" size="small" :fetch-suggestions="querySearchAsync" @select="handleSelect"></el-autocomplete>
          <el-input v-else v-model="addFormInfo.contractName" placeholder="请输入项目名称,20字以内" size="small" maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="客户名称:" prop="customerName" class="customerName">
          <el-input v-model="addFormInfo.customerName" size="small" placeholder="请输入客户名称,20字以内" maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="项目类型:" prop="state" class="state">
          <el-radio v-model="addFormInfo.state" :label="2">施工</el-radio>
          <el-radio v-model="addFormInfo.state" :label="1">研发</el-radio>
        </el-form-item>
        <el-form-item label="项目负责人:" label-width="99px" prop="userId" class="userId">
          <el-select v-model="addFormInfo.userId" placeholder="请选择项目负责人" size="small" @focus="getUserList">
            <el-option v-for="item in userList" :key="item.userId" :label="item.realName" :value="item.userId"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="项目描述:" class="description">
          <el-input v-model="addFormInfo.description" size="small" type="textarea" placeholder="请输入项目描述,500字以内" maxlength="500"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button type="primary" plain @click="showDialog = false">取 消</el-button>
        <el-button type="primary" @click="addProject">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { projectList, projectSaveProject, projectDelete, secretContractList } from '@/api/project.js'
import { getOrganizationTree } from '@/api/organization'
import { getList } from '@/api/systemUser'
import { mapGetters } from 'vuex'
import { formatDate } from '@/filters'
export default {
  name: 'Aftermarket',
  data() {
    return {
      queryInfo: {
        name: null, // 项目名称
        contractName: null, // 合同名称
        customerName: null, // 客户名称
        realName: null, // 负责人名称
        organizationId: null, // 负责部门
        organizationName: null,
        type: 1,
        startTime: null,
        endTime: null,
        stage: null, // 阶段
        pageNum: 1,
        pageSize: 7
      },
      date: null,
      menu: [],
      defaultProps: {
        label: 'organizationName',
        children: 'children'
      },
      stageOptions: [
        {
          label: '未立项',
          value: 1
        },
        {
          label: '调研中',
          value: 2
        },
        {
          label: '研发中',
          value: 3
        },
        {
          label: '已归档',
          value: 4
        },
        {
          label: '已交付',
          value: 5
        }
      ],
      stageOptions_after: [
        {
          label: '施工准备中',
          value: 1
        },
        {
          label: '施工中',
          value: 2
        },
        {
          label: '竣工',
          value: 3
        }
      ],
      total: null,
      projectTable: [],
      showDialog: false,
      title: '添加项目',
      addFormInfo: {
        contractName: null, // 项目名称
        customerName: null, // 客户名称
        userId: null, // 负责人id
        state: 1, // 项目状态 1 研发 2 施工
        description: null, // 描述
        contractId: null, // 关联合同id
        type: 1 // 类型 1 项目管理 2 售后记录
      },
      rules: {
        contractName: [
          {
            required: true,
            tigger: 'blur',
            message: '项目名称不能为空'
          }
        ],
        customerName: [
          {
            required: true,
            tigger: 'blur',
            message: '客户名称不能为空'
          }
        ],
        userId: [
          {
            required: true,
            tigger: 'change',
            message: '负责人不能为空',
            type: 'string' || 'number'
          }
        ],
        state: [
          {
            required: true,
            tigger: 'change',
            message: '项目类型不能为空',
            type: 'number'
          }
        ]
      },
      userList: [],
      type: 1,
      secretContractList: [],
      timeout: null
    }
  },
  computed: {
    ...mapGetters(['organizationId', 'keyList'])
  },
  mounted() {
    if (this.keyList.includes('Aftermarket')) {
      this.type = 2
      this.queryInfo.type = 2
      this.addFormInfo.type = 2
      this.addFormInfo.state = 2
      this.title = '添加售后记录'
    }
    this.getProjectList()
  },
  methods: {
    async getProjectList() {
      const { data } = await projectList(this.queryInfo)
      this.projectTable = data.list
      this.total = data.total
    },
    datePickerChange(val) {
      if (val) {
        this.queryInfo.startTime = formatDate(val[0])
        this.queryInfo.endTime = formatDate(val[1], 'yyyy-MM-dd') + ' 23:59:59'
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getProjectList()
    },
    reset() {
      this.queryInfo = {
        name: null, // 项目名称
        customerName: null, // 客户名称
        realName: null, // 负责人名称
        organizationId: null, // 负责部门
        organizationName: null,
        type: this.type,
        startTime: null,
        endTime: null,
        stage: null, // 阶段
        pageNum: 1,
        pageSize: 7
      }
      this.getProjectList()
    },
    addProject() {
      this.$refs['addFormRef'].validate(async (val) => {
        if (val) {
          await projectSaveProject(this.addFormInfo)
          if (this.addFormInfo.type === 2) {
            this.$message.success('添加售后记录成功')
          } else {
            this.$message.success('添加项目成功')
          }
          this.showDialog = false
          this.getProjectList()
        }
      })
    },
    edit(row, type) {
      this.$router.push(`/project/aftermarket_details/${row.id}/${type}`)
    },
    del(row) {
      this.$confirm('确定要删除吗, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await projectDelete({ id: row.id, type: this.type })
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getProjectList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 获取当前部门下的用户
    async getUserList() {
      const { data } = await getList({ pageNum: 1, pageSize: 200, organizationId: this.organizationId })
      this.userList = data.list
    },
    onSelectClear() {
      this.queryInfo.organizationId = null
    },
    getMenuTreeOfParent() {
      getOrganizationTree().then((res) => {
        this.menu = res.data
      })
    },
    handleNodeClick(node) {
      this.queryInfo.organizationName = node.organizationName
      this.queryInfo.organizationId = node.id
      this.$refs['selecteltree'].blur()
    },

    querySearchAsync(queryString, cb) {
      clearTimeout(this.timeout)

      this.timeout = setTimeout(async () => {
        const { data } = await secretContractList({ name: this.addFormInfo.contractName })
        this.secretContractList = data
        const cbList = []
        this.secretContractList.forEach((item) => {
          cbList.push({
            value: item.contractName,
            id: item.contractId
          })
        })
        cb(cbList)
      }, 1000 * Math.random())
    },
    handleSelect(item) {
      this.addFormInfo.contractId = item.id
    },
    rowClick(row, column, event) {
      this.edit(row, 0)
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  background: #e8eaed;
  min-height: 100%;
  box-shadow: none;
}
.project_box {
  border-radius: 8px;
  padding-top: 56px;
  padding-left: 37px;
  padding-right: 37px;
  padding-bottom: 15px;
  min-height: 800px;
  background: #f5f5f5;
  .nav_title {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-left: 19px !important;
    span {
      &:first-of-type {
        font-size: 18px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        font-weight: bold;
        color: #0b1a44;
      }
    }
    .addButton {
      display: block;
      margin-top: 23px;
      width: 126px;
      height: 38px;
      line-height: 38px;
      background: #3464e0;
      border-radius: 4px 4px 4px 4px;
      text-align: center;
      font-size: 16px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #ffffff;
      cursor: pointer;
      &:hover {
        background: #355fce;
      }
    }
  }

  .table {
    position: relative;
    padding-bottom: 15px;
    background: #fff;
    padding-bottom: 30px;
    .code {
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #0b1a44;
    }
    .name {
      // width: 300px; //设置宽
      display: flex !important;
      align-items: center;
      justify-content: flex-start;
      font-size: 14px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      color: #0b1a44;
      margin-right: 5px;
      text-align: left;

      img {
        margin-right: 5px;
      }
      span {
        font-weight: bold;
        overflow: hidden; //超出隐藏
        text-overflow: ellipsis; //溢出用省略号显示
        display: -webkit-box; // 将对象作为弹性伸缩盒子模型显示。
        // 控制行数
        -webkit-line-clamp: 2; //超出两行隐藏
        -webkit-box-orient: vertical; // 从上到下垂直排列子元素
      }
    }
    .stage {
      .round {
        display: inline-block;
        width: 10px;
        height: 10px;
        background: #b0b8c1;
        border-radius: 50%;
      }
      i {
        position: relative;
        z-index: 1;
      }
      img {
        position: relative;
        z-index: 1;
      }
      .el-icon-circle-check {
        font-size: 26px;
        color: #2ca454;
        background-color: #fff;
      }
      .line {
        position: absolute;
        top: 11px;
        left: -42px;
        z-index: 0;
        width: 84px;
        height: 2px;
        opacity: 0.69;
        background: #b0b8c1;
      }
      .line2 {
        top: 17px;
        background: #23bb87;
      }
      .line3 {
        top: 13px;
        background: #23bb87;
      }
      .text {
        font-size: 12px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #0b1a44;
      }
    }
    .editOperate {
      display: inline-block;
      width: 72px;
      height: 28px;
      line-height: 28px;
      background: rgba(255, 126, 38, 0.13);
      border-radius: 2px 2px 2px 2px;
      border: 1px solid rgba(255, 126, 38, 0.17);
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #ff7e26;
      text-align: center;
      cursor: pointer;
    }
    .delOperate {
      display: inline-block;
      margin-left: 16px;
      width: 72px;
      height: 28px;
      line-height: 28px;
      background: rgba(235, 101, 87, 0.13);
      border-radius: 2px 2px 2px 2px;
      border: 1px solid rgba(235, 101, 87, 0.17);
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #ff7e26;
      text-align: center;
      cursor: pointer;
    }
    ::v-deep {
      .el-table {
        border-radius: 6px;
        min-height: 625px;
        margin-bottom: 15px;
        &::before {
          display: none;
        }
      }
      .el-table__empty-block {
        display: none;
      }
      // 去掉表格身体背景样式
      .el-table tr {
        background: transparent;
      }

      // 更改表头单元格样式
      .el-table th.el-table__cell > .cell {
        font-size: 14px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        font-weight: 400;
        color: #fff;
      }
      // 去掉单元格边框
      // .el-table td.el-table__cell,
      // .el-table th.el-table__cell.is-leaf {
      //   border: none;
      // }
      // 每行鼠标经过得样式
      .el-table .el-table__row {
        height: 72px;
        background: #fff;
        border-radius: 6px;
        cursor: pointer;
      }
      .el-table td.el-table__cell div {
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #0b1a44;
      }
      .el-pagination {
        position: absolute;
        right: 0;
        bottom: 15px;
      }
      .el-empty {
        position: absolute;
        top: 49%;
        left: 50%;
        transform: translate(-50%, -50%);

        display: flex;
        flex-direction: column;
        align-items: center;
        .el-empty__image {
          width: 258px;
          height: 140px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .el-empty__description{
          img{
          width: 104px;

          }
        }
      }
    }
  }
  .searchForm {
    position: relative;
    .title {
      ::v-deep {
        .el-form-item__content {
          .el-input {
            width: 284px;
            height: 36px;
            width: 284px;
            .el-input__inner {
              height: 36px;
              border: 1px solid #eeeeef;
            }
          }
        }
      }
    }
    .contractName {
      ::v-deep {
        .el-form-item__content {
          .el-input {
            width: 264px;
            height: 36px;
            .el-input__inner {
              height: 36px;
              background: #ffffff;
              border: 1px solid #eeeeef;
            }
          }
        }
      }
    }
    .responsible_department {
      ::v-deep {
        .el-form-item__content {
          .el-input {
            width: 168px;
            height: 36px;
            .el-input__inner {
              height: 36px;

              background: #ffffff;
              border: 1px solid #eeeeef;
            }
          }
        }
      }
    }
    .date {
      ::v-deep {
        .el-form-item__content {
          .el-date-editor--daterange.el-input,
          .el-date-editor--daterange.el-input__inner,
          .el-date-editor--timerange.el-input,
          .el-date-editor--timerange.el-input__inner {
            width: 274px;
            height: 36px;
            background: #ffffff;
            border-radius: 4px 4px 4px 4px;
          }
          .el-range-editor--small .el-range__close-icon,
          .el-range-editor--small .el-range__icon {
            line-height: 31px;
          }
          // .el-range-input {
          //   background-color: #fff;
          // }
        }
      }
    }
    .button {
      position: absolute;
      right: 37px;
      // margin-right: 45px;
      ::v-deep {
        .el-button--primary.is-plain {
          border-color: #3464e0;
          background: transparent;
          color: #3464e0;
          &:active {
            background: transparent !important;
          }
        }
        .el-button--primary.is-plain:focus,
        .el-button--primary.is-plain:hover {
          color: #355fce !important;
          border-color: #355fce !important;
          background-color: transparent;
        }
        .el-button--primary:focus,
        .el-button--primary:hover {
          background-color: #355fce;
        }
      }
    }
    .processName {
      // display: flex;
      ::v-deep {
        .el-radio-button {
          margin-bottom: 10px;
        }
        .el-radio-button .el-radio-button__inner {
          background: transparent;
        }
        .el-radio-button__inner {
          padding: 8px 14px;
          border-color: transparent;
          border-radius: 4px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
        }
        .el-radio-button__orig-radio:checked + .el-radio-button__inner {
          border-color: #3464e0;
          color: #3464e0;
        }
      }
    }
    .status {
      ::v-deep {
        .el-radio-button {
          margin-bottom: 0;
        }
      }
    }
    ::v-deep {
      .el-input__inner {
        border: 1px solid #eeeeef;
        &::placeholder {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #b1bac7;
        }
      }
    }
  }
  ::v-deep {
    .el-form-item__label {
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #0b1a44;
    }
    .el-empty__image {
      width: 298px;
      height: 188px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .el-empty__description {
      img {
        width: 74px;
        height: 21px;
      }
    }
  }
}
::v-deep {
  .el-dialog {
    border-radius: 8px;
    .el-dialog__header {
      font-size: 18px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #0b1a44;
      border-bottom: 8px solid #eff5ff;
    }
    .el-dialog__body {
      padding-left: 48px;
      padding-right: 58px;
    }
    .el-dialog__headerbtn .el-dialog__close {
      font-size: 18px;
      color: #0b1a44;
    }

    .el-dialog__footer {
      padding-right: 58px;
    }
    // 以下是表单样式
    .el-form-item {
      margin-bottom: 25px;
    }
    .el-form-item__label {
      font-size: 14px;
      font-weight: 400;
      color: #0b1a44;
    }
    .contractName,
    .customerName {
      .el-input__inner {
        &::placeholder {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #b1bac7;
        }
        width: 430px;
        height: 40px;
        border: 1px solid #d8dbe1;
      }
    }
    .state {
        .el-radio__input.is-checked .el-radio__inner {
          // background: #3464e0;
          background: #fff;
          width: 18px;
          height: 18px;
          border-color: #3464e0;
          // border: none;
          &::after {
            background-color: #3464e0;
            width: 8px;
            height: 8px;
          }
        }
        .el-radio__inner {
          width: 18px;
          height: 18px;
        }
        .el-radio__input.is-checked + .el-radio__label {
          color: #0b1a44;
        }
        .el-radio__label {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #657081;
        }
    }
    .userId {
      .el-input {
        width: 193px;
        height: 40px;
        .el-input__inner {
          width: 193px;
          height: 40px;
          background: #f5f5f5;
          color: #0b1a44;
          &::placeholder {
            color: #0b1a44;
          }
        }
      }
    }
    .description {
      .el-textarea {
        width: 654px;
        height: 229px;
        .el-textarea__inner {
          width: 654px;
          height: 229px;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #d8dbe1;
          &::placeholder {
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #b1bac7;
          }
        }
        .el-textarea .el-input__count {
          font-size: 12px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #b1bac7;
        }
      }
    }
  }
}
.addFormClass {
  ::v-deep {
    .el-input,
    .el-textarea {
      width: 250px;
    }
  }
}
@media screen and(min-height:955px) {
  .app-container {
    padding: 15px 20px;
    .project_box {
      padding-top: 45px;
    }
  }
}
@media screen and(max-height:955px) {
  .app-container {
    padding-top: 15px;
    padding-bottom: 10px;
    .project_box {
      padding-top: 30px;
    }
    ::v-deep {
      .el-form-item {
        margin-bottom: 17px !important;
      }
    }
  }
}
</style>
