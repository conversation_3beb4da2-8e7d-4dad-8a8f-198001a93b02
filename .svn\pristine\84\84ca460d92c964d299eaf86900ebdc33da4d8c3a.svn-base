<template>
  <div class="app-container">
    <!-- 搜索查询 -->
    <el-row style="min-width: 1600px; margin-bottom: 20px">
      <el-col :span="24">
        <el-form ref="form" :model="searchForm" inline>
          <el-form-item label="用户名:" label-width="60px">
            <el-input v-model="searchForm.username" size="small" placeholder="请输入用户名" clearable />
          </el-form-item>
          <el-form-item label="性别:">
            <el-select v-model="searchForm.sex" placeholder="请选择" size="small" clearable>
              <el-option v-for="item in sexOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="联系方式:">
            <el-input v-model="searchForm.mobile" size="small" placeholder="请输入联系方式" clearable />
          </el-form-item>
          <el-form-item label="部门:" label-width="80px">
            <el-select ref="selecteltree" v-model="searchForm.organizationName" size="small" clearable placeholder="请选择部门" @clear="onSelectClear" @focus="getMenuTreeOfParent">
              <el-option v-for="item in menu" :key="item.id" :label="item.organizationName" :value="item.id" style="display: none" />
              <el-tree style="padding-left: 15px" :data="menu" node-key="id" empty-text="暂无菜单" highlight-current :expand-on-click-node="false" :props="defaultProps" current-node-key="id" default-expand-all @node-click="handleNodeClick" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="success" size="small" @click="searchUser">查询</el-button>
            <el-button type="primary" size="small" @click="addUser">添加用户</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <!-- 列表展示 -->
    <el-table v-loading="listLoading" :data="list" element-loading-text="Loading" border fit highlight-current-row>
      <el-table-column label="序号" align="center" width="120" type="index" />
      <el-table-column label="用户id" align="center" prop="userId" />
      <el-table-column label="用户名" align="center" prop="username" />
      <el-table-column label="姓名" align="center" prop="realName" />
      <el-table-column label="性别" align="center" prop="sexText" />
      <el-table-column label="联系方式" align="center" prop="mobile" />
      <el-table-column label="部门" align="center" prop="organizationName" />
      <el-table-column label="操作" align="center" width="400">
        <template v-slot="scope">
          <el-button size="small" type="warning" @click="editUser(scope.row)">修改用户</el-button>
          <el-button size="small" type="danger" @click="delUser(scope.row)">删除用户</el-button>
          <el-button size="small" type="primary" @click="resetPasswords(scope.row)">重置密码</el-button>
          <el-button size="small" type="success" @click="RoleAllot(scope.row)">分配角色</el-button>
          <!-- <el-button size="small" type="info">停用</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-pagination style="text-align: center; margin-top: 15px" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 15, 20, 30]" :total="total" :page-size.sync="page.pageSize" :current-page.sync="page.pageNum" @size-change="getTabList(page)" @current-change="getTabList(page)" />
    <!-- 添加用户弹窗 -->
    <improvement ref="improvement" :show-dialog.sync="showDialog" @addSuccess="getTabList(page)" />
    <!-- 分配角色弹窗 -->
    <allotRole ref="allotRole" :show-role-dialog.sync="showRoleDialog" :user-id="userId" />
  </div>
</template>

<script>
import { getList, delUser, passwordReset } from '@/api/systemUser'
import { getOrganizationTree } from '@/api/organization'

import improvement from './components/improvement'
import allotRole from './components/allot-role'
export default {
  name: 'SystemUser',
  components: {
    improvement,
    allotRole
  },
  data() {
    return {
      list: null,
      listLoading: true,
      page: {
        pageSize: 10,
        pageNum: 1
      },
      total: 0,
      searchForm: {
        mobile: null,
        username: '',
        sex: null,
        organizationName: ''
      },
      menu: [],

      defaultProps: {
        label: 'organizationName',
        children: 'children'
      },
      sexOptions: [
        {
          label: '男',
          value: 'M'
        },
        {
          label: '女',
          value: 'F'
        }
      ],
      organization: [],
      showDialog: false,
      showRoleDialog: false,
      userId: null
    }
  },
  created() {
    this.getTabList(this.page)
  },
  methods: {
    getTabList(data) {
      this.listLoading = true
      getList(data).then((response) => {
        console.log(response)
        this.list = response.data.list
        this.total = response.data.total
        this.listLoading = false
      })
    },
    // 添加用户
    addUser() {
      this.showDialog = true
    },
    editUser(row) {
      console.log(row)
      this.$refs['improvement'].showData(row)
      this.showDialog = true
    },
    async delUser(row) {
      try {
        await this.$confirm('确定要删除该用户吗, 是否继续?', '删除用户', {
          type: 'warning'
        })
        await delUser(row.userId)
        this.getTabList(this.page)
        this.$message.success('删除成功')
      } catch (err) {
        return new Error(err)
      }
      console.log(row)
    },
    // 获取简易组织列表
    // getOrganizationEsayList() {
    //   getOrganizationEsayList()
    //     .then((res) => {
    //       console.log(res)
    //       this.organization = res.data
    //     })
    //     .catch((err) => {
    //       console.log(err)
    //     })
    // },
    // 密码重置
    async resetPasswords(row) {
      try {
        await this.$confirm('确定要重置密码吗，密码为 111111 ', '重置密码', {
          type: 'warning'
        })
        const res = await passwordReset(row.userId)
        this.$message.success('密码重置成功')
        console.log(res)
      } catch (err) {
        console.log(err)
      }
    },
    // 查询用户
    searchUser() {
      this.getTabList({ ...this.searchForm, ...this.page })
      // this.searchForm = {
      //   mobile: null,
      //   username: '',
      //   realName: '',
      //   sex: null,
      //   organizationName: ''
      // }
    },
    sex(row, column, cellValue, index) {
      const sex = [
        {
          label: '男',
          value: 'M'
        },
        {
          label: '女',
          value: 'F'
        }
      ]
      const data = sex.map((item) => {
        return cellValue === item.value
      })
      return data.label
    },
    // 分配角色
    RoleAllot(row) {
      console.log(row)
      this.userId = row.userId
      this.$refs['allotRole'].getUserDetailById(row.userId)
      this.showRoleDialog = true
    },
    onSelectClear() {
      this.searchForm.organizationId = null
    },
    getMenuTreeOfParent() {
      getOrganizationTree().then((res) => {
        this.menu = res.data
        console.log(this.menu)
      })
    },
    handleNodeClick(node) {
      this.searchForm.organizationId = node.id
      this.searchForm.organizationName = node.organizationName
      this.$refs['selecteltree'].blur()
      console.log(node)
    }
  }
}
</script>
