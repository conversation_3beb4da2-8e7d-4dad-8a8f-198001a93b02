import router from './router'
import store from './store'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getToken } from '@/utils/auth' // get token from cookie
import getPageTitle from '@/utils/get-page-title'
NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = ['/login', '/demo'] // no redirect whitelist

router.beforeEach(async(to, from, next) => {
  // start progress bar
  NProgress.start()

  // set page title
  document.title = getPageTitle(to.meta.title)

  // 判断用户是否已经登录
  const hasToken = getToken()
  if (hasToken) {
    if (to.path === '/login') {
      // 有token并且访问的是login
      next({ path: '/' })
    } else {
      // 判断用户是否获取信息
      if (store.getters.name) {
        next()
      } else {
        try {
          // get user info
          const res = await store.dispatch('user/getInfo')
          const routes = await store.dispatch('permission/filterRoutes', res.keyList)
          router.addRoutes(routes) // 添加动态路由到路由表
          next(to.path)
        } catch (error) {
          // 删除token，强制跳转到登录页面
          await store.dispatch('user/logout')
          next('/login')
          NProgress.done()
        }
      }
    }
  } else {
    /* has no token*/
    if (whiteList.indexOf(to.path) !== -1) {
      // 在白名单上直接跳转
      next()
    } else {
      // 其他没有访问权限的页面被重定向到登录页面.
      next('/login')
      NProgress.done()
    }
    NProgress.done() // 解决手动切换地址时 进度条不关闭的问题
  }
  // console.log(hasToken)
  // if (hasToken) {
  //   if (to.path === '/login') {
  //     // 有token并且访问的是login
  //     next({ path: '/' })
  //     NProgress.done()
  //   } else {
  //     // 是否获取用户信息
  //     const hasGetUserInfo = store.getters.name
  //     debugger
  //     if (hasGetUserInfo) {
  //       next()
  //     } else {
  //       try {
  //         // get user info
  //         await store.dispatch('user/getInfo')

  //         next()
  //       } catch (error) {
  //         // 删除token，强制跳转到登录页面
  //         await store.dispatch('user/resetToken')
  //         Message.error(error || 'Has Error')
  //         next(`/login?redirect=${to.path}`)
  //         NProgress.done()
  //       }
  //     }
  //   }
  // } else {
  //   /* has no token*/
  //   if (whiteList.indexOf(to.path) !== -1) {
  //     // 在白名单上直接跳转
  //     next()
  //   } else {
  //     // 其他没有访问权限的页面被重定向到登录页面.
  //     next(`/login?redirect=${to.path}`)
  //     NProgress.done()
  //   }
  // }
  NProgress.done()
})

router.afterEach(() => {
  // finish progress bar
  NProgress.done()
})
