<template>
  <div class="login-container">
    <div class="login_left">
      <swiper ref="swiper" :options="swiperOption" class="van-swipe">
        <swiper-slide class="one">
          <div class="slideImg">
            <!-- <img v-if="isText1" src="@/assets/login/slideshowTitle1.png" alt="" class="slideshow_text" /> -->
            <img v-show="isText" src="@/assets/login/slideshowTitle1.png" alt="" class="slideshow_text" />
          </div>
        </swiper-slide>
        <swiper-slide>
          <div class="slideImg">
            <img v-show="isText" src="@/assets/login/slideshowTitle2.png" alt="" class="slideshow_text1" />
          </div>
        </swiper-slide>
        <swiper-slide class="tree">
          <div class="slideImg">
            <img v-show="isText" src="@/assets/login/slideshowTitle3.png" alt="" class="slideshow_text2" />
          </div>
        </swiper-slide>
        <!-- <swiper-slide>
          <img src="@/assets/女生 白色衬衫 耳机 房间 书桌 4k动漫壁纸_彼岸图网.png" alt="" />
        </swiper-slide> -->
      </swiper>
      <div class="swiper-pagination"></div>
    </div>
    <div class="login_right">
      <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" auto-complete="on" label-position="left">
        <div class="title-container">
          <img src="@/assets/login/logo.png" alt="" />
          <img src="@/assets/login/title.png" alt="" class="titleImg" />
          <img src="@/assets/login/text.png" alt="" />
        </div>

        <el-form-item prop="username">
          <el-input ref="username" v-model="loginForm.username" prefix-icon="el-icon-user" placeholder="用户名" name="userName" type="text" tabindex="1" auto-complete="on" @focus="showStyle" />
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            :key="passwordType"
            ref="password"
            v-model="loginForm.password"
            prefix-icon="el-icon-lock"
            :type="passwordType"
            placeholder="密码"
            name="password"
            tabindex="2"
            auto-complete="on"
            @keyup.enter.native="handleLogin"
          />
          <span class="show-pwd" @click="showPwd">
            <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
          </span>
        </el-form-item>
        <!-- <el-checkbox v-model="checked">下次自动登录</el-checkbox> -->
        <el-button :loading="loading" type="primary" class="loginButton" @click.native.prevent="handleLogin">登录</el-button>
      </el-form>
    </div>
  </div>
</template>

<script>
// import store from '@/store'
import { mapActions } from 'vuex'
// 引入插件
import { swiper, swiperSlide } from 'vue-awesome-swiper'
import anime from 'animejs/lib/anime.js'
import '@/styles/swiper.css'
export default {
  name: 'Login',
  data() {
    const that = this
    return {
      loginForm: {
        username: '',
        password: ''
      },
      components: {
        swiper,
        swiperSlide
      },
      loginRules: {
        username: [{ required: true, trigger: 'blur', message: '用户名不能为空' }],
        password: [{ required: true, trigger: 'blur', message: '密码不能为空' }]
      },
      loading: false,
      passwordType: 'password',
      redirect: undefined,
      checked: true,
      swiperOption: {
        observer: true, // 修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, // 修改swiper的父元素时，自动初始化swiper
        speed: 600, // 切换速度
        autoplay: {
          delay: 5000, // 自动切换的时间间隔
          disableOnInteraction: false //  用户操作swiper之后，是否禁止autoplay。默认为true：停止。
        },
        // pauseOnMouseEnter: true, // 鼠标置于swiper时暂停自动切换，鼠标离开时恢复自动切换
        loop: true, // 无限循环
        simulateTouch: false, // 在pc端禁止滑动
        // effect: 'fade', // 设置Slide的切换效果
        pagination: {
          el: '.swiper-pagination',
          clickable: true // 点击分页器的指示点分页器会控制Swiper切换
        },
        on: {
          slideChangeTransitionEnd() {
            if (this.realIndex === 0) {
              document.querySelectorAll('.one')[1].querySelector('.slideshow_text').style = 'display:block'
            }
            that.Animate()
          },
          transitionStart() {
            if (this.realIndex === 0) {
              that.$nextTick(() => {
                document.querySelectorAll('.one')[1].querySelector('.slideshow_text').style = 'display:none'
              })
            }
            that.isText = false
          }
        }
      },
      flag: true,
      timer: null,
      isText: true
    }
  },

  watch: {
    // $route: {
    //   handler: function(route) {
    //     this.redirect = route.query && route.query.redirect
    //   },
    //   immediate: true
    // }
  },
  created() {},
  mounted() {
    const that = this
    that.setHeight()
    // this.Animate()
    window.onresize = () => {
      return (() => {
        if (that.timer) {
          clearTimeout(that.timer)
        }
        that.timer = setTimeout(() => {
          that.setHeight()
        }, 500)
      })()
    }
  },
  methods: {
    ...mapActions('user', ['login', 'getInfo']),

    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true
          // this.$store
          //   .dispatch('user/login', this.loginForm)
          //   .then(() => {
          //     this.$router.push({ path: this.redirect || '/' })
          //     this.loading = false
          //   })
          //   .catch(() => {
          //     this.loading = false
          //   })
          this.login(this.loginForm)
            .then(() => {
              // this.getInfo().then(async(res) => {
              //   // const routes = await store.dispatch('permission/filterRoutes', res.keyList)
              //   // this.$router.addRoutes(routes) // 添加动态路由到路由表

              // })
              this.$router.push('/')
              // 成功之后跳转页面
              this.loading = false
              this.$message.success('登录成功')
            })
            .catch((err) => {
              console.log(err)

              this.loading = false
            })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    showStyle(event) {
      console.log(event.target.style)
      event.target.style = 'borderColor:red !important'
    },
    setHeight(e) {
      console.log()
      document.querySelectorAll('.slideImg').forEach((item) => {
        item.style.height = `${document.body.clientHeight}px`
      })
      document.querySelector('.login_left').style.height = `${document.body.clientHeight}px`
    },
    Animate() {
      const that = this
      anime({
        targets: ['.slideshow_text', '.slideshow_text1', '.slideshow_text2'],
        translateY: [300, 0],
        // opacity: [0, 1],
        easing: 'easeInOutExpo',
        duration: 800, // 动画时间
        // delay: anime.stagger(100), // 每个元素的延迟增加100毫秒。
        begin: function () {
          that.isText = true
        }
      })
    }
  }
}
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg: transparent;
$light_gray: #000;
$cursor: #000;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.login-container {
  .el-input {
    display: inline-block;
    height: 47px;
    width: 100%;
    .el-input__prefix {
      color: #b1b9c7;
      font-size: 18px;
    }
    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding-left: 42px;
      color: $light_gray;
      height: 47px;
      caret-color: $cursor;
      border-bottom: 2px dotted #889aa4;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px $bg inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
      &:focus + .el-input__prefix {
        color: #3464e0;
      }
      &:focus {
        border-bottom: 1px solid #3464e0;
      }
    }
  }
  .el-checkbox__inner {
    width: 18px;
    height: 18px;
    &::after {
      top: 3px;
      left: 7px;
    }
  }
  .el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #3464e0;
    border-color: #3464e0;
  }
  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #666666;
  }
  .loginButton {
    display: block;
    margin-top: 18px;
    width: 133px;
    height: 50px;
    font-size: 16px;
    font-family: Microsoft YaHei-Bold, Microsoft YaHei;
    font-weight: bold;
    color: #ffffff;
  }
  // .el-form-item {
  //   border-bottom: 2px dotted #889aa4;
  // }
}
</style>

<style lang="scss" scoped>
$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #eee;

.login-container {
  display: flex;
  min-height: 100%;
  width: 100%;
  overflow: hidden;
  .login_left {
    flex: 1;
    position: relative;
    // max-width: 1200px;
    // width: 1200px;
    overflow: hidden;

    ::v-deep {
      .swiper-wrapper {
        .swiper-slide {
          .slideImg {
            position: relative;
            width: 100%;
            background: url('../../assets/login/login_left_bg1.png') no-repeat;
            background-size: 100% 100%;
            img {
              position: absolute;
              bottom: 200px;
              left: 115px;
            }
          }
        }
        .one {
          .slideImg {
            background: url('../../assets/login/login_left_bg.png') no-repeat;
            background-size: 100% 100%;
          }
        }
        .tree {
          .slideImg {
            background: url('../../assets/login/login_left_bg2.png') no-repeat;
            background-size: 100% 100%;
          }
        }
      }
    }
    .swiper-pagination {
      position: absolute;
      bottom: 146px;
      left: 115px;
      ::v-deep {
        .swiper-pagination-bullet {
          width: 83px;
          height: 2px;
          margin-right: 9px;
          background: #ffffff;
          border-radius: 1px 1px 1px 1px;
          opacity: 0.17;
        }
        .swiper-pagination-bullet-active {
          width: 83px;
          height: 2px;
          background: #ffffff;
          border-radius: 1px 1px 1px 1px;
          opacity: 1;
        }
      }
    }
    // background: url('../../assets/login/login_left_bg.png') no-repeat;
    // background-size: 100% 100%;
  }
  .login_right {
    width: 716px;
    background: url('../../assets/login/login_right_bg.png') no-repeat;
    background-size: 100% 100%;
  }
  .login-form {
    position: relative;
    width: 520px;
    max-width: 100%;
    padding: 160px 35px 0;
    margin: 0 auto;
    overflow: hidden;
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;
    margin-bottom: 70px;
    img {
      display: block;
    }
    .titleImg {
      width: 340px;
      height: 36px;
      margin-top: 30px;
      margin-bottom: 15px;
    }
    .title {
      font-size: 26px;
      color: $light_gray;
      margin: 0px auto 40px auto;
      text-align: center;
      font-weight: bold;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }
}
</style>
