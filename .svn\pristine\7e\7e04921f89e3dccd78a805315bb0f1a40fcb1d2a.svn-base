// 数据字典
import request from '@/utils/request'
export function getdictTypeList(params) {
  return request({
    url: '/system/dict/dictTypeList',
    method: 'GET',
    params
  })
}
export function addDictType(data) {
  return request({
    url: '/system/dict/addDictType',
    method: 'post',
    data
  })
}
export function updateDictType(data) {
  return request({
    url: '/system/dict/updateDictType',
    method: 'post',
    data
  })
}
export function deleteDictType(params) {
  return request({
    url: '/system/dict/deleteDictType',
    method: 'DELETE',
    params
  })
}
// --- 数据字典结束

// 字典数据
export function dictDataList (params) {
  return request({
    url: '/system/dictData/dictDataList',
    method: 'GET',
    params
  })
}
export function addDictData (data) {
  return request({
    url: '/system/dictData/addDictData',
    method: 'post',
    data
  })
}
export function updateDictData(data) {
  return request({
    url: '/system/dictData/updateDictData',
    method: 'post',
    data
  })
}
export function deleteDictData(params) {
  return request({
    url: '/system/dictData/deleteDictData',
    method: 'DELETE',
    params
  })
}
/** 通过类型查询对应下拉框数据 */
export function dictDataSelect (params) {
  return request({
    url: '/system/dictData/dictDataSelect',
    method: 'GET',
    params
  })
}
