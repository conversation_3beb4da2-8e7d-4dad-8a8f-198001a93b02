<template>
  <div class="app-container">
    <el-card>
      <div slot="header">
        <el-radio-group v-model="queryInfo.type" @change="geTrainList">
          <el-radio-button :label="1">我接收的</el-radio-button>
          <el-radio-button :label="2">我发起的</el-radio-button>
        </el-radio-group>
      </div>
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form ref="form" label-width="90px" inline>
            <el-form-item label="培训标题:">
              <el-input v-model="queryInfo.trainName" size="small" placeholder="请输入培训标题" maxlength="50" clearable></el-input>
            </el-form-item>
            <el-form-item label="培训状态:">
              <el-select v-model="queryInfo.trainStatus" placeholder="请选择培训状态" size="small" clearable>
                <el-option label="未开始" :value="1"> </el-option>
                <el-option label="进行中" :value="2"> </el-option>
                <el-option label="已结束" :value="3"> </el-option>
                <el-option label="已归档" :value="4"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="主讲人:">
              <el-input v-model="queryInfo.trainUserName" size="small" placeholder="请输入主讲人名称" maxlength="50" clearable></el-input>
            </el-form-item>
            <el-form-item label="开始时间:">
              <el-date-picker v-model="queryInfo.startTime" type="date" size="small" placeholder="选择开始时间"> </el-date-picker>
            </el-form-item>
            <el-form-item label="结束时间:">
              <el-date-picker v-model="queryInfo.endTime" type="date" size="small" placeholder="选择结束时间"> </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="success" size="small" @click="geTrainList"> 查询 </el-button>
              <el-button type="primary" size="small" @click="reset">重置 </el-button>
              <el-button v-if="queryInfo.type === 2" type="primary" size="small" @click="showDialog = true">添加培训 </el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <div>
        <el-table :data="list" style="width: 100%" border>
          <!-- <el-table-column prop="meetingCode" label="会议编号" width="width" align="cent er"> </el-table-column> -->
          <el-table-column prop="trainName" label="培训标题" width="width" align="center"> </el-table-column>
          <!-- <el-table-column prop="meetingName" label="培训标题" width="width" align="center"> </el-table-column> -->
          <el-table-column prop="trainStatus" label="培训状态" width="width" align="center">
            <template v-slot="{ row }">
              <span>{{ row.trainStatus | trainStatus }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="trainUserName" label="主讲人" width="width" align="center"> </el-table-column>
          <el-table-column prop="startTime" label="培训时间" width="width" align="center"> </el-table-column>
          <el-table-column prop="meetingRoomName" label="培训地点" width="width" align="center"> </el-table-column>
          <el-table-column prop="powerType" label="培训范围" width="width" align="center"> </el-table-column>
          <el-table-column prop="score" label="培训得分" width="width" align="center"> </el-table-column>
          <el-table-column prop="distanceTime" label="距离会议开始时间" width="width" align="center">
            <template v-slot="{row}">
              <span>{{ row.distanceTime }}分钟</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="width" align="center">
            <template v-slot="{ row }">
              <template v-if="queryInfo.type === 1">
                <el-button v-if="row.trainStatus === 1 && row.isDefine === 0" type="primary" size="small" @click="comfirmJoin(row)">报名</el-button>
                <el-tag v-if="row.trainStatus === 1 && row.isDefine === 1">已报名</el-tag>
                <el-button v-if="row.trainStatus === 2 && row.isSign === 0" type="primary" size="small" @click="sign(row)">签到</el-button>
                <el-tag v-if="row.trainStatus === 2 && row.isSign === 1">已签到</el-tag>
                <el-button v-if="row.trainStatus === 4" type="primary" size="small" @click="check(row)">查看</el-button>
              </template>
              <template v-if="queryInfo.type === 2">
                <el-button v-if="row.trainStatus === 1" type="warning" size="small" @click="edit(row)">编辑</el-button>
                <el-button v-if="row.trainStatus === 1" type="danger" size="small" @click="del(row)">删除</el-button>
                <!-- <el-button v-if="row.trainStatus === 2 && row.isSign === 0" type="primary" size="small" @click="sign(row)">签到</el-button>
                <el-tag v-if="row.trainStatus === 2 && row.isSign === 1" style="margin-right: 10px">已签到</el-tag> -->
                <el-button v-if="row.trainStatus === 2" type="primary" size="small" @click="overMeeting(row)">结束会议</el-button>
                <el-button v-if="row.trainStatus === 3 && row.isSummary === 0" type="warning" size="small" @click="meeting_summary(row)">补充会议纪要</el-button>
                <el-button v-if="row.trainStatus === 3 && row.isSummary === 1" type="warning" size="small" @click="archive(row)">归档</el-button>
                <el-button v-if="row.trainStatus === 4" type="primary" size="small" @click="check(row)">查看</el-button>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination style="text-align: center; margin-top: 15px" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 15, 20, 30]" :total="total" :page-size.sync="queryInfo.pageSize" :current-page.sync="queryInfo.pageNum" @size-change="geTrainList" @current-change="geTrainList" />
      </div>
    </el-card>
    <addOrModify ref="addOrModify" :show-dialog.sync="showDialog" @success="geTrainList" />
    <el-dialog title="补充会议纪要" :visible.sync="replenishSummaryDialog" width="450px">
      <div style="width: 360px; margin: 0 auto">
        <el-upload class="upload-demo" drag action="http://************:8701/system/upload/file" :headers="header" :file-list="fileList" multiple :before-upload="beforeUpload" :on-success="uploadSuccess" :on-remove="uploadRemove">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div slot="tip" class="el-upload__tip" style="color: red; line-height: 15px">只能上传.doc .docx .xls .xlsx .pdf .jpg .png .zip格式的附件，且大小不超过50MB</div>
        </el-upload>
      </div>
      <div slot="footer">
        <el-button @click="replenishSummaryDialog = false">取 消</el-button>
        <el-button type="primary" @click="replenishSummary">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="会议查看" :visible.sync="meetingCheckDialog" width="800px">
      <div>
        <el-tabs v-model="activeName" type="card">
          <el-tab-pane label="会议信息" name="first">
            <el-descriptions v-if="JSON.stringify(trainDetails) !== `{}`" :column="3" border style="margin:0 auto">
              <el-descriptions-item label="预定开始时间">{{ trainDetails.startTime }} </el-descriptions-item>
              <el-descriptions-item label="预定结束时间">{{ trainDetails.endTime }} </el-descriptions-item>
              <el-descriptions-item label="会议室">{{ trainDetails.trainName }}</el-descriptions-item>
              <el-descriptions-item label="培训标题">{{ trainDetails.meetingName }}</el-descriptions-item>
              <el-descriptions-item label="培训状态">
                {{ trainDetails.trainStatus | trainStatus }}
              </el-descriptions-item>
              <el-descriptions-item label="预定人">{{ trainDetails.trainUserName }}</el-descriptions-item>
              <el-descriptions-item label="会议内容">{{ trainDetails.remark }}</el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>
          <el-tab-pane label="参会人员" name="second">
            <el-table :data="trainDetails.userDtos" style="width: 100%" border>
              <el-table-column align="center" label="序号" type="index" width="width"> </el-table-column>
              <el-table-column align="center" prop="trainUserName" label="姓名" width="width"> </el-table-column>
              <el-table-column align="center" prop="organizationName" label="部门" width="width"> </el-table-column>
              <el-table-column align="center" prop="jobName" label="岗位" width="width"> </el-table-column>
              <el-table-column align="center" prop="signTime" label="签到时间" width="width"> </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="相关附件" name="third">
            <el-table :data="trainDetails.fileDtos" style="width: 100%" border>
              <el-table-column align="center" label="序号" type="index" width="width"> </el-table-column>

              <el-table-column align="center" prop="fileName" label="文件名称" width="width"> </el-table-column>
              <el-table-column align="center" prop="fileSize" label="文件大小" width="width">
                <template v-slot="{row}">
                  <span>{{ row.fileSize }}KB</span>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="trainUserName" label="上传人" width="width"> </el-table-column>
              <el-table-column align="center" prop="createTime" label="上传时间" width="width"> </el-table-column>
              <el-table-column align="center" label="操作" width="width">
                <template v-slot="{row}">
                  <el-button type="primary" size="mini" @click="download(row)">下载</el-button>
                </template>

              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div slot="footer">
        <el-button type="primary" @click="meetingCheckDialog = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { trainList, trainDetails, meetingSign, meetingDefine, meetingUpdateStatus, meetingSummary } from '@/api/training.js'
import { mapGetters } from 'vuex'

import Add_or_modify from './components/Add_or_modify.vue'
export default {
  name: 'Meeting',
  components: {
    addOrModify: Add_or_modify
  },
  data() {
    return {
      queryInfo: {
        trainName: null,
        trainStatus: null,
        trainUserName: null,
        startTime: null,
        endTime: null,
        userId: null,
        type: 1,
        pageNum: 1,
        pageSize: 10
      },
      list: [],
      total: 0,
      showDialog: false,
      replenishSummaryDialog: false,
      fileList: [],
      header: {
        Authorization: null
      },
      trainId: null,
      meetingCheckDialog: false, // 会议查看dialog
      trainDetails: {},
      activeName: 'first'
    }
  },
  computed: {
    ...mapGetters(['token'])
  },
  created() {
    this.geTrainList()
  },

  methods: {
    async geTrainList() {
      const { data } = await trainList(this.queryInfo)
      this.total = data.total
      this.list = data.list
    },
    reset() {
      const type = this.queryInfo.type
      this.queryInfo = {
        trainName: null,
        trainStatus: null,
        trainUserName: null,
        userId: null,
        startTime: null,
        endTime: null,
        type,
        pageNum: 1,
        pageSize: 10
      }
      this.geTrainList()
    },
    async edit(row) {
      const { data } = await trainDetails({ trainId: row.trainId, belongType: 2, isReserve: 1 })
      this.$refs['addOrModify'].edit(data)
      this.showDialog = true
      console.log(data)
    },
    // 参加
    comfirmJoin(row) {
      this.$confirm('您确认要参加该会议吗, 是否继续?', '参加会议', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await meetingDefine({ trainId: row.trainId })
          this.$message.success('参加成功')
          this.geTrainList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
    },
    // 签到
    async sign(row) {
      await meetingSign({ trainId: row.trainId })
      this.$message.success('签到成功')
      this.geTrainList()
    },
    // 结束会议
    overMeeting(row) {
      this.$confirm('您确认要结束该会议吗, 是否继续?', '结束会议', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await meetingUpdateStatus({ status: 2, trainId: row.trainId })
          this.$message.success('结束会议成功')
          this.geTrainList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
    },
    // 删除
    del(row) {
      this.$confirm('您确认要删除该会议吗, 是否继续?', '删除会议', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await meetingUpdateStatus({ status: 1, trainId: row.trainId })
          this.$message.success('删除会议成功')
          this.geTrainList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
    },
    /** 上传会议纪要 */
    meeting_summary(row) {
      this.trainId = row.trainId
      this.replenishSummaryDialog = true
    },
    beforeUpload(file) {
      this.header.Authorization = `Bearer ${this.token}`
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isDOC = file.type === 'application/msword'
      const isDOCX = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      const isXLS = file.type === 'application/vnd.ms-excel'
      const isXLSX = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      const isPDF = file.type === 'application/pdf'
      const isZIP = file.type === 'application/x-zip-compressed'
      const isLt2M = file.size / 1024 / 1024 < 50
      if (!isLt2M) {
        this.$message.error('上传附件大小不能超过 50MB!')
      }
      if (!isJPG && !isPNG && !isDOC && !isDOCX && !isXLS && !isXLSX && !isPDF && !isZIP) {
        this.$message.warning('只能上传.doc .docx .xls .xlsx .pdf .jpg .png .zip 格式的附件')
      }
      return (isJPG || isPNG || isDOC || isDOCX || isXLS || isXLSX || isPDF || isZIP) && isLt2M
    },
    uploadSuccess(response, file, fileList) {
      this.fileList = fileList
    },
    // 文件列表移除文件时的钩子
    uploadRemove(file, fileList) {
      this.fileList = fileList
    },
    async replenishSummary() {
      const list = []
      this.fileList.forEach((item) => {
        list.push({
          fileName: item.name,
          fileSize: item.size / 1024,
          fileUrl: item.response.data[0],
          trainId: this.trainId
        })
      })
      await meetingSummary(list)
      this.$message.success('补充会议纪要成功')
      this.geTrainList()
      this.replenishSummaryDialog = false
      this.fileList = []
    },
    // 归档
    async archive(row) {
      await meetingUpdateStatus({ status: 3, trainId: row.trainId })
      this.$message.success('归档成功')
      this.geTrainList()
    },
    // 查看
    async check(row) {
      const { data } = await trainDetails({ trainId: row.trainId, belongType: 1 })
      this.trainDetails = data
      console.log(data)
      this.meetingCheckDialog = true
    },
    // 查看中附件下载
    download(row) {
      window.open(row.fileUrl, '_blank')
    }
  }
}
</script>

<style scoped lang="scss"></style>
