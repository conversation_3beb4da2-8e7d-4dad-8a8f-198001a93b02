<template>
  <div style="width: 100%; height: 100%">
    <div id="chart" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script>
import resize from '../mixins/resize'
export default {
  name: '',
  mixins: [resize],
  data() {
    return {
      chart: null
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.chart = this.$echarts.init(document.getElementById('chart'))
      this.setOpiton()
    },
    setOpiton() {
      this.chart.setOption({
        color: ['#6892ff'],
        tooltip: {
          backgroundColor: 'rgba(0,0,0,0.7)',
          //   color: '#FFFFFF',
          borderColor: 'gray',
          textStyle: {
            color: '#FFFFFF' // 设置文字颜色
          }
        },
        radar: {
          indicator: [
            { text: '基础工作', max: 30 },
            { text: '临时工作', max: 30 },
            { text: '其他工作', max: 30 }
          ],
          center: ['50%', '60%'],
          splitNumber: 4, // 雷达图圈数设置
          radius: 130,
          axisName: {
            color: '#657081',
            fontWeight: '12px',
            padding: [0, 0]
          },
          axisLine: {
            lineStyle: {
              color: '#bdbdbd'
            }
          },
          splitArea: {
            show: false,
            areaStyle: {
              color: 'rgba(255,0,0,0)' // 图表背景的颜色
            }
          }
        },
        series: [
          {
            type: 'radar',
            emphasis: {
              lineStyle: {
                width: 4
              }
            },
            data: [
              {
                value: [12, 20, 15],
                name: '工时统计',
                areaStyle: {
                  color: '#6892ff'
                }
              }
            ]
          }
        ]
      })
    }
  }
}
</script>

<style scoped lang="scss"></style>
