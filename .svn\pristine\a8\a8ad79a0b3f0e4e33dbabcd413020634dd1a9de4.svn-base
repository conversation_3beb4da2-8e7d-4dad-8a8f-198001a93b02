import request from '@/utils/request'
/** 签到 */
export function secret_sign(data) {
  return request({
    url: '/secret/sign/sign',
    method: 'POST',
    data
  })
}
/** 签到记录列表 */
export function secret_signList(params) {
  return request({
    url: '/secret/sign/signList',
    method: 'GET',
    params
  })
}
/** 签到统计 */
export function secret_signStatis(params) {
  return request({
    url: '/secret/sign/signStatis',
    method: 'GET',
    params
  })
}

// 获取用户列表
export function sysUserList(params) {
  return request({
    url: '/system/sysUser/secretUserList',
    method: 'get',
    params
  })
}
