<template>
  <div class="">
    <el-dialog title="客户联系人列表" :visible="showDialog" center width="1400px" top="4vh" :close-on-click-modal="false" @close="close" @open="getList">
      <el-form ref="form" :model="queryInfo" inline label-width="80px">
        <el-form-item label="客户负责人:" label-width="90px">
          <el-input v-model="queryInfo.realName" placeholder="请输入客户负责人" maxlength="50" clearable></el-input>
        </el-form-item>
        <el-form-item label="创建时间:">
          <el-date-picker v-model="date" type="daterange" range-separator="→" start-placeholder="开始日期" end-placeholder="结束日期" clearable @change="datePickerChange"> </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="getList">查询</el-button>
          <el-button type="primary" plain @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table :data="list" style="width: 100%" border>
        <el-table-column align="center" prop="customerName" label="客户姓名" width="width"> </el-table-column>
        <el-table-column align="center" prop="contactsName" label="客户联系人姓名" width="width"> </el-table-column>
        <el-table-column align="center" prop="phone" label="联系方式" width="width"> </el-table-column>
        <el-table-column align="center" prop="wechat" label="微信" width="width"> </el-table-column>
        <el-table-column align="center" prop="regionName" label="区域名称" width="width"> </el-table-column>
        <el-table-column align="center" prop="majorName" label="所属专业" width="width"> </el-table-column>
        <el-table-column align="center" prop="realName" label="客户负责人" width="width"> </el-table-column>
        <el-table-column align="center" prop="createTime" label="创建时间" width="width"> </el-table-column>
      </el-table>
      <el-pagination
        v-if="list.length > 0"
        layout="total,prev, pager, next"
        style="margin-top: 15px; text-align: center"
        :page-sizes="[5, 10, 15, 20]"
        background
        :total="total"
        :page-size.sync="queryInfo.pageSize"
        :current-page.sync="queryInfo.pageNum"
        @size-change="getList"
        @current-change="getList"
      />
      <div slot="footer">
        <el-button type="primary" @click="close">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { contactsUniqueList } from '@/api/clientele'
import { formatDate } from '@/filters'

export default {
  name: '',
  props: {
    showDialog: {
      type: Boolean,
      require: true
    }
  },
  data() {
    return {
      queryInfo: {
        pageNum: 1,
        pageSize: 10,
        realName: null, // 客户负责人名称
        startTime: null,
        endTime: null
      },
      list: [],
      total: null,
      date: null
    }
  },
  created() {},
  methods: {
    close() {
      this.$emit('update:showDialog', false)
    },
    async getList() {
      const { data } = await contactsUniqueList(this.queryInfo)
      this.list = data.list
      this.total = data.total
    },
    // 时间搜索的格式化
    datePickerChange(val) {
      if (val) {
        this.queryInfo.startTime = formatDate(val[0])
        this.queryInfo.endTime = formatDate(val[1], 'yyyy-MM-dd') + ' 23:59:59'
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getList()
    },
    // 重置搜索
    reset() {
      const oldInfo = Object.assign({}, this.queryInfo)
      this.queryInfo = {
        realName: null, // 客户负责人名称
        startTime: null,
        endTime: null,
        pageNum: oldInfo.pageNum,
        pageSize: oldInfo.pageSize
      }
      this.date = null
      this.getList()
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .el-dialog__body {
    padding: 20px !important;
  }
}
</style>
