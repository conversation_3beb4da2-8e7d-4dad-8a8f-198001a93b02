<template>
  <div class="workTrack_log">
    <el-dialog title="修改日志" :visible.sync="dialogVisible" width="width">
      <el-table :data="list" style="width: 100%" border>
        <el-table-column align="center" prop="type" label="类型" width="width">
          <template v-slot="{ row }">
            {{ row.type === 1 ? '修改' : '' }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="realName" label="修改人" width="width"> </el-table-column>
        <el-table-column align="center" prop="createTime" label="修改时间" width="width"> </el-table-column>

        <el-table-column align="center" prop="remark" label="备注" width="width"> </el-table-column>
      </el-table>
      <div slot="footer">
        <el-button type="primary" @click="dialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: '',
  data() {
    return {
      dialogVisible: false,
      list: []
    }
  },
  created() {},
  methods: {
    show(data) {
      this.list = data.logList
      this._.reverse(this.list)
      this.dialogVisible = true
    }
  }
}
</script>
<style scoped lang="scss"></style>
