<template>
  <div class="">
    <el-dialog title="" :visible="showDialog" width="680px" :close-on-click-modal="false" @close="close">
      <div>
        <el-form ref="form" :model="formInfo" label-width="100px" :rules="rules" inline>
          <el-form-item label="产品名称:" prop="productName">
            <el-input v-model="formInfo.productName" size="small" placeholder="请输入产品名称" maxlength="50" clearable></el-input>
          </el-form-item>
          <!-- <el-form-item label="版本号:" prop="version">
            <el-input v-model="formInfo.version" size="small" placeholder="请输入版本号" maxlength="50" oninput="value = value.replace(/[\u4E00-\u9FA5]/g,'')" clearable></el-input>
          </el-form-item> -->
          <el-form-item label="产品类型:" prop="productType">
            <el-select v-model="formInfo.productType" size="small" placeholder="请选择产品类型" clearable @change="selectChange">
              <el-option label="软件" :value="1"> </el-option>
              <el-option label="硬件" :value="2"> </el-option>
              <el-option label="虚拟仿真" :value="3"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="专业:" prop="majorId">
            <!-- <el-select v-model="formInfo.productType" size="small" placeholder="请选择专业" clearable @change="selectChange">
              <el-option label="软件" :value="1"> </el-option>
              <el-option label="硬件" :value="2"> </el-option>
              <el-option label="虚拟仿真" :value="3"> </el-option>
            </el-select> -->
            <el-select ref="selecteltree" v-model="formInfo.majorName" size="small" clearable placeholder="请选择专业" @focus="getSpecialty">
              <el-option v-for="item in menu" :key="item.id" :label="item.majorName" :value="item.id" style="display: none" />
              <el-tree style="padding-left: 15px" :data="menu" node-key="id" empty-text="暂无菜单" highlight-current :expand-on-click-node="false" :props="defaultProps" current-node-key="id" default-expand-all @node-click="handleNodeClick" />
            </el-select>
          </el-form-item>
          <el-form-item label="演示地址:">
            <el-input v-model="formInfo.perform" size="small" placeholder="请输入演示地址" maxlength="50" oninput="value = value.replace(/[\u4E00-\u9FA5]/g,'')" clearable></el-input>
          </el-form-item>
          <el-form-item label="产品描述:">
            <el-input v-model="formInfo.describe" size="small" placeholder="请输入描述,200字以内" maxlength="200" type="textarea" clearable></el-input>
          </el-form-item>
        </el-form>
        <el-card>
          <el-row :gutter="10" type="flex" justify="space-between" align="middle">
            <el-col :span="6">
              <span style="font-size: 16px; font-weight: 600"><span style="color: #f67979">*</span> 人员列表</span>
            </el-col>
            <el-col :span="4">
              <el-button type="primary" size="small" @click="addPerson">添加人员</el-button>
            </el-col>
          </el-row>

          <el-table :data="confirmData ? confirmData.slice((pagination.page - 1) * pagination.size, (pagination.page - 1) * pagination.size + pagination.size) : confirmData" style="width: 100%; margin-top: 15px" border>
            <el-table-column label="姓名" align="center" prop="realName" />
            <el-table-column label="部门" align="center" prop="organizationName" />
            <el-table-column label="岗位" align="center" prop="jobName" />
            <el-table-column align="center" label="操作">
              <template v-slot="{ row }">
                <el-button type="danger" size="small" @click="removerItem(row)">移除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination :page-size.sync="pagination.size" :current-page.sync="pagination.page" :total="confirmData ? confirmData.length : pagination.total" :page-sizes="[5, 10, 15, 20]" layout=" prev,pager,next,sizes,jumper" style="text-align: center; margin-top: 15px" @size-change="handleSizeChange" @current-change="handleCurrentChange"> </el-pagination>
        </el-card>
      </div>
      <div slot="footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="addProduct">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 添加人员 -->
    <el-dialog v-if="showDialog" title="添加人员" :visible.sync="dialogVisible" width="700px">
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form label-width="60px" inline>
            <el-form-item label="姓名:">
              <el-input v-model="queryInfo.realName" size="small" clearable placeholder="请输入姓名"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="success" size="small" @click="addPerson">查询</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <el-table :data="gridData" style="width: 100%" border :row-key="getRowKeys" @selection-change="selectionChange">
        <el-table-column align="center" type="selection" :reserve-selection="true" />
        <el-table-column label="姓名" align="center" prop="realName" />
        <el-table-column label="部门" align="center" prop="organizationName" />
        <el-table-column label="岗位" align="center" prop="jobName" />
      </el-table>
      <el-pagination style="text-align: center; margin-top: 15px" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[5, 10, 15, 20]" :total="total" :page-size.sync="queryInfo.pageSize" :current-page.sync="queryInfo.pageNum" @size-change="addPerson" @current-change="addPerson" />

      <div slot="footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmSelect">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { majorList } from '@/api/specialty.js'
import { getList } from '@/api/systemUser'
import { saveProduct, productUpdate } from '@/api/product.js'
export default {
  name: 'ProductImprovement',
  props: {
    showDialog: {
      required: true,
      type: Boolean
    }
  },
  data() {
    return {
      formInfo: {
        productName: null, // 产品名称
        version: null, // 版本号
        productType: null, // 产品类型 1 软件 2 硬件 3 虚拟仿真
        productTypeName: null, // 产品名称
        majorId: null, // 专业id
        majorName: null,
        userIds: [], // 人员ids
        svnUrl: null, // svn地址
        perform: null, // 演示地址及演示用户名密码
        describe: null // 产品描述
      },
      rules: {
        productName: [
          {
            required: true,
            tigger: 'blur',
            message: '产品名称不能为空'
          }
        ],
        version: [
          {
            required: true,
            tigger: 'blur',
            message: '版本号不能为空'
          }
        ],
        productType: [
          {
            required: true,
            tigger: 'change',
            message: '产品类型不能为空',
            type: 'number'
          }
        ],
        majorId: [
          {
            required: true,
            tigger: 'change',
            message: '专业不能为空',
            type: 'number'
          }
        ]
      },
      menu: [],
      defaultProps: {
        label: 'majorName',
        children: 'children'
      },
      gridData: [],
      checkedData: [], // 添加人员多选框选中的数据
      queryInfo: {
        realName: null,
        pageNum: 1,
        pageSize: 5
      },
      total: 0,
      dialogVisible: false,
      confirmData: [], // 添加人员确认选择的数据
      // 添加人员的假数据分页
      pagination: {
        size: 5,
        page: 1,
        total: 0
      }
    }
  },
  computed: {
    showTitle() {
      return this.formInfo.productId ? '修改产品' : '添加产品'
    }
  },
  methods: {
    // 产品类型 选中值发生变化时触发	目前的选中值

    selectChange(val) {
      if (val === 1) {
        this.formInfo.productTypeName = '软件'
      } else if (val === 2) {
        this.formInfo.productTypeName = '硬件'
      } else {
        this.formInfo.productTypeName = '虚拟仿真'
      }
      console.log(val)
    },
    // 选择专业
    async getSpecialty() {
      const { data } = await majorList()
      this.menu = data
      console.log(data)
    },
    // 节点被点击时的回调
    handleNodeClick(node) {
      console.log(node)
      this.formInfo.majorId = node.id
      this.formInfo.majorName = node.majorName
      this.$refs['selecteltree'].blur()
      this.$forceUpdate()
    },
    // 添加人员
    addPerson() {
      this.dialogVisible = true
      getList(this.queryInfo).then((response) => {
        console.log(response)
        this.gridData = response.data.list
        this.total = response.data.total
      })
    },
    // 当选择项发生变化时会触发该事件
    selectionChange(val) {
      this.checkedData = val
      console.log(val)
    },
    // 添加人员确定触发
    confirmSelect() {
      this.confirmData = this.checkedData
      this.totalCount = this.confirmData.length
      this.dialogVisible = false
    },
    // 添加产品人员列表移除按钮触发
    removerItem(row) {
      console.log(row)
      this.confirmData = this.confirmData.filter((item) => item.userId !== row.userId)
      this.totalCount = this.confirmData.length
    },
    edit(row) {
      console.log(row)
      this.formInfo = { ...row }
      this.formInfo.describe = row.description
      this.formInfo.productType = parseInt(row.productType)
      this.formInfo.majorId = parseInt(row.majorId)
      this.getSpecialty()
      this.confirmData = row.userDtos
    },
    // 添加/修改产品
    addProduct() {
      this.$refs['form'].validate(async (val) => {
        if (val) {
          this.formInfo.userIds = this.confirmData.map((item) => item.userId)
          if (this.formInfo.userIds.length < 1) {
            this.$message.warning('请选择人员')
          } else {
            if (this.formInfo.productId) {
              await productUpdate(this.formInfo)
              this.$message.success('修改成功')
              this.$emit('success')
              this.$emit('update:showDialog', false)
            } else {
              await saveProduct(this.formInfo)
              this.$message.success('添加成功')
              this.$emit('success')
              this.$emit('update:showDialog', false)
            }
          }
        }
      })
    },
    getRowKeys(row) {
      return row.userId
    },
    // dialog关闭事件
    close() {
      this.formInfo = {
        productName: null, // 产品名称
        version: null, // 版本号
        productType: null, // 产品类型 1 软件 2 硬件 3 虚拟仿真
        productTypeName: null, // 产品名称
        majorId: null, // 专业id
        majorName: null,

        userIds: [], // 人员ids
        svnUrl: null, // svn地址
        perform: null, // 演示地址及演示用户名密码
        describe: null // 产品描述
      }
      this.checkedData = []
      this.confirmData = []
      this.$refs['form'].resetFields()
      this.$emit('update:showDialog', false)
    },
    // 添加人员的假分页
    handleSizeChange(val) {
      this.pagination.size = val
    },
    handleCurrentChange(val) {
      this.pagination.page = val
    }
  }
}
</script>

<style scoped lang="scss"></style>
