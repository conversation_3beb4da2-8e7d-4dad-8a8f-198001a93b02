<template>
  <div class="app-container">
    <el-card class="sign_content">
      <el-tabs v-model="activeName">
        <el-tab-pane label="签到统计" name="统计">
          <div class="signStatistics">
            <el-card style="width: 187px" class="signStatistics_content_left">
              <div slot="header" class="header">人员列表</div>
              <div>
                <el-tree
                  ref="Tree"
                  :data="personList"
                  node-key="userId"
                  :props="defaultProps"
                  default-expand-all
                  check-on-click-node
                  highlight-current
                  :current-node-key="currentKey"
                  @node-click="handleNodeClick"
                >
                  <template v-slot="{ data }">
                    <div><img v-if="data.realName === '销售部门'" src="@/assets/sign/user_icon.png" alt="" /> {{ data.realName }}</div>
                  </template>
                </el-tree>
              </div>
            </el-card>
            <el-card style="flex: 1" class="signStatistics_content_right">
              <div slot="header">
                <div class="header_left">
                  <div class="title">签到日历</div>
                  <div class="status"><span class="rounds error"></span>签到异常 <span class="rounds success"></span>签到正常 <span class="rounds none"></span>无外访记录</div>
                </div>
                <div class="header_center">
                  <i class="el-icon-arrow-left" @click="MonthReduce"></i>
                  <span>{{ time }}</span>
                  <i class="el-icon-arrow-right" @click="MonthAdd"></i>
                </div>
                <div class="header_right">
                  本月签到:
                  <span>{{ signCount }}次</span>
                </div>
                <!-- <div>
                本月签到<span style="color: #409eff; font-weight: bold; margin: 0 5px">{{ signCount }}</span> 次
                <el-date-picker v-model="month" type="month" placeholder="选择年月" size="small" style="margin-left: 10px" @change="monthChange"> </el-date-picker>
              </div> -->
              </div>
              <div>
                <el-calendar v-model="signDate">
                  <template v-slot:dateCell="{ data }">
                    <el-tooltip class="item" effect="dark" placement="bottom" :disabled="my_status(data.day, 1)" popper-class="atooltip">
                      <template v-slot:content>
                        <div v-for="item in my_status(data.day, 2)" :key="item.time" :class="[{ middleTip: item.type === '1', lateTip: item.type === '2' }, 'tooltip']">
                          <span> {{ item.type | signType }} </span>
                          <span> {{ item.time }} </span>
                          <span> {{ item.location }} </span>
                        </div>
                      </template>
                      <div class="round" :style="my_status(data.day, 0)">
                        <div style="width: 100%; height: 100%">
                          <span v-if="my_status(data.day, 3) === null">{{ data.day.split('-').slice(2).join('-') }}</span>
                          <template v-if="my_status(data.day, 3)">
                            <div>
                              <span>{{ data.day.split('-').slice(2).join('-') }}</span>
                              <span class="success">
                                <i class="el-icon-success"></i>
                                签到正常
                              </span>
                            </div>
                            <div v-for="item in my_status(data.day, 2)" :key="item.time" :class="[{ middle: item.type === '1', late: item.type === '2' }, 'early']">
                              <span> {{ item.type | signType }} </span>
                              <span> {{ item.time }} </span>
                              <span> {{ item.location }} </span>
                            </div>
                          </template>
                          <template v-if="my_status(data.day, 3) === false">
                            <div>
                              <span>{{ data.day.split('-').slice(2).join('-') }}</span>
                              <span class="error">
                                <i class="el-icon-warning"></i>
                                签到异常
                              </span>
                            </div>
                            <div v-for="item in my_status(data.day, 2)" :key="item.time" :class="[{ middle: item.type === '1', late: item.type === '2' }, 'early']">
                              <span> {{ item.type | signType }} </span>
                              <span> {{ item.time }} </span>
                              <span> {{ item.location }} </span>
                            </div>
                          </template>
                        </div>
                      </div>
                    </el-tooltip>
                  </template>
                </el-calendar>
              </div>
            </el-card>
          </div>
        </el-tab-pane>
        <el-tab-pane label="签到列表" name="列表">
          <div class="signList">
            <div style="padding-top: 24px">
              <el-row :gutter="10" class="searchFrom">
                <el-form ref="form" label-width="80px" inline>
                  <el-form-item label="姓名:" label-width="55px">
                    <el-input v-model="queryInfo.realName" size="small" placeholder="请输入姓名" clearable></el-input>
                  </el-form-item>
                  <el-form-item label="签到时间:">
                    <el-date-picker v-model="date" size="small" type="daterange" range-separator="→" start-placeholder="开始日期" end-placeholder="结束日期" clearable @change="datePickerChange">
                    </el-date-picker>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" plain size="small" @click="reset">重置</el-button>
                    <el-button type="primary" size="small" @click="getSignList">查询</el-button>
                  </el-form-item>
                </el-form>
              </el-row>
              <div>
                <el-row style="margin-bottom: 16px">
                  <span class="signListTitle">签到列表</span>
                </el-row>
                <el-table :data="list" stripe style="width: 100%" header-cell-class-name="signTableHeader" :row-class-name="tableRowClassName">
                  <el-table-column align="center" prop="realName" label="姓名" width="width"> </el-table-column>
                  <el-table-column align="center" prop="time" label="日期" width="width"> </el-table-column>
                  <el-table-column align="center" prop="intoCount" label="进校次数" width="width"> </el-table-column>
                  <el-table-column align="center" prop="outCount" label="出校次数" width="width"> </el-table-column>
                  <el-table-column align="center" prop="nightCount" label="晚签次数" width="width"> </el-table-column>
                  <el-table-column align="center" prop="nightCount" label="操作" width="width">
                    <template v-slot="{ row, $index }">
                      <span :class="[{ checkRow: $index === listIndex }, 'details']" @click="getSignDetails(row, $index)">详情</span>
                    </template>
                  </el-table-column>
                </el-table>
                <el-pagination
                  v-if="list.length > 0"
                  layout="total,prev, pager, next"
                  style="margin-top: 15px; text-align: right"
                  :page-sizes="[5, 10, 15, 20]"
                  background
                  :total="total"
                  :page-size.sync="queryInfo.pageSize"
                  :current-page.sync="queryInfo.pageNum"
                  @size-change="getSignList"
                  @current-change="getSignList"
                />
              </div>
            </div>
            <div class="signListDetails">
              <el-row>
                <span class="signListTitle">签到详情</span>
              </el-row>
              <ul>
                <li v-for="item in listDetails" :key="item.time" :class="[{ type0: item.type === '0', type1: item.type === '1' }]">
                  <div>
                    <span>签到类型</span>
                    <span style="font-weight: bold">{{ item.type | signType }}</span>
                  </div>
                  <div>
                    <span>签到时间</span>
                    <span style="font-weight: bold">{{ item.time }}</span>
                  </div>
                  <div>
                    <span>定位</span>
                    <el-tooltip effect="dark" popper-class="locationTooltip" :content="item.location" placement="top">
                      <span class="location">{{ item.location }}</span>
                    </el-tooltip>
                  </div>
                </li>
              </ul>
              <!-- <el-table :data="listDetails" style="width: 100%" border>
                <el-table-column align="center" prop="time" label="签到时间" width="width"> </el-table-column>
                <el-table-column align="center" prop="type" label="签到类型" width="width">
                  <template v-slot="{ row }">
                    <span>{{ row.type | signType }}</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" prop="location" label="定位" width="width"> </el-table-column>
              </el-table> -->
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script>
import { formatDate } from '@/filters'
import { secret_signList, secret_signStatis, sysUserList } from '@/api/sign'
export default {
  name: 'SignRecord',
  data() {
    return {
      activeName: '列表',
      time: null,
      originalTime: null,
      // 签到统计
      signPersonInfo: {
        organizationId: 56642510,
        pageNum: 1,
        pageSize: 2000
      },
      currentKey: null,
      personList: [],
      defaultProps: {
        children: 'children',
        label: 'realName'
      },
      signStatisticsInfo: {
        userId: null,
        time: null
      },
      signDate: new Date(),
      signSignStatisticsList: [],
      signCount: 0,
      // 签到列表
      queryInfo: {
        realName: null,
        startTime: null,
        endTime: null,
        createUser: null,
        pageNum: 1,
        pageSize: 10
      },
      date: null,
      list: [],
      listDetails: [],
      total: 0,
      listIndex: 0
    }
  },
  created() {
    this.getSignList()
    this.getUserList()
    this.time = formatDate(new Date(), 'yyyy年MM月')
    this.originalTime = formatDate(new Date(), 'yyyy-MM')
  },
  mounted() {
    console.log(this.personList)
  },
  methods: {
    // 获取签到统计列表
    getUserList() {
      sysUserList(this.signPersonInfo).then((response) => {
        this.personList = [
          {
            realName: '销售部门',
            children: response.data.list
          }
        ]
        if (response.data.list.length > 0) {
          this.$nextTick(() => {
            this.currentKey = response.data.list[0].userId
            this.$refs.Tree.setCurrentKey(this.currentKey)
            this.signStatisticsInfo.userId = this.currentKey
            this.signStatisticsInfo.time = formatDate(new Date(), 'yyyy-MM')
            this.getSignStatis()
          })
        }
      })
    },
    // 点击月份加一
    MonthAdd() {
      let currentDate = this.originalTime
      currentDate = new Date(currentDate)
      let nextDate = currentDate.setMonth(currentDate.getMonth() + 1) // 输出日期格式为毫秒形式1556668800000
      nextDate = new Date(nextDate)
      const nextYear = nextDate.getFullYear()
      const nextMonth = this.checkMonth(nextDate.getMonth() + 1) // 因日期中的月份表示为0-11，所以要显示正确的月份，需要 + 1
      nextDate = nextYear + '-' + nextMonth // "2019-05"
      this.originalTime = nextDate
      this.signDate = this.originalTime
      this.time = formatDate(this.originalTime, 'yyyy年MM月')
      this.signStatisticsInfo.time = formatDate(this.originalTime, 'yyyy-MM')
      this.getSignStatis()
    },
    // 点击月份减一
    MonthReduce() {
      let currentDate = this.originalTime
      currentDate = new Date(currentDate)
      let lastDate = currentDate.setMonth(currentDate.getMonth() - 1) // 输出日期格式为毫秒形式1551398400000
      lastDate = new Date(lastDate)
      const lastYear = lastDate.getFullYear()
      const lastMonth = this.checkMonth(lastDate.getMonth() + 1) // 因日期中的月份表示为0-11，所以要显示正确的月份，需要 + 1
      lastDate = lastYear + '-' + lastMonth // "2019-03"
      this.originalTime = lastDate
      this.signDate = this.originalTime
      this.time = formatDate(this.originalTime, 'yyyy年MM月')
      this.signStatisticsInfo.time = formatDate(this.originalTime, 'yyyy-MM')
      this.getSignStatis()
    },
    checkMonth(i) {
      if (i < 10) {
        i = '0' + i
      }
      return i
    },
    // 签到统计
    async getSignStatis() {
      const { data } = await secret_signStatis(this.signStatisticsInfo)
      this.signSignStatisticsList = data.list
      this.signCount = data.signCount
      console.log(data)
    },
    handleNodeClick(data) {
      if (data.userId) {
        this.signStatisticsInfo.userId = data.userId
        this.signStatisticsInfo.time = formatDate(this.originalTime, 'yyyy-MM')
        this.getSignStatis()
      }
    },
    my_status(date, type) {
      /** 0:校验日期签到状态  1：校验文字提示是否显示 2:返回当前日期文字提示的数据 3:返回当前时间是否签到的状态 */
      if (type === 0) {
        const result = this.signSignStatisticsList.find((item) => item.time === date)
        return result ? (result.status === 0 ? 'background:#ffded4' : 'background:#c9ebd5') : 'background:#dfe2e7'
      } else if (type === 1) {
        const result = this.signSignStatisticsList.some((item) => item.time === date)
        return !result
      } else if (type === 2) {
        const data = this.signSignStatisticsList.find((item) => item.time === date)
        return data && data.list ? data.list : []
      } else if (type === 3) {
        const result = this.signSignStatisticsList.find((item) => item.time === date)
        return result ? result.status !== 0 : null
      }
    },
    // 获取签到列表
    async getSignList() {
      const { data } = await secret_signList(this.queryInfo)
      console.log(data)
      this.list = data.list
      if (this.list.length > 0) {
        this.listDetails = this.list[0].list
        this.listIndex = 0
        console.log(this.listDetails)
      } else {
        this.listDetails = []
      }
      this.total = data.total
    },
    tableRowClassName({ row, rowIndex }) {
      console.log(row, rowIndex)
      if (rowIndex === this.listIndex) {
        return 'check-row'
      }
      return ''
    },
    // 重置
    reset() {
      this.queryInfo = {
        realName: null,
        startTime: null,
        endTime: null,
        createUser: null,
        pageNum: 1,
        pageSize: 10
      }
      this.date = null
      this.getSignList()
    },
    // 获取签到详情
    getSignDetails(row, index) {
      console.log(index)
      this.listDetails = row.list
      this.listIndex = index
    },
    // 时间搜索的格式化
    datePickerChange(val) {
      if (val) {
        this.queryInfo.startTime = formatDate(val[0])
        this.queryInfo.endTime = formatDate(val[1], 'yyyy-MM-dd') + ' 23:59:59'
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getSignList()
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  background: #e8eaed;
  min-height: 100%;
  padding-bottom: 0;
  padding-top: 10px;
  .sign_content {
    background: #ffffff;
    max-height: 856px;
    border-radius: 8px 8px 8px 8px;
    ::v-deep {
      .el-card__body {
        padding-top: 0;
        padding-bottom: 10px;
      }
    }
  }
}
.signStatistics {
  display: flex;
  margin-top: 15px;
  .signStatistics_content_left {
    ::v-deep {
      .el-card__header {
        padding-top: 10px;
        padding-bottom: 10px;
        padding-left: 18px;
      }
      .el-card__body {
        padding-top: 15px;
        padding-left: 0;
        padding-right: 0;
      }
    }
    .header {
      font-size: 14px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #0b1a44;
    }
  }
  .signStatistics_content_right {
    border: none;
    ::v-deep {
      .el-card__header {
        border-bottom: none;
        padding-top: 0;
        padding-bottom: 10px;
        & > div {
          display: flex;
          justify-content: space-between;
          .header_left {
            display: flex;
            .title {
              margin-right: 40px;
              font-size: 14px;
              font-family: Microsoft YaHei-Bold, Microsoft YaHei;
              font-weight: bold;
              color: #0b1a44;
            }
            .status {
              font-size: 12px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #0b1a44;
              .rounds {
                display: inline-block;
                width: 11px;
                height: 9px;
                margin-right: 6px;
              }
              .error {
                background: #ffded4;
              }
              .success {
                background: #c9ebd5;
                margin-left: 20px;
              }
              .none {
                background: #dfe2e7;
                margin-left: 20px;
              }
            }
          }
          .header_center {
            margin-left: -180px;
            span {
              padding: 0 15px;
              font-size: 16px;
              font-family: Microsoft YaHei-Bold, Microsoft YaHei;
              font-weight: bold;
              color: #0b1a44;
            }
            i {
              color: #9095a7;
              cursor: pointer;
            }
          }
          .header_right {
            font-size: 14px;
            font-family: Microsoft YaHei-Bold, Microsoft YaHei;
            color: #0b1a44;
            span {
              font-size: 16px;
              font-weight: bold;
              color: #3464e0;
            }
          }
        }
      }
      .el-card__body {
        padding: 8px;
        background: #f5f5f5;
        border-radius: 8px;
        // 日历更改演示 <--
        .el-calendar__body {
          padding: 10px;
          border-radius: 8px;
          max-height: 736px;
          overflow: auto;
        }
        .round {
          display: inline-block;
          width: 100%;
          height: 100%;
          & > div {
            padding-top: 10px;
            padding-bottom: 10px;
            padding-left: 9px;
            padding-right: 12px;
            font-size: 14px;
            font-family: Microsoft YaHei-Bold, Microsoft YaHei;
            font-weight: bold;
            color: #0b1a44;
            & > div {
              display: flex;
              justify-content: space-between;
              .success {
                color: #1d7d37;
              }
              .error {
                color: #eb6557;
              }
            }
            .early {
              display: flex;
              justify-content: flex-start;
              align-items: center;
              margin-top: 8px;
              padding: 0 8px;
              height: 18px;
              background: #5d89dc;
              border-radius: 18px;
              span {
                &:first-of-type {
                  font-size: 12px;
                  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
                  font-weight: bold;
                  color: #ffffff;
                }
                &:nth-of-type(2) {
                  margin: 0 8px;
                  font-size: 12px;
                  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                  font-weight: 400;
                  color: #ffffff;
                }
                &:last-of-type {
                  display: inline-block;
                  width: 80px;
                  font-size: 12px;
                  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                  font-weight: 400;
                  color: #ffffff;
                  overflow: hidden;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                }
              }
            }
            .middle {
              background: #f78e40 !important;
            }
            .late {
              background: #384b66 !important  ;
            }
          }
        }
        .el-calendar__header {
          display: none;
        }
        .el-calendar-table thead th {
          padding-top: 0;
          &::before {
            content: '周';
          }
        }
        .el-calendar-table:not(.is-range) td.next,
        .el-calendar-table:not(.is-range) td.prev {
          pointer-events: none;
        }
        .el-calendar-table:not(.is-range) td.next {
          // visibility: hidden;
        }
        .el-calendar-day {
          width: 100%;
          height: 100%;
          position: relative;
          padding: 0;
        }

        .current {
          width: 217px;
          height: 115px;
          min-height: 115px;
        }
        .prev,
        .next {
          width: 217px;
          height: 115px;
          .round {
            background: #ffffff !important;
            & > div {
              color: #b1bac7;
            }
          }
        }

        .el-date-editor--year,
        .el-date-editor--month {
          width: 150px;
          .el-input__inner {
            width: 100%;
          }
        }
        // 日历更改演示 -->
      }
    }
  }
}

.signList {
  display: flex;
  & > div:first-of-type {
    width: 1200px;
    .searchFrom {
      ::v-deep {
        .el-form-item__label {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
        }
      }
    }

    ::v-deep {
      .signTableHeader {
        background: #3464e0;
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #ffffff;
      }
      .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
        background: #f2f2f2;
      }
      .details {
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #a3a8bb;
        cursor: pointer;
      }

      .check-row,
      .checkRow {
        color: #3464e0;
        font-weight: bold;
      }
    }
  }
  .signListTitle {
    font-size: 14px;
    font-family: Microsoft YaHei-Bold, Microsoft YaHei;
    font-weight: bold;
    color: #0b1a44;
  }
  .signListDetails {
    flex: 1;
    height: 800px;
    border-left: 1px solid #eeeeef;
    padding-top: 24px;
    padding-left: 24px;
    overflow: auto;
    &::-webkit-scrollbar {
      width: 5px;
      background: #eee;
    }
    &::-webkit-scrollbar-thumb {
      background: #3464e0;
      border-radius: 5px;
    }
    ul {
      margin-top: 16px;
      li {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        width: 472px;
        height: 78px;
        border: 1px solid #0b1a44;
        border-left: 5px solid #0b1a44;
        background: #fafafb;
        border-radius: 8px;
        div {
          display: flex;
          flex-direction: column;
          margin-left: 25px;
          & > span:first-of-type {
            padding-bottom: 10px;
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #a3a8bb;
          }
          & > span:last-of-type {
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #0b1a44;
          }
        }
        .location {
          width: 260px;
          display: inline-block;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .type1 {
        background: #fffaf6;
        border-color: #ff7e26;
        & > div:first-of-type span:last-of-type {
          color: #ff7e26;
        }
      }
      .type0 {
        background: #f7f9fe;
        border-color: #3464e0;
        & > div:first-of-type span:last-of-type {
          color: #3464e0;
        }
      }
    }
  }
}
::v-deep {
  .el-card {
    box-shadow: none;
  }
  .el-tabs__header {
    margin-bottom: 0;
  }
  // <-- 设置tabs样式
  .el-tabs__item {
    font-size: 16px;
    font-family: Microsoft YaHei-Bold, Microsoft YaHei;
    font-weight: bold;
    color: #0b1a44;
  }
  .el-tabs__item.is-active {
    color: #3464e0;
  }
  .el-tabs__active-bar {
    background-color: #3464e0;
    height: 1px;
  }
  .el-tabs__nav-wrap::after {
    background: #eeeeef;
    height: 1px;
  }
  // 设置tabs样式 -->

  // <--设置树形样式
  .el-tree-node {
    height: 36px;
  }
  .el-tree-node__content {
    height: 100%;
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #4a4a4a;
  }
  .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    background: #eff3fd;
    font-size: 14px;
    font-family: Microsoft YaHei-Bold, Microsoft YaHei;
    font-weight: bold;
    color: #3464e0;
  }
  // 设置树形样式 -->
}
</style>
<style lang="scss">
.atooltip {
  padding: 17px 32px;
  background: #363636;
  box-shadow: 0px 6px 12px 1px rgba(141, 163, 200, 0.16);
  border-radius: 4px 4px 4px 4px;
  opacity: 0.84;
  .tooltip {
    position: relative;
    padding-bottom: 8px;
    padding-top: 8px;
    border-bottom: 1px solid #595a5c;
    &::before {
      content: '';
      position: absolute;
      left: -10px;
      top: 5px;
      width: 2px;
      height: 24px;
      background: #3464e0;
    }
    & > span:first-of-type {
      font-size: 12px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #ffffff;
    }
    & > span:nth-of-type(2) {
      margin: 0 12px;
      font-size: 12px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      color: #ffffff;
    }
    & > span:last-of-type {
      margin: 0 12px;
      font-size: 12px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      color: #ffffff;
    }
  }
  .middleTip {
    &::before {
      background: #ff7e26;
    }
  }
  .lateTip {
    &::before {
      background: #0b1a44;
    }
  }
}
</style>
<style lang="scss">
.locationTooltip {
  max-width: 500px;
  line-height: 26px;
  font-size: 16px;
}
</style>
