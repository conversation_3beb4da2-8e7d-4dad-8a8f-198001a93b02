<template>
  <div class="app-container">
    <el-card>
      <div slot="header">
        <el-row type="flex" justify="space-between" align="middle">
          <el-col :span="6">
            <span>客户详情</span>
          </el-col>
          <el-col :span="1">
            <el-button type="primary" size="small" icon="el-icon-back">返回</el-button>
          </el-col>
        </el-row>
      </div>
      <el-tabs v-model="activeName">
        <el-tab-pane label="客户信息" name="clientInfo">
          <div v-if="JSON.stringify(clientInfo) !== '{}'" class="clientInfo">
            <div class="clientInfo_top">
              <h2 style="color: #000">{{ clientInfo.customerName }}</h2>
              <div class="source">{{ clientInfo.source | source }}</div>
              <div class="level">{{ clientInfo.level | level }}</div>
            </div>
            <el-descriptions :column="3" class="clientInfo_center">
              <el-descriptions-item label="联系方式">
                <span>{{ clientInfo.phone }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="联系地址">
                <span>{{ clientInfo.address }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="所属区域">
                <span>{{ clientInfo.name }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="客户负责人">
                <span>{{ clientInfo.realName }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">
                <span>{{ clientInfo.createTime }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="更新时间">
                <span>{{ clientInfo.updateTime }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="备注">
                <span>{{ clientInfo.remark }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-tab-pane>
        <el-tab-pane label="客户联系人" name="clientContact">
          <el-row type="flex" justify="end">
            <el-col :span="8">
              <div style="display: flex">
                <el-input v-model="detailsContactsInfo.contactsName" placeholder="请输入联系人" class="input-with-select" clearable>
                  <el-button slot="append" icon="el-icon-search" @click="getSecretCustomerDetailsContactsList"></el-button>
                </el-input>
                <el-button type="primary" size="small" style="margin-left: 15px" @click="addPersonDialog = true"> 添加联系人</el-button>
              </div>
            </el-col>
          </el-row>
          <el-table :data="detailsContactsList" style="width: 100%; margin-top: 15px" border>
            <el-table-column align="center" prop="phone" label="联系方式" width="width"> </el-table-column>
            <el-table-column align="center" prop="contactsName" label="姓名" width="width"> </el-table-column>
            <el-table-column align="center" prop="contactsName" label="性别" width="width">
              <template v-slot="{ row }">
                <span>{{ row.sex | sexHandle }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="majorName" label="所属专业" width="width"> </el-table-column>
            <el-table-column align="center" prop="duties" label="职务" width="width"> </el-table-column>
            <el-table-column align="center" prop="remark" label="备注" width="width"> </el-table-column>
            <el-table-column align="center" label="操作" width="width">
              <template v-slot="{ row }">
                <el-button type="danger" size="small" @click="removeContact(row)">移除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination background layout="prev, pager, next" :total="ContactTotal" :page-size="10" hide-on-single-page> </el-pagination>
        </el-tab-pane>
        <el-tab-pane label="商机信息" name="businessInfo">
          <el-table :data="chanceList" style="width: 100%" border>
            <el-table-column align="center" prop="chanceName" label="商机名称" width="width"> </el-table-column>
            <el-table-column align="center" prop="type" label="商机阶段" width="width">
              <template v-slot="{ row }">
                <span>{{ row.type | chanceType }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="updateTime" label="更新时间" width="width"> </el-table-column>
            <el-table-column align="center" label="操作" width="width">
              <template v-slot="{ row }">
                <el-button type="primary" size="small" @click="chanceDetails(row)">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="附件" name="files">
          <el-row type="flex" justify="end">
            <el-col :span="1.5">
              <el-button type="primary" size="small" @click="uploadingFileDialog = true">上传附件</el-button>
            </el-col>
          </el-row>
          <ul class="fileList">
            <li v-for="item in filesDetailsList" :key="item.name">
              <div>
                <div class="fileImg">
                  <img src="@/assets/meeting/file.png" alt="" />
                  <span>{{ item.fileSize }}KB</span>
                </div>
                <div>
                  <el-tooltip class="item" effect="dark" :content="item.fileName" placement="top">
                    <span>{{ item.fileName }}</span>
                  </el-tooltip>
                  <i v-if="$route.params.type !== '0'" class="el-icon-edit" @click="rechristen(item)"></i>
                </div>
              </div>
              <div v-if="item.percentage && item.percentage !== 100">
                <el-progress :percentage="item.percentage"></el-progress>
              </div>
              <div>
                <span>{{ item.realName }}上传于{{ item.createTime | formatDate }}</span>
                <i v-if="$route.params.type !== '0'" class="el-icon-download" @click="downloadFile(item)"></i>
                <i v-if="$route.params.type !== '0'" class="el-icon-delete" @click="delFile(item)"></i>
              </div>
            </li>
          </ul>
        </el-tab-pane>
        <el-tab-pane label="日志信息" name="logInfo">
          <el-table :data="logDetailsList" style="width: 100%" border @row-click="showLogDetails">
            <el-table-column align="center" prop="name" label="标题" width="width">
              <template v-slot="{ row }">
                <span>{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="customerNames" label="客户名称" width="width"> </el-table-column>
            <el-table-column align="center" prop="contactsNames" label="客户联系人" width="width"> </el-table-column>
            <el-table-column align="center" prop="realName" label="创建人" width="width"> </el-table-column>
            <el-table-column align="center" prop="createTime" label="创建时间" width="width"> </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-card>
    <el-dialog title="新增客户联系人" :visible.sync="addPersonDialog" width="500px" :close-on-click-modal="false" @close="ContactClose">
      <el-form ref="clientContactForm" :model="clientContactInfo" label-width="85px" :rules="clientContactFormRules">
        <el-form-item label="姓名:" prop="contactsName">
          <el-input v-model="clientContactInfo.contactsName" size="small" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="专业:">
          <el-select ref="selecteltree" v-model="majorName" size="small" clearable placeholder="请选择专业" @focus="getSpecialty">
            <el-option v-for="item in menu" :key="item.id" :label="item.majorName" :value="item.id" style="display: none" />
            <el-tree style="padding-left: 15px" :data="menu" node-key="id" empty-text="暂无菜单" highlight-current :expand-on-click-node="false" :props="defaultProps" current-node-key="id" default-expand-all @node-click="handleNodeClick" />
          </el-select>
        </el-form-item>
        <el-form-item label="联系方式:" prop="phone">
          <el-input v-model="clientContactInfo.phone" placeholder="请输入联系方式" size="small" maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="性别:" prop="sex">
          <el-radio v-model="clientContactInfo.sex" label="M">男</el-radio>
          <el-radio v-model="clientContactInfo.sex" label="F">女</el-radio>
        </el-form-item>
        <el-form-item label="职务:" prop="duties">
          <el-input v-model="clientContactInfo.duties" placeholder="请输入职务" size="small" maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="备注:">
          <el-input v-model="clientContactInfo.remark" placeholder="请输入备注,200字以内" size="small" type="textarea" maxlength="200"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="addPersonDialog = false">取 消</el-button>
        <el-button type="primary" @click="saveContact">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 商机信息详情 -->
    <el-dialog title="商机详情" :visible.sync="chanceDetailsDialog" width="550px">
      <el-descriptions v-if="chanceDetailsDialog" :column="1">
        <el-descriptions-item label="商机名称">{{ chanceDetailsInfo.chanceName }}</el-descriptions-item>
        <el-descriptions-item label="商机阶段">{{ chanceDetailsInfo.type | chanceType }}</el-descriptions-item>
        <el-descriptions-item label="创建人">{{ chanceDetailsInfo.realName }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ chanceDetailsInfo.updateTime }}</el-descriptions-item>
        <el-descriptions-item label="备注">{{ chanceDetailsInfo.remark }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer">
        <el-button size="small" @click="chanceDetailsDialog = false">取 消</el-button>
        <el-button type="primary" size="small" @click="chanceDetailsDialog = false">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 上传附件 -->
    <el-dialog title="上传附件" :visible.sync="uploadingFileDialog" width="450px" :close-on-click-modal="false">
      <div style="width: 360px; margin: 0 auto">
        <el-upload class="upload-demo" drag :action="action" :headers="header" :file-list="fileList" multiple :before-upload="beforeUpload" :on-success="uploadSuccess" :on-remove="uploadRemove">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div slot="tip" class="el-upload__tip" style="color: red; line-height: 15px">只能上传.doc .docx .xls .xlsx .pdf .jpg .png .zip格式的附件，且大小不超过50MB</div>
        </el-upload>
      </div>
      <div slot="footer">
        <el-button @click="uploadingFileDialog = false">取 消</el-button>
        <el-button type="primary" @click="uploadingFile">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 文件重命名dialog -->
    <el-dialog title="文件重命名" :visible.sync="rechristenDialog" width="400px">
      <div style="width: 300px; margin: 0 auto">
        <el-input v-model="fileName" placeholder="请输入文件名称" clearable></el-input>
      </div>
      <div slot="footer">
        <el-button @click="rechristenDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirmRechristen">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 日志查看详情dialog -->
    <el-dialog title="日志详情" :visible.sync="logDetailsDialog" width="550px" class="logDetailsDialog">
      <div>
        <el-descriptions :title="`${logDetailsInfo.realName}的工作日志(${logDetailsInfo.time})`" direction="vertical" :column="1">
          <el-descriptions-item label="拜访内容">{{ logDetailsInfo.visitContent }}</el-descriptions-item>
          <el-descriptions-item label="拜访结果">{{ logDetailsInfo.visitResult }}</el-descriptions-item>
          <el-descriptions-item label="跟踪客户">
            <el-table :data="logDetailsInfo.customers" style="width: 100%" border>
              <el-table-column align="center" prop="customerName" label="客户名称" width="width"> </el-table-column>
              <el-table-column align="center" prop="contactsName" label="客户联系人" width="width"> </el-table-column>
              <el-table-column align="center" prop="majorName" label="所属专业" width="width"> </el-table-column>
              <el-table-column align="center" prop="phone" label="联系方式" width="width"> </el-table-column>
            </el-table>
          </el-descriptions-item>
          <el-descriptions-item label="明日计划">{{ logDetailsInfo.planContent }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <div slot="footer">
        <el-button size="small" @click="logDetailsDialog = false">取 消</el-button>
        <el-button type="primary" size="small" @click="logDetailsDialog = false">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { majorList } from '@/api/specialty.js'
import { secretCustomerCustomerDetails, secretCustomerDetailsContactsList, secretCustomerSaveContacts, secretCustomeRemoveContacts, secretCustomeDetailsChanceList, secretCustomeDetailsLogList } from '@/api/clientele'
import { checkPhone } from '@/filters'
import { meetingUpdateFileName, meetingDeleteFile, meetingFilesDetails, meetingSaveFile, meetingRenameFile } from '@/api/meeting'
import { mapGetters } from 'vuex'

export default {
  name: '',
  data() {
    return {
      activeName: 'clientInfo',
      clientInfo: {},
      detailsContactsInfo: {
        customerId: this.$route.params.id,
        contactsName: null,
        pageNum: 1,
        pageSize: 10
      },
      detailsContactsList: [],
      ContactTotal: 0,
      searchDetailsContacts: null,
      addPersonDialog: false,
      clientContactInfo: {
        customerId: this.$route.params.id,
        contactsName: null,
        majorId: null,
        phone: null,
        sex: null,
        duties: null,
        remark: null
      },
      majorName: null,
      menu: [],
      defaultProps: {
        label: 'majorName',
        children: 'children'
      },
      clientContactFormRules: {
        contactsName: [
          {
            required: true,
            message: '请输入姓名',
            trigger: 'blur'
          }
        ],
        phone: [
          { required: true, message: '请输入联系方式', trigger: 'blur' },
          {
            validator: checkPhone
          }
        ],
        sex: [{ required: true, message: '请选择性别', trigger: 'change' }],
        duties: [{ required: true, message: '请输入职务', trigger: 'blur' }]
      },
      chanceList: [], // 商机
      chanceInfo: {
        customerId: this.$route.params.id,
        pageNum: 1,
        pageSize: 10
      },
      chanceDetailsInfo: {},
      chanceDetailsDialog: false,
      uploadingFileDialog: false, // 上传附件dialog
      filesDetailsList: [],
      filesDetailsTotal: 0,
      filesDetailsInfo: {
        meetingId: this.$route.params.id,
        belongType: 9,
        pageNum: 1,
        pageSize: 10
      },
      action: window.config.VUE_APP_BASE_API + '/system/upload/file',
      header: {
        Authorization: null
      },
      fileList: [],
      rechristenDialog: false,
      fileName: null,
      oldPath: null,
      fileId: null,
      logDetailsList: [], // 日志信息
      logInfo: {
        customerId: this.$route.params.id,
        pageNum: 1,
        pageSize: 10
      },
      logDetailsDialog: false,
      logDetailsDialogTitle: null,
      logDetailsInfo: {}
    }
  },
  computed: {
    ...mapGetters(['organizationId', 'token'])
  },
  created() {
    // 获取客户信息
    this.getSecretCustomerCustomerDetails()
    // 获取客户联系人
    this.getSecretCustomerDetailsContactsList()
    // 获取商机信息
    this.getSecretCustomeDetailsChanceList()
    // 获取附件详情
    this.getFilesDetails()
    // 获取日志信息
    this.getSecretCustomeDetailsLogList()
  },
  methods: {
    // 获取客户信息
    async getSecretCustomerCustomerDetails() {
      const { data } = await secretCustomerCustomerDetails({ customerId: parseInt(this.$route.params.id) })
      this.clientInfo = data
    },
    // 获取客户联系人列表
    async getSecretCustomerDetailsContactsList() {
      const { data } = await secretCustomerDetailsContactsList(this.detailsContactsInfo)
      this.detailsContactsList = data.list
      this.ContactTotal = data.total
    },
    // 获取商机信息
    async getSecretCustomeDetailsChanceList() {
      const { data } = await secretCustomeDetailsChanceList(this.chanceInfo)
      this.chanceList = data.list
    },
    // 选择专业
    async getSpecialty() {
      const { data } = await majorList()
      this.menu = data[0].children
    },

    // 树形节点被点击时的回调
    handleNodeClick(node) {
      this.clientContactInfo.majorId = node.id
      this.majorName = node.majorName
      this.$refs['selecteltree'].blur()
      this.$forceUpdate()
    },
    // 保存联系人
    saveContact() {
      this.$refs['clientContactForm'].validate(async (val) => {
        if (val) {
          await secretCustomerSaveContacts(this.clientContactInfo)
          this.$message.success('新增联系人成功!')
          this.addPersonDialog = false
          this.getSecretCustomerDetailsContactsList()
        }
      })
    },
    // 移除客户联系人
    removeContact(row) {
      console.log(row)
      this.$confirm('确定要移除该客户联系人吗, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await secretCustomeRemoveContacts({ contactsId: row.contactsId })
          this.$message({
            type: 'success',
            message: '移除成功!'
          })
          this.getSecretCustomerDetailsContactsList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消移除'
          })
        })
    },
    // 添加联系人dialog关闭触发事件
    ContactClose() {
      this.clientContactInfo = {
        customerId: this.$route.params.id,
        contactsName: null,
        majorId: null,
        phone: null,
        sex: null,
        duties: null,
        remark: null
      }
      this.majorName = null
      this.$refs['clientContactForm'].resetFields()
    },
    // 详情信息点击详情
    chanceDetails(row) {
      this.chanceDetailsInfo = { ...row }
      this.chanceDetailsDialog = true
      console.log(row)
    },
    // 获取附件详情列表
    async getFilesDetails() {
      const { data } = await meetingFilesDetails(this.filesDetailsInfo)
      this.filesDetailsList = data.list
      this.filesDetailsTotal = data.total
    },
    beforeUpload(file) {
      this.header.Authorization = `Bearer ${this.token}`
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isDOC = file.type === 'application/msword'
      const isDOCX = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      const isXLS = file.type === 'application/vnd.ms-excel'
      const isXLSX = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      const isPDF = file.type === 'application/pdf'
      const isZIP = file.type === 'application/x-zip-compressed'
      const isLt2M = file.size / 1024 / 1024 < 50
      if (!isLt2M) {
        this.$message.error('上传附件大小不能超过 50MB!')
      }
      if (!isJPG && !isPNG && !isDOC && !isDOCX && !isXLS && !isXLSX && !isPDF && !isZIP) {
        this.$message.warning('只能上传.doc .docx .xls .xlsx .pdf .jpg .png .zip 格式的附件')
      }
      return (isJPG || isPNG || isDOC || isDOCX || isXLS || isXLSX || isPDF || isZIP) && isLt2M
    },
    uploadSuccess(response, file, fileList) {
      this.fileList = fileList
    },
    uploadRemove(file, fileList) {
      this.fileList = fileList
    },
    // 确定上传
    async uploadingFile() {
      const list = []
      this.fileList.forEach((item) => {
        list.push({
          fileName: item.name,
          fileSize: item.size / 1024,
          fileUrl: item.response.data[0],
          meetingId: this.$route.params.id,
          belongType: 9
        })
      })
      await meetingSaveFile(list)
      this.$message.success('上传附件成功')
      this.uploadingFileDialog = false
      this.fileList = []
      this.getFilesDetails()
    },
    // 文件重命名
    rechristen(row) {
      console.log(row)
      this.fileName = row.fileName
      this.oldPath = row.fileUrl.split('workmanage/')[1]
      this.fileId = row.fileId
      this.rechristenDialog = true
    },
    // 确定重命名
    async confirmRechristen() {
      const { data } = await meetingRenameFile({ oldPath: this.oldPath, newPath: this.fileName })
      await meetingUpdateFileName({ fileId: this.fileId, fileName: this.fileName, fileUrl: data })
      this.$message.success('文件重命名成功')
      this.rechristenDialog = false
      this.getFilesDetails()
    },
    // 下载文件
    downloadFile(row) {
      console.log(row)
      window.open(row.fileUrl, '_blank')
    },
    // 删除附件
    delFile(row) {
      this.$confirm('确认要删除该文件吗, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await meetingDeleteFile({ fileId: row.fileId })
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getFilesDetails()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 获取日志信息
    async getSecretCustomeDetailsLogList() {
      const { data } = await secretCustomeDetailsLogList(this.logInfo)
      this.logDetailsList = data.list
      console.log(this.logDetailsList)
    },
    // 表格行点击事件，查看详情
    showLogDetails(row) {
      this.logDetailsInfo = { ...row }
      this.logDetailsDialog = true
      console.log(row)
    }
  }
}
</script>

<style scoped lang="scss">
.clientInfo {
  .clientInfo_top {
    display: flex;
    align-items: center;
    .source {
      margin-left: 15px;
      padding: 6px 20px;
      background: #3ba0ff;
      border-radius: 8px;
      color: #fff;
    }
    .level {
      margin-left: 15px;
      padding: 6px 20px;
      background: #fdbdbd;
      border-radius: 8px;
      color: #f8224f;
    }
  }
}

// 附件
.fileList {
  display: flex;
  flex-wrap: wrap;
  li {
    margin-right: 14px;
    margin-bottom: 14px;
    padding: 14px 10px 20px 10px;
    width: 268px;
    height: 124px;
    background: #f9f9f9;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #eeeeef;
    box-sizing: border-box;
    width: 19.3%;
    &:nth-of-type(5n) {
      margin-right: 0;
    }
    & > div {
      &:first-of-type {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .fileImg {
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          img {
            width: 46px;
            height: 38px;
          }
          span {
            font-size: 12px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #868b9f;
            line-height: initial;
          }
        }
        & > div {
          margin-left: 3px;
          line-height: initial;
          .el-textarea {
            width: initial;
            .el-textarea__inner {
              width: 100%;
            }
          }

          &:last-of-type {
            span {
              display: inline-block;
              max-width: 210px;
              font-size: 14px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #0b1a44;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            i {
              margin-left: 5px;
              color: #ff7e26;
              font-size: 18px;
              cursor: pointer;
            }
          }
        }
      }
      &:last-of-type {
        margin-top: 18px;
        font-size: 12px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #a3a8bb;
        line-height: initial;
        i {
          font-size: 18px;
          float: right;
          margin-left: 15px;
          cursor: pointer;
        }
        .el-icon-download:hover {
          color: #3464e0;
        }
        .el-icon-delete:hover {
          color: #eb6557;
        }
      }
    }
  }
}
::v-deep {
  .el-dialog__body {
    padding: 30px 50px;
  }
  .logDetailsDialog {
    .el-dialog__body {
      padding: 20px 25px;
      .el-descriptions-row {
        font-size: 16px;
        color: #000;
        .el-descriptions-item__label {
          font-weight: bold !important;
        }
      }
    }
  }
}
</style>
