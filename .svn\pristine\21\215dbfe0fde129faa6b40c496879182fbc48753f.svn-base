<template>
  <div class="app-container">
    <el-form ref="form" :model="queryInfo" inline label-width="80px">
      <el-form-item>
        <el-radio-group v-model="queryInfo.state" @change="stateChange">
          <el-radio-button :label="1">未解决工作问题</el-radio-button>
          <el-radio-button :label="2">已解决工作问题</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="问题部门:">
        <el-select v-model="queryInfo.problemOrgId" placeholder="请选择问题部门" @focus="getAllOrganization">
          <el-option v-for="item in allOrganization" :key="item.organizationId" :label="item.organizationName" :value="item.organizationId"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item style="margin-left: 50px" label="问题描述:">
        <el-input v-model="queryInfo.description" placeholder="请输入问题描述" maxlength="40"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button size="small" type="success" @click="getList">查询</el-button>
        <el-button size="small" type="primary" plain @click="reset">重置</el-button>
        <el-button size="small" type="primary" @click="addDialog = true">新增问题</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" style="width: 100%" border>
      <el-table-column align="center" prop="problemOrgName" label="问题部门" width="width"> </el-table-column>

      <el-table-column align="center" prop="name" label="问题名称" width="width"> </el-table-column>
      <el-table-column align="center" prop="description" label="问题描述" width="width"> </el-table-column>
      <el-table-column align="center" prop="state" label="问题状态" width="100">
        <template v-slot="{ row }">
          <el-tag effect="dark" :type="row.state === 1 ? 'warning' : 'success'">{{ row.state === 1 ? '未解决' : '已解决' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="feedbackTime" label="反馈时间" width="100"> </el-table-column>
      <el-table-column align="center" prop="resolveRealName" label="解决人" width="100"> </el-table-column>
      <el-table-column align="center" prop="helpOrgNames" label="协助部门	" width="width"> </el-table-column>
      <el-table-column align="center" prop="planTime" label="计划完成时间	" width="120"> </el-table-column>
      <el-table-column align="center" prop="realityTime" label="实际完成时间	" width="120"> </el-table-column>
      <el-table-column align="center" prop="createTime" label="创建时间	" width="150"> </el-table-column>
      <el-table-column align="center" prop="remark" label="备注	" width="width"> </el-table-column>
      <el-table-column align="center" label="操作" width="280">
        <template v-slot="{ row }">
          <el-button size="small" type="warning" @click="edit(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="remove(row)">删除</el-button>
          <el-button size="small" type="primary" @click="detils(row)">详情</el-button>
          <el-button size="small" type="info" @click="lookLog(row)">日志</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-pagination
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="[10, 15, 20, 30]"
      :total="total"
      :page-size.sync="queryInfo.pageSize"
      :current-page.sync="queryInfo.pageNum"
      style="text-align: center; margin-top: 15px"
      background
      @size-change="getList"
      @current-change="getList"
    >
    </el-pagination>
    <!-- 新增/修改 弹窗 -->
    <AddOrEditDialog ref="AddOrEditDialogRef" :show-dialog.sync="addDialog" @refreshList="getList" />
    <!-- 详情弹窗 -->
    <DetailsDialog ref="DetailsDialogRef" :show-dialog.sync="detailsDialog" />
    <!-- 日志 -->
    <LogDialog ref="LogDialogRef" />
  </div>
</template>
<script>
import AddOrEditDialog from './components/AddOrEditDialog.vue'
import DetailsDialog from './components/DetailsDialog.vue'
import LogDialog from './components/LogDialog.vue'
import { workTrackList, workTrackDetail, workTrackRemove } from '@/api/workTrack'
import { allOrganization } from '@/api/organization'

export default {
  name: '',
  components: {
    AddOrEditDialog,
    DetailsDialog,
    LogDialog
  },
  data() {
    return {
      queryInfo: {
        pageNum: 1,
        pageSize: 10,
        problemOrgId: null,
        description: null,
        state: 1
      },
      list: [],
      total: 0,
      allOrganization: [],
      addDialog: false,
      checkedInfo: null,
      detailsDialog: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    stateChange() {
      this.queryInfo.pageNum = 1
      this.queryInfo.pageSize = 10
      this.getList()
    },
    async getList() {
      const { data } = await workTrackList(this.queryInfo)
      this.list = data.list
      this.total = data.total
    },
    reset() {
      this.queryInfo.problemOrgId = null
      this.queryInfo.description = null
      this.getList()
    },
    async getAllOrganization() {
      const { data } = await allOrganization()
      this.allOrganization = data
    },
    async getDetails(id) {
      const { data } = await workTrackDetail(id)
      this.checkedInfo = data
    },
    async edit(row) {
      await this.getDetails(row.trackId)
      const info = this._.cloneDeep(this.checkedInfo)
      this.$refs['AddOrEditDialogRef'].showDetails(info)
      this.addDialog = true
    },
    remove(row) {
      this.$confirm('确定要删除该问题吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await workTrackRemove(row.trackId)
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    async detils(row) {
      await this.getDetails(row.trackId)
      this.$refs['DetailsDialogRef'].show(this.checkedInfo)
    },
    async lookLog(row) {
      await this.getDetails(row.trackId)
      this.$refs['LogDialogRef'].show(this.checkedInfo)
    }
  }
}
</script>
<style scoped lang="scss"></style>
