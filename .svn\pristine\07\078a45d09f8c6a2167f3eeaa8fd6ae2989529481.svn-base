<template>
  <div class="app-container">
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form ref="form" :model="queryInfo" label-width="80px" inline>
          <el-form-item label="产品名称:">
            <el-input v-model="queryInfo.productName" size="small" placeholder="请输入产品名称" clearable></el-input>
          </el-form-item>
          <el-form-item label="版本号:">
            <el-input v-model.number="queryInfo.version" type="number" min="0" size="small" clearable placeholder="请输入版本号"></el-input>
          </el-form-item>
          <el-form-item label="负责人:">
            <el-input v-model="queryInfo.username" size="small" placeholder="请输入负责人" clearable></el-input>
          </el-form-item>
          <el-form-item label="创建开始时间:" label-width="110px">
            <el-date-picker v-model="queryInfo.startTime" type="datetime" size="small" clearable placeholder="选择结束时间"> </el-date-picker>
          </el-form-item>
          <el-form-item label="结束时间:">
            <el-date-picker v-model="queryInfo.endTime" type="datetime" size="small" clearable placeholder="选择结束时间"> </el-date-picker>
          </el-form-item>
          <el-form-item label="产品类型:">
            <el-select v-model="queryInfo.productType" placeholder="请选择产品类型" size="small" clearable>
              <el-option label="软件" :value="1"> </el-option>
              <el-option label="硬件" :value="2"> </el-option>
              <el-option label="虚拟仿真" :value="3"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="success" size="small" @click="getProductList">查询</el-button>
            <el-button type="primary" size="small" @click="add">添加产品</el-button>
            <el-button type="primary" size="small" @click="reset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-table :data="productList" style="width: 100%" border>
      <el-table-column prop="productCode" label="产品编号" width="width" align="center"> </el-table-column>
      <el-table-column prop="productName" label="产品名称" width="width" align="center"> </el-table-column>
      <el-table-column prop="productType" label="产品类型" width="width" align="center">
        <template v-slot="{ row }">
          <span>{{ row.productType | formattingProductType }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="version" label="当前版本号" width="width" align="center"> </el-table-column>
      <el-table-column prop="userName" label="负责人" width="width" align="center"> </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="width" align="center"> </el-table-column>
      <el-table-column label="操作" width="350" align="center">
        <template v-slot="{ row }">
          <el-button type="success" size="small" @click="iterationRecord(row.productId)">迭代记录</el-button>
          <el-button type="primary" size="small" @click="details(row)">查看详情</el-button>
          <el-button type="warning" size="small" @click="edit(row)">修改</el-button>
          <el-button type="danger" size="small" @click="del(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination style="text-align: center; margin-top: 15px" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 15, 20, 30]" :total="total" :page-size.sync="queryInfo.pageSize" :current-page.sync="queryInfo.pageNum" @size-change="getProductList" @current-change="getProductList" />
    <el-drawer :visible.sync="drawer" direction="rtl" :show-close="false">
      <template v-slot:title>
        <el-row :gutter="10" type="flex" justify="space-between" align="middle">
          <el-col :span="4">
            <span style="color: #000; font-size: 18px">迭代记录</span>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" size="small" @click="iterationDialog = true">添加迭代</el-button>
          </el-col>
        </el-row>
      </template>
      <div style="border-top: 1px solid #eee">
        <light-timeline v-if="detailsInfo.iterations && detailsInfo.iterations.length >= 1" :items="detailsInfo.iterations" class="lightTimeline">
          <template v-slot:tag="{ item }">
            <div>版本号:{{ item.version }}</div>
            <div>{{ item.time | formatDate }}</div>
            <div>创建人:{{ item.realName }}</div>
          </template>
          <template v-slot:content="{ item }">
            <span>{{ item.description }}</span>
            <el-row :gutter="10" type="flex" justify="end" style="margin-top: 15px">
              <el-col :span="8.5">
                <el-button type="warning" size="mini" @click="editIteration(item)">修改</el-button>
                <el-button type="danger" size="mini">删除</el-button>
              </el-col>
            </el-row>
          </template>
        </light-timeline>
      </div>
    </el-drawer>
    <el-dialog :title="showTitle" :visible.sync="iterationDialog" width="500px" :close-on-click-modal="false" @close="close">
      <el-form ref="iterationForm" :model="iterationForm" label-width="90px" :rules="rules">
        <el-form-item label="版本号:" prop="version">
          <el-input v-model="iterationForm.version" size="small" placeholder="请输入版本号" maxlength="20" oninput="value = value.replace(/[\u4E00-\u9FA5]/g,'')"></el-input>
        </el-form-item>
        <el-form-item label="迭代时间:" prop="time">
          <el-date-picker v-model="iterationForm.time" size="small" type="datetime" placeholder="选择迭代时间"> </el-date-picker>
        </el-form-item>
        <el-form-item label="迭代描述:">
          <el-input v-model="iterationForm.description" size="small" placeholder="请输入描述,200字以内" maxlength="200" type="textarea" clearable></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="iterationDialog = false">取 消</el-button>
        <el-button type="primary" @click="addIteration">确 定</el-button>
      </div>
    </el-dialog>
    <improvement ref="improvement" :show-dialog.sync="showDialog" @success="getProductList" />
  </div>
</template>

<script>
import { getdictTypeList, productDetails, productRemove, saveProductIteration, updateProductIteration } from '@/api/product'
import improvement from '@/views/product/components/improvement'
import { formatDate } from '@/filters'
export default {
  name: 'Product',
  components: {
    improvement
  },
  data() {
    return {
      productList: [],
      queryInfo: {
        productName: null,
        version: null,
        username: null,
        startTime: null,
        endTime: null,
        productType: null,
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      showDialog: false,
      detailsInfo: {},
      drawer: false,
      iterationDialog: false,
      iterationForm: {
        productId: null,
        version: null,
        time: null,
        description: null
      },
      rules: {
        version: [
          {
            required: true,
            tigger: 'blur',
            message: '版本号不能为空'
          }
        ],
        time: [
          {
            required: true,
            tigger: 'blur',
            message: '迭代时间不能为空',
            type: 'date'
          }
        ]
      }
    }
  },
  computed: {
    showTitle() {
      return this.iterationForm.iterationId ? '修改迭代' : '添加迭代'
    }
  },
  created() {
    this.getProductList()
  },
  methods: {
    async getProductList() {
      const { data } = await getdictTypeList(this.queryInfo)
      this.productList = data.list
      this.total = data.total
      console.log(data)
    },
    reset() {
      this.queryInfo = {
        productName: null,
        version: null,
        username: null,
        startTime: null,
        endTime: null,
        productType: null,
        pageNum: 1,
        pageSize: 10
      }
      this.getProductList()
    },
    add() {
      this.showDialog = true
    },
    // 迭代记录
    async iterationRecord(productId) {
      const { data } = await productDetails({ productId })
      this.detailsInfo = data
      this.drawer = true
    },
    // 添加迭代记录
    addIteration() {
      this.$refs['iterationForm'].validate(async (val) => {
        if (val) {
          console.log(this.iterationForm)
          this.iterationForm.productId = this.detailsInfo.productId
          if (this.iterationForm.iterationId) {
            await updateProductIteration(this.iterationForm)
            this.$message.success('修改成功')
            this.iterationDialog = false
            this.iterationRecord(this.detailsInfo.productId)
          } else {
            await saveProductIteration(this.iterationForm)
            this.$message.success('添加成功')
            this.iterationDialog = false
            this.iterationRecord(this.detailsInfo.productId)
          }
        }
      })
    },
    // 修改迭代记录
    editIteration(item) {
      this.iterationDialog = true
      this.iterationForm = { ...item, productId: null }
      this.iterationForm.time = formatDate(this.iterationForm.time)
      this.iterationForm.time = new Date(this.iterationForm.time)
      console.log(this.iterationForm.time)
    },

    // 关闭添加迭代记录dialog触发的事件
    close() {
      this.iterationForm = {
        productId: null,
        version: null,
        time: null,
        description: null
      }
      this.$refs['iterationForm'].resetFields()
    },
    details(row) {
      this.$router.push(`/product/details/${row.productId}`)
    },
    async edit(row) {
      const { data } = await productDetails({ productId: row.productId })
      this.$refs['improvement'].edit(data)
      this.showDialog = true
    },
    del(row) {
      this.$confirm('确定要删除吗, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await productRemove({ productId: row.productId })
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getProductList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep {
  .el-drawer__header {
    margin-bottom: 15px;
  }
  .el-timeline-item__dot {
    flex-direction: column;
    align-items: flex-start;
  }
  .line-container {
    margin-left: 25px;
    margin-top: 25px;
    &::after {
      left: 115px;
    }
    .line-item {
      padding: 0;
      padding-left: 45px;
      margin-top: 15px;
      min-height: 100px;
      max-width: 430px;
      .item-tag {
        top: 0;
        width: 120px;
        text-align: start;
        line-height: 20px;
      }
      .item-symbol {
        left: 19px;
      }
    }
  }
}
</style>
