// 工作台
import request from '@/utils/request'
/** 待处理查询 */
export function workbenchTodo() {
  return request({
    url: '/workbench/todo',
    method: 'GET'
  })
}
/** 我的待办查询 */
export function workbenchMyHandling() {
  return request({
    url: '/workbench/myHandling',
    method: 'GET'
  })
}

/** 操作记录 */
export function workBenchLog() {
  return request({
    url: '/workbench/workBenchLog',
    method: 'GET'
  })
}
/** 待办统计占比查询 */
export function HandlingStatis(params) {
  return request({
    url: '/workbench/HandlingStatis',
    method: 'GET',
    params
  })
}
/** 待办统计 - 流程查询 */
export function processStatis(params) {
  return request({
    url: '/workbench/processStatis',
    method: 'GET',
    params
  })
}
/** 待办统计 - 会议查询 */
export function meetingStatis(params) {
  return request({
    url: '/workbench/meetingStatis',
    method: 'GET',
    params
  })
}
/** 待办统计 - 培训查询 */
export function trainStatis(params) {
  return request({
    url: '/workbench/trainStatis',
    method: 'GET',
    params
  })
}
/** 待办统计 - 项目查询 */
export function projectStatis(params) {
  return request({
    url: '/workbench/projectStatis',
    method: 'GET',
    params
  })
}
