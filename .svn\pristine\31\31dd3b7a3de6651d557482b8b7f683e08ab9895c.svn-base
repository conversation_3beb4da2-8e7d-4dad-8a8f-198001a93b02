<template>
  <div class="LogList">
    <el-table :data="list" style="width: 100%" border header-cell-class-name="headerCell" cell-class-name="allCell">
      <el-table-column align="center" label="序号" width="80" type="index"> </el-table-column>
      <el-table-column prop="content" align="center" label="操作内容" width="width"> </el-table-column>
      <el-table-column prop="realName" align="center" label="操作人" width="width"> </el-table-column>
      <el-table-column prop="createTime" align="center" label="操作时间" width="width"> </el-table-column>
    </el-table>
    <el-pagination
      v-if="list.length > 0"
      layout="sizes,total,prev, pager, next"
      style="margin-top: 20px; text-align: center"
      :page-sizes="[5, 10, 15, 20]"
      background
      :total="total"
      :page-size.sync="queryInfo.pageSize"
      :current-page.sync="queryInfo.pageNum"
      @size-change="getList"
      @current-change="getList"
    />
  </div>
</template>
<script>
import { contractLogList } from '@/api/contractNew.js'
export default {
  name: '',
  props: {
    contractId: {
      type: String,
      default: '0'
    }
  },
  data() {
    return {
      queryInfo: {
        pageNum: 1,
        pageSize: 5
      },
      list: [],
      total: 0
    }
  },
  created() {
    this.getList()
  },
  methods: {
    async getList() {
      const { data } = await contractLogList({ ...this.queryInfo, contractId: this.contractId })
      this.list = data.list
      this.total = data.total
      console.log('操作日志', data)
    }
  }
}
</script>
<style scoped lang="scss">
.LogList {
  padding: 20px;
}
.el-table {
  ::v-deep {
    .el-table__cell {
      border-right: 0;
    }
    .fileName {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #000000;
      .cell {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis; /* 当文本溢出时显示省略号 */
      }
    }
    .createTime,
    .realName {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #999999;
    }
    .headerCell {
      height: 40px;
      padding: 0;
      background: #f8f8f8;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #666666;
    }
    .allCell {
      padding: 0;
      height: 44px;
    }
  }
}
</style>
