<template>
  <div class="FileInfo">
    <el-row type="flex" align="middle" justify="space-between">
      <div class="title">
        <img src="@/assets/contractNew/fileIcon.png" alt="" />
        合同附件信息
      </div>
      <!-- <div class="lookAll">查看更多 <i class="el-icon-arrow-right"></i></div> -->
    </el-row>
    <el-table :data="list" style="width: 100%" border header-cell-class-name="headerCell" cell-class-name="allCell">
      <el-table-column prop="fileName" label="附件名称" align="center" width="width" class-name="fileName">
        <template v-slot="{ row }">
          <el-tooltip class="item" effect="dark" :content="row.fileName" placement="top">
            <span>{{ row.fileName }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="上传时间" align="center" width="150" class-name="createTime"> </el-table-column>
      <el-table-column prop="realName" label="上传人" align="center" width="100" class-name="realName"> </el-table-column>
      <el-table-column prop="belongType" label="文件类型" align="center" width="100" class-name="realName">
        <template v-slot="{ row }">
          <span>{{ row.belongType | belongType }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="80">
        <template v-slot="{ row }">
          <section class="Operate">
            <el-button type="text" class="fileOperate" @click="look(row)">查看</el-button>
            <!-- <i class="line"></i>
            <el-button type="text" class="fileOperate" @click="download(row)">下载</el-button> -->
          </section>
        </template>
      </el-table-column>
    </el-table>

    <!-- <el-pagination
      v-if="list.length > 0"
      layout="total,prev, pager, next"
      style="margin-top: 20px; text-align: center"
      :page-sizes="[5, 10, 15, 20]"
      background
      :total="total"
      :page-size.sync="queryInfo.pageSize"
      :current-page.sync="queryInfo.pageNum"
      @size-change="getFileList"
      @current-change="getFileList"
    /> -->
    <ElImageViewer v-if="isViewerVisible" :url-list="urlList" :on-close="viewerClose" :z-index="999999" />
  </div>
</template>
<script>
import { contractFileList } from '@/api/contractNew'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'

export default {
  name: '',
  components: {
    ElImageViewer
  },
  props: {
    contractId: {
      type: String,
      default: '0'
    }
  },
  data() {
    return {
      queryInfo: { contractId: this.contractId },
      list: [],
      total: 0,
      isViewerVisible: false,
      urlList: []
    }
  },
  methods: {
    async getFileList() {
      const { data } = await contractFileList(this.queryInfo)
      this.list = data.list
      this.total = data.total
      console.log('附件列表', data)
    },
    look(row) {
      console.log(row)

      // const fileType = this._.last(row.fileUrl.split('.'))
      // if (fileType === 'jpg' || fileType === 'png') {
      //   this.lookFile({ fileUrl: row.fileUrl })
      // } else {
      const downloadLink = document.createElement('a')
      downloadLink.href = row.fileUrl
      downloadLink.setAttribute('target', '_blank') // 可选，设置下载文件的名称
      document.body.appendChild(downloadLink)
      downloadLink.click()
      document.body.removeChild(downloadLink)
      // }
    },
    lookFile(item) {
      this.urlList = [item.fileUrl]
      this.isViewerVisible = true
    },
    viewerClose() {
      this.isViewerVisible = false
      this.urlList = []
    },
    download(row) {
      const downloadLink = document.createElement('a')
      downloadLink.href = row.fileUrl
      downloadLink.setAttribute('download', row.fileName) // 可选，设置下载文件的名称
      document.body.appendChild(downloadLink)
      downloadLink.click()
      document.body.removeChild(downloadLink)
    }
  }
}
</script>
<style scoped lang="scss">
.FileInfo {
  padding: 20px;
  .title {
    display: flex;
    align-items: center;
    font-family: Source Han Sans CN;
    font-weight: 500;
    font-size: 18px;
    color: #000000;
    img {
      margin-right: 8px;
      width: 38px;
      height: 38px;
    }
  }
  .lookAll {
    display: felx;
    align-items: center;
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #666666;
    cursor: pointer;
  }
  .el-table {
    margin-top: 20px;
    ::v-deep {
      .el-table__cell {
        border-right: 0;
      }
      .fileName {
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #000000;
        .cell {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis; /* 当文本溢出时显示省略号 */
        }
      }
      .createTime,
      .realName {
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #999999;
      }
      .headerCell {
        height: 40px;
        padding: 0;
        background: #f8f8f8;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #666666;
      }
      .allCell {
        padding: 0;
        height: 44px;
      }
    }
  }
  .Operate {
    display: flex;
    justify-content: center;
    align-items: center;
    .line {
      margin: 0 10px;
      height: 11px;
      width: 1px;
      background: #e5e5e5;
      font-style: normal;
    }
    .fileOperate {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #3465df;
    }
  }
}
</style>
