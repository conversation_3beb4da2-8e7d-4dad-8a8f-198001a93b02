<template>
  <div class="app-container">
    <el-form ref="form" label-width="80px" inline>
      <el-form-item label="模块分类:">
        <el-radio-group v-model="queryInfo.type" @change="getList">
          <el-radio :label="1">客户管理</el-radio>
          <el-radio :label="2">客户联系人</el-radio>
          <el-radio :label="3">商机管理</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item style="margin-left: 15px">
        <el-button type="primary" size="small" plain @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" style="width: 100%" border>
      <el-table-column prop="type" label="模块分类" width="width" align="center">
        <template v-slot="{ row }">
          <span>{{ row.type | operateRecordType }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="模块分类" width="width" align="center">
        <template v-slot="{ row }">
          <el-tag :type="row.operType == 1 ? '' : row.operType == 2 ? 'warning' : 'danger'">{{ row.operType | operateRecordOperType }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="content" label="内容" align="center"> </el-table-column>
      <el-table-column prop="createUser" label="创建人" align="center"> </el-table-column>
      <el-table-column prop="realName" label="创建人姓名" align="center"> </el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center"> </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-pagination
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="[10, 15, 20, 30]"
      :total="total"
      :page-size.sync="queryInfo.pageSize"
      :current-page.sync="queryInfo.pageNum"
      style="text-align: center; margin-top: 15px"
      @size-change="getList"
      @current-change="getList"
    >
    </el-pagination>
  </div>
</template>
<script>
import { secretRecordList } from '@/api/operateRecord'
export default {
  name: '',
  data() {
    return {
      queryInfo: {
        pageNum: 1,
        pageSize: 10,
        type: null
      },
      list: [],
      total: 0
    }
  },
  created() {
    this.getList()
  },
  methods: {
    async getList() {
      const { data } = await secretRecordList(this.queryInfo)
      this.list = data.list
      this.total = data.total
    },
    reset() {
      this.queryInfo.type = null
      this.getList()
    }
  }
}
</script>
<style scoped lang="scss"></style>
