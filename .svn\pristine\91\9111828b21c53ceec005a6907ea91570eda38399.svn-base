<template>
  <div class="app-container">
    <el-row :gutter="10" class="top">
      <el-col :span="24">
        <i class="el-icon-location"></i>
        <span>当前位置：软件产品库 /</span>
        <span>产品详情</span>
      </el-col>
    </el-row>
    <div class="box">
      <div class="header">产品详情</div>
      <div class="body">
        <el-descriptions v-if="info" :column="1">
          <el-descriptions-item label="产品名称">
            <span class="name">{{ info.name }}</span>
          </el-descriptions-item>
          <el-descriptions-item>
            <span slot="label">专 <i v-html="'&emsp;&nbsp;'"></i> 业</span>
            <span>{{ info.majorNames.join(',') }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="单价（万元）">{{ info.offer === '-1' ? '待定' : info.offer }}</el-descriptions-item>
          <el-descriptions-item>
            <span slot="label">单 <i v-html="'&emsp;&nbsp;'"></i> 位</span>
            <span>{{ info.unit | softwareUnit }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="产品类别">
            <span class="type">{{ info.type | softwareType }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="产品参数">
            <span style="white-space: pre-wrap;">{{ info.param }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="产品简介">
            <span style="white-space: pre-wrap;">{{ info.description }}</span>
          </el-descriptions-item>

          <el-descriptions-item>
            <span slot="label">备 <i v-html="'&emsp;&nbsp;'"></i> 注</span>
            <span>{{ info.remark }}</span>
          </el-descriptions-item>
        </el-descriptions>
        <div class="statistics">
          <echart ref="statisticsEchart" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { tSoftwareProductDetail, tProductHistoryTrend } from '@/api/software'
import echart from '@/views/software/echart'
export default {
  name: '',
  components: {
    echart
  },
  data() {
    return {
      info: null
    }
  },
  created() {
    this.getDetails()
    this.getProductHistoryTrend()
  },
  methods: {
    async getDetails() {
      const { data } = await tSoftwareProductDetail({ id: this.$route.params.id })
      this.info = { ...data }
    },
    async getProductHistoryTrend() {
      const { data } = await tProductHistoryTrend({ productId: this.$route.params.id, type: 2 })
      const info = {
        time: data.map((item) => item.createTime),
        offer: data.map((item) => item.offer)
      }
      this.$refs['statisticsEchart'].init(info)
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  position: relative;
  padding: 0;
  background-color: #e8eaed;
  min-height: 100%;
  padding-top: 58px;
  padding-bottom: 10px;
  .top {
    position: absolute;
    top: 0px;
    width: 100%;
    background-color: #e8eaed;
    padding: 24px 0 16px 0;
    z-index: 999;
    .el-col {
      i {
        font-size: 14px;
        color: #657081;
      }
      span {
        &:first-of-type {
          margin: 0 5px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #657081;
        }
        &:last-of-type {
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
      }
    }
  }
  .box {
    width: 1754px;
    min-height: 790px;
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    .header {
      padding-top: 24px;
      padding-left: 56px;
      padding-bottom: 11px;
      border-bottom: 1px solid #eeeeef;
      font-size: 16px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #0b1a44;
    }
    .body {
      padding-top: 28px;
      padding-bottom: 30px;
      padding-left: 60px;
      padding-right: 30px;
      .name {
        font-size: 14px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        font-weight: bold;
        color: #0b1a44;
      }
      .type {
        color: #3464e0;
      }
      .statistics {
        margin-top: 12px;
        width: 1511px;
        height: 424px;
        background: #f5f5f5;
        border-radius: 8px 8px 8px 8px;
      }

      ::v-deep {
        .el-descriptions-item__label {
          width: 98px;
          text-align: right;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #868b9f;
        }
        .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
          padding-bottom: 28px;
        }
        .el-descriptions-item__content {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
        }
      }
    }
  }
}
</style>
