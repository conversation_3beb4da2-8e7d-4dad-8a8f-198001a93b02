<template>
  <div class="app-container">
    <!-- 添加的dialog -->
    <el-dialog title="新增商机" :visible.sync="addDialog" width="1150px" top="100px" :close-on-click-modal="false" @close="close">
      <el-form ref="form" :model="formInfo" label-width="130px" inline :rules="rules">
        <el-form-item label="商机编号:" prop="code" class="level">
          <el-input v-model="formInfo.code" placeholder="请输入商机编号" maxlength="30" disabled></el-input>
        </el-form-item>
        <el-form-item label="客户名称:" prop="customerId" class="level">
          <el-select v-model="formInfo.customerId" placeholder="请选择客户名称" @change="selectCustomerChange">
            <el-option v-for="item in allCustomer" :key="item.customerId" :value="item.customerId" :label="item.customerName"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="客户联系人:" prop="contactsId" class="level">
          <el-select v-model="formInfo.contactsId" placeholder="请选择客户联系人" @focus="getContactsList">
            <el-option v-for="item in allContacts" :key="item.contactsId" :value="item.contactsId" :label="item.contactsName"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="项目名称:" prop="name" class="name_input">
          <el-input v-model="formInfo.name" placeholder="请输入项目名称" maxlength="30"></el-input>
        </el-form-item>
        <el-form-item label="项目内容:" prop="content" class="name_input">
          <el-input v-model="formInfo.content" placeholder="请输入项目内容" type="textarea" maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="项目等级:" prop="rank" class="level">
          <el-select v-model="formInfo.rank" placeholder="请选择项目等级">
            <el-option :value="'A'" label="A"> </el-option>
            <el-option :value="'B'" label="B"> </el-option>
            <el-option :value="'C'" label="C"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="项目金额(万元):" prop="money" class="name_input">
          <el-input v-model="formInfo.money" placeholder="请输入项目金额" type="number"></el-input>
        </el-form-item>
        <el-form-item label="存在竞争对手情况:" prop="competitorInfo" class="name_input">
          <el-input v-model="formInfo.competitorInfo" placeholder="请输入竞争对手情况" type="textarea" maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="采购形式:" prop="collectType" class="level">
          <el-select v-model="formInfo.collectType" placeholder="请选择采购形式">
            <el-option :value="1" label="校内招标"> </el-option>
            <el-option :value="2" label="公开招标"> </el-option>
            <el-option :value="3" label="直采"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="项目当前情况:" prop="current" class="name_input">
          <el-input v-model="formInfo.current" placeholder="请输入项目当前情况" type="textarea" maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="招标时间:" prop="inviteTime">
          <el-date-picker v-model="formInfo.inviteTime" type="date" placeholder="选择日期" :format="'yyyy-MM-dd'" value-format="yyyy-MM-dd"></el-date-picker>
        </el-form-item>
        <el-form-item label="备注:" prop="remark" class="name_input">
          <el-input v-model="formInfo.remark" placeholder="请输入备注" type="textarea" maxlength="100"></el-input>
        </el-form-item>
      </el-form>
      <el-alert type="warning" title="项目等级划分" :closable="false">
        <p>C类：商机编号、项目负责人、客户、联系人、项目名称、项目内容、项目当前情况</p>
        <p>B类：C类基础上加项目金额（万元）、存在竞争对手情况被划分为B类</p>
        <p>A类：所有信息全部填写被划分为A类</p>
      </el-alert>
      <div slot="footer">
        <el-button @click="addDialog = false">取 消</el-button>
        <el-button type="primary" @click="subData">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { secretCustomeAllCustomer, secretCustomeAllContacts } from '@/api/clientele'
import { chanceSaveChance } from '@/api/chance'
export default {
  name: '',
  data() {
    return {
      addDialog: false,
      allCustomer: [],
      allContacts: [],
      formInfo: {
        customerId: null, // 客户id
        contactsId: null, // 客户联系人id
        name: null, // 项目名称
        content: null, // 项目内容
        rank: null, // 等级 A B C
        money: null, // 项目金额(万元)
        competitorInfo: null, // 存在竞争对手情况
        collectType: null, // 采购形式 1 校内招标 2 公开招标 3 直采
        inviteTime: null, // 招标时间
        current: null, // 项目当前情况
        remark: null, // 备注
        code: null // 商机编号
      },
      rules: {
        // code: [{ required: true, message: '请输入商机编号', trigger: 'blur' }],
        customerId: [{ required: true, message: '请选择客户', trigger: 'change', type: 'number' }],
        contactsId: [{ required: true, message: '请选择客户联系人', trigger: 'change', type: 'number' }],
        name: [{ required: true, message: '请输入项目名称', trigger: 'blur' }]
      }
    }
  },
  created() {},
  methods: {
    open() {
      this.addDialog = true
      this.getCustomerList()
    },
    close() {
      this.$refs['form'].resetFields()

      this.formInfo = {
        customerId: null,
        contactsId: null,
        name: null,
        content: null,
        rank: null,
        money: null,
        competitorInfo: null,
        collectType: null,
        inviteTime: null,
        current: null,
        remark: null,
        code: null
      }
      // this.addDialog = false
      // this.allCustomer = []
      // this.allContacts = []
    },
    selectCustomerChange() {
      this.formInfo.contactsId = null
    },
    async getCustomerList() {
      const { data } = await secretCustomeAllCustomer()
      this.allCustomer = data
    },
    async getContactsList() {
      if (this.formInfo.customerId) {
        const { data } = await secretCustomeAllContacts({ customerId: this.formInfo.customerId })
        this.allContacts = data
      }
    },
    subData() {
      this.$refs['form'].validate(async (val) => {
        if (!val) return
        const loading = this.$loading({
          lock: true,
          text: '数据保存中...',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        try {
          await chanceSaveChance(this.formInfo)
          this.$message.success('新增商机成功!')
          this.$emit('refresh')
          this.addDialog = false
        } catch (error) {
          this.$message.error('新增商机失败!')
        } finally {
          loading.close()
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .el-dialog__body {
    padding: 10px 30px;
    max-height: 600px;
    overflow: auto;
  }
  .el-form-item__content {
    .el-date-editor.el-input,
    .el-date-editor.el-input__inner {
      width: 400px !important;

      background: #ffffff;
      border-radius: 4px 4px 4px 4px;
    }
  }
  .name_input {
    .el-input__inner,
    .el-textarea__inner {
      width: 400px;
    }
  }
  .level {
    .el-input__inner {
      width: 400px;
    }
  }
  .el-alert {
    .el-alert__title {
      margin-top: 10px;
      font-size: 18px;
    }
    .el-alert__description {
      p {
        margin-top: 10px;
        font-size: 16px;
      }
    }
  }
}
</style>
