<template>
  <div class="app-container">
    <el-form ref="searchForm" label-width="80px" inline>
      <el-form-item label="商机名称:">
        <el-input v-model="queryInfo.chanceName" size="small" placeholder="请输入商机名称" clearable></el-input>
      </el-form-item>
      <el-form-item label="客户名称:">
        <el-input v-model="queryInfo.customerName" size="small" placeholder="请输入客户名称" clearable></el-input>
      </el-form-item>
      <el-form-item label="负责人:" label-width="90px">
        <el-input v-model="queryInfo.realName" size="small" placeholder="请输入负责人" clearable></el-input>
      </el-form-item>

      <el-form-item label="创建时间:">
        <el-date-picker v-model="date" size="small" type="daterange" range-separator="→" start-placeholder="开始日期" end-placeholder="结束日期" clearable @change="datePickerChange"> </el-date-picker>
      </el-form-item>
      <el-form-item label="商机阶段:">
        <el-select v-model="queryInfo.type" placeholder="请选择商家阶段" size="small" clearable>
          <el-option :value="1" label="商机咨询"> </el-option>
          <el-option :value="2" label="需求分析"> </el-option>
          <el-option :value="3" label="方案报价"> </el-option>
          <el-option :value="4" label="正式签约"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="success" size="small" @click="getChanceList">查询</el-button>
        <el-button type="primary" size="small" plain @click="reset">重置</el-button>
        <el-button type="primary" size="small" @click="addDialog = true">新增商机</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" style="width: 100%" border @row-click="goDetails">
      <el-table-column prop="chanceName" label="商机名称" width="width" align="center"> </el-table-column>
      <el-table-column prop="customerName" label="客户名称" width="width" align="center"> </el-table-column>
      <el-table-column label="商机阶段" width="width" align="center">
        <template v-slot="{ row }">
          <span>{{ row.type | chanceType }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="realName" label="负责人" width="width" align="center"> </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="width" align="center"> </el-table-column>
      <el-table-column prop="updateTime" label="更新时间" width="width" align="center"> </el-table-column>
    </el-table>
    <el-pagination v-if="list.length > 0" layout="total,prev, pager, next" style="margin-top:15px; text-align:right" :page-sizes="[5, 10, 15, 20]" background :total="total" :page-size.sync="queryInfo.pageSize" :current-page.sync="queryInfo.pageNum" @size-change="getChanceList" @current-change="getChanceList" />

    <!-- 添加的dialog -->
    <el-dialog title="新增商机" :visible.sync="addDialog" width="550px" :close-on-click-modal="false" @close="close">
      <el-form ref="form" :model="formInfo" label-width="90px" :rules="rules">
        <el-form-item label="商机名称:" prop="chanceName" class="name_input">
          <el-input v-model="formInfo.chanceName" placeholder="请输入商机名称" size="small" maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="客户名称:" prop="customerId" class="level">
          <el-select v-model="formInfo.customerId" placeholder="请选择客户名称" size="small" @focus="getSecretCustomerCustomerList">
            <el-option v-for="item in allCustomer" :key="item.customerId" :value="item.customerId" :label="item.customerName"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="商机阶段:" prop="type" class="level">
          <el-select v-model="formInfo.type" placeholder="请选择商机阶段" size="small">
            <el-option :value="1" label="商机咨询"> </el-option>
            <el-option :value="2" label="需求分析"> </el-option>
            <el-option :value="3" label="方案报价"> </el-option>
            <el-option :value="4" label="正式签约"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注:" class="name_input">
          <el-input v-model="formInfo.remark" placeholder="请输入备注" type="textarea" size="small"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="addDialog = false">取 消</el-button>
        <el-button type="primary" @click="subData">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { secretCustomeAllCustomer } from '@/api/clientele'
import { chanceChanceList, chanceSaveChance } from '@/api/chance'

import { formatDate } from '@/filters'

export default {
  name: 'Clientele',
  data() {
    return {
      queryInfo: {
        chanceName: null,
        customerName: null,
        type: null,
        realName: null,
        startTime: null,
        endTime: null,
        pageNum: 1,
        pageSize: 10
      },
      list: [],
      total: null,
      date: null,
      addDialog: false,
      formInfo: {
        chanceName: null,
        customerId: null,
        type: null,
        remark: null
      },
      rules: {
        chanceName: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
        customerId: [{ required: true, message: '请选择客户名称', trigger: 'change', type: 'number' }],
        type: [{ required: true, message: '请选择商机阶段', trigger: 'change', type: 'number' }]
      },
      allCustomer: []
    }
  },
  created() {
    this.getChanceList()
  },
  methods: {
    async getChanceList() {
      const { data } = await chanceChanceList(this.queryInfo)
      this.total = data.total
      this.list = data.list
      console.log(data)
    },
    async getSecretCustomerCustomerList() {
      const { data } = await secretCustomeAllCustomer()
      this.allCustomer = data
      console.log(data)
    },
    // 时间搜索的格式化
    datePickerChange(val) {
      if (val) {
        this.queryInfo.startTime = formatDate(val[0])
        this.queryInfo.endTime = formatDate(val[1], 'yyyy-MM-dd') + ' 23:59:59'
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getChanceList()
    },
    goDetails(row) {
      console.log(row)
      this.$router.push(`/chance/details/${row.chanceId}`)
    },
    // 重置搜索
    reset() {
      this.queryInfo = {
        customerName: null,
        source: null,
        level: null,
        realName: null,
        startTime: null,
        endTime: null,
        pageNum: 1,
        pageSize: 10
      }
      this.date = null
      this.getChanceList()
    },
    subData() {
      this.$refs['form'].validate(async (val) => {
        if (!val) return
        await chanceSaveChance(this.formInfo)
        this.$message.success('新增商机成功!')
        this.getChanceList()
        this.addDialog = false
      })
    },
    close() {
      this.formInfo = {
        chanceName: null,
        customerName: null,
        customerId: null,
        type: null,
        remark: null
      }
      this.area = null
      this.$refs['form'].resetFields()
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep {
  .el-dialog__body {
    padding: 30px 50px;
  }
  .el-form-item__content {
    .el-date-editor--daterange.el-input,
    .el-date-editor--daterange.el-input__inner,
    .el-date-editor--timerange.el-input,
    .el-date-editor--timerange.el-input__inner {
      width: 274px;
      background: #ffffff;
      border-radius: 4px 4px 4px 4px;
    }
    // .el-range-input {
    //   background-color: #fff;
    // }
  }
  .name_input {
    .el-input__inner {
      width: 355px;
    }
  }
  .level {
    .el-input__inner {
      width: 170px;
    }
  }
}
</style>
