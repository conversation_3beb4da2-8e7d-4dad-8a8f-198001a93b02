<template>
  <div class="">
    <el-dialog title="历史记录" :visible="showDialog" width="1200px" @close="close" @open="getDetails">
      <el-table :data="list" style="width: 100%" border>
        <el-table-column align="center" prop="name" label="产品名称" width="width"> </el-table-column>
        <el-table-column align="center" prop="labelName" label="产品标签" width="150"> </el-table-column>
        <el-table-column align="center" label="产品类别" width="120">
          <template v-slot="{ row }">
            <span>{{ row.type | softwareType }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="所属专业" width="250">
          <template v-slot="{ row }">
            <span>{{ row.majorNames.join(',') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="offer" label="单价(万元)" width="120" sortable="custom">
          <template v-slot="{ row }">
            <span>{{ row.offer === '-1' ? '待定' : row.offer }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="单位" width="120">
          <template v-slot="{ row }">
            <span>{{ row.unit | softwareUnit }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="120">
          <template v-slot="{ row }">
            <el-button type="primary" size="small" @click="openDetails(row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-if="list.length > 0"
        style="margin-top: 32px; text-align: center"
        layout="total,  prev, pager, next"
        background
        :total="total"
        :page-size.sync="queryInfo.pageSize"
        :current-page.sync="queryInfo.pageNum"
        @size-change="getDetails"
        @current-change="getDetails"
      />
      <div slot="footer">
        <el-button type="primary" @click="close">关 闭</el-button>
      </div>
    </el-dialog>
    <el-dialog title="详情" :visible.sync="dialogVisible" width="1600px">
      <el-descriptions v-if="info" :column="1">
        <el-descriptions-item label="产品名称">
          <span class="name">{{ info.name }}</span>
        </el-descriptions-item>
        <el-descriptions-item>
          <span slot="label">专 <i v-html="'&emsp;&nbsp;'"></i> 业</span>
          <span>{{ info.majorNames.join(',') }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="单价（万元）">{{ info.offer === '-1' ? '待定' : info.offer }}</el-descriptions-item>
        <el-descriptions-item>
          <span slot="label">单 <i v-html="'&emsp;&nbsp;'"></i> 位</span>
          <span>{{ info.unit | softwareUnit }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="产品类别">
          <span class="type">{{ info.type | softwareType }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="产品参数">
          <span style="white-space: pre-wrap">{{ info.param }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="产品简介">
          <span style="white-space: pre-wrap">{{ info.description }}</span>
        </el-descriptions-item>
        <el-descriptions-item>
          <span slot="label">备 <i v-html="'&emsp;&nbsp;'"></i> 注</span>
          <span>{{ info.remark }}</span>
        </el-descriptions-item>
      </el-descriptions>
      <div slot="footer">
        <el-button type="primary" @click="dialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { tSoftwareProductRecordList } from '@/api/software'
export default {
  name: '',
  props: {
    showDialog: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      queryInfo: {
        softwareProductId: null,
        pageNum: 1,
        pageSize: 10
      },
      list: [],
      dialogVisible: false,
      info: null
    }
  },
  methods: {
    close() {
      this.$emit('update:showDialog', false)
    },
    async getDetails() {
      const { data } = await tSoftwareProductRecordList(this.queryInfo)
      this.list = data.list
      this.total = data.total
      console.log(data)
    },
    openDetails(row) {
      this.info = { ...row }
      this.dialogVisible = true
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep {
  .el-dialog {
    overflow: auto;
    max-height: 700px;
  }
  .el-descriptions-item__label {
    min-width: 98px;
    text-align: right;
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #868b9f;
  }
  .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
    padding-bottom: 28px;
  }
  .el-descriptions-item__content {
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #0b1a44;
  }
}
</style>
