import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },

  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true
  },
  {
    path: '/',
    component: () => import('@/Home')
  },
  {
    path: '/basicInfo/home',
    component: Layout,
    meta: {
      type: 0
    },
    children: [
      {
        path: '',
        name: '工作台',
        component: () => import('@/views/dashboard/index'),
        meta: { title: '工作台', icon: 'dashboard', type: 0 }
      }
    ]
  },
  {
    path: '/personal/center',
    name: '个人中心',
    component: () => import('@/views/personalCenter/index'),
    hidden: true,
    meta: {
      type: 0
    }
  }

  // 404 page must be placed at the end !!!
  // { path: '*', redirect: '/404', hidden: true }
]
// 角色路由
export const asyncRoutes = [
  {
    path: '/user',
    component: Layout,
    meta: {
      title: '用户管理',
      flag: 'sys:sys:sys',
      type: 1
    },
    children: [
      {
        path: '',
        name: 'SystemUser',
        component: () => import('@/views/systemUser/index'),
        meta: {
          title: '用户管理',
          icon: 'user',
          flag: 'sys:sys:sys',
          type: 1
        }
      }
    ]
  },
  {
    path: '/organization',
    component: Layout,
    meta: {
      flag: 'org:org:org',
      type: 1
    },
    children: [
      {
        path: '',
        name: 'Organization',
        component: () => import('@/views/organization/index'),
        meta: {
          title: '部门管理',
          icon: 'osce-organization',
          flag: 'org:org:org',
          type: 1
        }
      }
    ]
  },
  {
    path: '/role',
    component: Layout,
    meta: {
      icon: 'role',
      flag: 'role:role:role',
      type: 1
    },
    children: [
      {
        path: '',
        name: 'Role',
        component: () => import('@/views/role/index'),
        meta: {
          title: '角色管理',
          flag: 'role:role:role',
          type: 1
        }
      }
    ]
  },
  {
    path: '/menu',
    component: Layout,
    meta: {
      icon: 'menu',
      flag: 'menu:menu:menu',
      type: 1
    },
    children: [
      {
        path: '',
        name: 'Menu',
        component: () => import('@/views/menu/index'),
        meta: {
          icon: 'menu',
          title: '菜单管理',
          flag: 'menu:menu:menu',
          type: 1
        }
      }
    ]
  },
  {
    path: '/log',
    component: Layout,
    meta: {
      icon: 'log',
      flag: 'log:log:log',
      type: 1
    },
    children: [
      {
        path: '',
        name: 'Log',
        component: () => import('@/views/log/index'),
        meta: {
          title: '日志管理',
          flag: 'log:log:log',
          type: 1
        }
      }
    ]
  },
  {
    path: '/dictionaries',
    component: Layout,
    meta: {
      icon: 'zidian',
      flag: 'Dict:Dict:Dict',
      type: 1
    },
    children: [
      {
        path: '',
        name: 'Dictionaries',
        component: () => import('@/views/dictionaries/index'),
        meta: {
          title: '字典管理',
          flag: 'Dict:Dict:Dict',
          type: 1
        }
      },
      {
        path: '/dictionaries/data/:dictType',
        name: 'DictionariesData',
        hidden: true,
        component: () => import('@/views/dictionaries/components/dictionariesData'),
        meta: {
          title: '字典数据管理',
          flag: 'Dict:Dict:Dict',
          type: 1,
          activeMenu: '/dictionaries'
        }
      }
    ]
  },
  {
    path: '/specialty',
    component: Layout,
    meta: {
      icon: 'specialty',
      flag: 'specialty',
      type: 1
    },
    children: [
      {
        path: '',
        name: 'Specialty',
        component: () => import('@/views/specialty/index'),
        meta: {
          title: '专业管理',
          flag: 'specialty',
          type: 1
        }
      }
    ]
  },
  {
    path: '/product',
    component: Layout,
    meta: {
      icon: '产品管理',
      flag: 'product',
      type: 1
    },
    children: [
      {
        path: '',
        name: 'Product',
        component: () => import('@/views/product/index'),
        meta: {
          title: '产品管理',
          flag: 'product',
          type: 1
        }
      },
      {
        path: '/product/add/:type/:productId',
        hidden: true,
        component: () => import('@/views/product/components/improvement'),
        meta: {
          title: '产品添加',
          flag: 'product',
          type: 1,
          activeMenu: '/product'
        }
      },
      {
        path: '/product/details/:productId',
        hidden: true,
        component: () => import('@/views/product/details'),
        meta: {
          title: '产品详情',
          flag: 'product',
          type: 1,
          activeMenu: '/product'
        }
      }
    ]
  },
  // 流程管理
  {
    path: '/process/management',
    component: Layout,
    name: 'Process',
    meta: { flag: 'process', type: 2 },
    children: [
      {
        path: '',
        component: () => import('@/views/process/index'),
        name: 'Process',
        meta: { title: '流程管理', flag: 'process', icon: 'process', type: 2, keepAlive: true }
      },
      {
        path: '/process/management/details/:processInstanceId',
        component: () => import('@/views/process/details'),
        name: 'ProcessDetails',
        hidden: true,
        meta: { title: '查看详情', flag: 'process', type: 2, activeMenu: '/process/management' }
      }
    ]
  },

  // 会议管理
  {
    path: '/meeting',
    component: Layout,
    meta: { flag: 'meeting', type: 3 },
    children: [
      {
        path: '',
        component: () => import('@/views/meeting/index'),
        name: 'Meeting',
        meta: { title: '会议管理', flag: 'meeting', icon: 'meeting', type: 3 }
      },
      {
        path: '/addMeeting/:type',
        component: () => import('@/views/meeting/components/addMeeting'),
        name: 'addMeeting',
        hidden: true,
        meta: { title: '添加会议', flag: 'meeting', type: 3, activeMenu: '/meeting' }
      },
      {
        path: '/meetingDetails/:meetingId',
        component: () => import('@/views/meeting/components/meetingDetails'),
        name: 'meetingDetails',
        hidden: true,
        meta: { title: '会议详情', flag: 'meeting', type: 3, activeMenu: '/meeting' }
      }
    ]
  },
  // 项目管理
  {
    path: '/project',
    component: Layout,
    meta: { flag: 'project', type: 4 },
    children: [
      {
        path: '',
        component: () => import('@/views/project/index'),
        name: 'Project',
        meta: { title: '项目管理', icon: 'project', flag: 'project', type: 4 }
      },

      {
        path: '/project/details/:id/:type',
        component: () => import('@/views/project/details'),
        name: 'ProjectDetails',
        hidden: true,
        meta: { title: '项目详情', flag: 'project', type: 4, activeMenu: '/project' }
      }
    ]
  },
  // 售后服务
  {
    path: '/aftermarket',
    component: Layout,
    meta: { flag: 'Aftermarket', type: 4 },
    children: [
      {
        path: '',
        component: () => import('@/views/project/aftermarket'),
        name: 'Aftermarket',
        meta: { title: '售后服务', flag: 'Aftermarket', icon: 'Aftermarket', type: 4 }
      },
      {
        path: '/project/aftermarket_details/:id/:type',
        component: () => import('@/views/project/aftermarket_details'),
        name: 'ProjectDetails',
        hidden: true,
        meta: { title: '项目详情', flag: 'Aftermarket', type: 4, activeMenu: '/aftermarket' }
      }
    ]
  },
  // 培训管理
  {
    path: '/training',
    component: Layout,
    meta: { flag: 'training', type: 5 },
    children: [
      {
        path: '',
        component: () => import('@/views/training/index'),
        name: 'Training',
        meta: { title: '培训管理', icon: 'training', flag: 'training', type: 5 }
      },
      {
        path: '/addTraining/:type',
        component: () => import('@/views/training/components/addTraining'),
        name: 'addMeeting',
        hidden: true,
        meta: { title: '添加培训', flag: 'training', type: 5, activeMenu: '/training' }
      },
      {
        path: '/trainingDetails/:trainId',
        component: () => import('@/views/training/components/trainingDetails'),
        name: 'trainingDetails',
        hidden: true,
        meta: { title: '培训详情', flag: 'training', type: 5, activeMenu: '/training' }
      }
    ]
  },
  // 知识库管理
  {
    path: '/repository',
    component: Layout,
    meta: { flag: 'repository', type: 5 },
    children: [
      {
        path: '',
        component: () => import('@/views/repository/index'),
        name: 'Repository',
        meta: { title: '知识库管理', icon: 'repository', flag: 'repository', type: 5 }
      },
      {
        path: '/repository/add/:type',
        component: () => import('@/views/repository/components/addRepository'),
        hidden: true,
        name: 'RepositoryAdd',
        meta: { title: '添加知识库', flag: 'repository', type: 5, activeMenu: '/repository' }
      },
      {
        path: '/repository/details/:knowledgeBaseId',
        component: () => import('@/views/repository/components/details'),
        hidden: true,
        name: 'RepositoryDetails',
        meta: { title: '知识库详情', flag: 'repository', type: 5, activeMenu: '/repository' }
      }
    ]
  },
  {
    path: '/institution',
    component: Layout,
    meta: { flag: 'institution', type: 6 },
    children: [
      {
        path: '',
        component: () => import('@/views/institution/index'),
        name: 'Institution',
        meta: { title: '制度管理', icon: 'institution', flag: 'institution', type: 6 }
      }
    ]
  },
  // 小微秘
  /** 签到记录*/
  {
    path: '/sign',
    component: Layout,
    meta: { flag: 'sign', type: 7 },
    children: [
      {
        path: '',
        component: () => import('@/views/sign/index'),
        name: 'sign',
        meta: { title: '签到记录', icon: 'sign', flag: 'sign', type: 7 }
      }
    ]
  },
  /** 区域管理 */
  {
    path: '/area',
    component: Layout,
    meta: { flag: 'area', type: 7 },
    children: [
      {
        path: '',
        component: () => import('@/views/area/index'),
        name: 'area',
        meta: { title: '区域管理', icon: 'area', flag: 'area', type: 7 }
      }
    ]
  },
  /** 客户管理 */

  {
    path: '/clientele',
    component: Layout,
    meta: { flag: 'clientele', type: 7 },
    children: [
      {
        path: '',
        component: () => import('@/views/clientele/index'),
        name: 'clientele',
        meta: { title: '客户管理', icon: 'clientele', flag: 'clientele', type: 7 }
      },
      {
        path: '/clientele/details/:id',
        component: () => import('@/views/clientele/details'),
        name: 'clienteleDetails',
        hidden: true,
        meta: { title: '客户详情', flag: 'clientele', type: 7, activeMenu: '/clientele' }
      }
    ]
  },
  /** 商机管理 */
  {
    path: '/chance',
    component: Layout,
    meta: { flag: 'chance', type: 7 },
    children: [
      {
        path: '',
        component: () => import('@/views/chance/index'),
        name: 'chance',
        meta: { title: '商机管理', icon: 'chance', flag: 'chance', type: 7 }
      },
      {
        path: '/chance/details/:id',
        component: () => import('@/views/chance/details'),
        name: 'chanceDetails',
        hidden: true,
        meta: { title: '商机详情', flag: 'chance', type: 7, activeMenu: '/chance' }
      }
    ]
  },
  /** 招投标管理 */
  {
    path: '/bid',
    component: Layout,
    meta: { flag: 'bid', type: 7 },
    children: [
      {
        path: '',
        component: () => import('@/views/bid/index'),
        name: 'bid',
        meta: { title: '招投标管理', icon: 'bid', flag: 'bid', type: 7 }
      },
      {
        path: '/bid/details/:id',
        component: () => import('@/views/bid/details'),
        name: 'bidDetails',
        hidden: true,
        meta: { title: '招投标管理详情', flag: 'bid', type: 7, activeMenu: '/bid' }
      }
    ]
  },
  /** 日志管理 */
  {
    path: '/secretLog',
    component: Layout,
    meta: { flag: 'secretLog', type: 7 },
    children: [
      {
        path: '',
        component: () => import('@/views/secretLog/index'),
        name: 'secretLog',
        meta: { title: '日志管理', icon: 'log', flag: 'secretLog', type: 7 }
      },
      {
        path: '/secretLog/addLog',
        component: () => import('@/views/secretLog/addLog'),
        name: 'secretLog',
        hidden: true,
        meta: { title: '日志写日志', flag: 'secretLog', type: 7, activeMenu: '/secretLog' }
      }
    ]
  },
  /** 销售方案 */
  {
    path: '/tSalesPlan',
    component: Layout,
    meta: { flag: 'tSalesPlan', type: 7 },
    children: [
      {
        path: '',
        component: () => import('@/views/tSalesPlan/index'),
        name: 'tSalesPlan',
        meta: { title: '销售方案', icon: 'tSalesPlan', flag: 'tSalesPlan', type: 7 }
      },
      {
        path: 'add/:id',
        component: () => import('@/views/tSalesPlan/add'),
        name: 'tSalesPlanAdd',
        hidden: true,
        meta: { title: '新增销售方案', flag: 'tSalesPlan', type: 7, activeMenu: '/tSalesPlan' }
      },
      {
        path: 'details/:id',
        component: () => import('@/views/tSalesPlan/details'),
        name: 'tSalesPlanDetails',
        hidden: true,
        meta: { title: '销售方案详情', flag: 'tSalesPlan', type: 7, activeMenu: '/tSalesPlan' }
      }
    ]
  },
  // ----------------------------------------------

  /** 合同管理 */
  {
    path: '/contract',
    component: Layout,
    meta: { flag: 'contract', type: 8 },
    children: [
      {
        path: '',
        component: () => import('@/views/contract/index'),
        name: 'Contract',
        meta: { title: '合同管理', icon: 'contract', flag: 'contract', type: 8, keepAlive: true }
      },
      {
        path: '/contract/details/:id',
        component: () => import('@/views/contract/details'),
        name: 'ContractDetails',
        hidden: true,
        meta: { title: '招投标管理详情', flag: 'contract', type: 8, activeMenu: '/contract' }
      }
    ]
  },

  // bug 管理
  {
    path: '/bug',
    component: Layout,
    meta: { flag: 'bug', type: 9 },
    name: 'Bug',
    children: [
      {
        path: '',
        component: () => import('@/views/bug/index'),
        name: 'Bug',
        meta: { title: 'bug管理', icon: 'bug', flag: 'bug', type: 9, keepAlive: true }
      },
      {
        path: 'add/:id',
        component: () => import('@/views/bug/add.vue'),
        name: 'addBug',
        hidden: true,
        meta: { title: '新增bug', flag: 'bug', type: 9, activeMenu: '/bug' }
      },
      {
        path: 'details/:id',
        component: () => import('@/views/bug/details.vue'),
        name: 'BugDetails',
        hidden: true,
        meta: { title: 'bug详情', flag: 'bug', type: 9, activeMenu: '/bug' }
      }
    ]
  },

  // 资源库管理
  {
    path: '/library',
    component: Layout,
    meta: { flag: 'library', type: 10 },
    children: [
      {
        path: '',
        component: () => import('@/views/Library/index'),
        name: 'library',
        meta: { title: '资源库管理', icon: 'library', flag: 'library', type: 10 }
      },
      {
        path: 'add/:id',
        component: () => import('@/views/Library/add'),
        name: 'library_add',
        hidden: true,
        meta: { title: '新增资源', icon: 'library', flag: 'library', type: 10, activeMenu: '/library' }
      },
      {
        path: 'details/:id',
        component: () => import('@/views/Library/details'),
        name: 'library_details',
        hidden: true,
        meta: { title: '资源详情', icon: 'library', flag: 'library', type: 10, activeMenu: '/library' }
      }
    ]
  },
  // 绩效管理
  {
    path: '/performance',
    component: Layout,
    meta: { flag: 'performance', type: 11 },
    children: [
      {
        path: '',
        component: () => import('@/views/tAchievement/index'),
        name: 'performance',
        meta: { title: '绩效管理', icon: 'performance', flag: 'performance', type: 11 }
      },
      {
        path: 'add/:type/:id',
        component: () => import('@/views/tAchievement/add'),
        name: 'performanceAdd',
        hidden: true,
        meta: { title: '新增绩效', flag: 'performance', type: 11, activeMenu: '/performance' }
      }
    ]
  },
  // 产品库管理
  /** 软件产品库*/
  {
    path: '/software',
    component: Layout,
    meta: { flag: 'software', type: 12 },
    children: [
      {
        path: '',
        component: () => import('@/views/software/index'),
        name: 'Software',
        meta: { title: '软件产品库', icon: 'software', flag: 'software', type: 12, keepAlive: true }
      },
      {
        path: 'add/:id',
        component: () => import('@/views/software/add'),
        name: 'addSoftware',
        hidden: true,
        meta: { title: '新增软件产品', flag: 'software', type: 12, activeMenu: '/software' }
      },
      {
        path: 'details/:id',
        component: () => import('@/views/software/details'),
        name: 'SoftwareDetails',
        hidden: true,
        meta: { title: '软件产品详情', flag: 'software', type: 12, activeMenu: '/software' }
      }
    ]
  },
  /** 硬件产品库 */
  {
    path: '/hardware',
    component: Layout,
    meta: { flag: 'hardware', type: 12 },
    children: [
      {
        path: '',
        component: () => import('@/views/hardware/index'),
        name: 'Hardware',
        meta: { title: '硬件产品库', icon: 'hardware', flag: 'hardware', type: 12, keepAlive: true }
      },
      {
        path: 'add/:id',
        component: () => import('@/views/hardware/add'),
        name: 'addHardware',
        hidden: true,
        meta: { title: '新增硬件产品', flag: 'hardware', type: 12, activeMenu: '/hardware' }
      },
      {
        path: 'details/:id',
        component: () => import('@/views/hardware/details'),
        name: 'HardwareDetails',
        hidden: true,
        meta: { title: '硬件产品详情', flag: 'hardware', type: 12, activeMenu: '/hardware' }
      }
    ]
  },
  {
    path: '/emulation',
    component: Layout,
    meta: { flag: 'emulation', type: 13 },
    children: [
      {
        path: '',
        component: () => import('@/views/emulation/index'),
        name: 'emulation',
        meta: { title: '虚拟仿真', icon: 'emulation', flag: 'emulation', type: 13 }
      },
      {
        path: 'examRecord/:id',
        component: () => import('@/views/emulation/examRecord'),
        hidden: true,
        name: 'examRecord',
        meta: { title: '考试记录', flag: 'emulation', type: 13, activeMenu: '/emulation' }
      },
      {
        path: 'model/:id',
        component: () => import('@/views/emulation/model'),
        hidden: true,
        name: 'emulationModel',
        meta: { title: '赋分模型', flag: 'emulation', type: 13, activeMenu: '/emulation' }
      },
      {
        path: ':type/:id',
        component: () => import('@/views/emulation/add.vue'),
        hidden: true,
        name: 'addEmulation',
        meta: { title: '', flag: 'emulation', type: 13, activeMenu: '/emulation' }
      }
    ]
  },
  {
    path: '/previewEmulation/:id',
    component: () => import('@/views/emulation/previewEmulation'),
    name: 'previewEmulation',
    hidden: true,
    meta: { title: '实验预览', flag: 'emulation', type: 13 }
  },
  // 方案共享模块
  {
    path: '/tPlanShare',
    component: Layout,
    meta: { flag: 'tPlanShare', type: 14 },
    children: [
      {
        path: '',
        component: () => import('@/views/tPlanShare/index'),
        name: 'tPlanShare',
        meta: { title: '方案共享', icon: 'tPlanShare', flag: 'tPlanShare', type: 14 }
      }
    ]
  },
  { path: '*', redirect: '/404', hidden: true }
]

const createRouter = () =>
  new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes
    // routes: [...constantRoutes, ...asyncRoutes]
  })

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
