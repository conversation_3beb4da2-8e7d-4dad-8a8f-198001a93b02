<template>
  <div v-loading.fullscreen="fullscreenLoading" element-loading-text="数据保存中" element-loading-background="rgba(0, 0, 0, 0.8)" class="app-container">
    <el-row :gutter="10" class="top">
      <el-col :span="24">
        <i class="el-icon-location"></i>
        <span>当前位置：软件产品库 /</span>
        <span>{{ pageType }}</span>
      </el-col>
    </el-row>
    <div class="box">
      <div class="header">
        {{ pageType }}
      </div>
      <el-form ref="form" :model="formInfo" :rules="rules" label-width="85px">
        <el-form-item label="产品名称:" prop="name" class="name">
          <el-input v-model="formInfo.name" placeholder="请输入产品名称" maxlength="40"></el-input>
        </el-form-item>
        <el-form-item label="所属专业:" prop="majorIds" class="majorId">
          <el-select v-model="majorIds" placeholder="请选择专业" multiple @focus="getMajorList" @change="majorChange">
            <el-option v-for="item in majorList" :key="item.majorId" :label="item.majorName" :value="item.majorId"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="单价:" prop="offer" class="offer">
          <el-input v-model="formInfo.offer" placeholder="请输入单价(万元)" maxlength="40"></el-input>
        </el-form-item>
        <el-form-item label="单位:" prop="unit" class="unit">
          <el-select v-model="formInfo.unit" placeholder="请选择单位">
            <el-option label="套" :value="1"> </el-option>
            <el-option label="台" :value="2"> </el-option>
            <el-option label="个" :value="3"> </el-option>
            <el-option label="端" :value="4"> </el-option>
            <el-option label="把" :value="5"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="产品类别:" prop="type" class="type">
          <el-radio-group v-model="formInfo.type">
            <el-radio :label="1">平台</el-radio>
            <el-radio :label="2">系统</el-radio>
            <el-radio :label="3">虚拟仿真</el-radio>
            <el-radio :label="4">VR/MR</el-radio>
            <el-radio :label="5">虚实结合</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="产品标签:" class="unit">
          <el-select v-model="formInfo.label" placeholder="请选择产品标签" filterable @focus="getDictData">
            <el-option v-for="item in labelList" :key="item.dictDataId" :label="item.dictLabel" :value="item.dictDataId"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="产品描述:" class="description">
          <el-input v-model="formInfo.description" placeholder="请输入产品描述" type="textarea" resize="none" maxlength="500"></el-input>
        </el-form-item>
        <el-form-item label="备注:" class="remark">
          <el-input v-model="formInfo.remark" placeholder="请输入产品备注" type="textarea" resize="none" maxlength="500"></el-input>
        </el-form-item>
      </el-form>
      <div class="footer">
        <span @click="$router.push('/software')">取 消</span>
        <span @click="save">保 存</span>
      </div>
    </div>
  </div>
</template>

<script>
import { allMajor } from '@/api/specialty'
import { tSoftwareProductAdd, tSoftwareProductUpdate, tSoftwareProductDetail } from '@/api/software'
import { dictDataSelectData } from '@/api/dictionaries'
export default {
  name: 'SoftwareAdd',

  data() {
    var checkNum = (rule, value, callback) => {
      if (!isNaN(parseInt(value))) {
        callback()
      } else {
        return callback(new Error('请输入数字'))
      }
    }
    return {
      formInfo: {
        name: null, // 产品名称
        majorIds: null, // 专业id
        type: null, // 类别 1 平台 2 系统 3 虚拟仿真
        description: null, // 描述
        offer: null, // 单价(万元)
        unit: null, // 单位;1 套 2 台 3 个 4 端 5 把
        label: null, // 产品标签
        remark: null //	备注
      },
      rules: {
        name: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
        majorIds: [{ required: true, message: '请选择专业', trigger: 'blur' }],
        offer: [
          { required: true, message: '请输入单价', trigger: 'blur' },
          { validator: checkNum, trigger: 'blur' }
        ],
        unit: [{ required: true, message: '请选择单位', trigger: 'change' }],
        type: [{ required: true, message: '请选择类别', trigger: 'change' }]
      },
      majorList: [],
      majorIds: [], // 被选中的专业
      labelList: [], // 标签列表
      fullscreenLoading: false
    }
  },
  computed: {
    pageType() {
      return this.$route.params.id === '0' ? '添加产品' : '编辑产品'
    }
  },
  created() {
    if (this.$route.params.id !== '0') {
      this.getDetails()
    }
  },
  methods: {
    // 获取专业列表
    async getMajorList() {
      const { data } = await allMajor()
      this.majorList = data
    },
    majorChange(val) {
      this.formInfo.majorIds = val.join(',')
      this.$refs['form'].validateField('majorIds')
    },
    async getDictData() {
      const { data } = await dictDataSelectData({ dictType: 'product_label', dictLabel: null })
      this.labelList = data
    },
    save() {
      this.$refs['form'].validate((val) => {
        if (val) {
          this.fullscreenLoading = true
          if (this.pageType === '添加产品') {
            tSoftwareProductAdd(this.formInfo)
              .then(() => {
                this.fullscreenLoading = false
                this.$message.success('保存成功')
                this.$router.push('/software')
              })
              .catch(() => {
                this.fullscreenLoading = false
              })
          } else {
            tSoftwareProductUpdate(this.formInfo)
              .then(() => {
                this.fullscreenLoading = false
                this.$message.success('修改成功')
                this.$router.push('/software')
              })
              .catch(() => {
                this.fullscreenLoading = false
              })
          }
        }
      })
    },
    async getDetails() {
      const { data } = await tSoftwareProductDetail({ id: this.$route.params.id })
      this.formInfo = { ...data, label: parseFloat(data.label) }
      this.majorIds = data.majorIds.split(',').map(item => parseInt(item))
      this.getMajorList()
      this.getDictData()
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  position: relative;
  padding: 0;
  background-color: #e8eaed;
  min-height: 100%;
  padding-top: 58px;
  padding-bottom: 10px;
  .top {
    position: absolute;
    top: 0px;
    width: 100%;
    background-color: #e8eaed;
    padding: 24px 0 16px 0;
    z-index: 999;
    .el-col {
      i {
        font-size: 14px;
        color: #657081;
      }
      span {
        &:first-of-type {
          margin: 0 5px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #657081;
        }
        &:last-of-type {
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
      }
    }
  }
  .box {
    width: 1754px;
    // height: 946px;
    min-height: 790px;
    padding-bottom: 30px;
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    .header {
      padding: 24px 0 11px 48px;
      border-bottom: 1px solid #eeeeef;
      font-size: 16px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #0b1a44;
    }
    ::v-deep {
      .el-form {
        padding-left: 488px;
        padding-top: 24px;
        border-bottom: 1px solid #eeeeef;
        .el-form-item {
          margin-bottom: 28px;
        }
      }
      .el-form-item__label {
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #0b1a44;
      }
      .el-input__inner {
        height: 40px;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #d8dbe1;
        &::placeholder {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #b1bac7;
        }
      }
      .name {
        .el-input__inner {
          width: 436px;
        }
      }
      .unit {
        .el-input__inner {
          width: 235px;
        }
      }
      .majorId {
        .el-input__inner {
          width: 436px;
        }
      }

      .offer {
        .el-input__inner {
          width: 318px;
        }
      }
      .el-radio-group {
        .el-radio__input.is-checked .el-radio__inner {
          // background: #3464e0;
          background: #fff;
          width: 18px;
          height: 18px;
          border-color: #3464e0;
          // border: none;
          &::after {
            background-color: #3464e0;
            width: 8px;
            height: 8px;
          }
        }
        .el-radio__inner {
          width: 18px;
          height: 18px;
        }
        .el-radio__input.is-checked + .el-radio__label {
          color: #0b1a44;
        }
        .el-radio__label {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
        }
      }
      .description,
      .remark {
        .el-textarea__inner {
          width: 682px;
          height: 138px;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #d8dbe1;
        }
      }
    }
    .footer {
      display: flex;
      justify-content: center;
      margin-top: 34px;
      span {
        display: inline-block;
        width: 260px;
        height: 46px;
        line-height: 46px;
        font-size: 14px;
        font-weight: 400;
        border-radius: 4px 4px 4px 4px;
        text-align: center;
        cursor: pointer;
      }
      & > span:first-of-type {
        background: #ffffff;
        border: 1px solid #d8dbe1;
        color: #a3a8bb;
      }
      & > span:last-of-type {
        margin-left: 32px;
        background: #3464e0;
        font-weight: bold;
        color: #ffffff;
      }
    }
  }
}
</style>
