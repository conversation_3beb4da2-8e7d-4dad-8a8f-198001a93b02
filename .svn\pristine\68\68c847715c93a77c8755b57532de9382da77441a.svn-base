<template>
  <div class="app-container">
    <template v-if="info">
      <header>
        <ul class="statusList">
          <li v-for="item in info.statusDetailDto" :key="item.statusId" :class="{ overStatus: item.isOver }">
            <div>
              <span>{{ item.isOver ? '已完成' : '进行中' }}</span>
              <span>{{ item.type | contractNew_status }}</span>
            </div>
            <div class="time">{{ item.time }}</div>
          </li>
        </ul>
      </header>
      <div class="body">
        <div class="body_left">
          <section class="contractInfo">
            <ContractInfo :info="info" :contract-id="contractId" :type="contractType" @refreshData="getDetails" />
          </section>
          <section class="fileInfo">
            <FileInfo ref="FileInfo" :contract-id="contractId" />
          </section>
          <section class="operateInfo">
            <LogList :contract-id="contractId" />
          </section>
        </div>
        <div class="body_right">
          <Comment :contract-id="contractId" :type="contractType" @success="getDetails" />
        </div>
      </div>
    </template>
  </div>
</template>
<script>
import { contractDetail } from '@/api/contractNew.js'

import ContractInfo from '@/views/contractNew/components/ContractInfo'
import FileInfo from '@/views/contractNew/components/FileInfo'
import LogList from '@/views/contractNew/components/LogList'
import Comment from '@/views/contractNew/components/Comment'

export default {
  name: '',
  components: {
    ContractInfo,
    FileInfo,
    LogList,
    Comment
  },
  data() {
    return {
      info: null
    }
  },
  computed: {
    contractId() {
      return this.$route.params.contractId
    },
    contractType() {
      return this.info ? this.info.type : 1
    }
  },
  created() {
    this.getDetails()
  },

  methods: {
    async getDetails() {
      const { data } = await contractDetail({ id: this.contractId })
      this.info = data
      this.$nextTick(() => {
        this.$refs['FileInfo'].getFileList()
      })
      console.log(data)
    }
  }
}
</script>
<style scoped lang="scss">
.app-container {
  width: 100%;
  min-height: 100%;
  padding-left: 0;
  background: #e8eaed;
  header {
    width: 100%;
    background: #fff;
    border-radius: 4px;
    padding: 20px 0;
    .statusList {
      display: flex;
      align-items: center;
      li {
        width: 308px;
        height: 90px;
        margin-left: 40px;
        background: #e5f7ee;
        border-radius: 10px;
        & > div:first-of-type {
          display: flex;
          align-items: center;
          padding-top: 19px;
          padding-left: 23px;
          & > span:first-of-type {
            width: 64px;
            height: 24px;
            background: #12b257;
            border-radius: 3px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            font-size: 14px;
            color: #ffffff;
            line-height: 24px;
            text-align: center;
          }
          & > span:last-of-type {
            margin-left: 10px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            font-size: 20px;
            color: #000000;
          }
        }
        .time {
          margin-left: 23px;
          margin-top: 8px;
          font-family: DIN, DIN;
          font-weight: 400;
          font-size: 18px;
          color: #656565;
          line-height: 18px;
        }
      }
      .overStatus {
        background: #e7efff;
        & > div:first-of-type {
          & > span:first-of-type {
            background: #3a6ff4;
          }
        }
      }
    }
  }
  .body {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    .body_left {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      width: 1390px;
      .contractInfo {
        width: 685px;
        height: 493px;
        padding: 20px;
        background: #ffffff;
        border-radius: 4px;
      }
      .fileInfo {
        width: 685px;
        height: 493px;
        background: #ffffff;
        border-radius: 4px;
      }
      .operateInfo {
        width: 100%;
        margin-top: 20px;
        background: #ffffff;
        border-radius: 4px;
      }
    }
    .body_right {
      flex: 1;
      height: 708px;
      margin-left: 20px;
      background: #ffffff;
      border-radius: 4px;
    }
  }
}
</style>
