<template>
  <div class="productRelease-addDialog">
    <el-dialog :title="dialogTitle" :visible="showDialog" width="845px" top="10vh" @open="open" @close="close">
      <el-form ref="formRef" :model="formInfo" :rules="rules" label-width="140px">
        <el-form-item label="产品名称" prop="name">
          <el-input v-model="formInfo.name" placeholder="请输入产品名称" maxlength="50"></el-input>
        </el-form-item>
        <!-- <el-form-item label="产品所属部门:" prop="type">
          <el-select v-model="formInfo.type" placeholder="请选择产品所属部门">
            <el-option label="虚拟仿真部" :value="2"> </el-option>
            <el-option label="平台部" :value="1"> </el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="实验版本:" prop="version" class="version">
          <el-radio-group v-model="formInfo.version">
            <el-radio label="Unity2018">Unity2018</el-radio>
            <el-radio label="Unity2022">Unity2022</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="专业:" prop="majorId">
          <el-select v-model="formInfo.majorId" placeholder="请选择所属专业" @focus="getMajorList">
            <el-option v-for="item in majorList" :key="item.majorId" :label="item.majorName" :value="item.majorId"> </el-option>
          </el-select>
        </el-form-item>
        <template v-if="formInfo.type == 2">
          <el-form-item label="实验模式:" prop="mode" class="testType">
            <el-radio-group v-model="formInfo.mode">
              <el-radio :label="2">学习模式</el-radio>
              <el-radio :label="1">考核模式</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="实验资源:" prop="testReqs">
            <el-upload
              ref="upload-emulation"
              class="upload-emulation"
              :action="uploadUrl"
              :data="emulationData"
              :headers="header"
              :show-file-list="!isUploadOver"
              :file-list="fileList_emulation"
              :before-upload="emulationUpload"
              :on-success="emulationSuccess"
              multiple
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">点击选择文件夹上传</div>
              <div v-if="isExistResource" class="el-upload__SuccessText">已存在资源</div>
            </el-upload>
          </el-form-item>
        </template>
        <template v-else>
          <el-form-item label="产品地址" prop="addressList">
            <div v-for="(item, index) in formInfo.addressList" :key="index" class="addressBox">
              <el-input v-model="item.url" placeholder="请输入产品地址" maxlength="50"></el-input>
              <i class="el-icon-delete" @click="removeUrl(index)"></i>
            </div>
            <el-button type="primary" size="small" @click="addUrl">添加地址</el-button>
          </el-form-item>
        </template>
        <el-form-item label="版本说明:">
          <el-input v-model="formInfo.versionNote" placeholder="请输入版本说明" maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="备注:">
          <el-input v-model="formInfo.remark" placeholder="请输入备注" type="textarea" maxlength="300" resize="none"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="save">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'

import { allMajor } from '@/api/specialty'
import { debounce2 } from '@/utils'
import { validAddress } from '@/utils/validate'
import { releaseProductAdd, releaseProductUpdate, releaseProductDetail } from '@/api/productRelease'
export default {
  name: '',
  props: {
    showDialog: {
      type: Boolean,
      require: true
    }
  },

  data() {
    return {
      state: null,
      formInfo: {
        productId: null, // 产品id
        emulationId: null,
        name: null, // 产品名称
        type: null, // 产品类型 1 平台部产品 2 仿真部产品
        majorId: null, // 专业id
        version: 'Unity2018', // 版本
        versionNote: null, // 版本说明
        remark: null,

        // 虚拟仿真字段
        mode: 2, // 实验模式 1 考核模式 2 学习模式
        testReqs: [],
        // 产品字段
        address: null,
        addressList: [] // 产品地址ulr
      },
      rules: {
        name: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
        type: [{ required: true, tiggle: 'change', message: '请选择产品所属部门', type: 'number' }],
        majorId: [{ required: true, message: '请选择专业', trigger: 'change', type: 'number' }],
        version: [{ required: true, message: '请选择实验版本', trigger: 'change' }],
        mode: [{ required: true, trigger: 'change', type: 'number' }],
        testReqs: [{ required: true, trigger: 'blur', type: 'array', message: '实验资源不能为空' }],
        addressList: [
          { required: true, trigger: 'change', type: 'array', message: '产品地址至少有一个' },
          {
            required: true,
            trigger: 'change',
            validator: validAddress
          }
        ]
      },
      majorList: [],
      uploadUrl: window.config.VUE_APP_UPLOAD_Library_URL + '/system/upload/file',
      header: {
        Authorization: null
      },
      emulationData: {
        fileName: null
      },
      fileTimestamp: null,
      fileList_emulation: [],
      isUploadOver: false, // 是否上传完成
      isExistResource: false // 是否存在资源
    }
  },
  computed: {
    ...mapGetters(['token', 'organizationId']),
    currentOrganizationType() {
      return this.organizationId === 56612504 ? 1 : 2
    },
    dialogTitle() {
      return this.formInfo.productId ? '修改产品' : '新增产品'
    }
  },
  watch: {
    'formInfo.type': {
      handler(val) {
        if (val === 2 && this.showDialog) {
          this.$nextTick(() => {
            this.$refs['upload-emulation'].$children[0].$refs.input.webkitdirectory = true
          })
        }
      }
    }
  },
  mounted() {
    this.fileTimestamp = `a${new Date().getTime()}e`
  },

  methods: {
    async getMajorList() {
      const { data } = await allMajor()
      this.majorList = data
    },
    /** 虚拟仿真产品字段功能 */
    // 实验资源上传
    emulationUpload(file) {
      this.emulationData.fileName = 'productRelease/' + this.fileTimestamp + '/' + file.webkitRelativePath
      this.isUploadOver = false
      if (this.header.Authorization) return
      this.header.Authorization = `Bearer ${this.token}`
    },
    emulationSuccess(res, file, fileList) {
      this.fileList_emulation = fileList
      this.isOverUpload(fileList)
    },
    isOverUpload: debounce2(function (list) {
      const uploadSuccessNum = list.filter((item) => {
        return item.response && item.response.code === 200
      }).length
      const uploadErrorNum = list.filter((item) => {
        return item.response && item.response.code !== 200
      }).length

      if (uploadErrorNum === list.length) {
        this.fileList_emulation = []
        return this.$message.error('上传失败请重试')
      } else if (uploadErrorNum > 0) {
        this.fileList_emulation = []
        return this.$message.error('有文件上传失败，请重新上传')
      }
      if (list.length === uploadSuccessNum) {
        this.isUploadOver = true
        this.isExistResource = true
      }
      if (list.length) {
        const arr = []

        list.forEach((item) => {
          arr.push({
            fileName: item.name,
            fileSize: parseInt(item.size / 1024),
            fileUrl: item.response.data[0],
            belongType: 22
          })
        })
        console.log(`原上传列表:`, list, `现列表:`, arr)

        this.formInfo.testReqs = arr
        this.$refs['formRef'].validateField('testReqs')
      }
    }, 1000),

    /** 平台产品字段功能 */
    addUrl() {
      if (this.formInfo.addressList.length) {
        const noAdd = this.formInfo.addressList.some((item) => !item.url.trim())
        if (noAdd) return this.$message.warning('请将产品地址填写完成')
      }
      this.formInfo.addressList.push({
        url: ''
      })
    },

    removeUrl(index) {
      this.formInfo.addressList.splice(index, 1)
      this.$refs['formRef'].validateField('addressList')
    },
    /** 通用功能  */
    save() {
      this.$refs['formRef'].validate((val) => {
        if (val) {
          const generalInfo = {
            name: this.formInfo.name,
            productId: this.formInfo.productId,
            type: this.formInfo.type,
            majorId: this.formInfo.majorId,
            version: this.formInfo.version,
            versionNote: this.formInfo.versionNote,
            remark: this.formInfo.remark,
            state: this.state
          }
          let info
          if (this.formInfo.type === 1) {
            info = { ...generalInfo, address: this.formInfo.addressList.map((item) => item.url).join(',') }
          } else {
            info = { ...generalInfo, mode: this.formInfo.mode, testReqs: this.formInfo.testReqs }
          }
          const loading = this.$loading({
            text: '保存中，请稍后...',
            background: 'rgba(0,0,0,0.7)'
          })
          if (this.formInfo.productId) {
            releaseProductUpdate(info)
              .then((res) => {
                loading.close()
                this.$message.success('保存成功！')
                this.close()
                this.$emit('success')
              })
              .catch(() => {
                loading.close()
              })
          } else {
            releaseProductAdd(info)
              .then((res) => {
                loading.close()
                this.$message.success('保存成功！')
                this.close()
                this.$emit('success')
              })
              .catch(() => {
                loading.close()
              })
          }
        }
      })
    },
    async showDetalis(state, item) {
      this.state = state
      const { data } = await releaseProductDetail({ productId: item.productId, state })
      this.formInfo = {
        productId: data.productId,
        name: data.name,
        type: data.type,
        mode: data.mode,
        version: data.version,
        address: data.address,
        majorId: data.majorId,

        versionNote: data.versionNote,
        remark: data.remark,
        testReqs: data.meetingFileReqs,
        addressList: []
      }
      this.getMajorList()
      if (this.formInfo.type === 1) {
        this.formInfo.addressList.push(
          ...data.address.split(',').map((item) => {
            return {
              url: item
            }
          })
        )
      } else {
        this.isUploadOver = true
        this.isExistResource = true
      }
    },
    open() {
      this.formInfo.type = this.currentOrganizationType
      if (this.formInfo.type === 2) {
        this.$nextTick(() => {
          this.$refs['upload-emulation'].$children[0].$refs.input.webkitdirectory = true
        })
      }
    },
    close() {
      this.$refs['formRef'].clearValidate()
      this.formInfo = {
        productId: null,
        emulationId: null,
        name: null,
        type: this.currentOrganizationType,
        majorId: null,
        version: 'Unity2018',
        versionNote: null,
        remark: null,
        mode: 2,
        testReqs: [],
        address: null,
        addressList: []
      }
      this.fileList_emulation = []
      this.isUploadOver = false
      this.isExistResource = false

      this.$emit('update:showDialog', false)
    }
  }
}
</script>
<style scoped lang="scss">
.productRelease-addDialog {
  ::v-deep {
    .el-dialog {
      max-height: 800px;
      background: #ffffff;
      box-shadow: -4px 4px 35px 0px rgba(0, 0, 0, 0.15);
      border-radius: 10px 10px 10px 10px;
      overflow: auto;
      .el-dialog__header {
        padding: 20px 30px;
        border-bottom: 1px solid #d9d9d9;
        .el-dialog__title {
          font-family: PingFang SC, PingFang SC;
          font-size: 20px;
          color: #333333;
        }
        .el-dialog__headerbtn {
          i {
            font-size: 32px;
          }
        }
      }
      .el-dialog__body {
        padding: 30px;
        padding-bottom: 0;
        .el-form {
          .el-form-item {
            .el-form-item__label {
              font-family: PingFang SC, PingFang SC;
              font-weight: 500;
              font-size: 18px;
              color: #333333;
            }
            .el-form-item__content {
              .el-input {
                width: 588px;
                height: 42px;
                .el-input__inner {
                  background: #f2f3f3;
                  border-radius: 4px 4px 4px 4px;
                  font-size: 18px;
                }
              }
              .el-select {
                .el-input {
                  width: 279px;
                  height: 42px;
                  border-radius: 4px 4px 4px 4px;
                  .el-input__inner {
                    background: #f2f3f3;
                    font-family: PingFang SC, PingFang SC;
                    font-size: 20px;
                    color: #333333;
                    &::placeholder {
                      font-size: 18px;
                      color: #b5b7b8;
                    }
                  }
                }
              }
              .upload-emulation {
                position: relative;
                .el-upload {
                  background-color: #fff;
                  border: 1px dashed #d9d9d9;
                  border-radius: 6px;
                  box-sizing: border-box;
                  width: 300px;
                  height: 160px;
                  text-align: center;
                  cursor: pointer;
                }
                .el-upload-list {
                  width: 300px;
                  max-height: 300px;
                  overflow: auto;
                  transition: all 0.3s;
                }

                .el-icon-upload {
                  font-size: 67px;
                  color: #c0c4cc;
                  margin: 32px 0 16px;
                  line-height: 50px;
                }
                .el-upload__text {
                  color: #606266;
                  font-size: 14px;
                  text-align: center;
                }
                .el-upload__SuccessText {
                  margin-top: -15px;
                  color: #00be29;
                }
              }
              .el-textarea {
                .el-textarea__inner {
                  width: 622px;
                  height: 101px;
                  background: #f2f3f3;
                  border-radius: 4px 4px 4px 4px;
                }
              }
              .addressBox {
                display: flex;
                align-items: center;
                margin-bottom: 12px;

                i {
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  width: 36px;
                  height: 36px;
                  margin-left: 16px;
                  border: 1px solid #d9d9d9;
                  border-radius: 5px;
                  font-size: 28px;
                  cursor: pointer;
                  &:hover {
                    background: #cecece;
                  }
                }
              }
            }
          }
          .testType,
          .version {
            .el-form-item__content {
              .el-radio__input.is-checked .el-radio__inner {
                // background: #3464e0;
                background: #fff;
                width: 18px;
                height: 18px;
                border-color: #3464e0;
                // border: none;
                &::after {
                  background-color: #3464e0;
                  width: 8px;
                  height: 8px;
                }
              }
              .el-radio-group {
                display: flex;
                align-items: center;
                height: 100%;
                margin-top: 10px;
                .el-radio {
                  display: flex;
                  align-items: center;
                }
                .el-radio__inner {
                  margin-left: 4px;
                  width: 18px;
                  height: 18px;
                }
                .el-radio__input.is-checked + .el-radio__label {
                  color: #333333;
                }
                .el-radio__label {
                  font-size: 20px;
                  font-family: PingFang SC, PingFang SC;
                  font-weight: 400;
                  color: #333333;
                }
              }
            }
          }
        }
      }
      .el-dialog__footer {
        & > div {
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}
</style>
