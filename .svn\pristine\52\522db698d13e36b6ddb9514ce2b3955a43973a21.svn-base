<template>
  <div v-loading.fullscreen="fullscreenLoading" class="app-container" element-loading-text="数据保存中" element-loading-background="rgba(0, 0, 0, 0.8)">
    <el-row :gutter="10" class="top">
      <el-col :span="24">
        <i class="el-icon-location"></i>
        <span>当前位置：虚拟仿真 /</span>
        <span>{{ pageType }}</span>
      </el-col>
    </el-row>
    <div class="box">
      <div class="header">
        <span>{{ pageType }}</span>
      </div>
      <el-form ref="form" :rules="rules" class="addForm" :model="formInfo" label-width="90px">
        <el-form-item label="实验名称:" prop="name">
          <el-input v-model="formInfo.name" placeholder="请输入实验名称" maxlength="40"></el-input>
        </el-form-item>
        <el-form-item label="专业:" prop="majorId">
          <el-select v-model="formInfo.majorId" placeholder="请选择专业" @focus="getAllMajor">
            <el-option v-for="item in majorList" :key="item.majorId" :label="item.majorName" :value="item.majorId"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="上传封面:" prop="coverUrl" class="coverUrl">
          <el-upload
            ref="uploadCover"
            :action="uploadUrl"
            :disabled="formInfo.coverUrl ? true : false"
            :show-file-list="false"
            :headers="header"
            :before-upload="beforeUpload_cover"
            :on-success="uploadSuccess_cover"
          >
            <div v-if="!formInfo.coverUrl" class="uploadCover"></div>
            <div v-else class="previewImg">
              <img :src="formInfo.coverUrl" alt="" />
              <div class="close" @click.stop="delCover"></div>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="实验模式:" prop="mode">
          <el-radio-group v-model="formInfo.mode">
            <el-radio :label="1">考核模式</el-radio>
            <el-radio :label="2">学习模式</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="版本:">
          <el-input v-model="formInfo.version" placeholder="请输入版本" maxlength="40"></el-input>
        </el-form-item>

        <el-form-item label="试题内容:">
          <el-input v-model="formInfo.topic" placeholder="请输入试题内容" type="textarea" maxlength="500"></el-input>
        </el-form-item>
        <el-form-item label="备注:">
          <el-input v-model="formInfo.remark" placeholder="请输入备注" type="textarea" maxlength="500"></el-input>
        </el-form-item>
        <el-form-item label="实验资源:">
          <el-upload
            ref="upload-emulation"
            class="upload-emulation"
            :action="uploadUrl"
            :data="emulationData"
            :headers="header"
            :show-file-list="!isUploadOver"
            :file-list="fileList_emulation"
            :before-upload="emulationUpload"
            :on-success="emulationSuccess"
            multiple
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">点击选择文件夹上传</div>
            <div v-if="formInfo.testReqs.length" class="el-upload__SuccessText">已存在资源</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="ab包资源:">
          <el-upload class="upload-ab" :action="uploadUrl" :headers="header" multiple :file-list="fileList_ab" :before-upload="beforeUpload_ab" :on-success="ab_Success" :on-remove="ad_Remove">
            <el-button size="small" type="primary">点击上传</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div class="bottom">
        <div class="closeButton" @click="goBack">取 消</div>
        <div class="confirmButton" @click="confirm">确 定</div>
      </div>
    </div>
  </div>
</template>

<script>
import { allMajor } from '@/api/specialty'
import { mapGetters } from 'vuex'
import { debounce1 } from '@/utils'
import { emulationAdd, emulationDetail, emulationUpdate } from '@/api/emulation'
export default {
  name: 'AddEmulation',
  data() {
    return {
      fullscreenLoading: false,
      formInfo: {
        name: null, // 实验名称
        coverUrl: null, // 封面地址
        majorId: null, // 专业id
        version: null, // 版本
        mode: null, // 实验模式 1 考核模式 2 学习模式
        topic: null, // 试题内容
        remark: null, // 备注
        testReqs: [], // 实验资源
        ABReqs: [] // ab包资源
      },
      rules: {
        name: [{ required: true, message: '请输入实验名称', trigger: 'blur' }],
        coverUrl: [{ required: true, message: '请上传封面', trigger: 'change' }],
        majorId: [{ required: true, message: '请选择专业', trigger: 'change', type: 'number' }],
        mode: [{ required: true, message: '请选择实验模式', trigger: 'change' }]
      },
      majorList: [],
      uploadUrl: window.config.VUE_APP_BASE_API + '/system/upload/file',
      header: {
        Authorization: null
      },
      emulationData: {
        fileName: null
      },
      fileList_emulation: [],
      isUploadOver: false, // 是否上传完成
      fileList_ab: []
    }
  },
  computed: {
    pageType() {
      return this.$route.params.type === 'add' ? '新增实验' : '编辑实验'
    },
    ...mapGetters(['token'])
  },
  mounted() {
    this.$refs['upload-emulation'].$children[0].$refs.input.webkitdirectory = true
    if (this.$route.params.type === 'edit') {
      this.getDetails()
    }
  },
  methods: {
    async getDetails() {
      const { data } = await emulationDetail({ id: this.$route.params.id })
      this.formInfo = { ...data, ABReqs: data.abreqs }
      this.formInfo.ABReqs.forEach((item) => {
        this.fileList_ab.push({
          name: item.fileName,
          fileSize: item.fileSize,
          fileUrl: item.fileUrl
        })
      })
      this.getAllMajor()
      console.log(data)
    },
    async getAllMajor() {
      const { data } = await allMajor()
      this.majorList = data
    },
    // 上传封面的事件
    beforeUpload_cover(file) {
      this.header.Authorization = `Bearer ${this.token}`
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      if (!isJPG && !isPNG) {
        this.$message.warning('只能上传.jpg .png 格式的附件')
      }
      return isJPG || isPNG
    },
    uploadSuccess_cover(res, file, fileList) {
      if (res.code !== 200) {
        this.$message.error(res.message)
      } else {
        this.formInfo.coverUrl = res.data[0]
        this.$refs['form'].validateField('coverUrl')
      }
    },
    delCover() {
      this.formInfo.coverUrl = null
    },
    // 实验资源上传
    emulationUpload(file) {
      this.emulationData.fileName = file.webkitRelativePath
      this.isUploadOver = false
      if (this.header.Authorization) return
      this.header.Authorization = `Bearer ${this.token}`
    },
    emulationSuccess: debounce1(function (res, file, fileList) {
      this.fileList_emulation = fileList

      this.isOverUpload(fileList)
    }, 1000),
    isOverUpload(list) {
      const uploadSuccessNum = list.filter((item) => {
        return item.response && item.response.code === 200
      }).length
      const uploadErrorNum = list.filter((item) => {
        return item.response && item.response.code !== 200
      }).length
      if (list.length === uploadSuccessNum) {
        this.isUploadOver = true
      }
      if (uploadErrorNum === list.length) {
        this.fileList_emulation = []
        return this.$message.error('上传失败请重试')
      } else if (uploadErrorNum > 0) {
        this.fileList_emulation = []
        return this.$message.error('有文件上传失败，请重新上传')
      }
    },
    // ab包资源上传
    beforeUpload_ab() {
      if (this.header.Authorization) return
      this.header.Authorization = `Bearer ${this.token}`
    },
    ab_Success(res, file, fileList) {
      // if (res.code !== 200) return this.$message.error('上传失败请重试')
      this.fileList_ab = fileList
    },
    ad_Remove(file, fileList) {
      this.fileList_ab = fileList
    },

    // 保存
    confirm() {
      this.$refs['form'].validate((val) => {
        if (val) {
          if (this.fileList_emulation.length) {
            const arr = []
            this.fileList_emulation.forEach((item) => {
              arr.push({
                fileName: item.name,
                fileSize: parseInt(item.size / 1024),
                fileUrl: item.response.data[0]
              })
            })
            this.formInfo.testReqs = arr
          }
          if (this.fileList_ab.length) {
            this.formInfo.ABReqs = this.fileList_ab.map((item) => {
              return {
                fileName: item.name,
                fileSize: item.fileSize ? item.fileSize : parseInt(item.size / 1024),
                fileUrl: item.fileUrl ? item.fileUrl : item.response.data[0]
              }
            })
          }
          this.fullscreenLoading = true
          if (this.$route.params.type === 'edit') {
            emulationUpdate(this.formInfo)
              .then(() => {
                this.fullscreenLoading = false
                this.goBack()
              })
              .catch(() => {
                this.fullscreenLoading = false
              })
          } else {
            emulationAdd(this.formInfo)
              .then(() => {
                this.fullscreenLoading = false
                this.goBack()
              })
              .catch(() => {
                this.fullscreenLoading = false
              })
          }
        }
      })
    },
    goBack() {
      this.$router.push('/emulation')
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  position: relative;
  padding: 0;
  background-color: #e8eaed;
  min-height: 100%;
  padding-top: 58px;
  padding-bottom: 10px;
  .top {
    position: absolute;
    top: 0px;
    width: 100%;
    background-color: #e8eaed;
    padding: 24px 0 16px 0;
    z-index: 999;

    .el-col {
      i {
        font-size: 14px;
        color: #657081;
      }

      span {
        &:first-of-type {
          margin: 0 5px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #657081;
        }

        &:last-of-type {
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
      }
    }
  }
  .box {
    width: 1754px;
    max-height: 791px;
    background: #ffffff;
    // padding-bottom: 40px;
    border-radius: 8px 8px 8px 8px;
    overflow: auto;
    .header {
      padding: 24px 48px 11px 48px;
      border-bottom: 1px solid #eeeeef;
      font-size: 16px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #0b1a44;
    }
    .addForm {
      margin-top: 20px;
      padding-left: 498px;
      ::v-deep {
        .el-form-item {
          margin-bottom: 28px;
        }
        .el-form-item__label {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
        }
        .el-input__inner {
          height: 40px;
          width: 436px;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #d8dbe1;
          &::placeholder {
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #b1bac7;
          }
        }
        .el-radio__input.is-checked .el-radio__inner {
          background: #fff;
          width: 18px;
          height: 18px;
          border-color: #3464e0;
          &::after {
            background-color: #3464e0;
            width: 8px;
            height: 8px;
          }
        }
        .el-radio__inner {
          width: 18px;
          height: 18px;
        }
        .el-radio__input.is-checked + .el-radio__label {
          color: #0b1a44;
        }
        .el-radio__label {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
        }
        .el-textarea {
          .el-textarea__inner {
            width: 436px;
          }
        }
      }
      .coverUrl {
        .el-form-item__content {
          display: flex;
        }
        .el-upload {
          margin-right: 24px;
          .uploadCover {
            width: 120px;
            height: 88px;
            background: url('~@/assets/library/upload_cover_bg.png') no-repeat;
            background-size: cover;
            cursor: pointer;
          }
          .previewImg {
            position: relative;
            width: 120px;
            height: 88px;
            img {
              width: 100%;
              height: 100%;
              border-radius: 5px;
            }
            .close {
              position: absolute;
              right: -9px;
              top: -9px;
              width: 18px;
              height: 18px;
              background: url('~@/assets/library/close.png') no-repeat;
              background-size: cover;
              cursor: pointer;
              &:hover {
                background: url('~@/assets/library/close_hover.png') no-repeat;
                background-size: cover;
              }
            }
          }
        }
      }
      .upload-emulation {
        position: relative;
        ::v-deep {
          .el-upload {
            background-color: #fff;
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            box-sizing: border-box;
            width: 360px;
            height: 180px;
            text-align: center;
            cursor: pointer;
          }
          .el-upload-list {
            width: 360px;
            max-height: 300px;
            overflow: auto;
            transition: all 0.3s;
          }
        }

        .el-icon-upload {
          font-size: 67px;
          color: #c0c4cc;
          margin: 32px 0 16px;
          line-height: 50px;
        }
        .el-upload__text {
          color: #606266;
          font-size: 14px;
          text-align: center;
        }
        .el-upload__SuccessText {
          color: #00be29;
        }
      }
      .upload-ab {
        ::v-deep {
          .el-upload-list {
            width: 360px;
            max-height: 300px;
            overflow: auto;
            transition: all 0.3s;
          }
        }
      }
    }
    .bottom {
      display: flex;
      justify-content: center;
      padding-top: 20px;
      padding-bottom: 20px;
      border-top: 1px solid #eeeeef;
      .closeButton {
        width: 260px;
        height: 46px;
        line-height: 46px;
        background: #ffffff;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #d8dbe1;
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        color: #a3a8bb;
        text-align: center;
        cursor: pointer;
        &:hover {
          background: #f5f5f5;
        }
      }
      .confirmButton {
        margin-left: 32px;
        width: 260px;
        height: 46px;
        line-height: 46px;
        background: #3464e0;
        border-radius: 4px 4px 4px 4px;
        font-size: 14px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        font-weight: bold;
        color: #ffffff;
        text-align: center;
        cursor: pointer;
        &:hover {
          background: #355fce;
        }
      }
    }
  }
}
</style>
