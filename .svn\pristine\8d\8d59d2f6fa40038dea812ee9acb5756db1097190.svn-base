<template>
  <div id="softwareStatistics"></div>
</template>

<script>
import resize from '@/mixins/resize'
export default {
  name: '',
  mixins: [resize],
  data() {
    return {
      chart: null
    }
  },
  methods: {
    init(data) {
      this.chart = this.$echarts.init(document.getElementById('softwareStatistics'))
      this.setOpiton(data)
    },
    setOpiton(data) {
      const option = {
        title: {
          text: '价格趋势'
        },
        xAxis: {
          type: 'category',
          data: data.time,
          boundaryGap: false
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: data.offer,
            showSymbol: false,
            type: 'line',
            areaStyle: {}
          }
        ]
      }

      this.chart.setOption(option)
    }
  }
}
</script>

<style scoped lang="scss">
#softwareStatistics {
  width: 100%;
  height: 100%;
}
</style>
