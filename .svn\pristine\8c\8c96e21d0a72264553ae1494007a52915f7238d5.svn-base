'use strict'
import autoUpdate from '@/utils/autoUpdate'

// const process = require('process')
const path = require('path')
import { app, protocol, BrowserWindow, globalShortcut, ipcMain, Menu, Tray } from 'electron'
import { createProtocol } from 'vue-cli-plugin-electron-builder/lib'
const fs = require('fs')
const axios = require('axios')
const FormData = require('form-data')
// import installExtension, { VUEJS_DEVTOOLS } from 'electron-devtools-installer'
const isDevelopment = process.env.NODE_ENV !== 'production'
// Scheme must be registered before the app is ready
protocol.registerSchemesAsPrivileged([{ scheme: 'app', privileges: { secure: true, standard: true } }])
let win
async function createWindow() {
  // Create the browser window.
  win = new BrowserWindow({
    width: 1920,
    height: 1080,
    icon: path.join(__dirname, 'favicon.ico'),
    webPreferences: {
      // Use pluginOptions.nodeIntegration, leave this alone
      // See nklayman.github.io/vue-cli-plugin-electron-builder/guide/security.html#node-integration for more info
      preload: path.join(__dirname, 'preload.js'),
      // preload: path.join(app.getAppPath(), '..', '..', 'src', 'preload.js'),

      // nodeIntegration: process.env.ELECTRON_NODE_INTEGRATION,
      contextIsolation: true,
      nodeIntegration: false,
      enableRemoteModule: false,
      // 安全
      webSecurity: true
      // allowRunningInsecureContent: true
    }
  })
  win.show() // 显示并聚焦于窗口
  Menu.setApplicationMenu(null)
  win.setMenu(null) // 去除菜单
  win.maximize() // 最大化窗口
  if (process.env.WEBPACK_DEV_SERVER_URL) {
    // Load the url of the dev server if in development mode
    await win.loadURL(process.env.WEBPACK_DEV_SERVER_URL)
    if (!process.env.IS_TEST) win.webContents.openDevTools()
  } else {
    createProtocol('app')
    // Load the index.html when not in development
    win.loadURL('app://./index.html')
  }
  ipcMain.on('open-window', (event) => {
    win.show() // 显示并聚焦于窗口
    win.maximize() // 最大化窗口
  })

  app.setAppUserModelId('公司内部管理平台')
}

// Quit when all windows are closed.
app.on('window-all-closed', () => {
  // On macOS it is common for applications and their menu bar
  // to stay active until the user quits explicitly with Cmd + Q
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  // On macOS it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) createWindow()
})

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.on('ready', async () => {
  // // Install Vue Devtools
  //   try {
  //     await installExtension(VUEJS_DEVTOOLS)
  //   } catch (e) {
  //     console.error('Vue Devtools failed to install:', e.toString())
  //   }
  // }
  globalShortcut.register('Alt+CommandOrControl+I', () => {
    BrowserWindow.getFocusedWindow().webContents.openDevTools()
  })
  createWindow()
  autoUpdate.handleUpdate(win)
})

ipcMain.on('asynchronous-message', () => {
  // eslint-disable-next-line eqeqeq
  if (win.isVisible() && !timer) {
    shinkTray()
  }
  // eslint-disable-next-line eqeqeq
  // if (timer && arg == '0') {
  //   tray.setImage(trayIcon)
  //   clearInterval(timer)
  // }
})

ipcMain.on('download-file', (event, fileUrl) => {
  // 使用当前窗口的 webContents 触发下载
  const win = BrowserWindow.getFocusedWindow()
  if (win) {
    win.webContents.downloadURL(fileUrl)
  }
})

// 处理文件下载请求-根据层级自动划分
ipcMain.handle('download-folder', async (event, { files, folderName }) => {
  const downloadFolderPath = path.join('D:', folderName)

  // 创建文件夹
  if (!fs.existsSync(downloadFolderPath)) {
    fs.mkdirSync(downloadFolderPath) // 创建新的文件夹
  }

  const downloadPaths = []
  const date = `\\${new Date().getTime()}`

  // console.log(`文件存放地址:${downloadFolderPath}`)

  for (const file of files) {
    const fileUrl = new URL(file.fileUrl)
    // 使用decodeURIComponent对中文进行编码
    const relativePath = decodeURIComponent(fileUrl.pathname.split('emulation')[1]) // 获取路径
    // 去掉以a开头的时间戳
    const newPath = relativePath.replace(/^\/a[^\/]*/, '')

    // 拼接路径
    const filePath = path.join(downloadFolderPath, date, newPath)
    // 获取文件的目录部分
    const directoryPath = path.dirname(filePath)
    // console.log(`文件路径：${filePath}`)

    // 创建目录
    if (!fs.existsSync(directoryPath)) {
      fs.mkdirSync(directoryPath, { recursive: true }) // 创建多层级目录
    }
    const writer = fs.createWriteStream(filePath)

    const fileResponse = await axios({
      url: fileUrl.href,
      method: 'GET',
      responseType: 'stream'
    })

    fileResponse.data.pipe(writer)

    await new Promise((resolve, reject) => {
      writer.on('finish', () => {
        downloadPaths.push(filePath) // 记录下载路径
        resolve()
      })
      writer.on('error', reject)
    })
  }

  return downloadPaths // 返回所有下载文件的路径
})

// 处理文件上传请求
ipcMain.handle('upload-files', async (event, { filePaths, uploadUrl, token }) => {
  const uploadResults = []

  for (const filePath of filePaths) {
    try {
      if (!fs.existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`)
      }
      // const stats = fs.statSync(filePath)
      const fileName = filePath.split('emulationFiles\\')[1].replace(/\\/g, '/')
      // console.log(`文件路径:${filePath}文件大小:${stats.size}文件名称:${fileName}`)

      const form = new FormData()
      form.append('file', fs.createReadStream(filePath)) // 使用流上传
      form.append('fileName', 'productRelease/' + fileName)

      const response = await axios.post(uploadUrl, form, {
        headers: {
          ...form.getHeaders(),
          Authorization: `Bearer ${token}`
        },
        maxBodyLength: Infinity,
        maxContentLength: Infinity
      })
      uploadResults.push({ filePath, response: response.data })
    } catch (error) {
      console.log(error)

      // console.log(error.message)

      uploadResults.push({ filePath, error: error.message })
    }
  }
  const folderName = filePaths[0].split('\\').slice(0, 3).join('\\')

  deleteFolder(folderName)
  return uploadResults
})

// 删除文件夹的函数
function deleteFolder(folderPath) {
  fs.rm(folderPath, { recursive: true, force: true }, (err) => {
    if (err) {
      console.error(`删除文件夹时出错: ${err.message}`)
      return
    }
    console.log('文件夹已成功删除')
  })
}

// 设置托盘
let tray = null
var timer = null
var trayIcon = path.join(__dirname, 'favicon.ico')
var trayAIcon = path.join(__dirname, 'faviconT.ico')
var trayAIcon_lucency = path.join(__dirname, 'lucency.ico')
app.whenReady().then(() => {
  tray = new Tray(path.join(trayIcon))
  const contextMenu = Menu.buildFromTemplate([
    {
      label: '打开',
      click: function () {
        win.show()
        win.maximize() // 最大化窗口
      } // 打开相应页面
    },
    {
      label: '退出',
      click: function () {
        app.quit()
      }
    }
  ])
  // 设置此托盘图标的悬停提示内容
  tray.setToolTip('公司内部管理平台')
  // 设置此图标的上下文菜单
  tray.setContextMenu(contextMenu)
  // 单点击 1.主窗口显示隐藏切换 2.清除闪烁
  tray.on('click', function () {
    if (timer) {
      win.show()
      tray.setImage(trayIcon)
      clearInterval(timer)
      timer = null
    } else {
      // 主窗口显示隐藏切换
      win.isVisible() ? win.hide() : win.show()
    }
  })
})

function shinkTray() {
  // 系统托盘图标闪烁
  var count = 0
  timer = setInterval(function () {
    count++
    // eslint-disable-next-line eqeqeq
    if (count == 0) {
      tray.setImage(trayIcon)
      // eslint-disable-next-line eqeqeq
    } else if (count % 2 == 0) {
      tray.setImage(trayAIcon_lucency)
    } else {
      tray.setImage(trayAIcon)
    }
  }, 500)
}

// if (process.platform === 'win32') {
//   app.setAppUserModelId(process.execPath)
// }
// Exit cleanly on request from parent process in development mode.
if (isDevelopment) {
  if (process.platform === 'win32') {
    process.on('message', (data) => {
      if (data === 'graceful-exit') {
        app.quit()
      }
    })
  } else {
    process.on('SIGTERM', () => {
      app.quit()
    })
  }
}

app.on('will-quit', () => {
  globalShortcut.unregisterAll()
})
