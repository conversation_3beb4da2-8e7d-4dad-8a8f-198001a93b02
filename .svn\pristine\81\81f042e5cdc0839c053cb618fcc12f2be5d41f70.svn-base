<template>
  <div class="app-container">
    <el-row :gutter="10" class="top">
      <el-col :span="24">
        <i class="el-icon-location"></i>
        <span>当前位置：合同管理 /</span>
        <span>合同详情/售后维护信息</span>
      </el-col>
    </el-row>
    <div class="box">
      <el-row style="margin-bottom: 20px">
        <el-button type="primary" icon="el-icon-back" size="small" @click="$router.push(`/newContract/details/${$route.params.contractId}`)">返回合同详情</el-button>
        <el-button type="primary" icon="el-icon-plus" size="small" @click="addDialog = true">新增</el-button>
      </el-row>
      <el-table :data="list" style="width: 100%" border>
        <el-table-column align="center" prop="realName" label="维护人" width="width"> </el-table-column>
        <el-table-column align="center" prop="contactsName" label="客户联系人" width="width"> </el-table-column>
        <el-table-column align="center" prop="content" label="维护内容" width="width"> </el-table-column>
        <el-table-column align="center" prop="saleWay" label="维护方式" width="width">
          <template v-slot="{ row }">
            <span>{{ row.saleWay === 1 ? '线上' : '线下' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="prop" label="维护时间" width="width">
          <template v-slot="{ row }">
            <span>{{ row.startTime }} - {{ row.endTime }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="remark" label="备注" width="width"> </el-table-column>
        <el-table-column align="center" label="操作" width="width">
          <template v-slot="{ row }">
            <el-button type="warning" size="small" @click="edit(row)">编辑</el-button>
            <el-button type="primary" size="small" @click="details(row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-if="list.length > 0"
        background
        style="text-align: center; margin-top: 15px"
        layout="total, prev, pager, next"
        :page-sizes="[5, 10, 15, 30]"
        :total="total"
        :page-size.sync="queryInfo.pageSize"
        :current-page.sync="queryInfo.pageNum"
        @size-change="getList"
        @current-change="getList"
      />
    </div>
    <!-- 新增修改 -->
    <el-dialog :title="dialogTitle" :visible.sync="addDialog" width="1000px" :close-on-click-modal="false" @open="dialogOpen" @close="dialogClose">
      <el-form ref="form" class="addForm" :model="formInfo" :rules="formRules" inline label-width="120px">
        <el-form-item label="维护人:" prop="userId">
          <el-select v-model="formInfo.userId" placeholder="请选择维护人" clearable>
            <el-option v-for="item in userList" :key="item.userId" :label="item.realName" :value="item.userId"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="客户联系人:">
          <el-select v-model="formInfo.contactsId" placeholder="请选择客户联系人" clearable>
            <el-option v-for="item in contactsList" :key="item.contactsId" :label="item.contactsName" :value="item.contactsId"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="维护内容:">
          <el-input v-model="formInfo.content" type="textarea" placeholder="请输入维护内容" clearable></el-input>
        </el-form-item>
        <el-form-item label="维护方式:">
          <el-radio-group v-model="formInfo.saleWay">
            <el-radio :label="1">线上</el-radio>
            <el-radio :label="2">线下</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="维护时间:">
          <el-date-picker v-model="date" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd" @change="dateChange"> </el-date-picker>
        </el-form-item>
        <el-form-item label="备注:">
          <el-input v-model="formInfo.remark" type="textarea" placeholder="请输入备注" clearable></el-input>
        </el-form-item>
        <el-form-item label="维护单文件">
          <el-upload drag multiple :action="action" :headers="header" :data="dataObj" :file-list="fileList" :before-upload="beforeUpload" :on-success="uploadSuccess" :on-remove="uploadRemove">
            <div slot="tip" class="el-upload__tip" style="color: red; line-height: 15px">上传多个 上传类型: .doc .docx .xls .xlsx .pdf .jpg .png .zip格式的附件，且大小不超过50MB</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="addDialog = false">取 消</el-button>
        <el-button type="primary" @click="save">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 详情 -->
    <el-dialog title="售后维护详情" :visible.sync="detailsDialog" width="1200px">
      <el-descriptions v-if="checkedInfo" :column="2" border>
        <el-descriptions-item label="维护人">{{ checkedInfo.realName }}</el-descriptions-item>
        <el-descriptions-item label="客户联系人">{{ checkedInfo.contactsName }}</el-descriptions-item>
        <el-descriptions-item label="维护内容">{{ checkedInfo.content }}</el-descriptions-item>
        <el-descriptions-item label="维护方式">
          <el-tag>{{ checkedInfo.saleWay === 1 ? '线上' : '线下' }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="维护时间">{{ checkedInfo.startTime }} - {{ checkedInfo.endTime }}</el-descriptions-item>
        <el-descriptions-item label="备注">{{ checkedInfo.remark }}</el-descriptions-item>
        <el-descriptions-item label="维护单文件">
          <template v-if="checkedInfo.files.length">
            <a v-for="item in checkedInfo.files" :key="item.fileId" :href="item.fileUrl" target="_blank" class="lookFile">{{ item.fileName }}</a>
          </template>
        </el-descriptions-item>
      </el-descriptions>
      <div slot="footer">
        <el-button @click="detailsDialog = false">取 消</el-button>
        <el-button type="primary" @click="detailsDialog = false">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { contractAfterList, contractAfterUpdate, contractAfterAdd, contractDetail } from '@/api/contractNew'
import { secretCustomeAllContacts } from '@/api/clientele'
import { getList } from '@/api/systemUser'
import { mapGetters } from 'vuex'
import _ from 'lodash'
export default {
  name: 'ContractNewaFtersalesInfo',
  data() {
    return {
      queryInfo: {
        pageNum: 1,
        pageSize: 10,
        contractId: this.$route.params.contractId
      },
      list: [],
      total: 0,
      addDialog: false,
      formInfo: {
        contractId: this.$route.params.contractId,
        userId: null, // 维护人
        contactsId: null, // 客户联系人id
        content: null, // 维护内容
        saleWay: null, // 维护方式 1 线上 2 线下
        startTime: null,
        endTime: null,
        remark: null, // 备注
        reqs: []
      },
      contactsList: [], // 客户联系人列表
      userList: [],
      date: null,
      formRules: {
        userId: [{ required: true, message: '请选择维护人', trigger: 'blur' }]
      },
      action: window.config.VUE_APP_BASE_API + '/system/upload/file',
      header: {
        Authorization: null
      },
      fileList: [],
      dataObj: {
        fileName: ''
      },
      // 详情
      detailsDialog: false,
      checkedInfo: null
    }
  },
  computed: {
    ...mapGetters(['token']),
    dialogTitle() {
      return this.formInfo.saleId ? '编辑售后维护信息' : '新增售后维护信息'
    }
  },
  created() {
    this.getList()
  },

  methods: {
    async getList() {
      const { data } = await contractAfterList(this.queryInfo)
      this.list = data.list
      this.total = data.total
    },
    // #region 售后维护-新增修改
    async dialogOpen() {
      const { data: info } = await contractDetail({ id: this.$route.params.contractId })
      const { data } = await secretCustomeAllContacts({ customerId: info.customerId })
      this.contactsList = data
      const { data: user } = await getList({ organizationId: 553086145, pageNum: 1, pageSize: 200 })
      this.userList = user.list
    },
    dialogClose() {
      this.formInfo = {
        contractId: this.$route.params.contractId,
        userId: null, // 维护人
        contactsId: null, // 客户联系人id
        content: null, // 维护内容
        saleWay: null, // 维护方式 1 线上 2 线下
        startTime: null,
        endTime: null,
        remark: null, // 备注
        reqs: []
      }
      this.date = null
      this.fileList = []
    },
    dateChange(val) {
      if (val) {
        this.formInfo.startTime = val[0]
        this.formInfo.endTime = val[1]
      } else {
        this.formInfo.startTime = null
        this.formInfo.endTime = null
      }
    },
    // 添加合同- 上传前触发事件
    beforeUpload(file) {
      this.dataObj.fileName = 'contract/' + file.name
      this.header.Authorization = `Bearer ${this.token}`
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isDOC = file.type === 'application/msword'
      const isDOCX = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      const isXLS = file.type === 'application/vnd.ms-excel'
      const isXLSX = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      const isPDF = file.type === 'application/pdf'
      const isZIP = file.type === 'application/x-zip-compressed'
      const isLt2M = file.size / 1024 / 1024 < 50
      if (!isLt2M) {
        this.$message.error('上传附件大小不能超过 50MB!')
      }
      if (!isJPG && !isPNG && !isDOC && !isDOCX && !isXLS && !isXLSX && !isPDF && !isZIP) {
        this.$message.warning('只能上传.doc .docx .xls .xlsx .pdf .jpg .png .zip 格式的附件')
      }
      return (isJPG || isPNG || isDOC || isDOCX || isXLS || isXLSX || isPDF || isZIP) && isLt2M
    },
    // 添加合同- 上传成功事件
    uploadSuccess(response, file, fileList) {
      this.fileList = fileList
    },
    // 添加合同- 上传删除事件
    uploadRemove(file, fileList) {
      this.fileList = fileList
    },
    edit(row) {
      this.formInfo = _.cloneDeep(row)
      if (row.startTime && row.endTime) {
        this.date = [row.startTime, row.endTime]
      }
      this.formInfo.reqs = row.files.length ? row.files : []
      this.fileList = this.formInfo.reqs.map((item) => {
        return {
          name: item.fileName,
          size: item.fileSize * 1024,
          url: item.fileUrl
        }
      })
      this.addDialog = true
    },
    save() {
      this.$refs['form'].validate((val) => {
        if (val) {
          const list = []
          if (this.fileList.length) {
            this.fileList.forEach((item) => {
              list.push({
                fileName: item.name,
                fileSize: Math.floor(item.size / 1024),
                fileUrl: item.response ? item.response.data[0] : item.url,
                belongType: 26,
                meetingId: this.formInfo.saleId ? this.formInfo.saleId : null
              })
            })
          }
          this.formInfo.reqs = list.length ? list : []
          const loading = this.$loading({
            text: '数据保存中，请稍后...',
            background: 'rgba(0,0,0,0.7)'
          })
          if (this.formInfo.saleId) {
            contractAfterUpdate(this.formInfo)
              .then((res) => {
                this.$message.success('修改信息成功！')
                this.addDialog = false
                this.getList()
              })
              .finally(() => {
                loading.close()
              })
          } else {
            contractAfterAdd(this.formInfo)
              .then((res) => {
                this.$message.success('添加信息成功！')
                this.addDialog = false
                this.getList()
              })
              .finally(() => {
                loading.close()
              })
          }
        }
      })
    },
    // #endregion
    // 详情
    details(row) {
      this.checkedInfo = _.cloneDeep(row)
      this.detailsDialog = true
    }
  }
}
</script>
<style scoped lang="scss">
.app-container {
  position: relative;
  padding: 0;
  background-color: #e8eaed;
  min-height: 100%;
  height: 100%;
  padding-top: 58px;
  padding-right: 40px;
  padding-bottom: 10px;
  .top {
    position: absolute;
    top: 0px;
    width: 100%;
    background-color: #e8eaed;
    padding: 24px 0 16px 0;
    z-index: 999;
    .el-col {
      i {
        font-size: 14px;
        color: #657081;
      }
      span {
        &:first-of-type {
          margin: 0 5px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #657081;
        }
        &:last-of-type {
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
      }
    }
  }
  .box {
    width: 100%;
    height: 100%;
    padding: 16px;
    background: #fff;
    border-radius: 16px;
  }
  .addForm {
    ::v-deep {
      .el-input {
        width: 350px;
      }
      .el-textarea {
        width: 350px;
      }
      .el-upload-dragger {
        width: 88px;
        height: 88px;
        background: url('~@/assets/contractNew/uploadFile.png') no-repeat;
        background-size: cover;
        border: none;
      }
    }
  }
  .lookFile {
    font-size: 16px;
    margin-right: 30px;
    color: #1862ec;
  }
  ::v-deep {
    .el-descriptions-item__label {
      width: 150px;
      text-align: center;
    }
    .el-descriptions-item__content {
      width: 500px;
    }
  }
}
</style>
