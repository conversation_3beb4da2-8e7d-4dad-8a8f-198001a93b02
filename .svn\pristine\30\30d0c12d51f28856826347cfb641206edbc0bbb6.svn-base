// 数据迁移
import request from '@/utils/request'

/** 迁移数据列表
 *  @param {number} pageNum 当前页码
 *  @param {number} pageSize 每页条数
 *  @param {string} username 用户名
 *  @param {string} realName 姓名
 *  @param {string} status 状态 1 在职 2 离职
 *  @param {string} sex 性别 F-女;M-男
 */
export function transfer_UserList(params) {
  return request({
    url: '/transfer/userList',
    method: 'GET',
    params
  })
}

/** 迁移数据 - 迁出人员及钱迁入人员查询
 *  @param {string} name 用户名或姓名
 *  @param {string} status 状态 1 在职 2 离职
 *  @param {string} sex 性别 F-女;M-男
 */
export function transfer_transferUserList(params) {
  return request({
    url: '/transfer/transferUserList',
    method: 'GET',
    params
  })
}

/** 迁移数据 - 查询用户下所有区域
 *  @param {string | number} userId 用户id
 */
export function transfer_regionByUserId(params) {
  return request({
    url: '/transfer/regionByUserId',
    method: 'GET',
    params
  })
}

/** 迁移数据 - 迁移数据变更
 *  @param {string} outUserId 迁出人员id
 *  @param {string} enterUserId 迁入人员id
 *  @param {number[]} regions 迁移区域
 */
export function transfer_dataChange(params) {
  return request({
    url: '/transfer/dataChange',
    method: 'POST',
    data: params
  })
}

/** 迁移数据 - 迁移数据介绍
 *  @param {string} userId 用户id
 */
export function transfer_userDetail(params) {
  return request({
    url: '/transfer/userDetail',
    method: 'GET',
    params
  })
}
