'use strict'
// const process = require('process')
const path = require('path')
import { app, protocol, BrowserWindow, ipcMain, Menu, Tray } from 'electron'
import { createProtocol } from 'vue-cli-plugin-electron-builder/lib'
// import installExtension, { VUEJS_DEVTOOLS } from 'electron-devtools-installer'
const isDevelopment = process.env.NODE_ENV !== 'production'

// Scheme must be registered before the app is ready
protocol.registerSchemesAsPrivileged([{ scheme: 'app', privileges: { secure: true, standard: true }}])
let win

async function createWindow() {
  // Create the browser window.
  win = new BrowserWindow({
    width: 1920,
    height: 1080,
    icon: path.join(__dirname, 'favicon.ico'),
    webPreferences: {
      // Use pluginOptions.nodeIntegration, leave this alone
      // See nklayman.github.io/vue-cli-plugin-electron-builder/guide/security.html#node-integration for more info
      preload: path.join(__dirname, 'preload.js'),
      // preload: path.join(app.getAppPath(), '..', '..', 'src', 'preload.js'),

      nodeIntegration: process.env.ELECTRON_NODE_INTEGRATION,
      contextIsolation: true
    }
  })
  win.show() // 显示并聚焦于窗口
  win.setMenu(null) // 去除菜单
  win.maximize() // 最大化窗口

  if (process.env.WEBPACK_DEV_SERVER_URL) {
    // Load the url of the dev server if in development mode
    await win.loadURL(process.env.WEBPACK_DEV_SERVER_URL)
    if (!process.env.IS_TEST) win.webContents.openDevTools()
  } else {
    createProtocol('app')
    // Load the index.html when not in development
    win.loadURL('app://./index.html')
  }
  ipcMain.on('open-window', (event) => {
    win.show() // 显示并聚焦于窗口
    win.maximize() // 最大化窗口
  })

  app.setAppUserModelId('公司内部管理平台')
}

// Quit when all windows are closed.
app.on('window-all-closed', () => {
  // On macOS it is common for applications and their menu bar
  // to stay active until the user quits explicitly with Cmd + Q
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  // On macOS it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) createWindow()
})

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.on('ready', async () => {
  // if (isDevelopment && !process.env.IS_TEST) {
  // // Install Vue Devtools
  //   try {
  //     await installExtension(VUEJS_DEVTOOLS)
  //   } catch (e) {
  //     console.error('Vue Devtools failed to install:', e.toString())
  //   }
  // }
  createWindow()
})

ipcMain.on('asynchronous-message', () => {
  // eslint-disable-next-line eqeqeq
  if (win.isVisible() && !timer) {
    shinkTray()
  }
  // eslint-disable-next-line eqeqeq
  // if (timer && arg == '0') {
  //   tray.setImage(trayIcon)
  //   clearInterval(timer)
  // }
})
// 设置托盘
let tray = null
var timer = null
var trayIcon = path.join(__dirname, 'favicon.ico')
var trayAIcon = path.join(__dirname, 'faviconT.ico')
var trayAIcon_lucency = path.join(__dirname, 'lucency.ico')
app.whenReady().then(() => {
  tray = new Tray(path.join(trayIcon))
  const contextMenu = Menu.buildFromTemplate([
    {
      label: '打开',
      click: function () {
        win.show()
        win.maximize() // 最大化窗口
      } // 打开相应页面
    },
    {
      label: '退出',
      click: function () {
        app.quit()
      }
    }
  ])
  // 设置此托盘图标的悬停提示内容
  tray.setToolTip('公司内部管理平台')
  // 设置此图标的上下文菜单
  tray.setContextMenu(contextMenu)
  // 单点击 1.主窗口显示隐藏切换 2.清除闪烁
  tray.on('click', function () {
    if (timer) {
      win.show()
      tray.setImage(trayIcon)
      clearInterval(timer)
      timer = null
    } else {
      // 主窗口显示隐藏切换
      win.isVisible() ? win.hide() : win.show()
    }
  })
})

function shinkTray() {
  // 系统托盘图标闪烁
  var count = 0
  timer = setInterval(function () {
    count++
    // eslint-disable-next-line eqeqeq
    if (count == 0) {
      tray.setImage(trayIcon)
    // eslint-disable-next-line eqeqeq
    } else if (count % 2 == 0) {
      tray.setImage(trayAIcon_lucency)
    } else {
      tray.setImage(trayAIcon)
    }
  }, 500)
}

// if (process.platform === 'win32') {
//   app.setAppUserModelId(process.execPath)
// }
// Exit cleanly on request from parent process in development mode.
if (isDevelopment) {
  if (process.platform === 'win32') {
    process.on('message', (data) => {
      if (data === 'graceful-exit') {
        app.quit()
      }
    })
  } else {
    process.on('SIGTERM', () => {
      app.quit()
    })
  }
}
