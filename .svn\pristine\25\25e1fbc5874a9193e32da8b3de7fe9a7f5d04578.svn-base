<template>
  <div class="app-container">
    <el-row type="flex" justify="space-between">
      <el-button type="primary" icon="el-icon-back" size="small" @click="$router.push('/contract')">返回</el-button>
      <!-- <el-button type="primary" size="small">编辑</el-button> -->
    </el-row>
    <el-card style="margin-top: 15px">
      <div slot="header">合同信息</div>
      <el-descriptions :title="`合同名称：${detailsInfo.contractName}(${detailsInfo.contractCode})`" :column="2">
        <el-descriptions-item label="客户名称">{{ detailsInfo.customerName }}</el-descriptions-item>
        <el-descriptions-item label="项目名称">{{ detailsInfo.projectName }}</el-descriptions-item>
        <el-descriptions-item label="创建人">{{ detailsInfo.realName }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ detailsInfo.createTime }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ detailsInfo.updateTime }}</el-descriptions-item>
      </el-descriptions>
    </el-card>
    <el-card style="margin-top: 15px">
      <div slot="header">合同</div>
      <el-row type="flex" class="imgFile">
        <el-image v-for="item in imgList" :key="item.fileId" style="width: 310px; height: 200px; margin-right: 29px" :src="item.fileUrl" fit="scale-down" :preview-src-list="previewList"></el-image>
      </el-row>
      <el-row>
        <div v-for="item in fileList" :key="item.fileId" class="files" @click="lookDetails(item)">
          {{ item.fileName }}
        </div>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { contract_contractDetails } from '@/api/contract'
export default {
  name: '',
  data() {
    return {
      detailsInfo: {},
      imgList: [],
      fileList: [],
      previewList: []
    }
  },
  created() {
    this.getContract_contractDetails()
  },
  methods: {
    async getContract_contractDetails() {
      const { data } = await contract_contractDetails({ contractId: this.$route.params.id })
      this.detailsInfo = data
      if (this.detailsInfo.fileDtos.length > 0) {
        this.detailsInfo.fileDtos.forEach((item) => {
          const fileType = item.fileName.substr(item.fileName.lastIndexOf('.'))
          if (fileType === '.png' || fileType === '.jpg' || fileType === '.jpeg') {
            this.imgList.push(item)
          } else {
            this.fileList.push(item)
          }
        })
      }
      this.previewList = this.imgList.map((item) => item.fileUrl)
    },
    lookDetails(item) {
      window.open(item.fileUrl, '_blank')
      console.log(item)
    }
  }
}
</script>

<style scoped lang="scss">
.imgFile {
  flex-wrap: wrap !important;
}
.el-image {
  margin-bottom: 29px;
  box-shadow: 0 0 5px #409eff;
  border-radius: 12px;
}
.files{
    color: #409eff;
    cursor: pointer;
    &:hover{
        text-decoration: underline;
    }
}
</style>
