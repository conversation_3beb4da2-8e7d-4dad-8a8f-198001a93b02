// vuex的权限模块
import { asyncRoutes, constantRoutes } from '@/router'
// vuex中的permission模块用来存放当前的 静态路由 + 当前用户的 权限路由

function hasPermission(flags, route) {
  if (route.meta && route.meta.flag) {
    return flags.includes(route.meta.flag)
  } else {
    return true
  }
}

/**
 * Filter asynchronous routing tables by recursion
 * @param routes asyncRoutes
 * @param roles
 */
export function filterAsyncRoutes(routes, flags) {
  const res = []
  routes.forEach(route => {
    const tmp = {
      ...route
    }
    if (hasPermission(flags, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, flags)
      }
      res.push(tmp)
    }
  })
  return res
}

const state = {
  routes: constantRoutes // 所有人默认拥有静态路由
}
const mutations = {
  setRoutes(state, newRoutes) {
    state.routes = [...constantRoutes, ...newRoutes]
  }
}
const actions = {

  filterRoutes(context, menus) {
    // 获取到当前用户的权限
    const routes = filterAsyncRoutes(asyncRoutes, menus)
    context.commit('setRoutes', routes) // 将动态路由提交给mutations
    return routes // 这里为什么还要return  state数据 是用来 显示左侧菜单用的  return  是给路由addRoutes用的
  }
}
export default {
  namespaced: true,
  state,
  mutations,
  actions
}
