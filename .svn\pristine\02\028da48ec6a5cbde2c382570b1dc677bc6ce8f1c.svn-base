// 专业
import request from '@/utils/request'
/** 专业列表 */
export function majorList(params) {
  return request({
    url: '/major/majorList',
    method: 'GET',
    params
  })
}
/** 添加专业 */
export function save(data) {
  return request({
    url: '/major/save',
    method: 'POST',
    data
  })
}
/** 修改专业 */
export function majorUpdate(data) {
  return request({
    url: '/major/majorUpdate',
    method: 'POST',
    data
  })
}
/** 专业树 */
export function majorTree (params) {
  return request({
    url: '/major/majorTree',
    method: 'GET',
    params
  })
}

/** 删除专业 */
export function majorRemove (params) {
  return request({
    url: '/major/majorRemove',
    method: 'DELETE',
    params
  })
}
/** 查询所有专业 */
export function allMajor(params) {
  return request({
    url: '/major/allMajor',
    method: 'GET',
    params
  })
}
