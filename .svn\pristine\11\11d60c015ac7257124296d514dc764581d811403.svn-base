<template>
  <div class="navbar">
    <el-row :gutter="50" type="flex" justify="space-between" align="middle">
      <el-col :span="5">
        <div class="left_title">
          <img src="@/assets/Home/logo.png" alt="" class="logo" />
          <img src="@/assets/Home/title.png" alt="" class="title" />
        </div>
      </el-col>
      <el-col :span="18">
        <div class="right-menu">
          <div class="fastTab" style="margin-right: 30px">
            <el-dropdown trigger="click">
              <div class="cut">
                <img src="@/assets/Home/fastTab.png" alt="" />
                <span v-if="type == 1" style="margin: 0 5px">基础数据管理</span>
                <span v-if="type == 2" style="margin: 0 5px">流程管理</span>
                <span v-if="type == 3" style="margin: 0 5px">会议管理</span>
                <span v-if="type == 4" style="margin: 0 5px">项目管理</span>
                <i class="el-icon-caret-bottom" />
              </div>
              <el-dropdown-menu slot="dropdown" size="small">
                <el-dropdown-item v-for="item in mode" :key="item.path" @click.native="checkTab(item)">
                  <span style="display: block">{{ item.name }}</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <el-dropdown class="avatar-container" trigger="click">
            <div class="cut">
              <img :src="avatar + '?imageView2/1/w/80/h/80'" class="user-avatar" />
              <i class="el-icon-caret-bottom" />
            </div>
            <el-dropdown-menu slot="dropdown" class="user-dropdown">
              <router-link to="/">
                <el-dropdown-item> Home </el-dropdown-item>
              </router-link>
              <a target="_blank" href="https://github.com/PanJiaChen/vue-admin-template/">
                <el-dropdown-item>Github</el-dropdown-item>
              </a>
              <a target="_blank" href="https://panjiachen.github.io/vue-element-admin-site/#/">
                <el-dropdown-item>Docs</el-dropdown-item>
              </a>
              <el-dropdown-item divided @click.native="logout">
                <span style="display: block">Log Out</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </el-col>
    </el-row>
    <!-- <hamburger :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" /> -->

    <!-- <breadcrumb class="breadcrumb-container" /> -->
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
// import Breadcrumb from '@/components/Breadcrumb'
// import Hamburger from '@/components/Hamburger'
export default {
  name: 'NavBar',
  components: {
    // Breadcrumb
    // Hamburger
  },
  data() {
    return {
      mode: [
        {
          name: '基础信息管理',
          path: '/user',
          type: 1
        },
        {
          name: '流程管理',
          path: '/process/management',
          type: 2
        },
        {
          name: '会议管理',
          path: '/meeting',
          type: 3
        },
        {
          name: '项目管理',
          path: '/project',
          type: 4
        }
      ],
      type: 1
    }
  },
  computed: {
    ...mapGetters(['sidebar', 'avatar'])
  },
  created() {
    this.type = window.localStorage.getItem('zf_oa_type')
  },
  methods: {
    toggleSideBar() {
      // this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
      await this.$store.dispatch('user/logout')
      this.$router.push(`/login?redirect=${this.$route.fullPath}`)
    },
    checkTab(item) {
      this.type = item.type
      this.$store.commit('checkedData/set_data_type', item.type)
      this.$nextTick(() => {
        this.$router.push(item.path)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 68px;
  width: 100%;
  overflow: hidden;
  position: relative;
  // background: #fff;
  background: url('../../assets/Home/nav_bg.png');
  background-size: cover;
  // box-shadow: 0 1px 4px rgba(0,21,41,.08);

  // .hamburger-container {
  //   line-height: 46px;
  //   height: 100%;
  //   float: left;
  //   cursor: pointer;
  //   transition: background 0.3s;
  //   -webkit-tap-highlight-color: transparent;

  //   &:hover {
  //     background: rgba(0, 0, 0, 0.025);
  //   }
  // }

  // .breadcrumb-container {
  //   float: left;
  // }

  .left_title {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    .logo {
      width: 48px;
      height: 38px;
    }
    .title {
      width: 240px;
      height: 25px;
      padding-left: 8px;
    }
  }
  .right-menu {
    display: flex;
    justify-content: flex-end;
    height: 100%;
    line-height: 50px;
    &:focus {
      outline: none;
    }
    .cut {
      display: flex;
      align-items: center;
      height: 100%;
      font-size: 16px;
      img {
        width: 16px;
        height: 16px;
      }
    }
    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .cut {
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
.el-row {
  height: 100%;
  .el-col {
    height: 100%;
  }
}
.el-dropdown {
  height: 100%;
}
</style>
