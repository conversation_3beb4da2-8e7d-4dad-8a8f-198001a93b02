<template>
  <div class="all_new_cont">
    <div class="resource_filecont_left">
      <div class="resource_filecont_lefitem" :class="activeTab == 0 ? 'active' : ''" @click="activeTab = 0">我的素材</div>
      <div class="resource_filecont_lefitem" :class="activeTab == 1 ? 'active' : ''" @click="activeTab = 1">我的收藏</div>
      <div class="resource_filecont_lefitem" :class="activeTab == 2 ? 'active' : ''" @click="activeTab = 2">共享素材</div>
    </div>
    <myfiles v-if="activeTab == 0" ref="myfiles" @openfiles="openfiles"></myfiles>
    <mycollect v-if="activeTab == 1" @openfiles="openfiles"></mycollect>
    <sharefiles v-if="activeTab == 2" @openfiles="openfiles"></sharefiles>
  </div>
</template>

<script>
import myfiles from '@/views/resources/myfiles.vue'
import sharefiles from '@/views/resources/sharefiles.vue'
import mycollect from '@/views/resources/mycollect.vue'
export default {
  components: {
    myfiles,
    sharefiles,
    mycollect
  },
  data() {
    return {
      activeTab: this.$route.query.activeTab || '0'
    }
  },
  created() {},
  mounted() {},
  methods: {
    openfiles(activeTab) {
      this.activeTab = activeTab
    },
    uploadMy() {
      this.activeTab = '1'
      this.$nextTick(() => {
        this.$refs.myfiles.openbeforeUpload()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep {
  .bigScreen {
    width: 100%;
    height: 100%;
    .el-dialog__header {
      padding: 0;
    }
    .el-dialog__body {
      width: 100%;
      height: 100%;
      padding-top: 0;
      padding-bottom: 0;
    }
    .el-dialog__headerbtn {
      position: fixed;
      top: 20px;
      right: 30px;
      z-index: 9999;
      .el-dialog__close {
        font-size: 50px;
        color: red;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
::v-deep {
  .el-image {
    width: 100%;
    height: 100%;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}
</style>

<style lang="scss">
.el-image-viewer__mask {
  opacity: 1;
}
</style>
