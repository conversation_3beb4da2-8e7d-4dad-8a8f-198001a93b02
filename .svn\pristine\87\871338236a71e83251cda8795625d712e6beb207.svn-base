import request from '@/utils/request'

/** 查询资源详情 */
export function selectResourceDetail(data) {
  return request({
    url: '/teacher/resource/selectResourceDetail',
    method: 'get',
    params: data
  })
}
/** 新建文件夹 */
export function saveResourceFile(data) {
  return request({
    url: '/teacher/resource/saveResourceFile',
    method: 'post',
    data
  })
}
/** 新建课件资源 */
export function saveResource(data) {
  return request({
    url: '/teacher/resource/saveResource',
    method: 'post',
    data
  })
}
/** 新建外链资源 */
export function saveLinkResource(data) {
  return request({
    url: '/teacher/resource/saveLinkResource',
    method: 'post',
    data
  })
}
/** 更新资源分享状态 */
export function updateState(data) {
  return request({
    url: '/teacher/resource/updateState',
    method: 'put',
    params: data
  })
}
/** 删除文件夹/课件资源 */
export function removeResource(data) {
  return request({
    url: '/teacher/resource/removeResource',
    method: 'delete',
    params: data
  })
}
/** 更新文件夹/资源位置 */
export function updateResourceSite(data) {
  return request({
    url: '/teacher/resource/updateResourceSite',
    method: 'put',
    params: data
  })
}
/** 更新文件夹/资源名称 */
export function updateResourceName(data) {
  return request({
    url: '/teacher/resource/updateResourceName',
    method: 'put',
    params: data
  })
}
/** 文件夹树 */
export function resourceTree(data) {
  return request({
    url: '/teacher/resource/resourceTree',
    method: 'get',
    params: data
  })
}
/** 资源分页列表/文件夹查看下级列表*/
export function resourceList(data) {
  return request({
    url: '/teacher/resource/resourceList',
    method: 'get',
    params: data
  })
}
/** 共享资源分页列表/文件夹查看下级列表*/
export function resourcePublicList(data) {
  return request({
    url: '/teacher/resource/resourcePublicList',
    method: 'get',
    params: data
  })
}

/** 批量新增实验资源*/
export function saveVrResources(data) {
  return request({
    url: '/teacher/resource/saveVrResources',
    method: 'post',
    data
  })
}
/** 最近-资源列表*/
export function resourceRecentList(data) {
  return request({
    url: '/teacher/resource/resourceRecentList',
    method: 'get',
    params: data
  })
}
/** 首页-共享资源列表*/
export function resourcePublicIndexList(data) {
  return request({
    url: '/teacher/resource/resourcePublicIndexList',
    method: 'get',
    params: data
  })
}
/** 我的收藏资源列表*/
export function resourceCollectRecentList(data) {
  return request({
    url: '/teacher/resource/resourceCollectRecentList',
    method: 'get',
    params: data
  })
}
/** 我的收藏资源分页列表/文件夹查看下级列表 */
export function resourceCollectList(data) {
  return request({
    url: '/teacher/resource/resourceCollectList',
    method: 'get',
    params: data
  })
}

/** 更新资源收藏状态 */
export function updateCollectState(data) {
  return request({
    url: '/teacher/resource/updateCollectState',
    method: 'put',
    params: data
  })
}

/** 批量删除文件夹/课件资源*/
export function resourceBatchRemove(data) {
  return request({
    url: '/teacher/resource/resourceBatchRemove',
    method: 'delete',
    data
  })
}

/** 批量共享资源*/
export function updateBatchPublicState(data) {
  return request({
    url: '/teacher/resource/updateBatchPublicState',
    method: 'put',
    data
  })
}
