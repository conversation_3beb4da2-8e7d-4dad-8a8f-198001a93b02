<template>
  <div class="app-container">
    <el-row class="top" type="flex" justify="space-between">
      <el-col :span="22">
        <i class="el-icon-location"></i>
        <span>当前位置：<router-link to="/product">产品管理</router-link> /</span>
        <span>{{ showTitle }}</span>
      </el-col>
      <el-col :span="1">
        <el-button type="primary" size="small" icon="el-icon-back" @click="$router.push('/product')">返回</el-button>
      </el-col>
    </el-row>
    <el-card class="content">
      <div slot="header">
        <span>{{ showTitle }}</span>
      </div>
      <div class="form">
        <el-form ref="form" class="addForm" :model="formInfo" label-width="100px" :rules="rules" inline-message>
          <el-form-item label="产品名称:" prop="productName">
            <el-input v-model="formInfo.productName" size="small" placeholder="请输入产品名称" maxlength="50" show-word-limit clearable></el-input>
          </el-form-item>
          <!-- <el-form-item label="版本号:" prop="version">
            <el-input v-model="formInfo.version" size="small" placeholder="请输入版本号" maxlength="50" oninput="value = value.replace(/[\u4E00-\u9FA5]/g,'')" clearable></el-input>
          </el-form-item> -->
          <el-form-item label="产品类型:" prop="productType" class="productType">
            <el-radio-group v-model="formInfo.productType" @change="selectChange">
              <el-radio :label="1">软件</el-radio>
              <el-radio :label="2">硬件</el-radio>
              <el-radio :label="3">虚拟仿真</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="专业:" prop="majorId">
            <el-select ref="selecteltree" v-model="formInfo.majorName" class="majorId" size="small" clearable placeholder="请选择专业" @focus="getSpecialty">
              <el-option v-for="item in menu" :key="item.id" :label="item.majorName" :value="item.id" style="display: none" />
              <el-tree style="padding-left: 15px" :data="menu" node-key="id" empty-text="暂无菜单" highlight-current :expand-on-click-node="false" :props="defaultProps" current-node-key="id" default-expand-all @node-click="handleNodeClick" />
            </el-select>
          </el-form-item>
          <el-form-item label="演示地址:">
            <el-input v-model="formInfo.perform" size="small" placeholder="请输入演示地址" maxlength="50" oninput="value = value.replace(/[\u4E00-\u9FA5]/g,'')" clearable></el-input>
          </el-form-item>
          <el-form-item label="产品描述:">
            <el-input v-model="formInfo.describe" size="small" placeholder="请输入描述,200字以内" maxlength="200" type="textarea" clearable show-password></el-input>
          </el-form-item>
          <el-form-item label="人员列表:" class="attendUsers">
            <div class="checkPerson">
              <div v-for="item in confirmData" :key="item.userId">
                <div>
                  <span>{{ item.realName }}</span>
                  <img v-if="item.sex === 'F'" src="@/assets/meeting/wuman.png" alt="" />
                  <img v-else src="@/assets/meeting/man.png" alt="" />
                </div>
                <span>{{ item.organizationName }}-{{ item.jobName }}</span>
                <img class="close" src="@/assets/meeting/close.png" alt="" @click="removerItem(item)" />
              </div>
            </div>
            <el-button round icon="el-icon-plus" class="addButton" @click="addPerson">添加</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div v-if="$route.params.type == 1" class="record">
        <div class="record_header">
          <span>迭代记录</span>
          <el-button type="primary" size="mini" icon="el-icon-plus" @click="iterationDialog = true">添加迭代</el-button>
        </div>
        <div>
          <light-timeline v-if="detailsInfo.iterations && detailsInfo.iterations.length >= 1" :items="detailsInfo.iterations" class="lightTimeline">
            <template v-slot:tag="{ item }">
              <div>
                <img src="@/assets/product/version_icon.png" alt="" /> <span class="version">版本号:{{ item.version }}</span>
              </div>
              <div class="createTime">{{ item.time | formatDate('yyyy年MM月dd日 hh:mm') }}</div>
            </template>
            <template v-slot:symbol>
              <img src="@/assets/product/record_icon.png" alt="" />
            </template>
            <template v-slot:content="{ item }">
              <div :class="[{ newItem: item.flag }, 'record_item']">
                <span>{{ item.description }}</span>
                <div class="operateButton">
                  <el-tooltip class="item" effect="light" content="修改" placement="top">
                    <img v-if="item.flag" src="@/assets/product/edit_blue.png" alt="" @click.stop="editIteration(item)" />
                    <img v-else src="@/assets/product/edit.png" alt="" @click.stop="editIteration(item)" />
                  </el-tooltip>
                  <el-tooltip class="item" effect="light" content="删除" placement="top">
                    <img v-if="item.flag" src="@/assets/product/del_blue.png" alt="" @click.stop="delIteration(item)" />
                    <img v-else src="@/assets/product/del.png" alt="" @click.stop="delIteration(item)" />
                  </el-tooltip>
                </div>
              </div>
            </template>
          </light-timeline>
        </div>
      </div>
    </el-card>
    <el-row type="flex" justify="center" style="margin-top: 15px">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="addProduct">提 交</el-button>
    </el-row>

    <!-- <el-card>
      <el-row :gutter="10" type="flex" justify="space-between" align="middle">
        <el-col :span="6">
          <span style="font-size: 16px; font-weight: 600"><span style="color: #f67979">*</span> 人员列表</span>
        </el-col>
        <el-col :span="4">
          <el-button type="primary" size="small" @click="addPerson">添加人员</el-button>
        </el-col>
      </el-row>

      <el-table :data="confirmData ? confirmData.slice((pagination.page - 1) * pagination.size, (pagination.page - 1) * pagination.size + pagination.size) : confirmData" style="width: 100%; margin-top: 15px" border>
        <el-table-column label="姓名" align="center" prop="realName" />
        <el-table-column label="部门" align="center" prop="organizationName" />
        <el-table-column label="岗位" align="center" prop="jobName" />
        <el-table-column align="center" label="操作">
          <template v-slot="{ row }">
            <el-button type="danger" size="small" @click="removerItem(row)">移除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination :page-size.sync="pagination.size" :current-page.sync="pagination.page" :total="confirmData ? confirmData.length : pagination.total" :page-sizes="[5, 10, 15, 20]" layout=" prev,pager,next,sizes,jumper" style="text-align: center; margin-top: 15px" @size-change="handleSizeChange" @current-change="handleCurrentChange"> </el-pagination>
    </el-card> -->

    <!-- 添加人员 -->
    <el-dialog title="添加人员" :visible.sync="dialogVisible" width="620px">
      <el-transfer v-model="checkedData" style="width: 582px" filterable filter-placeholder="请输入姓名" :data="gridData" :titles="['所有人员', '选中人员']" :props="{ key: 'userId', label: 'realName' }"></el-transfer>
      <!-- <el-row :gutter="10">
        <el-col :span="24">
          <el-form label-width="60px" inline>
            <el-form-item label="姓名:">
              <el-input v-model="queryInfo.realName" size="small" clearable placeholder="请输入姓名"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="success" size="small" @click="addPerson">查询</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row> -->
      <!-- <el-table :data="gridData" style="width: 100%" border :row-key="getRowKeys" @selection-change="selectionChange">
        <el-table-column align="center" type="selection" :reserve-selection="true" />
        <el-table-column label="姓名" align="center" prop="realName" />
        <el-table-column label="部门" align="center" prop="organizationName" />
        <el-table-column label="岗位" align="center" prop="jobName" />
      </el-table>
      <el-pagination style="text-align: center; margin-top: 15px" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[5, 10, 15, 20]" :total="total" :page-size.sync="queryInfo.pageSize" :current-page.sync="queryInfo.pageNum" @size-change="addPerson" @current-change="addPerson" /> -->

      <div slot="footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmSelect">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 迭代记录 -->
    <el-dialog :title="showRecordTitle" :visible.sync="iterationDialog" width="686px" :close-on-click-modal="false" class="iterationDialog" @close="RecordClose">
      <el-form ref="iterationForm" :model="iterationForm" label-width="90px" :rules="recordRules">
        <el-form-item label="版本号:" prop="version" class="version">
          <el-input v-model="iterationForm.version" size="small" placeholder="请输入版本号" maxlength="20" :disabled="iterationForm.iterationId" oninput="value = value.replace(/[\u4E00-\u9FA5]/g,'')"></el-input>
        </el-form-item>
        <el-form-item label="迭代时间:" prop="time" class="time">
          <el-date-picker v-model="iterationForm.time" size="small" type="datetime" placeholder="选择迭代时间"> </el-date-picker>
        </el-form-item>
        <el-form-item label="迭代描述:" class="description">
          <el-input v-model="iterationForm.description" size="small" placeholder="请输入描述,200字以内" maxlength="200" type="textarea" clearable></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-row type="flex" justify="center">
          <el-button @click="iterationDialog = false">取消</el-button>
          <el-button type="primary" @click="addIteration">提交 </el-button>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { majorList } from '@/api/specialty.js'
import { getList } from '@/api/systemUser'
import { saveProduct, productUpdate, saveProductIteration, updateProductIteration, productDetails, iterationRemove } from '@/api/product.js'
import { formatDate } from '@/filters'
export default {
  name: 'ProductImprovement',
  // props: {
  //   showDialog: {
  //     required: true,
  //     type: Boolean
  //   }
  // },
  data() {
    return {
      formInfo: {
        productName: null, // 产品名称
        version: null, // 版本号
        productType: null, // 产品类型 1 软件 2 硬件 3 虚拟仿真
        productTypeName: null, // 产品名称
        majorId: null, // 专业id
        majorName: null,
        userIds: [], // 人员ids
        svnUrl: null, // svn地址
        perform: null, // 演示地址及演示用户名密码
        describe: null // 产品描述
      },
      rules: {
        productName: [
          {
            required: true,
            tigger: 'blur',
            message: '产品名称不能为空'
          }
        ],
        version: [
          {
            required: true,
            tigger: 'blur',
            message: '版本号不能为空'
          }
        ],
        productType: [
          {
            required: true,
            tigger: 'change',
            message: '产品类型不能为空',
            type: 'number'
          }
        ],
        majorId: [
          {
            required: true,
            tigger: 'change',
            message: '专业不能为空',
            type: 'number'
          }
        ]
      },
      menu: [],
      defaultProps: {
        label: 'majorName',
        children: 'children'
      },
      gridData: [],
      checkedData: [], // 添加人员多选框选中的数据
      queryInfo: {
        realName: null,
        pageNum: 1,
        pageSize: 200
      },
      total: 0,
      dialogVisible: false,
      confirmData: [], // 添加人员确认选择的数据
      // 添加人员的假数据分页
      pagination: {
        size: 5,
        page: 1,
        total: 0
      },
      iterationDialog: false,
      iterationForm: {
        productId: null,
        version: null,
        time: null,
        description: null
      },
      detailsInfo: {},
      recordRules: {
        version: [
          {
            required: true,
            tigger: 'blur',
            message: '版本号不能为空'
          }
        ],
        time: [
          {
            required: true,
            tigger: 'blur',
            message: '迭代时间不能为空',
            type: 'date'
          }
        ]
      }
    }
  },
  computed: {
    showTitle() {
      return this.formInfo.productId ? '修改产品' : '添加产品'
    },
    showRecordTitle() {
      return this.iterationForm.iterationId ? '修改迭代' : '添加迭代'
    }
  },
  created() {
    if (parseInt(this.$route.params.type) === 1) {
      this.edit(this.$route.params.productId)
    }
    console.log(this.$route.params)
  },
  methods: {
    // 产品类型 选中值发生变化时触发	目前的选中值
    selectChange(val) {
      if (val === 1) {
        this.formInfo.productTypeName = '软件'
      } else if (val === 2) {
        this.formInfo.productTypeName = '硬件'
      } else {
        this.formInfo.productTypeName = '虚拟仿真'
      }
      console.log(val)
    },
    // 选择专业
    async getSpecialty() {
      const { data } = await majorList()
      this.menu = data
      console.log(data)
    },
    // 节点被点击时的回调
    handleNodeClick(node) {
      console.log(node)
      this.formInfo.majorId = node.id
      this.formInfo.majorName = node.majorName
      this.$refs['selecteltree'].blur()
      this.$forceUpdate()
    },
    // 添加人员
    addPerson() {
      this.dialogVisible = true
      getList(this.queryInfo).then((response) => {
        console.log(response)
        this.gridData = response.data.list
        this.total = response.data.total
      })
    },
    // 当选择项发生变化时会触发该事件
    selectionChange(val) {
      this.checkedData = val
      console.log(val)
    },
    // 添加人员确定触发
    confirmSelect() {
      const data = []
      this.checkedData.forEach((item) => {
        data.push(this.gridData.find((list) => list.userId === item))
      })
      this.confirmData = data
      this.totalCount = this.confirmData.length
      this.dialogVisible = false
    },
    // 添加产品人员列表移除按钮触发
    removerItem(row) {
      console.log(row)
      this.confirmData = this.confirmData.filter((item) => item.userId !== row.userId)
      this.totalCount = this.confirmData.length
    },
    async edit(productId) {
      const { data } = await productDetails({ productId })
      this.formInfo = { ...data, describe: null }
      this.formInfo.describe = data.description
      this.formInfo.productType = parseInt(data.productType)
      this.formInfo.majorId = parseInt(data.majorId)
      this.getSpecialty()
      this.confirmData = data.userDtos
      // 获取迭代记录
      this.iterationRecord(productId)
    },
    // 添加/修改产品
    addProduct() {
      this.$refs['form'].validate(async (val) => {
        if (val) {
          this.formInfo.userIds = this.confirmData.map((item) => item.userId)
          if (this.formInfo.userIds.length < 1) {
            this.$message.warning('请选择人员')
          } else {
            if (this.formInfo.productId) {
              await productUpdate(this.formInfo)
              this.$message.success('修改成功')
              this.$emit('success')
              this.$router.push('/product')
            } else {
              await saveProduct(this.formInfo)
              this.$message.success('添加成功')
              this.$emit('success')
              this.$router.push('/product')
            }
          }
        }
      })
    },
    getRowKeys(row) {
      return row.userId
    },
    // dialog关闭事件
    close() {
      this.formInfo = {
        productName: null, // 产品名称
        version: null, // 版本号
        productType: null, // 产品类型 1 软件 2 硬件 3 虚拟仿真
        productTypeName: null, // 产品名称
        majorId: null, // 专业id
        majorName: null,
        userIds: [], // 人员ids
        svnUrl: null, // svn地址
        perform: null, // 演示地址及演示用户名密码
        describe: null // 产品描述
      }
      this.checkedData = []
      this.confirmData = []
      this.$refs['form'].resetFields()
      this.$router.push('/product')
    },
    // 关闭添加迭代记录dialog触发的事件
    RecordClose() {
      this.iterationForm = {
        productId: null,
        version: null,
        time: null,
        description: null
      }
      this.$refs['iterationForm'].resetFields()
    },
    // 添加迭代记录
    addIteration() {
      this.$refs['iterationForm'].validate(async (val) => {
        if (val) {
          console.log(this.iterationForm)
          this.iterationForm.productId = this.$route.params.productId
          if (this.iterationForm.iterationId) {
            await updateProductIteration(this.iterationForm)
            this.$message.success('修改成功')
            this.iterationDialog = false
            this.iterationRecord(this.$route.params.productId)
          } else {
            await saveProductIteration(this.iterationForm)
            this.$message.success('添加成功')
            this.iterationDialog = false
            this.iterationRecord(this.$route.params.productId)
          }
        }
      })
    },
    // 迭代记录
    async iterationRecord(productId) {
      const { data } = await productDetails({ productId })
      console.log(data)
      this.detailsInfo = data
      if (this.detailsInfo.iterations.length > 0) {
        this.detailsInfo.iterations[0].flag = true
      }
    },

    // 修改迭代记录
    editIteration(item) {
      console.log(item)
      this.iterationDialog = true
      this.iterationForm = { ...item, productId: null }
      this.iterationForm.time = formatDate(this.iterationForm.time)
      this.iterationForm.time = new Date(this.iterationForm.time)
      console.log(this.iterationForm.time)
    },
    // 删除迭代记录
    delIteration(row) {
      console.log(row)
      this.$confirm('确定要删除吗, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await iterationRemove({ iterationId: row.iterationId, productId: this.detailsInfo.productId })
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.iterationRecord(this.detailsInfo.productId)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  padding: 0;
  background-color: #e8eaed;
  min-height: 100%;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  .top {
    top: 0px;
    width: 100%;
    background-color: #e8eaed;
    padding: 24px 39px 16px 0;
    z-index: 999;
    .el-col {
      i {
        font-size: 14px;
        color: #657081;
      }
      span {
        &:first-of-type {
          margin: 0 5px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #657081;
        }
        &:last-of-type {
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
      }
    }
  }
}
.content {
  width: 1754px;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  box-shadow: none;
  ::v-deep {
    .el-card__header {
      padding-left: 56px;
      padding-top: 24px;
      font-size: 16px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #0b1a44;
    }
    .el-card__body {
      display: flex;
      justify-content: space-between;
      padding-right: 79px;
    }
    .addForm {
      width: 1000px;
      .el-form-item__label {
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #0b1a44;
      }
      .el-input {
        width: 436px;
        height: 40px;
        .el-input__inner {
          width: 436px;
          height: 40px;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #d8dbe1;
        }
      }
      .el-textarea {
        width: 857px;
        height: 104px;
        .el-textarea__inner {
          width: 100%;
          height: 100%;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #d8dbe1;
        }
      }
      .productType {
        .el-radio__input.is-checked .el-radio__inner {
          // background: #3464e0;
          background: #fff;
          width: 18px;
          height: 18px;
          border-color: #3464e0;
          // border: none;
          &::after {
            background-color: #3464e0;
            width: 8px;
            height: 8px;
          }
        }
        .el-radio__inner {
          width: 18px;
          height: 18px;
        }
        .el-radio__input.is-checked + .el-radio__label {
          color: #0b1a44;
        }
        .el-radio__label {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #657081;
        }
      }
      .majorId {
        .el-input {
          width: 235px;
          height: 38px;
          .el-input__inner {
            width: 100%;
            height: 100%;
            border-radius: 4px 4px 4px 4px;
            border: 1px solid #d8dbe1;
          }
        }
      }
      .attendUsers {
        .checkPerson {
          display: flex;
          flex-wrap: wrap;
          & > div {
            position: relative;
            width: 148px;
            height: 58px;
            padding: 8px 8px 8px 10px;
            background: #f5f5f5;
            border-radius: 4px 4px 4px 4px;
            box-sizing: border-box;
            margin-right: 18px;
            margin-bottom: 18px;
            & > div {
              display: flex;
              align-items: center;
              line-height: 0;
              span {
                margin-right: 3px;
                font-size: 14px;
                font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                font-weight: 400;
                color: #0b1a44;
              }
            }
            span {
              font-size: 12px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #a3a8bb;
              text-overflow: ellipsis; /*文字隐藏后添加省略号*/
              white-space: nowrap; /*强制不换行*/
            }
            .close {
              width: 18px;
              height: 18px;
              position: absolute;
              right: -4px;
              top: -4px;
              cursor: pointer;
            }
          }
        }
        .addButton {
          padding: 0;
          width: 80px;
          height: 32px;
          background: #eaeffc;
          // border-radius: 20px 20px 20px 20px;
          border: none;
          span {
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #3464e0;
          }
          .el-icon-plus {
            font-weight: 600;
            color: #3464e0;
          }
        }
      }
    }
    .record {
      width: 528px;
      height: 697px;
      padding-bottom: 15px;
      background: #f5f5f5;
      border-radius: 8px;
      box-sizing: border-box;
      overflow: auto;
      /* 设置滚动条的样式 */
      &::-webkit-scrollbar {
        width: 3px;
      }
      &::-webkit-scrollbar-track {
        box-shadow: 0 0 10px #000;
      }
      &::-webkit-scrollbar-thumb {
        background-color: #cdcfd3;
        border-radius: 30px;
      }
      .record_header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        padding: 0 24px;
        // padding-top: 12px;
        height: 42px;
        background: #d5dff1;
        font-size: 14px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        font-weight: bold;
        color: #0b1a44;
        border-radius: 8px 8px 0 0;
      }
      .lightTimeline {
        .version,
        .createTime {
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
        .record_item {
          position: relative;
          margin-left: 55px;
          padding: 11px 21px;
          padding-bottom: 35px;
          width: 285px;
          min-height: 126px;
          background: #fff;
          border-radius: 8px 8px 8px 8px;
          span {
            font-size: 12px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #0b1a44;
          }
          .operateButton {
            position: absolute;
            right: 16px;
            bottom: 0px;
          }
        }
        .newItem {
          background: #3464e0;
          span {
            color: #fff;
          }
        }
      }
    }
  }
}
.iterationDialog {
  ::v-deep {
    .el-dialog__header {
      border-bottom: 1px solid #eeeeef;
      .el-dialog__title {
        font-size: 16px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        font-weight: bold;
        color: #0b1a44;
      }
      .el-dialog__headerbtn {
        .el-icon-close {
          color: #657081;
          font-weight: bold;
          font-size: 18px;
          &:hover {
            color: #0b1a44;
          }
        }
      }
    }
    .el-form {
      .el-form-item {
        .el-form-item__label {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
        }
      }
      .version {
        .el-form-item__content {
          .el-input {
            width: 268px;
            height: 40px;
            .el-input__inner {
              width: 268px;
              height: 40px;
              border-radius: 4px 4px 4px 4px;
              border: 1px solid #d8dbe1;
            }
          }
        }
      }
      .time {
        .el-form-item__content {
          .el-input {
            width: 176px;
            height: 38px;
            .el-input__inner {
              width: 176px;
              height: 38px;
              padding-left: 15px;
              border-radius: 4px 4px 4px 4px;
              background: #f5f5f5;
              border: 1px solid #e9ebef;
            }
            .el-input__prefix {
              display: none;
            }
            .el-input__suffix {
              .el-input__suffix-inner {
                .el-input__icon {
                  color: #000;
                  font-size: 14px;
                }
              }
            }
          }
        }
      }
      .description {
        .el-form-item__content {
          .el-textarea {
            width: 494px;
            height: 228px;
            .el-textarea__inner {
              width: 494px;
              height: 228px;
              border-radius: 4px 4px 4px 4px;
              border: 1px solid #d8dbe1;
            }
          }
        }
      }
    }
    .el-dialog__footer {
      .el-button {
        width: 114px;
        height: 38px;
        padding: 0;
        border: none;
        font-size: 14px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        font-weight: bold;
      }
      .el-button--default {
        background: #f2f4ff;
        color: #868b9f;
        &:hover {
          background: #e8ebf8;
        }
      }
      .el-button--primary {
        margin-left: 32px;
      }
    }
  }
}
::v-deep {
  .line-container {
    margin-left: 25px;
    margin-top: 25px;
    &::after {
      border-right: 2px dashed #b1bac7;
      left: 176px;
      top: 3px;
      width: 2px;
      background: none;
    }
    .line-item {
      padding: 0;
      padding-left: 45px;
      margin-top: 15px;
      min-height: 100px;
      max-width: 630px;
      .item-tag {
        top: 0;
        width: 160px;
        text-align: start;
        line-height: 20px;
      }
      .item-symbol {
        left: 75px;
        background: none;
      }
    }
  }
}
</style>
