<template>
  <div class="app-container">
    <el-row :gutter="10" class="top">
      <el-col :span="24">
        <i class="el-icon-location"></i>
        <span>当前位置：<router-link to="/repository">知识库管理</router-link> /</span>
        <span>分享知识</span>
      </el-col>
    </el-row>
    <el-card>
      <template v-slot:header>
        <span>{{ showTitle }}</span>
        <!-- <span class="submitButton" @click="submit">分享</span> -->
      </template>
      <el-form ref="form" :model="formInfo" label-width="110px" :rules="formRules">
        <el-form-item label="分享标题:" prop="name">
          <el-input v-model="formInfo.name" placeholder="请输入分享标题" show-word-limit maxlength="40"></el-input>
        </el-form-item>
        <el-form-item label="分享类型:" class="fileType">
          <el-radio-group v-model="formInfo.type">
            <el-radio :label="1">技术类</el-radio>
            <el-radio :label="2">销售类 </el-radio>
            <el-radio :label="3">管理类</el-radio>
            <el-radio :label="4">政策类</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="部门性质:" class="fileType">
          <el-radio-group v-model="formInfo.organizationType">
            <el-radio :label="1">职能部门</el-radio>
            <el-radio :label="2">研发部门 </el-radio>
            <el-radio :label="3">全员 </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="分享内容:">
          <!-- <quill-editor ref="myQuillEditor" placeholder="请输入内容" class="myQuillEditor ql-editor" style="min-height: 150px" :options="editorOption" @change="onEditorChange($event)" /> -->
          <vueQuillEditor ref="contentQuill" @change="formInfo.content = $event.html" />
        </el-form-item>
        <el-form-item label="上传附件:" class="uploadFile">
          <ul class="fileList">
            <li v-for="(item, index) in fileList" :key="item.name">
              <div>
                <div class="fileImg">
                  <img src="@/assets/meeting/file.png" alt="" />
                  <span v-if="item.fileSize">{{ item.fileSize }}KB</span>
                  <span v-else>{{ item.size | formattingFileSize }}</span>
                </div>
                <div>
                  <el-tooltip class="item" effect="dark" :content="item.fileName || item.name" placement="top">
                    <span v-if="item.fileName && !item.flag">{{ item.fileName }}</span>
                    <span v-if="item.name && !item.flag">{{ item.name }}</span>
                  </el-tooltip>

                  <el-input v-if="item.flag" v-model="fileName" size="small" type="textarea" @blur.stop="uploadingConfirm(index)"></el-input>
                  <i v-if="!item.flag" class="el-icon-edit" @click="rechristen(item, index)"></i>
                </div>
              </div>
              <div v-if="item.percentage && item.percentage !== 100">
                <el-progress :percentage="parseInt(item.percentage)"></el-progress>
              </div>
              <div>
                <span v-if="item.name && item.createTime">{{ realName }}上传于{{ item.createTime | formatDate }}</span>
                <span v-else>{{ realName }}上传于{{ new Date() | formatDate }}</span>
                <i class="el-icon-download" @click="downloadFile(item)"></i>
                <i class="el-icon-delete" @click="delFile(item)"></i>
              </div>
            </li>
          </ul>
          <el-upload drag :action="`${actionUrl}/system/upload/file`" :headers="header" :file-list="fileList" multiple :before-upload="beforeUpload" :show-file-list="false" :on-success="uploadSuccess" :on-progress="uploadProgress" :on-remove="uploadRemove">
            <div class="uploadIcon">
              <i class="el-icon-circle-plus-outline"></i>
              点击添加文件
            </div>
            <div class="el-upload__text">支持doc /docx /xls /xlsx /pdf /jpg /png 格式,大小不超过50MB</div>
            <div slot="tip" class="el-upload__tip" style="color: red; line-height: 15px"></div>
          </el-upload>
        </el-form-item>
      </el-form>
      <el-row type="flex" justify="center" class="submitButton_row">
        <span class="submitButton" @click="submit">分享</span>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { meetingRenameFile } from '@/api/meeting'
import { mapGetters } from 'vuex'
import { knowledgeBaseSave, knowledgeBaseDetails, knowledgeBaseUdpate } from '@/api/repository'
import vueQuillEditor from '@/views/repository/components/vueQuillEditor'
export default {
  name: '',
  components: {
    vueQuillEditor
  },
  data() {
    return {
      formInfo: {
        name: null,
        type: 1,
        content: null,
        organizationType: 1,
        meetingFileReqs: []
      },
      TiLength: 0,
      formRules: {
        name: [{ required: true, message: '请输入分享标题', trigger: 'blur' }]
      },
      fileName: null,

      header: {
        Authorization: null
      },
      fileList: [],
      oldPath: null,
      actionUrl: window.config.VUE_APP_BASE_API
    }
  },
  computed: {
    ...mapGetters(['name', 'token', 'realName']),
    showTitle() {
      return this.formInfo.knowledgeBaseId ? '分享知识修改' : '分享知识'
    }
  },
  mounted() {
    if (parseInt(this.$route.params.type) !== 0) {
      this.edit()
    }
    // this.initTitle()
  },

  methods: {
    beforeUpload(file) {
      this.header.Authorization = `Bearer ${this.token}`
      console.log(file.type)
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isDOC = file.type === 'application/msword'
      const isDOCX = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      const isXLS = file.type === 'application/vnd.ms-excel'
      const isXLSX = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      const isPDF = file.type === 'application/pdf'
      const isMP4 = file.type === 'video/mp4'
      const isLt2M = file.size / 1024 / 1024 < 50
      if (!isLt2M) {
        this.$message.error('上传附件大小不能超过 50MB!')
      }
      if (!isJPG && !isPNG && !isDOC && !isDOCX && !isXLS && !isXLSX && !isPDF && !isMP4) {
        this.$message.warning('只能上传.doc .docx .xls .xlsx .pdf .jpg .png .mp4格式的附件')
      }
      return (isJPG || isPNG || isDOC || isDOCX || isXLS || isXLSX || isPDF || isMP4) && isLt2M
    },
    // 上传时的钩子
    uploadProgress(event, file, fileList) {
      this.fileList = fileList
      console.log(event, file, fileList)
    },
    // 上传成功回调
    uploadSuccess(response, file, fileList) {
      this.fileList = fileList
    },
    // 文件列表移除文件时的钩子
    uploadRemove(file, fileList) {
      this.fileList = fileList
    },
    downloadFile(row) {
      if (row.fileId) {
        window.open(row.fileUrl, '_blank')
      } else {
        window.open(row.response.data[0], '_blank')
      }
    },
    delFile(row) {
      console.log(row)
      this.fileList = this.fileList.filter((item) => item.uid !== row.uid)
    },
    async edit() {
      const { data } = await knowledgeBaseDetails({ knowledgeBaseId: this.$route.params.type, belongType: 3, sort: 1 })
      this.formInfo = { ...data }
      this.formInfo.meetingFileReqs = []
      this.fileList = data.fileDtos
      this.$refs['contentQuill'].setContent(data.content)
      // document.querySelectorAll('.ql-editor')[1].innerHTML = data.content
    },
    submit() {
      this.$refs['form'].validate(async (val) => {
        if (val) {
          if (this.formInfo.knowledgeBaseId) {
            if (this.fileList.length >= 1) {
              this.fileList.forEach((item) => {
                this.formInfo.meetingFileReqs.push({
                  fileName: item.fileName || item.name,
                  fileSize: item.fileSize || item.size / 1024,
                  fileUrl: item.fileUrl || item.response.data[0],
                  belongType: 3,
                  meetingId: this.formInfo.knowledgeBaseId
                })
              })
            }
            await knowledgeBaseUdpate(this.formInfo)
            this.$message.success('修改成功')
            this.$router.push('/repository')
          } else {
            if (this.fileList.length >= 1) {
              this.fileList.forEach((item) => {
                this.formInfo.meetingFileReqs.push({
                  fileName: item.name,
                  fileSize: item.size / 1024,
                  fileUrl: item.response.data[0],
                  belongType: 3,
                  meetingId: null
                })
              })
            }
            await knowledgeBaseSave(this.formInfo)
            this.$message.success('分享成功')
            this.$router.push('/repository')
          }
        }
      })

      console.log(this.formInfo)
    },

    // 重命名
    rechristen(row, index) {
      console.log(row)
      this.$set(this.fileList[index], 'flag', true)
      if (!row.fileId) {
        this.oldPath = row.response.data[0].split('workmanage/')[1]
        this.fileName = row.name
      } else {
        this.oldPath = row.fileUrl.split('workmanage/')[1]
        this.fileName = row.fileName
      }
      this.uid = row.uid
      // this.rechristenDialog = true
    },
    // 重命名触发事件
    async uploadingConfirm(index) {
      const { data } = await meetingRenameFile({ oldPath: this.oldPath, newPath: this.fileName })
      this.fileList.forEach((item) => {
        if (item.uid === this.uid) {
          if (!item.fileId) {
            item.response.data[0] = data
            item.name = this.fileName
          } else {
            item.fileUrl = data
            item.fileName = this.fileName
          }
        }
      })
      this.fileList[index].flag = false
      // this.rechristenDialog = false
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  position: relative;

  padding: 0;
  background-color: #e8eaed;
  min-height: 100%;
  padding-top: 58px;
  padding-right: 12px;
  padding-bottom: 30px;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
}
.top {
  position: absolute;
  top: 0px;
  width: 100%;
  background-color: #e8eaed;
  padding: 24px 0 16px 0;
  z-index: 999;
  .el-col {
    i {
      font-size: 14px;
      color: #657081;
    }
    span {
      &:first-of-type {
        margin: 0 5px;
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #657081;
      }
      &:last-of-type {
        font-size: 14px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        font-weight: bold;
        color: #0b1a44;
      }
    }
  }
}
.fileType {
  ::v-deep {
    .el-radio__input.is-checked .el-radio__inner {
      // background: #3464e0;
      background: #fff;
      width: 18px;
      height: 18px;
      border-color: #3464e0;
      // border: none;
      &::after {
        background-color: #3464e0;
        width: 8px;
        height: 8px;
      }
    }
    .el-radio__inner {
      width: 18px;
      height: 18px;
    }
    .el-radio__input.is-checked + .el-radio__label {
      color: #0b1a44;
    }
    .el-radio__label {
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #0b1a44;
    }
  }
}
::v-deep {
  .uploadFile {
    .el-form-item__content {
      display: flex;
    }
    .fileList {
      display: flex;
      flex-wrap: wrap;
      li {
        margin-right: 15px;
        margin-bottom: 32px;
        padding: 14px 10px 20px 10px;
        width: 275px;
        height: 124px;
        background: #f9f9f9;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #eeeeef;
        box-sizing: border-box;
        & > div {
          &:first-of-type {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            .fileImg {
              display: flex;
              flex-direction: column;
              justify-content: flex-start;
              img {
                width: 46px;
                height: 38px;
              }
              span {
                font-size: 12px;
                font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                font-weight: 400;
                color: #868b9f;
                line-height: initial;
              }
            }

            & > div {
              margin-left: 3px;
              line-height: initial;
              .el-textarea {
                width: initial;
                .el-textarea__inner {
                  width: 100%;
                }
              }

              &:last-of-type {
                span {
                  display: inline-block;
                  max-width: 170px;
                  font-size: 14px;
                  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                  font-weight: 400;
                  color: #0b1a44;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
                i {
                  margin-left: 5px;
                  color: #ff7e26;
                  font-size: 18px;
                  cursor: pointer;
                }
              }
            }
          }
          &:last-of-type {
            margin-top: 18px;
            font-size: 12px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #a3a8bb;
            line-height: initial;
            i {
              font-size: 18px;
              float: right;
              margin-left: 15px;
              cursor: pointer;
            }
            .el-icon-download:hover {
              color: #3464e0;
            }
            .el-icon-delete:hover {
              color: #eb6557;
            }
          }
        }
      }
    }

    .el-upload-dragger {
      width: 268px;
      height: 124px;
      margin-right: 14px;
      background: #f9f9f9;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #eeeeef;
    }
    .uploadIcon {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 15px;
      i {
        font-size: 18px;
        margin-right: 5px;
      }
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #3464e0;
    }
    .el-upload {
      order: 999 !important;
    }
    .el-upload__text {
      padding: 0 10px;
      margin: 0 auto;
      line-height: 25px;
      font-size: 12px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #a3a8bb;
    }
  }
}
.submitButton_row {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
}
.submitButton {
  display: block;
  width: 114px;
  height: 38px;
  line-height: 38px;
  background: #3464e0;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
  color: #ffffff;
  text-align: center;
  cursor: pointer;
  &:hover {
    background: #355fce;
  }
}
::v-deep {
  .el-card {
    position: relative;
    border-radius: 8px;
    box-shadow: none;

    .el-card__header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      span {
        &:first-of-type {
          font-size: 18px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
      }
    }
    .el-card__body {
      padding-left: 262px;
      padding-bottom: 60px;
    }
    .el-form {
      width: 1200px;
      .el-form-item__label {
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #0b1a44;
      }
    }
  }
  .myQuillEditor {
    padding-top: 0;
    padding-left: 0;
    height: 100% !important;
    // img{
    //   width: 60px;
    // }
  }
  .ql-editor {
    height: 400px;
    line-height: normal;
  }
}
</style>
