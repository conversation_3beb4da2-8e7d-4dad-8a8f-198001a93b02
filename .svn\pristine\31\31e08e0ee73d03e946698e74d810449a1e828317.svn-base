// 硬件产品库管理
import request from '@/utils/request'
/** 添加 */
export function tHardProductAdd(data) {
  return request({
    url: '/tHardProduct/add',
    method: 'POST',
    data
  })
}
/** 修改 */
export function tHardProductUpdate(data) {
  return request({
    url: '/tHardProduct/update',
    method: 'POST',
    data
  })
}
/** 删除 */
export function tHardProductRemove(params) {
  return request({
    url: '/tHardProduct/remove',
    method: 'DELETE',
    params
  })
}
/** 列表 */
export function tHardProductList(params) {
  return request({
    url: '/tHardProduct/list',
    method: 'GET',
    params
  })
}
/** 列表导出 */
export function tHardProductListExport(params) {
  return request({
    url: '/tHardProduct/listExport',
    method: 'GET',
    params
  })
}
/** 详情 */
export function tHardProductDetail(params) {
  return request({
    url: '/tHardProduct/detail',
    method: 'GET',
    params
  })
}
/** 批量导入 */
export function tHardProductImport(data) {
  return request({
    url: '/tHardProduct/import',
    method: 'POST',
    data
  })
}
