<template>
  <div class="navbar">
    <el-row :gutter="50" type="flex" justify="space-between" align="middle">
      <el-col :span="5" style="min-width: 360px">
        <div class="left_title">
          <router-link to="/">
            <img src="@/assets/Home/logo.png" alt="" class="logo" />
            <img src="@/assets/Home/title.png" alt="" class="title" />
          </router-link>
        </div>
      </el-col>
      <el-col :span="18">
        <div class="right-menu">
          <div class="fastTab" style="margin-right: 30px">
            <el-dropdown trigger="click" placement="bottom">
              <div class="cut">
                <img src="@/assets/Home/fastTab.png" alt="" />
                <span v-if="type" style="margin: 0 5px">{{ type | navBarModule }}</span>
                <i class="el-icon-caret-bottom" style="font-size: 12px" />
              </div>
              <el-dropdown-menu slot="dropdown" size="small" class="navBar_dropdown-menu">
                <el-dropdown-item style="width: 100%">
                  <img src="@/assets/dashboard/navBar_tabs_img.png" alt="" />
                </el-dropdown-item>
                <div ref="contentBox" class="contentBox">
                  <div class="content">
                    <el-dropdown-item v-for="item in moduleList.slice((pageNum - 1) * pageSize, pageSize * pageNum)" :key="item.path" @click.native="checkTab(item)">
                      <div>
                        <img class="module" :src="item.navBarImgUrl" alt="" style="margin-top: 7px" />
                        <span>{{ item.name }}</span>
                      </div>
                    </el-dropdown-item>
                  </div>
                </div>

                <template v-if="showPage">
                  <div class="goPageBox">
                    <div v-show="showLastPage" @click="goPage('up')">上一页</div>
                    <div v-show="showNextPage" @click="goPage('down')">下一页</div>
                  </div>
                </template>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <el-popover placement="top" trigger="click" popper-class="meesage_popover">
            <template v-slot:reference>
              <div class="messageHint">
                <el-badge value="new" class="item" :hidden="!messageList.length">
                  <i class="el-icon-message-solid"></i>
                  <i class="el-icon-caret-bottom"></i>
                </el-badge>
              </div>
            </template>
            <template>
              <el-row class="meesage_top" type="flex" justify="space-between" align="middle">
                <span>最新通知({{ messageList.length }})</span>
              </el-row>
              <el-row style="margin-top: 14px">
                <img src="@/assets/personCenter/message_bg.png" alt="" />
              </el-row>
              <template v-if="messageList.length">
                <el-row v-for="item in messageList" :key="item.messageId" class="meesage_info" @click.native="goJump(item)">
                  <span></span>
                  <span>{{ item.description }}</span>
                </el-row>
              </template>

              <div v-else class="no_message">
                <img src="@/assets/personCenter/no_message.png" alt="" />
                <div>暂无最新消息</div>
              </div>
              <div class="lookAll" @click="lookAll">查看全部</div>
            </template>
          </el-popover>
          <el-dropdown class="avatar-container" trigger="click">
            <div class="cut">
              <el-avatar :src="avatar" shape="circle" :size="40" fit="cover" class="user-avatar" @error="true">
                <img src="@/assets/login/logo.png" />
              </el-avatar>
              <span class="user_realName">{{ realName }}</span>
              <i class="el-icon-caret-bottom" />
            </div>
            <el-dropdown-menu slot="dropdown" class="user-dropdown">
              <router-link to="/">
                <el-dropdown-item>
                  <span style="display: block">首页</span>
                </el-dropdown-item>
              </router-link>
              <el-dropdown-item @click.native="goUserCenter">
                <span style="display: block">个人中心</span>
              </el-dropdown-item>
              <el-dropdown-item @click.native="changePassword"> 修改密码 </el-dropdown-item>

              <el-dropdown-item divided @click.native="logout">
                <span style="display: block">退出登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </el-col>
    </el-row>
    <!-- 修改密码弹窗 -->
    <el-dialog title="修改密码" :visible.sync="changePasswordDialog" width="420px" append-to-body>
      <div>
        <el-form ref="passwordForm" :model="changePasswordInfo" label-width="80px" :rules="passwordRules">
          <el-form-item label="原密码" prop="oldPassword">
            <el-input v-model="changePasswordInfo.oldPassword" type="password"></el-input>
          </el-form-item>
          <el-form-item label="新密码" prop="newPassword">
            <el-input v-model="changePasswordInfo.newPassword" type="password"></el-input>
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input v-model="changePasswordInfo.confirmPassword" type="password"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="changePasswordDialog = false">取 消</el-button>
        <el-button type="primary" @click="onClickPassword">确 定</el-button>
      </div>
    </el-dialog>
    <!-- <hamburger :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" /> -->

    <!-- <breadcrumb class="breadcrumb-container" /> -->
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { changePassword } from '@/api/systemUser'
import { tMessageList } from '@/api/tMessage'
import { getPassword } from '@/utils/auth'
import moduleList from '@/Home/menuModule.js'
import { gsap } from 'gsap'

// import Breadcrumb from '@/components/Breadcrumb'
// import Hamburger from '@/components/Hamburger'
export default {
  name: 'NavBar',
  components: {
    // Breadcrumb
    // Hamburger
  },
  data() {
    return {
      type: 1,
      changePasswordDialog: false,
      changePasswordInfo: {
        oldPassword: null,
        newPassword: null,
        confirmPassword: null
      },
      passwordRules: {
        oldPassword: [{ required: true, message: '请输入原密码', trigger: 'blur' }],
        newPassword: [{ required: true, message: '请输入新密码', trigger: 'blur' }],
        confirmPassword: [{ required: true, message: '请确认新密码', trigger: 'blur' }]
      },
      moduleList,
      pageNum: 1,
      pageSize: 9,
      messageList: []
    }
  },
  computed: {
    ...mapGetters(['sidebar', 'avatar', 'keyList', 'routes', 'realName', 'userId']),

    showPage() {
      return this.moduleList.length > 9
    },
    showNextPage() {
      const totalPage = this.moduleList.length / this.pageSize
      return this.pageNum < totalPage
    },
    showLastPage() {
      return this.pageNum > 1
    }
  },
  created() {
    this.type = window.localStorage.getItem('zf_oa_type')
  },
  mounted() {
    const that = this
    // 根据自己需要来监听对应的key
    window.addEventListener('setItemEvent', function (e) {
      // e.key : 是值发生变化的key
      // 例如 e.key==="token";
      // e.newValue : 是可以对应的新值
      if (e.key === 'zf_oa_type') {
        that.$nextTick(() => {
          that.type = e.newValue
        })
      }
    })
    this.gettMessageList()
  },
  methods: {
    toggleSideBar() {
      // this.$store.dispatch('app/toggleSideBar')
    },
    // 获取消息接口
    async gettMessageList() {
      const { data } = await tMessageList({ pageNum: 1, pageSize: 5 })
      this.messageList = data.list
    },
    goJump(item) {
      const type = parseInt(item.type)
      const dataType = type === 1 ? 9 : type === 2 ? 6 : type === 3 ? 3 : type === 4 ? 7 : type === 5 ? 12 : 15
      this.$store.commit('checkedData/set_data_type', dataType)
      const jumpModule = type === 1 ? 'Bug' : type === 2 ? 'Institution' : type === 3 ? 'Meeting' : type === 4 ? 'SecretLog' : type === 5 ? 'Software' : 'newContract'
      this.$nextTick(() => {
        this.$router.push({
          name: jumpModule
        })
      })
    },
    lookAll() {
      window.sessionStorage.setItem('zf_admin_userCenter_activeTab', 2)
      this.$router.push('/personal/center')
    },
    goUserCenter() {
      this.$router.push('/personal/center')
      window.sessionStorage.setItem('zf_admin_userCenter_activeTab', 0)
    },
    async logout() {
      const loading = this.$loading({
        text: '正在退出，请稍后',
        background: 'rgba(0,0,0,0.8)'
      })
      await this.$store.dispatch('user/logout')
      loading.close()
      this.$router.push(`/login`)
    },
    checkTab(item) {
      const type = item.moduleType
      this.type = type
      this.$store.commit('checkedData/set_data_type', type)

      this.$nextTick(() => {
        if (type === 5) {
          if (this.keyList.includes('training')) {
            this.$router.push('/training')
          } else {
            this.$router.push('/repository')
          }
        } else if (type === 7) {
          const route = this.routes.filter((item) => {
            if (item.meta && item.meta.type === 7) {
              return item
            }
          })
          this.$router.push(route[0].path)
        } else if (type === 12) {
          const route = this.routes.filter((item) => {
            if (item.meta && item.meta.type === 12) {
              return item
            }
          })
          this.$router.push(route[0].path)
        } else {
          if (type === 'task') {
            window.open(item.path, 'blank')
          } else {
            this.$router.push(item.routePath)
          }
        }
      })
    },
    changePassword() {
      this.changePasswordDialog = true
    },
    onClickPassword() {
      this.$refs['passwordForm'].validate(async (val) => {
        if (val) {
          if (this.changePasswordInfo.oldPassword === getPassword()) {
            if (this.changePasswordInfo.confirmPassword === this.changePasswordInfo.newPassword) {
              // 通过
              await changePassword({
                userId: this.userId,
                newPassword: this.changePasswordInfo.newPassword,
                oldPassword: this.changePasswordInfo.oldPassword
              })
              this.changePasswordDialog = false
              // 修改密码后退出登录
              this.$nextTick(() => {
                this.logout()

                this.$message.warning('请重新登录')
              })
            } else {
              this.$message.warning('两次密码不一致')
            }
          } else {
            this.$message.warning('原密码不正确')
          }
        }
      })
    },
    goPage(type) {
      if (type === 'up') {
        this.pageNum--
        gsap.fromTo(this.$refs['contentBox'], { duration: 0.3, opacity: 0, x: -250, ease: 'power1.inOut' }, { duration: 0.4, opacity: 1, x: 0, ease: 'sine.out' })
      } else {
        this.pageNum++
        gsap.fromTo(this.$refs['contentBox'], { duration: 0.3, opacity: 0, x: 250, ease: 'power1.inOut' }, { duration: 0.4, opacity: 1, x: 0, ease: 'sine.out' })
      }

      // this.$refs['contentBox'].style = type === 'down' ? `transform: translateX(${this.$px2rem(-400)})` : 'transform: translateX(0)'
      // this.currentPage = type === 'down' ? 2 : 1
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 68px;
  width: 100%;
  overflow: hidden;
  position: relative;
  // background: #fff;
  background: url('../../assets/Home/nav_bg.png');
  background-size: cover;
  // box-shadow: 0 1px 4px rgba(0,21,41,.08);

  // .hamburger-container {
  //   line-height: 46px;
  //   height: 100%;
  //   float: left;
  //   cursor: pointer;
  //   transition: background 0.3s;
  //   -webkit-tap-highlight-color: transparent;

  //   &:hover {
  //     background: rgba(0, 0, 0, 0.025);
  //   }
  // }

  // .breadcrumb-container {
  //   float: left;
  // }

  .left_title {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    .logo {
      width: 48px;
      height: 38px;
    }
    .title {
      width: 240px;
      height: 25px;
      padding-left: 8px;
    }
  }
  .right-menu {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-right: 40px;
    height: 100%;
    line-height: 50px;
    &:focus {
      outline: none;
    }
    .cut {
      display: flex;
      align-items: center;
      height: 100%;
      font-size: 16px;
      cursor: pointer;
    }

    .messageHint {
      display: flex;
      align-items: center;
      margin-right: 30px;
      height: 20px;
      line-height: 20px;
      cursor: pointer;
      .el-icon-message-solid {
        position: relative;
        font-size: 21px;
        &::after {
          display: none;
          position: absolute;
          top: 0;
          right: 3px;
          z-index: 2;
          width: 5px;
          height: 5px;
          border-radius: 50%;
          background: #eb6557;
          content: '';
        }
      }
      .el-icon-caret-bottom {
        color: #606266;
        font-size: 12px;
      }
    }

    .avatar-container {
      margin-right: 30px;

      .cut {
        // margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 50%;
        }
        .user_realName {
          padding: 0 0px 0 10px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
        }
        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 30px;
          font-size: 12px;
        }
      }
    }
  }
}
.el-row {
  height: 100%;
  .el-col {
    height: 100%;
  }
}
.el-dropdown {
  height: 100%;
}
</style>

<style lang="scss">
.navBar_dropdown-menu {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
  padding: 21px 19px;
  padding-bottom: 40px;
  top: 70px !important;
  width: 418px;
  overflow: hidden;
  .contentBox {
    display: flex;
    width: 1200px;
    margin-top: 20px;
    .content {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      align-content: flex-start;
      width: 400px;
      height: 318px;
      overflow: hidden;
    }
  }
  .el-dropdown-menu__item {
    width: 98px;
    height: 82px;
    // margin-right: 42px;
    margin-bottom: 24px;
    padding: 0;
    border-radius: 10px 10px 10px 10px;
    &:nth-of-type(2),
    &:nth-of-type(5),
    &:nth-of-type(8) {
      margin: 0 42px;
    }
    // &:nth-of-type(1),
    // &:nth-of-type(4),
    // &:nth-of-type(7),
    // &:nth-of-type(10) {
    //   margin-right: 0;
    // }
    &:nth-of-type(1) {
      // height: 96px;
      margin-bottom: 24px;
    }
    & > div {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 98px;
      height: 82px;
      background: #f2f7ff;
      border-radius: 10px 10px 10px 10px;
      text-align: center;
      span {
        font-size: 12px;
        // font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        color: #0b1a44;
      }
    }
  }
  .goPageBox {
    position: absolute;
    left: 50%;
    bottom: 10px;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    & > div {
      margin-right: 20px;
      width: 70px;
      height: 30px;
      line-height: 30px;
      background: #f2f7ff;
      font-size: 14px;
      color: #4783ed;
      text-align: center;
      border-radius: 15px;
      cursor: pointer;
    }
  }
}
</style>

<style lang="scss">
// 消息通知弹出框中的样式
.meesage_popover {
  position: relative;
  padding-bottom: 16px;
  &::after {
    content: '';
    position: absolute;
    bottom: 51px;
    left: 0;
    width: 100%;
    height: 1px;
    background: #eeeeef;
  }
  .meesage_top {
    & > span:first-of-type {
      font-size: 16px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #0b1a44;
      cursor: pointer;
    }
  }
  .no_message {
    margin-top: 39px;
    text-align: center;
    & > div {
      margin-top: 10px;
      font-size: 14px;
      color: #b1bac7;
    }
  }
  .meesage_info {
    display: flex;
    align-items: center;
    margin-top: 12px;
    cursor: pointer;
    &:last-of-type {
      margin-bottom: 24px;
    }
    & > span:first-of-type {
      display: inline-block;
      width: 4px;
      height: 4px;
      background: #eb6557;
      margin: 0 10px;
      border-radius: 50%;
    }
    & > span:last-of-type {
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #657081;
    }
  }
  .lookAll {
    margin-top: 40px;
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #0b1a44;
    text-align: center;
    cursor: pointer;
  }
}
</style>
