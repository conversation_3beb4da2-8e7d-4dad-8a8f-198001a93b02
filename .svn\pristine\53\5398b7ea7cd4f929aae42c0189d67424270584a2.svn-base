// 项目管理
import request from '@/utils/request'

/** 项目及售后记录列表 */
export function projectList(params) {
  return request({
    url: '/project/list',
    method: 'GET',
    params
  })
}

/** 添加项目或 售后记录 */
export function projectSaveProject(data) {
  return request({
    url: '/project/saveProject',
    method: 'POST',
    data
  })
}
/** 删除项目或售后记录 */
export function projectDelete(params) {
  return request({
    url: '/project/delete',
    method: 'DELETE',
    params
  })
}
/** 项目管理或售后记录详情 - 人员列表 */
export function projectUsersDetails(params) {
  return request({
    url: '/project/usersDetails',
    method: 'GET',
    params
  })
}

/** 添加人员 */
export function projectSaveUser(data) {
  return request({
    url: '/project/saveUser',
    method: 'POST',
    data
  })
}
/** 移除人员 */
export function projectRemoveUser(data) {
  return request({
    url: '/project/removeUser',
    method: 'POST',
    data
  })
}

/** 项目管理或售后记录详情 - 文件列表 */
export function projectFilesDetails(params) {
  return request({
    url: '/project/filesDetails',
    method: 'GET',
    params
  })
}

/** 添加附件(项目管理或售后记录) */
export function projectSaveFile(data) {
  return request({
    url: '/project/saveFile',
    method: 'POST',
    data
  })
}
/** 项目管理或售后记录详情 - 版本列表 */
export function projectVersionDetails(params) {
  return request({
    url: '/project/versionDetails',
    method: 'GET',
    params
  })
}
/** 保存版本记录 */
export function projectSaveVersion(data) {
  return request({
    url: '/project/saveVersion',
    method: 'POST',
    data
  })
}
/** 更新版本记录 */
export function projectUpdateVersion(data) {
  return request({
    url: '/project/updateVersion',
    method: 'POST',
    data
  })
}
/** 删除版本记录 */
export function projectDeleteVersion(params) {
  return request({
    url: '/project/deleteVersion',
    method: 'GET',
    params
  })
}
/** 项目管理或售后记录详情 - 施工列表 */
export function projectBuildDetails(params) {
  return request({
    url: '/project/buildDetails',
    method: 'GET',
    params
  })
}
/** 保存施工记录 */
export function projectBuildSaveBuild(data) {
  return request({
    url: '/project/saveBuild',
    method: 'POST',
    data
  })
}
/** 更新施工记录 */
export function projectBuildUpdateBuild(data) {
  return request({
    url: '/project/updateBuild',
    method: 'POST',
    data
  })
}
/** 删除施工记录 */
export function projectBuildDeleteBuild(params) {
  return request({
    url: '/project/deleteBuild',
    method: 'GET',
    params
  })
}
/** 修改 项目管理 或 售后记录 */
export function projectUpdateProject(data) {
  return request({
    url: '/project/updateProject',
    method: 'POST',
    data
  })
}

/** 项目及售后记录详情 */
export function projectDetails(params) {
  return request({
    url: '/project/details',
    method: 'GET',
    params
  })
}

/** 研发类项目列表 */
export function projectDevelopList(params) {
  return request({
    url: '/project/developList',
    method: 'GET',
    params
  })
}

