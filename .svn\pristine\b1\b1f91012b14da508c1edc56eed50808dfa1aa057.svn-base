<template>
  <div :class="{ 'has-logo': showLogo }">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu :default-active="activeMenu" :collapse="false" :background-color="variables.menuBg" :text-color="variables.menuText" :unique-opened="false" :active-text-color="variables.menuActiveText" :collapse-transition="false" mode="vertical">
        <sidebar-item v-for="route in checkedRoutes" :key="route.path" class="sideBar_item" :item="route" :base-path="route.path" />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import variables from '@/styles/variables.scss'

export default {
  components: { SidebarItem, Logo },
  data() {
    return {
      checkedRoutes: []
    }
  },
  computed: {
    ...mapGetters(['sidebar', 'routes', 'checkedType']),
    // routes() {
    //   return this.$router.options.routes
    // },
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    variables() {
      return variables
    },
    isCollapse() {
      return !this.sidebar.opened
    }
  },
  watch: {
    checkedType(val) {
      this.setType()
      this.changeType()
    }
  },
  mounted() {
    this.setType()
  },
  methods: {
    setType() {
      const routes = []
      if (window.localStorage.getItem('zf_oa_type')) {
        this.routes.forEach((item) => {
          if (item.meta && (item.meta.type === parseInt(window.localStorage.getItem('zf_oa_type')) || item.meta.type === 0)) {
            routes.push(item)
          }
        })
      } else {
        this.routes.forEach((item) => {
          if (item.meta && (item.meta.type === parseInt(this.checkedType) || item.meta.type === 0)) {
            routes.push(item)
          }
        })
      }
      this.checkedRoutes = routes
    },
    changeType() {
      this.$router.replace(this.checkedRoutes[0].path)
    }
  }
}
</script>
<style lang="scss" scoped>
.sideBar_item {
  height: 76px;
  margin-bottom: 16px;
  &:first-of-type {
    margin-top: 16px;
  }
  ::v-deep {
    a {
      display: flex !important;
      justify-content: center;
      align-content: center;
      height: 100%;
    }
    .el-menu-item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 0 !important;
      height: 100%;
      width: 76px;
      background: #fbfbfc !important;
      border-radius: 13px;
      line-height: 40px;
      svg {
        font-size: 20px;
        margin-right: 0 !important;
      }
      span {
        height: 30px !important;
      }
    }
    .is-active {
      background: #3465df !important;
    }
  }

  // li {

  // }
}
</style>
