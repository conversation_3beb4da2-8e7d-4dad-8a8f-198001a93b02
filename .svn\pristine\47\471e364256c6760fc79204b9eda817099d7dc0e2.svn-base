<template>
  <div class="app-container">
    <el-row :gutter="50" class="top" type="flex" justify="space-between" align="middle" style="margin-bottom: 16px">
      <el-col :span="6">
        <div class="top_left">
          <span class="meeting_icon">
            <img src="@/assets/product/product_icon.png" alt="" />
            产品管理
          </span>
        </div>
      </el-col>
    </el-row>
    <div class="box_container">
      <div class="box_inside">
        <el-row style="padding: 0 24px; border-bottom: 2px solid #f5f5f5">
          <el-col :span="24">
            <el-form ref="form" class="searchForm" :model="queryInfo" label-width="80px" inline>
              <el-form-item label="产品名称:">
                <el-input v-model="queryInfo.productName" size="small" placeholder="请输入产品名称" clearable></el-input>
              </el-form-item>
              <el-form-item label="版本号:">
                <el-input v-model="queryInfo.version" min="0" size="small" clearable placeholder="请输入版本号" oninput="value = value.replace(/[\u4E00-\u9FA5]/g,'')"></el-input>
              </el-form-item>
              <el-form-item label="负责人:">
                <el-input v-model="queryInfo.username" size="small" placeholder="请输入负责人" clearable></el-input>
              </el-form-item>
              <el-form-item label="创建开始时间:" label-width="110px">
                <el-date-picker v-model="date" size="small" type="daterange" range-separator="→" start-placeholder="开始日期" end-placeholder="结束日期" @change="datePickerChange"> </el-date-picker>
              </el-form-item>
              <el-form-item label="产品类型:">
                <el-select v-model="queryInfo.productType" placeholder="请选择产品类型" size="small" clearable>
                  <el-option label="软件" :value="1"> </el-option>
                  <el-option label="硬件" :value="2"> </el-option>
                  <el-option label="虚拟仿真" :value="3"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" size="small" style="margin-left: 20px" @click="getProductList">查询</el-button>
                <el-button type="primary" plain size="small" @click="reset">重置</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row class="addButton" style="padding: 16px 24px">
          <el-button type="primary" size="small" @click="add">
            <img src="@/assets/project/add_icon.png" alt="" />
            <span>添加产品</span>
          </el-button>
        </el-row>
        <div style="padding: 0 24px">
          <template v-if="productList.length > 0">
            <el-table :data="productList" style="width: 100%" border :header-cell-style="{ background: '#d5dff1', color: '#0B1A44', fontSize: '14px', fontFamiy: 'Microsoft YaHei-Bold, Microsoft YaHei' }" @row-click="details">
              <el-table-column prop="productCode" label="产品编号" width="192" align="center">
                <template v-slot="{ row }"> <img src="@/assets/project/table_code_icon.png" alt="" style="margin-right: 8px" />{{ row.productCode }} </template>
              </el-table-column>
              <el-table-column prop="productName" label="产品名称" width="373" align="center">
                <template v-slot="{ row }">
                  <div class="productName">
                    <img src="@/assets/product/productName_icon.png" alt="" style="margin-right: 5px" />
                    <el-tooltip class="item" effect="light" :content="row.productName" placement="top">
                      <span>{{ row.productName }}</span>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="productType" label="产品类型" width="197" align="center">
                <template v-slot="{ row }">
                  <div class="productType">
                    <img v-if="row.productType == 1" src="@/assets/product/productType1.png" alt="" />
                    <img v-if="row.productType == 2" src="@/assets/product/productType2.png" alt="" />
                    <img v-if="row.productType == 3" src="@/assets/product/productType3.png" alt="" />
                  </div>
                  <!-- <span>{{ row.productType | formattingProductType }}</span> -->
                </template>
              </el-table-column>
              <el-table-column prop="version" label="当前版本号" width="183" align="center">
                <template v-slot="{ row }">
                  <div v-if="row.version" class="version">
                    {{ row.version }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="userName" label="负责人" width="197" align="center"> </el-table-column>
              <el-table-column prop="createTime" label="创建时间" width="220" align="center"> </el-table-column>
              <el-table-column label="操作" width="width" align="center">
                <template v-slot="{ row }">
                  <!-- <el-button type="success" size="small" @click="iterationRecord(row.productId)">迭代记录</el-button> -->
                  <!-- <el-button type="primary" size="small" @click="details(row)">查看详情</el-button> -->
                  <el-tooltip class="item" effect="light" content="修改" placement="top">
                    <img src="@/assets/meeting/edit.png" alt="" @click.stop="edit(row)" />
                  </el-tooltip>
                  <el-tooltip class="item" effect="light" content="删除" placement="top">
                    <img src="@/assets/meeting/del.png" alt="" @click.stop="del(row)" />
                  </el-tooltip>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination style="text-align: right; margin-top: 15px" layout="prev, pager, next" background :page-sizes="[10, 15, 20, 30]" :total="total" :page-size.sync="queryInfo.pageSize" :current-page.sync="queryInfo.pageNum" @size-change="getProductList" @current-change="getProductList" />
          </template>
          <template v-else>
            <div style="width: 100%; height: 499px; position: relative">
              <el-empty>
                <template v-slot:image>
                  <img src="@/assets/product/noData.png" alt="" />
                </template>
                <template v-slot:description>
                  <img src="@/assets/product/noData_text.png" alt="" />
                </template>
              </el-empty>
            </div>
          </template>
        </div>
      </div>
    </div>
    <el-drawer :visible.sync="drawer" direction="rtl" :show-close="false">
      <template v-slot:title>
        <el-row :gutter="10" type="flex" justify="space-between" align="middle">
          <el-col :span="4">
            <span style="color: #000; font-size: 18px">迭代记录</span>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" size="small" @click="iterationDialog = true">添加迭代</el-button>
          </el-col>
        </el-row>
      </template>
      <div style="border-top: 1px solid #eee">
        <light-timeline v-if="detailsInfo.iterations && detailsInfo.iterations.length >= 1" :items="detailsInfo.iterations" class="lightTimeline">
          <template v-slot:tag="{ item }">
            <div>版本号:{{ item.version }}</div>
            <div>{{ item.time | formatDate }}</div>
            <div>创建人:{{ item.realName }}</div>
          </template>
          <template v-slot:content="{ item }">
            <span>{{ item.description }}</span>
            <el-row :gutter="10" type="flex" justify="end" style="margin-top: 15px">
              <el-col :span="8.5">
                <el-button type="warning" size="mini" @click="editIteration(item)">修改</el-button>
                <el-button type="danger" size="mini" @click="delIteration(item)">删除</el-button>
              </el-col>
            </el-row>
          </template>
        </light-timeline>
        <el-empty v-else description="暂无数据" class="noData"></el-empty>
      </div>
    </el-drawer>

    <!-- <improvement ref="improvement" :show-dialog.sync="showDialog" @success="getProductList" /> -->
  </div>
</template>

<script>
import { getdictTypeList, productRemove } from '@/api/product'
// import improvement from '@/views/product/components/improvement'
import { formatDate } from '@/filters'
export default {
  name: 'Product',
  components: {
    // improvement
  },
  data() {
    return {
      productList: [],
      queryInfo: {
        productName: null,
        version: null,
        username: null,
        startTime: null,
        endTime: null,
        productType: null,
        pageNum: 1,
        pageSize: 6
      },
      date: null,
      total: 0,
      showDialog: false,
      detailsInfo: {},
      drawer: false,
      iterationDialog: false
    }
  },
  computed: {
    showTitle() {
      return this.iterationForm.iterationId ? '修改迭代' : '添加迭代'
    }
  },
  created() {
    this.getProductList()
  },
  methods: {
    async getProductList() {
      const { data } = await getdictTypeList(this.queryInfo)
      this.productList = data.list
      this.total = data.total
      console.log(data)
    },
    datePickerChange(val) {
      if (val) {
        this.queryInfo.startTime = formatDate(val[0])
        this.queryInfo.endTime = formatDate(val[1], 'yyyy-MM-dd') + ' 23:59:59'
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getProductList()
    },
    reset() {
      this.queryInfo = {
        productName: null,
        version: null,
        username: null,
        startTime: null,
        endTime: null,
        productType: null,
        pageNum: 1,
        pageSize: 6
      }
      this.date = null
      this.getProductList()
    },
    add() {
      // this.showDialog = true
      this.$router.push(`/product/add/${0}/${null}`)
    },

    details(row) {
      this.$router.push(`/product/details/${row.productId}`)
    },
    async edit(row) {
      this.$router.push(`/product/add/${1}/${row.productId}`)

      // const { data } = await productDetails({ productId: row.productId })
      // this.$refs['improvement'].edit(data)
      // this.showDialog = true
    },
    del(row) {
      this.$confirm('确定要删除吗, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await productRemove({ productId: row.productId })
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getProductList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  background-color: #e8eaed;
  min-height: 100%;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
  .top {
    .top_left {
      display: flex;
      align-items: center;
      .meeting_icon {
        display: flex;
        align-items: center;
        margin-right: 10px;
        font-size: 16px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        font-weight: bold;
        color: #0b1a44;
        img {
          margin-right: 8px;
        }
      }
    }
  }
  .box_container {
    padding: 24px;
    background: #f5f5f5;
    .box_inside {
      background: #ffffff;
      padding: 20px 0 53px;
      .searchForm {
        ::v-deep {
          .el-form-item__label {
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #0b1a44;
          }
          .el-input {
            width: 284px;
            height: 36px;
          }
          .el-input__inner {
            width: 284px;
            height: 36px;
            background: #ffffff;
            border: 1px solid #d8dbe1;
            &::placeholder {
              font-size: 14px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #b1bac7;
            }
          }
          .el-date-editor {
            line-height: 36px;
            .el-range__icon {
              display: none;
            }
            .el-range-separator {
              line-height: 30px;
            }
            .el-range__close-icon {
              line-height: 30px;
            }
          }
        }
      }
      .addButton {
        .el-button {
          ::v-deep {
            & > span {
              display: flex;
              align-items: center;
              img {
                margin-right: 5px;
              }
            }
          }
        }
      }
      .el-table {
        height: 499px !important;

        ::v-deep {
          .el-table__cell {
            font-size: 14px;
            color: #0b1a44;
          }
          .el-table__row {
            cursor: pointer;
          }
          .productName {
            display: flex;
            align-items: center;
            justify-content: center;
            span {
              color: #0b1a44;
              font-weight: bold;
              overflow: hidden; //超出隐藏
              text-overflow: ellipsis; //溢出用省略号显示
              display: -webkit-box; // 将对象作为弹性伸缩盒子模型显示。
              // 控制行数
              -webkit-line-clamp: 1; //超出两行隐藏
              -webkit-box-orient: vertical; // 从上到下垂直排列子元素
            }
          }
          .productType {
            box-sizing: border-box;
            img {
              padding-top: 5px;
            }
          }
          .version {
            margin: 0 auto;
            width: 60px;
            height: 22px;
            line-height: 22px;
            border-radius: 16px;
            border: 1px solid #ff7e26;
            background: #f7e5d8;
            font-size: 12px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #ff7e26;
          }
        }
      }

      .el-empty {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        ::v-deep {
          .el-empty__image {
            width: 266px;
            height: 142px;
            img {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }
  }
}

@media screen and(max-height:955px) {
  .app-container {
    padding: 15px 20px;
    .top {
      margin-bottom: 15px;
    }
    .addButton {
      padding: 13px 24px !important;
    }
  }
  .box_container {
    padding: 15px !important;
  }
  .box_inside {
    padding-bottom: 18px !important;
    padding-top: 18px !important;
  }
}
@media screen and(min-height:955px) {
  .box_container {
    padding: 21px !important;
  }
  .box_inside {
    padding-bottom: 20px !important;
    padding-top: 20px !important;
  }
}
::v-deep {
  .el-drawer__header {
    margin-bottom: 15px;
  }
  .el-timeline-item__dot {
    flex-direction: column;
    align-items: flex-start;
  }
  .line-container {
    margin-left: 25px;
    margin-top: 25px;
    &::after {
      left: 115px;
    }
    .line-item {
      padding: 0;
      padding-left: 45px;
      margin-top: 15px;
      min-height: 100px;
      max-width: 430px;
      .item-tag {
        top: 0;
        width: 120px;
        text-align: start;
        line-height: 20px;
      }
      .item-symbol {
        left: 19px;
      }
    }
  }
  .el-drawer__body {
    position: relative;
  }
  .noData {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
