<template>
  <div v-loading.fullscreen="fullscreenLoading" element-loading-text="数据保存中" element-loading-background="rgba(0, 0, 0, 0.8)" class="app-container">
    <el-row :gutter="10" class="top">
      <el-col :span="24">
        <i class="el-icon-location"></i>
        <span>当前位置：硬件产品库 /</span>
        <span>{{ pageType }}</span>
      </el-col>
    </el-row>
    <div class="box">
      <div class="header">
        {{ pageType }}
      </div>
      <el-form ref="form" :model="formInfo" :rules="rules" label-width="110px">
        <el-form-item label="产品名称:" prop="name" class="name">
          <el-input v-model="formInfo.name" placeholder="请输入产品名称" maxlength="40"></el-input>
        </el-form-item>
        <el-form-item label="品牌:" class="brand">
          <el-input v-model="formInfo.brand" placeholder="请输入品牌" maxlength="40"></el-input>
        </el-form-item>
        <el-form-item label="型号:" class="model">
          <el-input v-model="formInfo.model" placeholder="请输入型号" maxlength="40"></el-input>
        </el-form-item>
        <el-form-item label="规格:" class="specs">
          <el-input v-model="formInfo.specs" placeholder="请输入规格" maxlength="40"></el-input>
        </el-form-item>
        <el-form-item label="单价:" prop="offer" class="offer">
          <el-input v-model="formInfo.offer" placeholder="请输入单价(万元)" maxlength="40"> </el-input>
        </el-form-item>
        <el-form-item label="单位:" prop="unit" class="unit">
          <el-select v-model="formInfo.unit" placeholder="请选择单位">
            <el-option label="套" :value="1"> </el-option>
            <el-option label="台" :value="2"> </el-option>
            <el-option label="个" :value="3"> </el-option>
            <el-option label="端" :value="4"> </el-option>
            <el-option label="把" :value="5"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="采购联系方式:" prop="phone" class="phone">
          <el-input v-model="formInfo.phone" placeholder="请输入采购联系方式" maxlength="40"></el-input>
        </el-form-item>
        <el-form-item label="产品资质:" class="coverReqs">
          <el-upload ref="uploadCover" :action="uploadUrl" multiple :show-file-list="false" :headers="header" :before-upload="beforeUpload" :on-success="uploadSuccess">
            <div class="uploadAptitude"></div>
          </el-upload>
          <div v-if="formInfo.fileReqs && formInfo.fileReqs.length" class="coverPreview" @click.stop>
            <div v-for="(item, index) in formInfo.fileReqs" :key="item.fileUrl">
              <img :src="item.fileUrl" alt="" />
              <div class="close" @click="delCover(item, index)"></div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="备注:" class="remark">
          <el-input v-model="formInfo.remark" placeholder="请输入产品备注" type="textarea" resize="none" maxlength="500"></el-input>
        </el-form-item>
      </el-form>
      <div class="footer">
        <span @click="$router.push('/hardware')">取 消</span>
        <span @click="save">保 存</span>
      </div>
    </div>
  </div>
</template>

<script>
import { tHardProductAdd, tHardProductUpdate, tHardProductDetail } from '@/api/hardware'
import { checkTel } from '@/filters'
export default {
  name: 'HardwareAdd',

  data() {
    var checkNum = (rule, value, callback) => {
      if (!isNaN(parseInt(value))) {
        callback()
      } else {
        return callback(new Error('请输入数字'))
      }
    }
    return {
      formInfo: {
        name: null, // 产品名称
        brand: null, // 品牌
        model: null, // 型号
        specs: null, // 规格
        offer: null, // 单价(万元)
        unit: null, // 单位;1 套 2 台 3 个 4 端 5 把
        remark: null, //	备注
        phone: null, // 采购联系方式
        fileReqs: [] // 资质
      },
      rules: {
        name: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
        offer: [
          { required: true, message: '请输入单价', trigger: 'blur' },
          { validator: checkNum, trigger: 'blur' }
        ],
        unit: [{ required: true, message: '请选择单位', trigger: 'change' }],
        phone: [
          { required: true, message: '请输入采购联系方式', trigger: 'blur' },
          { validator: checkTel, trigger: 'blur' }
        ]
      },
      fullscreenLoading: false,
      uploadUrl: window.config.VUE_APP_BASE_API + '/system/upload/file',
      header: {
        Authorization: null
      },
      fileList: []
    }
  },
  computed: {
    pageType() {
      return this.$route.params.id === '0' ? '添加产品' : '编辑产品'
    }
  },
  created() {
    if (this.$route.params.id !== '0') {
      this.getDetails()
    }
  },
  methods: {
    save() {
      this.$refs['form'].validate((val) => {
        if (val) {
          this.fullscreenLoading = true
          if (this.pageType === '添加产品') {
            tHardProductAdd(this.formInfo)
              .then(() => {
                this.fullscreenLoading = false
                this.$message.success('保存成功')
                this.$router.push('/hardware')
              })
              .catch(() => {
                this.fullscreenLoading = false
              })
          } else {
            tHardProductUpdate(this.formInfo)
              .then(() => {
                this.fullscreenLoading = false
                this.$message.success('修改成功')
                this.$router.push('/hardware')
              })
              .catch(() => {
                this.fullscreenLoading = false
              })
          }
        }
      })
    },
    async getDetails() {
      const { data } = await tHardProductDetail({ id: this.$route.params.id })
      this.formInfo = { ...data, fileReqs: data.files }
    },
    // 上传封面的事件
    beforeUpload(file) {
      this.header.Authorization = `Bearer ${this.$store.getters.token}`
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      if (!isJPG && !isPNG) {
        this.$message.warning('只能上传.jpg .png 格式的附件')
      }
      return isJPG || isPNG
    },
    uploadSuccess(res, file, fileList) {
      if (res.code !== 200) {
        this.$message.error(res.message)
      } else {
        this.setCoverReqs(file)
      }
    },
    setCoverReqs(item) {
      this.formInfo.fileReqs.push({
        fileName: item.name || item.fileName,
        fileSize: item.size / 1024 || item.fileSize,
        fileUrl: item.response.data[0] || item.fileUrl,
        belongType: 11
      })
    },
    delCover(data) {
      this.formInfo.fileReqs = this.formInfo.fileReqs.filter((item, Index) => {
        if (item.fileName !== data.fileName) {
          return item
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  position: relative;
  padding: 0;
  background-color: #e8eaed;
  min-height: 100%;
  padding-top: 58px;
  padding-bottom: 10px;
  .top {
    position: absolute;
    top: 0px;
    width: 100%;
    background-color: #e8eaed;
    padding: 24px 0 16px 0;
    z-index: 999;
    .el-col {
      i {
        font-size: 14px;
        color: #657081;
      }
      span {
        &:first-of-type {
          margin: 0 5px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #657081;
        }
        &:last-of-type {
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
      }
    }
  }
  .box {
    width: 1754px;
    // height: 946px;
    min-height: 790px;
    padding-bottom: 30px;
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    .header {
      padding: 24px 0 11px 48px;
      border-bottom: 1px solid #eeeeef;
      font-size: 16px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #0b1a44;
    }
    ::v-deep {
      .el-form {
        padding-left: 488px;
        padding-top: 24px;
        border-bottom: 1px solid #eeeeef;
        .el-form-item {
          margin-bottom: 28px;
        }
      }
      .el-form-item__label {
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #0b1a44;
      }
      .el-input__inner {
        height: 40px;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #d8dbe1;
        &::placeholder {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #b1bac7;
        }
      }
      .name,
      .brand {
        .el-input__inner {
          width: 436px;
        }
      }
      .model,
      .specs,
      .offer,
      .unit,
      .phone {
        .el-input__inner {
          width: 318px;
        }
      }

      // 上传封面
      .coverReqs {
        .el-form-item__content {
          display: flex;
        }
        .el-upload {
          margin-right: 24px;
          .uploadAptitude {
            width: 120px;
            height: 88px;
            background: url('~@/assets/hardware/upload.png') no-repeat;
            background-size: cover;
            cursor: pointer;
          }
        }
        .coverPreview {
          display: flex;
          & > div {
            position: relative;
            width: 120px;
            height: 88px;
            margin-right: 16px;
            img {
              width: 100%;
              height: 100%;
              border-radius: 6px;
            }
            .close {
              position: absolute;
              right: -9px;
              top: -9px;
              width: 18px;
              height: 18px;
              background: url('~@/assets/library/close.png') no-repeat;
              background-size: cover;
              cursor: pointer;
              &:hover {
                background: url('~@/assets/library/close_hover.png') no-repeat;
                background-size: cover;
              }
            }
          }
        }
      }

      .remark {
        .el-textarea__inner {
          width: 682px;
          height: 138px;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #d8dbe1;
        }
      }
    }
    .footer {
      display: flex;
      justify-content: center;
      margin-top: 34px;
      span {
        display: inline-block;
        width: 260px;
        height: 46px;
        line-height: 46px;
        font-size: 14px;
        font-weight: 400;
        border-radius: 4px 4px 4px 4px;
        text-align: center;
        cursor: pointer;
      }
      & > span:first-of-type {
        background: #ffffff;
        border: 1px solid #d8dbe1;
        color: #a3a8bb;
      }
      & > span:last-of-type {
        margin-left: 32px;
        background: #3464e0;
        font-weight: bold;
        color: #ffffff;
      }
    }
  }
}
</style>
