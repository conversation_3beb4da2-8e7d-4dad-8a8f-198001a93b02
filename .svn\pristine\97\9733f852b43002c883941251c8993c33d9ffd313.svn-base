<template>
  <div class="app-container">
    <el-row :gutter="10" class="top">
      <el-col :span="24">
        <i class="el-icon-location"></i>
        <span>当前位置：资源库管理 /</span>
        <span>资源详情</span>
      </el-col>
    </el-row>
    <div class="box">
      <div v-if="JSON.stringify(info) !== '{}'" class="left">
        <div class="header">
          <div class="header_left">
            <span>{{ info.code }}</span>
            <span>{{ info.resourceName }}</span>
          </div>
          <div class="header_right">
            <template v-if="info.status === 1">
              <span class="button" @click="edit">编辑</span>
              <span class="button" @click="download">下载</span>
              <span class="button" @click="del">删除</span>
            </template>
            <template v-else>
              <span class="disabled">编辑</span>
              <span class="button" @click="download">下载</span>
              <span class="disabled">删除</span>
            </template>
          </div>
        </div>
        <div class="row2">
          <div class="row2_left">
            <img :src="info.headurl" alt="" />
            <span class="realName">{{ info.realName }}</span>
            <span class="createTime">{{ info.createTime }}上传</span>
            <span class="type">{{ info.type | resourceType }}</span>
            <span class="quality">{{ info.quality | quality }}</span>
            <span class="modelType">{{ info.modelType === 1 ? 'FBX' : 'GLB' }}</span>
          </div>
          <div class="row2_right">
            <span>{{ info.majorName }}</span>
            <i>|</i>
            <span>{{ info.projectName }}</span>
          </div>
        </div>
        <div class="previewArea">
          <template v-if="showType === 0">
            <model v-if="info.modelType === 2" ref="model" style="width: 100%; height: 100%" />
            <fbxModel v-else ref="fbxModel" style="width: 100%; height: 100%" />
          </template>
          <template v-else>
            <img :src="showImg" alt="" style="height: 100%" />
          </template>
        </div>
        <div class="thumbnail">
          <div class="leftButton">
            <img v-if="imgIndex !== 0" src="@/assets/library/details_left_button_1.png" alt="" @click="previewImg('up', item, itemIndex)" />
            <img v-else src="@/assets/library/details_left_button_2.png" alt="" />
          </div>
          <div class="preview">
            <div ref="moveBox">
              <span :class="{ checked: imgIndex === -1 }" @click="showResource">资源</span>
              <img v-for="(img, index) in info.coverFiles" :key="index" :src="img.fileUrl" alt="" :class="{ checked: imgIndex === index }" @click="exhibition(img, index)" />
            </div>
          </div>
          <div class="rightButton">
            <img v-if="imgIndex !== info.coverFiles.length" src="@/assets/library/details_right_button_1.png" alt="" @click="previewImg('down', item, itemIndex)" />
            <img v-else src="@/assets/library/details_right_button_2.png" alt="" />
          </div>
        </div>
        <div class="introduce">
          <div class="title">资源介绍</div>
          <div class="description">{{ info.description }}</div>
        </div>
      </div>
      <div class="right">
        <div class="title">操作日志</div>
        <div v-for="item in info.logs" :key="item.resourceLogId" class="log">
          <div class="realName">
            <img :src="item.headurl" alt="" />
            <span>{{ item.realName }}</span>
          </div>
          <span class="type">{{ item.type === 1 ? '预览' : item.type === 2 ? '下载' : '修改' }}了资源</span>
          <span class="time">{{ formatTime(item.createTime) }}</span>
        </div>
      </div>
    </div>

    <!-- 删除弹窗 -->
    <el-dialog :visible.sync="delDialog" width="413px">
      <template v-slot:title>
        <img src="@/assets/library/hint.png" alt="" class="hint" />
        <span class="hintText">提示</span>
      </template>
      <div class="delText">确定删除该资源吗？</div>
      <el-form label-width="80px" style="margin-top: 15px">
        <el-form-item label="备注" required class="description">
          <el-input v-model="delInfo.remark" type="textarea" placeholder="请输入备注说明" maxlength="150"></el-input>
        </el-form-item>
      </el-form>
      <div class="operate">
        <span class="closeButton" @click="closeDel">取 消</span>
        <span class="confirmButton" @click="confirmDel">确 定</span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { resourceDetail, resourceRemove, resourceLogAdd } from '@/api/Library'
import { formatTime } from '@/filters'
import model from '@/views/Library/model'
import fbxModel from '@/views/Library/fbxModel'
import { downUrl } from '@/utils/index'
export default {
  name: '',
  components: {
    model,
    fbxModel
  },
  data() {
    return {
      info: {},
      imgIndex: -1,
      showType: 0, // 0是模型1是图片
      showImg: null,
      delDialog: false,
      delInfo: {
        id: null,
        remark: null
      }
    }
  },
  created() {
    this.getDetails()
  },
  methods: {
    async getDetails() {
      const { data } = await resourceDetail({ id: this.$route.params.id })
      this.info = { ...data }
      this.initModel()
    },
    formatTime(val) {
      const time = new Date(val)
      console.log(time)
      return formatTime(time)
    },
    previewImg(type, item, index) {
      if (type === 'down') {
        if (item.imgIndex >= 2) {
          this.$refs['moveBox'][index].style = `transform: translateX(${(item.imgIndex - 1) * -33}px);`
        }
        return item.imgIndex++
      } else {
        if (item.imgIndex >= 3) {
          this.$refs['moveBox'][index].style = `transform: translateX(${(item.imgIndex - 2) * -33 + 33}px);`
        }
        return item.imgIndex--
      }
    },
    exhibition(item, index) {
      this.showType = 1
      this.showImg = item.fileUrl
      this.imgIndex = index
    },
    showResource() {
      this.showType = 0
      this.imgIndex = -1
      this.initModel()
    },
    initModel() {
      if (this.info.modelType === 2) {
        this.$nextTick(() => {
          this.$refs['model'].init(this.info.fileUrl) // 初始化模型
        })
      } else {
        this.$nextTick(() => {
          this.$refs['fbxModel'].init(this.info.fileUrl) // 初始化模型
        })
      }
    },
    edit() {
      this.$router.push(`/library/add/${this.$route.params.id}`)
    },
    async del() {
      this.delDialog = true
      this.delInfo.id = this.$route.params.id
    },
    closeDel() {
      this.delDialog = false
      this.delInfo = {
        id: null,
        remark: null
      }
    },
    confirmDel() {
      if (!this.delInfo.remark) {
        return this.$message.warning('备注不能为空')
      }
      const loading = this.$loading({
        text: '正在删除，请稍后',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      resourceRemove(this.delInfo)
        .then(() => {
          loading.close()
          this.$message.success('删除成功')
          this.delDialog = false
          this.delInfo = {
            id: null,
            remark: null
          }
          this.$router.push('/library')
        })
        .catch(() => {
          loading.close()
          this.$message.error('删除失败,请重试')
        })
    },
    async download() {
      await resourceLogAdd({ resourceId: this.$route.params.id, type: 2 })
      const fileName = this.info.fileUrl.split('/')[this.info.fileUrl.split('/').length - 1]
      downUrl(fileName, this.info.fileUrl)
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  position: relative;
  padding: 0;
  background-color: #e8eaed;
  min-height: 100%;
  padding-top: 58px;
  padding-bottom: 10px;
  .top {
    position: absolute;
    top: 0px;
    width: 100%;
    background-color: #e8eaed;
    padding: 24px 0 16px 0;
    z-index: 999;
    .el-col {
      i {
        font-size: 14px;
        color: #657081;
      }
      span {
        &:first-of-type {
          margin: 0 5px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #657081;
        }
        &:last-of-type {
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
      }
    }
  }
  .box {
    display: flex;
    width: 1754px;
    padding-right: 24px;
    padding-bottom: 14px;
    padding-top: 24px;
    min-height: 800px;
    // height: 800px;
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;

    .left {
      min-height: 100%;
      .header {
        display: flex;
        justify-content: space-between;
        padding-left: 56px;
        .header_left {
          span {
            font-size: 24px;
            font-family: Microsoft YaHei-Bold, Microsoft YaHei;
            font-weight: bold;
            color: #0b1a44;
            &:first-of-type {
              margin-right: 20px;
            }
          }
        }
        .header_right {
          .button {
            display: inline-block;
            margin-left: 16px;
            width: 60px;
            height: 32px;
            line-height: 30px;
            border-radius: 6px 6px 6px 6px;
            border: 1px solid #3464e0;
            font-size: 16px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #3464e0;
            text-align: center;
            cursor: pointer;
            &:hover {
              color: #ffffff;
              background: #3464e0;
            }
          }
          .disabled {
            display: inline-block;
            margin-left: 16px;
            width: 60px;
            height: 32px;
            line-height: 30px;
            border-radius: 6px 6px 6px 6px;
            border: 1px solid #d9dadd;
            font-size: 16px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #d9dadd;
            text-align: center;
            cursor: pointer;
          }
        }
      }
      .row2 {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-left: 60px;
        padding-top: 24px;
        .row2_left {
          display: flex;
          align-items: center;
          img {
            width: 33px;
            height: 33px;
            border-radius: 50%;
          }
          .realName {
            margin-left: 8px;
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #657081;
          }
          .createTime {
            margin-left: 12px;
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #f3c057;
          }
          .type,
          .quality,
          .modelType {
            margin-left: 24px;
            width: 64px;
            height: 24px;
            line-height: 23px;
            border-radius: 5px;
            background: #f3c057;
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #657081;
            text-align: center;
          }
          .quality {
            margin-left: 10px;
            background: #3464e0;
            color: #fff;
          }
          .modelType {
            margin-left: 10px;
            background: #23bb87;
            color: #fff;
          }
        }
        .row2_right {
          span {
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #0b1a44;
          }
          i {
            margin: 0 5px;
            font-style: normal;
            color: #d8dbe1;
          }
        }
      }
      .previewArea {
        margin-left: 56px;
        margin-top: 22px;
        width: 1220px;
        height: 608px;
        // background: #36383a;
        background: #eee;
        text-align: center;
      }
      .thumbnail {
        display: flex;
        align-items: center;
        width: 100%;
        padding-left: 56px;
        margin-top: 16px;
        .leftButton,
        .rightButton {
          img {
            width: 32px;
            height: 32px;
            cursor: pointer;
          }
        }
        .preview {
          flex: 1;
          margin: 0 14px;
          max-width: 1126px;
          overflow: hidden;
          & > div {
            display: flex;
            width: 500%;
            transition: all 0.2s;
            span {
              display: inline-block;
              width: 118px;
              height: 80px;
              line-height: 80px;
              background: #eee;
              margin-right: 8px;
              border: 2px solid transparent;
              text-align: center;
              font-size: 26px;
              font-weight: bold;
              cursor: pointer;
            }
            img {
              width: 118px;
              height: 80px;
              margin-right: 8px;
              border: 2px solid transparent;
              cursor: pointer;
              &:nth-of-type(9n) {
                margin-right: 0 !important;
              }
            }
            .checked {
              border: 2px solid #3465df;
            }
          }
        }
      }
      .introduce {
        margin-top: 40px;
        padding-top: 24px;
        border-top: 2px solid #eeeeef;
        .title {
          margin-left: 56px;
          font-size: 18px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
        }
        .description {
          margin-left: 56px;
          margin-top: 20px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
          line-height: 26px;
        }
      }
    }
    .right {
      margin-left: 48px;
      padding-top: 32px;
      width: 406px;
      background: #f5f5f5;
      border-radius: 8px 8px 8px 8px;
      .title {
        margin-left: 32px;
        margin-bottom: 25px;
        font-size: 18px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #0b1a44;
      }
      .log {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-left: 32px;
        padding-right: 24px;
        height: 48px;
        width: 100%;
        &:hover {
          background: #e9ebef;
        }
        .realName {
          display: flex;
          align-items: center;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #657081;
          img {
            margin-right: 16px;
            width: 32px;
            height: 32px;
            border-radius: 50%;
          }
        }
        .type {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #657081;
        }
        .time {
          width: 91px;
          font-size: 12px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #a3a8bb;
          text-align: right;
        }
      }
    }
  }
  ::v-deep {
    .el-dialog {
      height: 266px;
      border-radius: 8px !important;
      .el-dialog__header {
        display: flex;
        align-items: center;
        padding-top: 12px;
        padding-left: 24px;
        padding-right: 20px;
        padding-bottom: 8px;
        border-bottom: 1px solid #eeeeef;
        .hint {
          width: 30px;
          height: 30px;
        }
        .hintText {
          margin-left: 6px;
          font-size: 16px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #303a40;
        }
        .el-dialog__headerbtn {
          right: 20px;
          top: 20px;
          .el-dialog__close {
            font-weight: bold;
          }
        }
      }
      .delText {
        text-align: center;
        font-size: 16px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #303a40;
      }
      .description {
        .el-textarea__inner {
          width: 250px;
          height: 80px;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #d8dbe1;
        }
      }
      .operate {
        display: flex;
        justify-content: center;
        .closeButton {
          width: 114px;
          height: 38px;
          line-height: 36px;
          background: #ffffff;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #d8dbe1;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          color: #a3a8bb;
          text-align: center;
          cursor: pointer;
          &:hover {
            background: #f5f5f5;
          }
        }
        .confirmButton {
          margin-left: 32px;
          width: 114px;
          height: 38px;
          line-height: 36px;
          background: #3464e0;
          border-radius: 4px 4px 4px 4px;
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #ffffff;
          text-align: center;
          cursor: pointer;
          &:hover {
            background: #355fce;
          }
        }
      }
    }
  }
}
</style>
