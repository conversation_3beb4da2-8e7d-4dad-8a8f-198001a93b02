const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron')

contextBridge.exposeInMainWorld('electronAPI', {
  openWindow: (title) => ipcRenderer.send('open-window', title),
  asynchronousMessage: (data) => ipcRenderer.send('asynchronous-message', data),
  downLoadAllFile: (data) => ipcRenderer.invoke('download-folder', data),
  uploadFile: (data) => ipcRenderer.invoke('upload-files', data)
})

contextBridge.exposeInMainWorld('rendererOperate', {
  on: (message, data) => ipcRenderer.on(message, data),
  send: (data) => ipcRenderer.send(data)
})

contextBridge.exposeInMainWorld('electronDownload', {
  downloadFile: (fileUrl) => ipcRenderer.send('download-file', fileUrl)
})
