<template>
  <div v-loading.fullscreen="loading" element-loading-text="数据保存中，请稍后" element-loading-background="rgba(0, 0, 0, 0.8)" class="app-container">
    <el-row :gutter="10" class="top">
      <el-col :span="24">
        <i class="el-icon-location"></i>
        <span>当前位置：绩效管理 /</span>
        <span>{{ pageType }}</span>
      </el-col>
    </el-row>
    <div class="box">
      <div class="header">
        <div class="left">
          <img :src="$route.params.type !== 'add' ? dataInfo.headurl : avatar" alt="" class="avatar" />
          <div class="userInfo">
            <div>
              <span class="realName">{{ $route.params.type !== 'add' ? dataInfo.realName : realName }}</span>
              <template v-if="$route.params.type !== 'add'">
                <img v-if="dataInfo.sex === 'F'" src="@/assets/meeting/wuman.png" alt="" />
                <img v-else src="@/assets/meeting/man.png" alt="" />
              </template>
              <template v-else>
                <img v-if="sex === 'F'" src="@/assets/meeting/wuman.png" alt="" />
                <img v-else src="@/assets/meeting/man.png" alt="" />
              </template>
              <i>|</i>
              <span class="jobName">{{ $route.params.type !== 'add' ? dataInfo.jobName : jobName }}</span>
            </div>
            <div class="organizationName">{{ $route.params.type !== 'add' ? dataInfo.organizationName : organizationName }}</div>
          </div>
        </div>
        <template v-if="$route.params.type !== 'details'">
          <div class="center">
            <div>
              <span>个人任务</span>
              <span>{{ personTask ? personTask : '---' }}</span>
            </div>
            <div>
              <span>自评分</span>
              <span>{{ dataInfo.totalSelfScore ? dataInfo.totalSelfScore : '---' }}</span>
            </div>
            <div>
              <span>上级评分</span>
              <span>{{ dataInfo.totalSuperiorScore ? dataInfo.totalSuperiorScore : '---' }}</span>
            </div>
            <div>
              <span>联查评分</span>
              <span>{{ dataInfo.totalUnionScore ? dataInfo.totalUnionScore : '---' }}</span>
            </div>
            <div>
              <span>最终得分</span>
              <span>{{ dataInfo.totalLastScore ? dataInfo.totalLastScore : '---' }}</span>
            </div>
          </div>
          <div class="right">
            <div v-print="print" class="print" @click="printStart">打 印</div>
            <div v-if="$route.params.type !== 'score'" class="submit" @click="submit">提 交</div>
            <template v-else>
              <div class="sendBack" @click="sendBack">退 回</div>
              <div class="confirmScore" @click="confirmScore">确定评分</div>
            </template>
          </div>
        </template>
        <template v-else>
          <div class="statistics">
            <div>
              <section>
                <span>个人任务</span>
                <span>{{ personTask }}</span>
              </section>
              <section>
                <span>自评分</span>
                <span>{{ dataInfo.totalSelfScore }}</span>
              </section>
              <section>
                <span>上级评分</span>
                <span>{{ dataInfo.totalSuperiorScore }}</span>
              </section>
              <section>
                <span>联查评分</span>
                <span>{{ dataInfo.totalUnionScore }}</span>
              </section>
              <section>
                <span>最终得分</span>
                <span>{{ dataInfo.totalLastScore }}</span>
              </section>
            </div>
            <div>
              <span @click="reviseRecordDialog = true">修订记录</span>
              <i>|</i>
              <span v-print="print" @click="printStart">打印</span>
            </div>
          </div>
        </template>
      </div>
      <div class="content">
        <div :class="[{ detailsTitle: $route.params.type === 'details' }, 'title']">
          <template v-if="$route.params.type !== 'details'">
            <span>选择添加绩效月份：</span>
            <el-date-picker v-model="dataInfo.time" :disabled="isDisabled" type="month" format="yyyy/MM月 KPI" value-format="yyyy/MM"> </el-date-picker>
          </template>
          <template v-else>
            <div class="detailsTime">{{ dataInfo.time }}月 KPI</div>
          </template>
        </div>
        <!-- 表格 -->
        <div id="printArea" class="table">
          <el-row class="table_header">
            <div class="sort">序号</div>
            <div>
              目标和任务
              <el-tooltip
                effect="dark"
                popper-class="stateTooltip"
                content="清晰描述工作任务目标，避免超过30分以上的工作计划内容，如果只有一个项目开发，将项目的大模块拆分出来，重点工作填充绿色单元格，临时/置换工作用红色文字。"
                placement="top"
              >
                <img src="@/assets/performance/state3.png" alt="" />
              </el-tooltip>
            </div>
            <div>
              <div>
                权重
                <el-tooltip
                  effect="dark"
                  popper-class="stateTooltip"
                  content="这里总分数不要超过100分，如果有临时工作置换，将临时工作分数写为*x分*，这样不会计算分数，要保证置换的工作与原计划工作分数一致"
                  placement="top"
                >
                  <img src="@/assets/performance/state3.png" alt="" />
                </el-tooltip>
              </div>
              <div>（工作量或重要性占比）</div>
            </div>
            <div>
              <div>
                考核标准
                <el-tooltip effect="dark" popper-class="stateTooltip" content="考核标准写清楚，通过什么标准可以对前面的目标和任务进行检查，例如完成xx模块工作，百分比多少" placement="top">
                  <img src="@/assets/performance/state3.png" alt="" />
                </el-tooltip>
              </div>
              <div>（要有完成准确的节点，完成结果和输出物等在编辑中补充）</div>
            </div>
            <div>
              <div>
                绩效分析
                <el-tooltip effect="dark" popper-class="stateTooltip" content="如有扣分项目说明扣分原因，满分代表工作内容、进度、细节无瑕疵" placement="top">
                  <img src="@/assets/performance/state3.png" alt="" />
                </el-tooltip>
              </div>
              <div>（完成情况及扣分原因）</div>
            </div>
            <div>任务状态</div>
            <div>
              <div>自评分</div>
              <div>（20%）</div>
            </div>
            <div>
              <div>上级评分</div>
              <div>（加权处理50%）</div>
            </div>
            <div>
              <div>根据联查小组的检查 进行分数复核</div>
              <div>（联查的时候已经出来基本分值30%）</div>
            </div>
            <div>最终分值</div>
            <div>操作</div>
          </el-row>
          <!-- 月度关键业绩 -->
          <el-row class="kpi" type="flex" justify="space-between" align="middle">
            <div>月度关键业绩：85%</div>
            <div>
              <img v-if="kpiList.length" src="@/assets/performance/arrow.png" alt="" class="arrow" @click="showKpi = !showKpi" />
              <img v-else src="@/assets/performance/arrow_disable.png" alt="" class="arrow" />
              <i v-if="!isDisabled" class="add el-icon-circle-plus" @click="add(1)"></i>
            </div>
          </el-row>
          <el-collapse-transition>
            <div v-if="showKpi" class="kpiTable">
              <el-row v-for="(item, index) in kpiList" :key="index" :class="{ substitution: item.state === 3, emphasis: item.state === 2, beReplaced: item.state === 4 }">
                <div>{{ index + 1 }}</div>
                <div>
                  <el-tooltip effect="dark" popper-class="stateTooltip" :content="item.content" placement="top">
                    <span class="textHidden">{{ item.content }}</span>
                  </el-tooltip>
                </div>
                <div>{{ item.weight }}</div>
                <div>
                  <el-tooltip effect="dark" popper-class="stateTooltip" :content="item.standard" placement="top">
                    <span class="textHidden">{{ item.standard }}</span>
                  </el-tooltip>
                </div>
                <div>
                  <el-tooltip effect="dark" popper-class="stateTooltip" :content="item.analyse" placement="top">
                    <span class="textHidden">{{ item.analyse }}</span>
                  </el-tooltip>
                </div>
                <div>
                  <img v-if="item.state === 1" src="@/assets/performance/normal.png" alt="" />
                  <img v-if="item.state === 2" src="@/assets/performance/emphasis.png" alt="" />
                  <img v-if="item.state === 4" src="@/assets/performance/substitution.png" alt="" />
                  <span>{{ item.state === 1 ? '正常' : item.state === 2 ? '重点' : item.state === 3 ? '置换' : item.state === 4 ? '被置换' : '---' }}</span>
                </div>
                <div>{{ item.selfScore ? item.selfScore : '---' }}</div>
                <div>{{ item.seniorScore ? item.seniorScore : '---' }}</div>
                <div>{{ item.togetherScore ? item.togetherScore : '---' }}</div>
                <div>{{ item.lastScore ? item.lastScore : '---' }}</div>
                <div>
                  <template v-if="$route.params.type === 'score'">
                    <el-tooltip effect="dark" :enterable="false" content="上级评分" placement="top" popper-class="stateTooltip">
                      <span class="scoreButton" @click="score(item, index, 0)"></span>
                    </el-tooltip>
                    <el-tooltip effect="dark" :enterable="false" content="联查评分" placement="top" popper-class="stateTooltip">
                      <span class="scoreButton2" @click="score(item, index, 1)"></span>
                    </el-tooltip>
                  </template>
                  <el-tooltip effect="dark" :enterable="false" content="详情" placement="top" popper-class="stateTooltip">
                    <span class="detailsButton" @click="details(item, index)"></span>
                  </el-tooltip>
                  <template v-if="!isDisabled">
                    <el-tooltip effect="dark" :enterable="false" content="编辑" placement="top" popper-class="stateTooltip">
                      <span class="editButton" @click="edit(item, index)"></span>
                    </el-tooltip>
                    <el-tooltip effect="dark" :enterable="false" content="删除" placement="top" popper-class="stateTooltip">
                      <span class="delButton" @click="del(item, index)"></span>
                    </el-tooltip>
                  </template>
                </div>
              </el-row>
            </div>
          </el-collapse-transition>
          <!-- 月度关键业绩 --over -->

          <!-- 团队协作指考核标 -->
          <el-row class="team" type="flex" justify="space-between" align="middle">
            <div>团队协作考核指标：5%</div>
            <div>
              <img v-if="teamList.length" src="@/assets/performance/arrow.png" alt="" class="arrow" @click="showTeam = !showTeam" />
              <img v-else src="@/assets/performance/arrow_disable.png" alt="" class="arrow" />
              <i v-if="!isDisabled" class="add el-icon-circle-plus" @click="add(2)"></i>
            </div>
          </el-row>
          <el-collapse-transition>
            <div v-if="showTeam" class="kpiTable">
              <el-row v-for="(item, index) in teamList" :key="index" :class="{ substitution: item.state === 3, emphasis: item.state === 2, beReplaced: item.state === 4 }">
                <div>{{ index + 1 }}</div>
                <div>
                  <el-tooltip effect="dark" popper-class="stateTooltip" :content="item.content" placement="top">
                    <span class="textHidden">{{ item.content }}</span>
                  </el-tooltip>
                </div>
                <div>{{ item.weight }}</div>
                <div>
                  <el-tooltip effect="dark" popper-class="stateTooltip" :content="item.standard" placement="top">
                    <span class="textHidden">{{ item.standard }}</span>
                  </el-tooltip>
                </div>
                <div>
                  <el-tooltip effect="dark" popper-class="stateTooltip" :content="item.analyse" placement="top">
                    <span class="textHidden">{{ item.analyse }}</span>
                  </el-tooltip>
                </div>
                <div>
                  <img v-if="item.state === 1" src="@/assets/performance/normal.png" alt="" />
                  <img v-if="item.state === 2" src="@/assets/performance/emphasis.png" alt="" />
                  <img v-if="item.state === 4" src="@/assets/performance/substitution.png" alt="" />
                  <span>{{ item.state === 1 ? '正常' : item.state === 2 ? '重点' : item.state === 3 ? '置换' : item.state === 4 ? '被置换' : '---' }}</span>
                </div>
                <div>{{ item.selfScore ? item.selfScore : '---' }}</div>
                <div>{{ item.seniorScore ? item.seniorScore : '---' }}</div>
                <div>{{ item.togetherScore ? item.togetherScore : '---' }}</div>
                <div>{{ item.lastScore ? item.lastScore : '---' }}</div>
                <div>
                  <template v-if="$route.params.type === 'score'">
                    <el-tooltip effect="dark" :enterable="false" content="上级评分" placement="top" popper-class="stateTooltip">
                      <span class="scoreButton" @click="score(item, index, 0)"></span>
                    </el-tooltip>
                    <el-tooltip effect="dark" :enterable="false" content="联查评分" placement="top" popper-class="stateTooltip">
                      <span class="scoreButton2" @click="score(item, index, 1)"></span>
                    </el-tooltip>
                  </template>
                  <el-tooltip effect="dark" :enterable="false" content="详情" placement="top" popper-class="stateTooltip">
                    <span class="detailsButton" @click="details(item, index)"></span>
                  </el-tooltip>
                  <template v-if="!isDisabled">
                    <el-tooltip effect="dark" :enterable="false" content="编辑" placement="top" popper-class="stateTooltip">
                      <span class="editButton" @click="edit(item, index)"></span>
                    </el-tooltip>
                    <el-tooltip effect="dark" :enterable="false" content="删除" placement="top" popper-class="stateTooltip">
                      <span class="delButton" @click="del(item, index)"></span>
                    </el-tooltip>
                  </template>
                </div>
              </el-row>
            </div>
          </el-collapse-transition>
          <!-- 团队协作指考核标  --over -->

          <!-- 工作态度考核指标 -->
          <el-row class="jobManner" type="flex" justify="space-between" align="middle">
            <div>工作态度考核指标：10%</div>
            <div>
              <img v-if="jobMannerList.length" src="@/assets/performance/arrow.png" alt="" class="arrow" @click="showJobManner = !showJobManner" />
              <img v-else src="@/assets/performance/arrow_disable.png" alt="" class="arrow" />
              <i v-if="!isDisabled" class="add el-icon-circle-plus" @click="add(3)"></i>
            </div>
          </el-row>
          <el-collapse-transition>
            <div v-if="showJobManner" class="kpiTable">
              <el-row v-for="(item, index) in jobMannerList" :key="index" :class="{ substitution: item.state === 3, emphasis: item.state === 2, beReplaced: item.state === 4 }">
                <div>{{ index + 1 }}</div>
                <div>
                  <el-tooltip effect="dark" popper-class="stateTooltip" :content="item.content" placement="top">
                    <span class="textHidden">{{ item.content }}</span>
                  </el-tooltip>
                </div>
                <div>{{ item.weight }}</div>
                <div>
                  <el-tooltip effect="dark" popper-class="stateTooltip" :content="item.standard" placement="top">
                    <span class="textHidden">{{ item.standard }}</span>
                  </el-tooltip>
                </div>
                <div>
                  <el-tooltip effect="dark" popper-class="stateTooltip" :content="item.analyse" placement="top">
                    <span class="textHidden">{{ item.analyse }}</span>
                  </el-tooltip>
                </div>
                <div>
                  <img v-if="item.state === 1" src="@/assets/performance/normal.png" alt="" />
                  <img v-if="item.state === 2" src="@/assets/performance/emphasis.png" alt="" />
                  <img v-if="item.state === 4" src="@/assets/performance/substitution.png" alt="" />
                  <span>{{ item.state === 1 ? '正常' : item.state === 2 ? '重点' : item.state === 3 ? '置换' : item.state === 4 ? '被置换' : '---' }}</span>
                </div>
                <div>{{ item.selfScore ? item.selfScore : '---' }}</div>
                <div>{{ item.seniorScore ? item.seniorScore : '---' }}</div>
                <div>{{ item.togetherScore ? item.togetherScore : '---' }}</div>
                <div>{{ item.lastScore ? item.lastScore : '---' }}</div>
                <div>
                  <template v-if="$route.params.type === 'score'">
                    <el-tooltip effect="dark" :enterable="false" content="上级评分" placement="top" popper-class="stateTooltip">
                      <span class="scoreButton" @click="score(item, index, 0)"></span>
                    </el-tooltip>
                    <el-tooltip effect="dark" :enterable="false" content="联查评分" placement="top" popper-class="stateTooltip">
                      <span class="scoreButton2" @click="score(item, index, 1)"></span>
                    </el-tooltip>
                  </template>
                  <el-tooltip effect="dark" :enterable="false" content="详情" placement="top" popper-class="stateTooltip">
                    <span class="detailsButton" @click="details(item, index)"></span>
                  </el-tooltip>
                  <template v-if="!isDisabled">
                    <el-tooltip effect="dark" :enterable="false" content="编辑" placement="top" popper-class="stateTooltip">
                      <span class="editButton" @click="edit(item, index)"></span>
                    </el-tooltip>
                    <el-tooltip effect="dark" :enterable="false" content="删除" placement="top" popper-class="stateTooltip">
                      <span class="delButton" @click="del(item, index)"></span>
                    </el-tooltip>
                  </template>
                </div>
              </el-row>
            </div>
          </el-collapse-transition>
          <!-- 工作态度考核指标  --over -->
          <!-- 特殊情况加分 -->
          <el-row class="special" type="flex" justify="space-between" align="middle">
            <div>特殊情况加分</div>
            <div>
              <img v-if="specialList.length" src="@/assets/performance/arrow.png" alt="" class="arrow" @click="showSpecial = !showSpecial" />
              <img v-else src="@/assets/performance/arrow_disable.png" alt="" class="arrow" />
              <i v-if="!isDisabled" class="add el-icon-circle-plus" @click="add(4)"></i>
            </div>
          </el-row>
          <el-collapse-transition>
            <div v-if="showSpecial" class="kpiTable">
              <el-row v-for="(item, index) in specialList" :key="index" :class="{ substitution: item.state === 3, emphasis: item.state === 2, beReplaced: item.state === 4 }">
                <div>{{ index + 1 }}</div>
                <div>
                  <el-tooltip effect="dark" popper-class="stateTooltip" :content="item.content" placement="top">
                    <span class="textHidden">{{ item.content }}</span>
                  </el-tooltip>
                </div>
                <div>{{ item.weight }}</div>
                <div>
                  <el-tooltip effect="dark" popper-class="stateTooltip" :content="item.standard" placement="top">
                    <span class="textHidden">{{ item.standard }}</span>
                  </el-tooltip>
                </div>
                <div>
                  <el-tooltip effect="dark" popper-class="stateTooltip" :content="item.analyse" placement="top">
                    <span class="textHidden">{{ item.analyse }}</span>
                  </el-tooltip>
                </div>
                <div>
                  <img v-if="item.state === 1" src="@/assets/performance/normal.png" alt="" />
                  <img v-if="item.state === 2" src="@/assets/performance/emphasis.png" alt="" />
                  <img v-if="item.state === 4" src="@/assets/performance/substitution.png" alt="" />
                  <span>{{ item.state === 1 ? '正常' : item.state === 2 ? '重点' : item.state === 3 ? '置换' : item.state === 4 ? '被置换' : '---' }}</span>
                </div>
                <div>{{ item.selfScore ? item.selfScore : '---' }}</div>
                <div>{{ item.seniorScore ? item.seniorScore : '---' }}</div>
                <div>{{ item.togetherScore ? item.togetherScore : '---' }}</div>
                <div>{{ item.lastScore ? item.lastScore : '---' }}</div>
                <div>
                  <template v-if="$route.params.type === 'score'">
                    <el-tooltip effect="dark" :enterable="false" content="上级评分" placement="top" popper-class="stateTooltip">
                      <span class="scoreButton" @click="score(item, index, 0)"></span>
                    </el-tooltip>
                    <el-tooltip effect="dark" :enterable="false" content="联查评分" placement="top" popper-class="stateTooltip">
                      <span class="scoreButton2" @click="score(item, index, 1)"></span>
                    </el-tooltip>
                  </template>
                  <el-tooltip effect="dark" :enterable="false" content="详情" placement="top" popper-class="stateTooltip">
                    <span class="detailsButton" @click="details(item, index)"></span>
                  </el-tooltip>
                  <template v-if="!isDisabled">
                    <el-tooltip effect="dark" :enterable="false" content="编辑" placement="top" popper-class="stateTooltip">
                      <span class="editButton" @click="edit(item, index)"></span>
                    </el-tooltip>
                    <el-tooltip effect="dark" :enterable="false" content="删除" placement="top" popper-class="stateTooltip">
                      <span class="delButton" @click="del(item, index)"></span>
                    </el-tooltip>
                  </template>
                </div>
              </el-row>
            </div>
          </el-collapse-transition>
          <!-- 特殊情况加分  --over -->
          <!-- 合计 -->
          <el-row class="table_header table_footer">
            <div>合计</div>
            <div></div>
            <div>{{ sumWeight }}</div>
            <div></div>
            <div>{{ dataInfo.totalSelfScore ? dataInfo.totalSelfScore : '---' }}</div>
            <div>{{ dataInfo.totalSuperiorScore ? dataInfo.totalSuperiorScore : '---' }}</div>
            <div>{{ dataInfo.totalUnionScore ? dataInfo.totalUnionScore : '---' }}</div>
            <div>{{ dataInfo.totalLastScore ? dataInfo.totalLastScore : '---' }}</div>
            <div></div>
          </el-row>
          <!-- 合计  --over -->
        </div>
      </div>
    </div>
    <!-- 新增目标和任务 -->
    <el-dialog :visible.sync="addDialog" custom-class="addDialog" top="8vh">
      <template v-slot:title>
        <div class="header">
          <span>{{ addDialogTitle }}</span>
          <span>{{ addTitle }} </span>
        </div>
      </template>
      <div>
        <el-form ref="addInfo" :model="addInfo" label-width="80px">
          <el-form-item label-width="148px">
            <template v-slot:label>
              <div class="label1">
                <el-tooltip
                  effect="dark"
                  popper-class="stateTooltip"
                  content="清晰描述工作任务目标，避免超过30分以上的工作计划内容，如果只有一个项目开发，将项目的大模块拆分出来，重点工作填充绿色单元格，临时/置换工作用红色文字。"
                  placement="top"
                >
                  <img src="@/assets/performance/state3.png" alt="" />
                </el-tooltip>
                <span>目标和任务：</span>
              </div>
            </template>
            <el-input v-model="addInfo.content" placeholder="请输入目标和任务" type="textarea" resize="none" maxlength="100"></el-input>
          </el-form-item>
          <el-form-item label-width="148px">
            <template v-slot:label>
              <div class="label2">
                <div>
                  <el-tooltip
                    effect="dark"
                    popper-class="stateTooltip"
                    content="这里总分数不要超过100分，如果有临时工作置换，将临时工作分数写为*x分*，这样不会计算分数，要保证置换的工作与原计划工作分数一致"
                    placement="top"
                  >
                    <img src="@/assets/performance/state3.png" alt="" />
                  </el-tooltip>
                  <span>权 <span v-html="'&nbsp;&nbsp;'"></span> 重：</span>
                </div>
                <div>(工作量或重要性占比)</div>
              </div>
            </template>
            <el-input-number v-if="addInfo.type === 1" v-model="addInfo.weight" :min="0" :max="85 - kpiWeight + editWeight"></el-input-number>
            <el-input-number v-if="addInfo.type === 2" v-model="addInfo.weight" :min="0" :max="5 - teamWeight + editWeight"></el-input-number>
            <el-input-number v-if="addInfo.type === 3" v-model="addInfo.weight" :min="0" :max="10 - jobMannerWeight + editWeight"></el-input-number>
            <el-input-number v-if="addInfo.type === 4" v-model="addInfo.weight" :min="0" :max="100"></el-input-number>
          </el-form-item>
          <el-form-item label-width="148px" class="label3">
            <template v-slot:label>
              <div>
                <el-tooltip effect="dark" popper-class="stateTooltip" content="考核标准写清楚，通过什么标准可以对前面的目标和任务进行检查，例如完成xx模块工作，百分比多少" placement="top">
                  <img src="@/assets/performance/state3.png" alt="" />
                </el-tooltip>
                <span>考核标准：</span>
              </div>
              <div>(要有完成准确的节点)</div>
            </template>
            <el-input v-model="addInfo.standard" placeholder="请输入考核标准" type="textarea" resize="none" maxlength="100"></el-input>
          </el-form-item>
          <el-form-item label-width="148px" class="label4">
            <template v-slot:label>
              <div>
                <el-tooltip effect="dark" popper-class="stateTooltip" content="如有扣分项目说明扣分原因，满分代表工作内容、进度、细节无瑕疵" placement="top">
                  <img src="@/assets/performance/state3.png" alt="" />
                </el-tooltip>
                <span>绩效分析：</span>
              </div>
              <div>(完成情况及扣分原因)</div>
            </template>
            <el-input v-model="addInfo.analyse" placeholder="请输入绩效分析" type="textarea" resize="none" maxlength="100"></el-input>
          </el-form-item>
          <el-form-item label-width="148px" label="任务状态：" class="state">
            <el-select v-model="addInfo.state" placeholder="任务状态">
              <template v-slot:prefix>
                <img v-if="addInfo.state === 1" src="@/assets/performance/normal.png" alt="" />
                <img v-if="addInfo.state === 2" src="@/assets/performance/emphasis.png" alt="" />
                <img v-if="addInfo.state === 4" src="@/assets/performance/substitution.png" alt="" />
              </template>
              <el-option label="正常" :value="1"> </el-option>
              <el-option label="重点" :value="2"> </el-option>
              <el-option label="置换" :value="3"> </el-option>
              <el-option label="被置换" :value="4"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label-width="148px" label="输出物：" class="upload">
            <el-upload class="upload-demo" :action="action" :headers="header" :before-upload="beforeUpload" :on-success="uploadSuccess" multiple :file-list="fileList" :show-file-list="false">
              <img src="@/assets/performance/upload.png" alt="" />
            </el-upload>
            <div class="uploadList">
              <div v-for="(item, index) in addInfo.fileReqs" :key="index">
                <span>{{ item.fileName }}</span>
                <span>{{ item.fileSize }}KB</span>
                <span class="download" @click="download(item)">下载</span>
                <span class="remove" @click="remove(item)">删除</span>
              </div>
              <el-input v-model="addInfo.output" placeholder="请输入输出物" type="textarea" resize="none"></el-input>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <template v-slot:footer>
        <div class="footer_left">
          <div>
            <span>自评分：</span>
            <span>(20%)</span>
          </div>
          <div>
            <el-input-number v-model="addInfo.selfScore" controls-position="right" :min="0" :max="addInfo.weight"></el-input-number>
            <!-- <el-input v-model="addInfo.selfScore" type="number" :max="addInfo.weight" :min="0" maxlength="2"></el-input> -->
          </div>
        </div>
        <div class="footer_right">
          <div class="confirm" @click="confirm_add"></div>
          <div class="cancel" @click="cancel_add"></div>
        </div>
      </template>
    </el-dialog>
    <!-- 删除弹窗 -->
    <el-dialog :visible.sync="delDialog" width="413px" top="30vh" custom-class="delDialog">
      <template v-slot:title>
        <img src="@/assets/library/hint.png" alt="" class="hint" />
        <span class="hintText">提示</span>
      </template>
      <div class="delText">确定删除该资源吗？</div>
      <div class="operate">
        <span class="closeButton" @click="delDialog = false">取 消</span>
        <span class="confirmButton" @click="confirmDel">确 定</span>
      </div>
    </el-dialog>
    <!-- 详情弹窗 -->
    <el-dialog :visible.sync="detailsDialog" width="910px" custom-class="detailsDialog">
      <template v-slot:title>
        <div class="header">
          <span>详 情</span>
          <span>{{ detailsTitle }} </span>
        </div>
      </template>
      <el-row type="flex" class="scoreRow">
        <div class="score">
          <span>自评分</span>
          <span>{{ detailsInfo.selfScore ? detailsInfo.selfScore : '---' }}</span>
        </div>
        <div class="score">
          <span>上级评分</span>
          <span>{{ detailsInfo.seniorScore ? detailsInfo.seniorScore : '---' }}</span>
        </div>
        <div class="score">
          <span>联查评分</span>
          <span>{{ detailsInfo.togetherScore ? detailsInfo.togetherScore : '---' }}</span>
        </div>
        <div class="score">
          <span>最终得分</span>
          <span>{{ detailsInfo.lastScore ? detailsInfo.lastScore : '---' }}</span>
        </div>
        <div class="describe">自评分（20%），上级评分（加权处理 50%），根据联查小组的检查进行分数复 核，联查的时候已经出来基本分值30%</div>
      </el-row>
      <el-row class="titleRow">
        <div>
          <el-tooltip
            effect="dark"
            popper-class="stateTooltip"
            content="清晰描述工作任务目标，避免超过30分以上的工作计划内容，如果只有一个项目开发，将项目的大模块拆分出来，重点工作填充绿色单元格，临时/置换工作用红色文字。"
            placement="top"
          >
            <img src="@/assets/performance/state3.png" alt="" />
          </el-tooltip>
          <span>目标和任务:</span>
        </div>
        <div>{{ detailsInfo.content }}</div>
      </el-row>
      <el-row class="describeRow">
        <div>
          <div>
            <el-tooltip
              effect="dark"
              popper-class="stateTooltip"
              content="这里总分数不要超过100分，如果有临时工作置换，将临时工作分数写为*x分*，这样不会计算分数，要保证置换的工作与原计划工作分数一致"
              placement="top"
            >
              <img src="@/assets/performance/state3.png" alt="" />
            </el-tooltip>
            <span>权 <span v-html="'&emsp;'"></span> 重:</span>
          </div>
          <div class="titleDescribe">(工作量或重要性占比)</div>
        </div>
        <div>{{ detailsInfo.weight }}</div>
      </el-row>
      <el-row class="describeRow">
        <div>
          <div>
            <el-tooltip effect="dark" popper-class="stateTooltip" content="考核标准写清楚，通过什么标准可以对前面的目标和任务进行检查，例如完成xx模块工作，百分比多少" placement="top">
              <img src="@/assets/performance/state3.png" alt="" />
            </el-tooltip>
            <span>考核标准:</span>
          </div>
          <div class="titleDescribe">(要有完成准确的节点)</div>
        </div>
        <div>{{ detailsInfo.standard }}</div>
      </el-row>
      <el-row class="describeRow">
        <div>
          <div>
            <el-tooltip effect="dark" popper-class="stateTooltip" content="如有扣分项目说明扣分原因，满分代表工作内容、进度、细节无瑕疵" placement="top">
              <img src="@/assets/performance/state3.png" alt="" />
            </el-tooltip>
            <span>绩效分析:</span>
          </div>
          <div class="titleDescribe">(完成情况及扣分原因)</div>
        </div>
        <div>{{ detailsInfo.analyse }}</div>
      </el-row>
      <el-row class="titleRow taskRow">
        <div>
          <span>任务状态:</span>
        </div>
        <div>
          <img v-if="detailsInfo.state === 1" src="@/assets/performance/normal.png" alt="" />
          <img v-if="detailsInfo.state === 2" src="@/assets/performance/emphasis.png" alt="" />
          <img v-if="detailsInfo.state === 4" src="@/assets/performance/substitution.png" alt="" />
          <span>{{ detailsInfo.state === 1 ? '正常' : detailsInfo.state === 2 ? '重点' : detailsInfo.state === 3 ? '置换' : detailsInfo.state === 4 ? '被置换' : '---' }}</span>
        </div>
      </el-row>
      <el-row class="titleRow outputRow">
        <div>输出物:</div>
        <div>
          <template v-if="detailsInfo.fileReqs && detailsInfo.fileReqs.length">
            <div v-for="(item, index) in detailsInfo.fileReqs" :key="index" class="files">
              <span>{{ item.fileName }}</span>
              <span>{{ item.fileSize }}KB</span>
              <span @click="download(item)">下载</span>
            </div>
          </template>
          <template v-else>
            <div class="output">{{ detailsInfo.output }}</div>
          </template>
        </div>
      </el-row>
    </el-dialog>
    <!-- 评分弹窗 -->
    <scoreDialog ref="scoreDialog" :show-dialog.sync="scoreDialog" @scoreOver="scoreOver" />
    <!-- 退回弹窗 -->
    <sendBack ref="sendBack" :show-dialog.sync="sendBackDialog" />
    <!-- 被退回的提示 -->
    <div v-if="dataInfo.state === 3" class="sendBackMessage">
      <img src="@/assets/performance/sendBack_icon.png" alt="" />
      <span>当前已被"上级"退回</span>
      <span>{{ dataInfo.returnRemark }}</span>
    </div>
    <!-- 修订记录 -->
    <el-dialog title="修订记录" :visible.sync="reviseRecordDialog" width="564px" custom-class="reviseRecordDialog">
      <template v-if="logList && logList.length">
        <div v-for="(item, index) in logList" :key="index" class="record">
          <span>{{ item.createTime }}</span>
          <span>{{ item.realName }}</span>
          <span>{{ item.type === 1 ? '新增' : item.type === 2 ? '删除' : '修改' }}</span>
        </div>
      </template>
      <template v-else>
        <div class="empty">
          <img src="@/assets/performance/empty.png" alt="" />
          <span>暂无数据</span>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { downUrl } from '@/utils'
import { tAchievementAdd, tAchievementDetail, tAchievementUpdate } from '@/api/tAchievement'
import scoreDialog from '@/views/tAchievement/scoreDialog'
import sendBack from '@/views/tAchievement/sendBack'
// 单组件引用
import print from 'vue-print-nb'

export default {
  name: '',
  components: {
    scoreDialog,
    sendBack
  },
  // 在自定义指令中注册
  directives: {
    print
  },
  data() {
    return {
      loading: false,
      dataInfo: {
        time: null, // 考核时间
        state: null, // 状态;1 待评分 2 已评分 3 已退回
        totalLastScore: 0, // 最终总分值
        totalSuperiorScore: 0, // 上级总评分
        totalUnionScore: 0, // 联查总分值
        totalSelfScore: 0, // 自评分总分值
        detailReqs: [] // 绩效详情
      },
      addDialog: false,
      addDialogTitle: '添加目标和任务',
      addInfo: {
        content: '', // 目标和任务
        weight: null, // 权重
        type: null, // 类型 1 月度关键业绩 2 团队合作指标 3 工作态度指标 4 特殊情况加分
        standard: null, // 考核标准
        output: null, // 	输出物说明
        analyse: null, // 绩效分析(完成情况及扣分原因)
        selfScore: 0, // 自评分
        seniorScore: 0, // 上级评分
        togetherScore: 0, // 联查评分
        lastScore: 0, // 最终分数
        state: null, // 任务状态 1正常 2 重点 3 置换 4 被置换
        fileReqs: []
      },
      editWeight: 0, // 被编辑的权重
      showKpi: false, // 是否显示月度关键业绩列表
      kpiList: [], // 月度关键业绩
      showTeam: false, // 是否显示团队协作考核指标列表
      teamList: [], // 团队协作考核指标列表
      showJobManner: false, // 是否显示工作态度考核指标列表
      jobMannerList: [], // 工作态度考核指标列表
      showSpecial: false, // 是否显示特殊情况列表
      specialList: [], // 特殊情况列表
      action: window.config.VUE_APP_BASE_API + '/system/upload/file',
      header: {
        Authorization: null
      },
      fileList: [],
      openIndex: 0, // 被打开的索引
      delInfo: {},
      delDialog: false, // 删除弹窗
      detailsInfo: {}, // 详情信息
      detailsDialog: false, // 详情弹窗
      scoreDialog: false, // 评分弹窗
      kpiWeight: 0, // 月度业绩总权重
      teamWeight: 0, // 团队协作总权重
      jobMannerWeight: 0, // 工作态度总权重
      specialWeight: 0, // 特殊情况总权重
      sumWeight: 0, // 总权重
      sendBackDialog: false, // 退回弹窗
      reviseRecordDialog: false, // 修订记录弹窗
      logList: [], // 修订记录
      print: {
        id: 'printArea'
      }
    }
  },
  computed: {
    ...mapGetters(['avatar', 'realName', 'sex', 'jobName', 'organizationName']),
    pageType() {
      return this.$route.params.type === 'add' ? '新增绩效' : this.$route.params.type === 'edit' ? '编辑绩效' : this.$route.params.type === 'score' ? '绩效评分' : '绩效详情'
    },

    addTitle() {
      return this.addInfo.type === 1 ? '（月度关键业绩85%）' : this.addInfo.type === 2 ? '（团队合作考核指标5%）' : this.addInfo.type === 3 ? '（工作态度考核指标5%）' : '（特殊情况加分）'
    },
    detailsTitle() {
      return this.detailsInfo.type === 1 ? '（月度关键业绩85%）' : this.detailsInfo.type === 2 ? '（团队合作考核指标5%）' : this.detailsInfo.type === 3 ? '（工作态度考核指标5%）' : '（特殊情况加分）'
    },
    // 计算个人任务
    personTask() {
      const kpiNum = this.kpiList.length
      const teamNum = this.teamList.length
      const jobMannerNum = this.jobMannerList.length
      const specialNum = this.specialList.length
      return kpiNum + teamNum + jobMannerNum + specialNum
    },
    // 判断一些按钮的显示与禁用
    isDisabled() {
      return this.$route.params.type === 'score' || this.$route.params.type === 'details'
    }
  },
  created() {
    if (this.$route.params.type === 'edit' || this.$route.params.type === 'score' || this.$route.params.type === 'details') {
      this.getDetails()
      this.showKpi = true
      this.showTeam = true
      this.showJobManner = true
      this.showSpecial = true
    }
  },
  methods: {
    add(type) {
      // 类型 1 月度关键业绩 2 团队合作指标 3 工作态度指标 4 特殊情况加分
      this.addInfo.type = type
      this.addDialog = true
      this.addDialogTitle = '添加目标和任务'
      this.editWeight = 0
    },

    beforeUpload(file) {
      this.header.Authorization = `Bearer ${this.$store.getters.token}`
    },
    uploadSuccess(response, file, fileList) {
      if (response.code !== 200) {
        return this.$message.error(response.message)
      }
      this.fileList = fileList
      this.addInfo.fileReqs = this.fileList.map((item) => {
        return {
          fileName: item.name,
          fileSize: parseInt(item.size / 1024),
          fileUrl: item.response.data[0],
          belongType: 12
        }
      })
    },
    download(data) {
      downUrl(data.fileName, data.fileUrl)
    },
    remove(data) {
      this.fileList = this.fileList.filter((item) => item.name !== data.fileName)
      this.addInfo.fileReqs = this.fileList.filter((item) => item.fileName !== data.fileName)
    },
    // 新增目标和任务确定
    confirm_add() {
      // 必填选项的校验
      if (!this.addInfo.content || !this.addInfo.standard) {
        return this.$message.warning('目标和任务、考核标准为必填项')
      }
      if (this.addInfo.type === 1) {
        this.addKpi()
      } else if (this.addInfo.type === 2) {
        this.addTeam()
      } else if (this.addInfo.type === 3) {
        this.addJobManner()
      } else {
        this.addSpecial()
      }
      this.computeSumWeight()
      this.computeTotalSelfScore()
    },
    cancel_add() {
      this.addInfo = {
        content: null, // 目标和任务
        weight: null, // 权重
        type: null, // 类型 1 月度关键业绩 2 团队合作指标 3 工作态度指标 4 特殊情况加分
        standard: null, // 考核标准
        output: null, // 	输出物说明
        analyse: null, // 绩效分析(完成情况及扣分原因)
        selfScore: 0, // 自评分
        seniorScore: 0, // 上级评分
        togetherScore: 0, // 联查评分
        lastScore: 0, // 最终分数
        state: null, // 任务状态 1正常 2 重点 3 置换 4 被置换
        fileReqs: []
      }
      this.addDialog = false
    },
    // 新增月度业绩
    addKpi() {
      if (this.addDialogTitle === '修改目标和任务') {
        if (this.judgeKpiWeight(this.addInfo.weight)) {
          return this.$message.warning('月度绩效总权重不能超过85')
        }
        this.kpiList.forEach((item, index) => {
          if (index === this.openIndex) {
            for (const key in item) {
              item[key] = this.addInfo[key]
            }
          }
        })
        this.addDialog = false
        this.showKpi = true // 展开
        this.cancel_add()
        this.$message.success('修改成功')
      } else {
        if (this.addInfo.type === 1) {
          if (this.kpiWeight + this.addInfo.weight > 85) {
            return this.$message.warning('月度业绩总权重不能超过85')
          }
          this.kpiList.push(this.addInfo)
          this.addDialog = false
          this.showKpi = true // 展开
          this.cancel_add()
          this.$message.success('新增成功')
        }
      }
    },
    // 新增团队协作
    addTeam() {
      if (this.addDialogTitle === '修改目标和任务') {
        if (this.judgeTeamWeight(this.addInfo.weight)) {
          return this.$message.warning('团队协作总权重不能超过5')
        }
        this.teamList.forEach((item, index) => {
          if (index === this.openIndex) {
            for (const key in item) {
              item[key] = this.addInfo[key]
            }
          }
        })
        this.addDialog = false
        this.showTeam = true // 展开
        this.cancel_add()
        this.$message.success('修改成功')
      } else {
        if (this.teamWeight + this.addInfo.weight > 5) {
          return this.$message.warning('团队协作总权重不能超过5')
        }
        this.teamList.push(this.addInfo)
        this.addDialog = false
        this.showTeam = true // 展开
        this.cancel_add()
        this.$message.success('新增成功')
      }
    },
    // 新增工作态度考核指标
    addJobManner() {
      if (this.addDialogTitle === '修改目标和任务') {
        if (this.judgeJobMannerWeight(this.addInfo.weight)) {
          return this.$message.warning('工作态度总权重不能超过10')
        }
        this.jobMannerList.forEach((item, index) => {
          if (index === this.openIndex) {
            for (const key in item) {
              item[key] = this.addInfo[key]
            }
          }
        })
        this.addDialog = false
        this.showJobManner = true // 展开
        this.cancel_add()
        this.$message.success('修改成功')
      } else {
        if (this.jobMannerWeight + this.addInfo.weight > 10) {
          return this.$message.warning('工作态度总权重不能超过10')
        }
        this.jobMannerList.push(this.addInfo)
        this.addDialog = false
        this.showJobManner = true // 展开
        this.cancel_add()
        this.$message.success('新增成功')
      }
    },
    addSpecial() {
      if (this.addDialogTitle === '修改目标和任务') {
        this.specialList.forEach((item, index) => {
          if (index === this.openIndex) {
            for (const key in item) {
              item[key] = this.addInfo[key]
            }
          }
        })
        this.addDialog = false
        this.showSpecial = true // 展开
        this.cancel_add()
        this.$message.success('修改成功')
      } else {
        this.specialList.push(this.addInfo)
        this.addDialog = false
        this.showSpecial = true // 展开
        this.cancel_add()
        this.$message.success('新增成功')
      }
    },
    details(item) {
      this.detailsInfo = { ...item }
      this.detailsDialog = true
    },
    edit(item, index) {
      this.openIndex = index
      this.addDialog = true
      this.addDialogTitle = '修改目标和任务'
      this.addInfo = { ...item }
      this.editWeight = item.weight
    },
    del(item, index) {
      this.delInfo = { ...item }
      this.openIndex = index
      this.delDialog = true
    },
    confirmDel() {
      if (this.delInfo.type === 1) {
        this.kpiList = this.kpiList.filter((item, index) => index !== this.openIndex)
      } else if (this.delInfo.type === 2) {
        this.teamList = this.teamList.filter((item, index) => index !== this.openIndex)
      } else if (this.delInfo.type === 3) {
        this.jobMannerList = this.jobMannerList.filter((item, index) => index !== this.openIndex)
      } else if (this.delInfo.type === 4) {
        this.specialList = this.specialList.filter((item, index) => index !== this.openIndex)
      }
      this.delDialog = false
      this.$message.success('删除成功')
      this.computeSumWeight()
      this.computeTotalSelfScore()
    },
    // 判断月度绩效权重
    judgeKpiWeight(data) {
      let kpiWeight = 0
      this.kpiList.forEach((item, index) => {
        if (this.openIndex !== index) {
          kpiWeight += parseInt(item.weight)
        }
      })
      kpiWeight += data
      return kpiWeight > 85
    },
    // 判断团队协作权重
    judgeTeamWeight(data) {
      let teamWeight = 0
      this.teamList.forEach((item, index) => {
        if (this.openIndex !== index) {
          teamWeight += parseInt(item.weight)
        }
      })
      teamWeight += data
      return teamWeight > 5
    },
    // 判断工作态度考核指标权重
    judgeJobMannerWeight(data) {
      let jobMannerWeight = 0
      this.jobMannerList.forEach((item, index) => {
        if (this.openIndex !== index) {
          jobMannerWeight += parseInt(item.weight)
        }
      })
      jobMannerWeight += data
      return jobMannerWeight > 10
    },

    // 计算总权重
    computeSumWeight() {
      let kpiWeight = 0
      let teamWeight = 0
      let jobMannerWeight = 0
      let specialWeight = 0
      this.kpiList.forEach((item) => {
        if (item.state !== 3) {
          kpiWeight += parseInt(item.weight)
        }
      })
      this.teamList.forEach((item) => {
        if (item.state !== 3) {
          teamWeight += parseInt(item.weight)
        }
      })
      this.jobMannerList.forEach((item) => {
        if (item.state !== 3) {
          jobMannerWeight += parseInt(item.weight)
        }
      })
      this.specialList.forEach((item) => {
        if (item.state !== 3) {
          specialWeight += parseInt(item.weight)
        }
      })
      this.kpiWeight = kpiWeight
      this.teamWeight = teamWeight
      this.jobMannerWeight = jobMannerWeight
      this.specialWeight = specialWeight
      this.sumWeight = kpiWeight + teamWeight + jobMannerWeight + specialWeight
    },
    // 计算自总评分
    computeTotalSelfScore() {
      let kpiSelfScore = 0
      let teamSelfScore = 0
      let jobMannerSelfScore = 0
      let specialSelfScore = 0
      this.kpiList.forEach((item) => {
        if (item.state !== 3) {
          kpiSelfScore += parseInt(item.selfScore)
        }
      })
      this.teamList.forEach((item) => {
        if (item.state !== 3) {
          teamSelfScore += parseInt(item.selfScore)
        }
      })
      this.jobMannerList.forEach((item) => {
        if (item.state !== 3) {
          jobMannerSelfScore += parseInt(item.selfScore)
        }
      })
      this.specialList.forEach((item) => {
        if (item.state !== 3) {
          specialSelfScore += parseInt(item.selfScore)
        }
      })
      this.dataInfo.totalSelfScore = kpiSelfScore + teamSelfScore + jobMannerSelfScore + specialSelfScore
    },
    // 计算上级总评分
    computeSeniorScore() {
      let kpiSeniorScore = 0
      let teamSeniorScore = 0
      let jobMannerSeniorScore = 0
      let specialSeniorScore = 0
      this.kpiList.forEach((item) => {
        if (item.state !== 3) {
          kpiSeniorScore += item.seniorScore
        }
      })
      this.teamList.forEach((item) => {
        if (item.state !== 3) {
          teamSeniorScore += item.seniorScore
        }
      })
      this.jobMannerList.forEach((item) => {
        if (item.state !== 3) {
          jobMannerSeniorScore += item.seniorScore
        }
      })
      this.specialList.forEach((item) => {
        if (item.state !== 3) {
          specialSeniorScore += item.seniorScore
        }
      })
      this.dataInfo.totalSuperiorScore = kpiSeniorScore + teamSeniorScore + jobMannerSeniorScore + specialSeniorScore
    },
    // 计算联查总评分
    computeTogetherScore() {
      let kpiTogetherScore = 0
      let teamTogetherScore = 0
      let jobMannerTogetherScore = 0
      let specialTogetherScore = 0
      this.kpiList.forEach((item) => {
        if (item.state !== 3) {
          kpiTogetherScore += item.togetherScore
        }
      })
      this.teamList.forEach((item) => {
        if (item.state !== 3) {
          teamTogetherScore += item.togetherScore
        }
      })
      this.jobMannerList.forEach((item) => {
        if (item.state !== 3) {
          jobMannerTogetherScore += item.togetherScore
        }
      })
      this.specialList.forEach((item) => {
        if (item.state !== 3) {
          specialTogetherScore += item.togetherScore
        }
      })
      this.dataInfo.totalUnionScore = kpiTogetherScore + teamTogetherScore + jobMannerTogetherScore + specialTogetherScore
    },
    // 计算最终总分值
    computeTotalLastScore() {
      let kpiTotalLastScore = 0
      let teamTotalLastScore = 0
      let jobMannerTotalLastScore = 0
      let specialTotalLastScore = 0
      this.kpiList.forEach((item) => {
        if (item.state !== 3) {
          kpiTotalLastScore += parseFloat(item.lastScore.toFixed(2))
        }
      })
      this.teamList.forEach((item) => {
        if (item.state !== 3) {
          teamTotalLastScore += parseFloat(item.lastScore.toFixed(2))
        }
      })
      this.jobMannerList.forEach((item) => {
        if (item.state !== 3) {
          jobMannerTotalLastScore += parseFloat(item.lastScore.toFixed(2))
        }
      })
      this.specialList.forEach((item) => {
        if (item.state !== 3) {
          specialTotalLastScore += parseFloat(item.lastScore.toFixed(2))
        }
      })
      this.dataInfo.totalLastScore = kpiTotalLastScore + teamTotalLastScore + jobMannerTotalLastScore + specialTotalLastScore
    },
    // 提交
    submit() {
      if (!this.dataInfo.time) return this.$message.warning('请选择绩效月份')
      this.dataInfo.detailReqs = [...this.kpiList, ...this.teamList, ...this.jobMannerList, ...this.specialList]
      if (!this.dataInfo.detailReqs.length) return this.$message.warning('请填写绩效详情')
      this.dataInfo.state = 1
      this.loading = true

      if (this.$route.params.type === 'edit') {
        tAchievementUpdate(this.dataInfo)
          .then((res) => {
            this.loading = false
            this.$router.push('/performance')
            this.$message.success('保存成功')
          })
          .catch(() => {
            this.loading = false
            this.$message.error('保存失败，请重试')
          })
      } else {
        tAchievementAdd(this.dataInfo)
          .then((res) => {
            this.loading = false
            this.$router.push('/performance')
            this.$message.success('保存成功')
          })
          .catch(() => {
            this.loading = false
            this.$message.error('保存失败，请重试')
          })
      }
    },

    async getDetails() {
      const { data } = await tAchievementDetail({ id: this.$route.params.id })
      this.dataInfo.time = data.time
      this.dataInfo.state = data.state
      this.dataInfo.returnRemark = data.returnRemark // 退回原因
      this.dataInfo.achievementId = data.achievementId
      this.dataInfo.totalSelfScore = data.totalSelfScore // 自评分总分
      this.dataInfo.totalUnionScore = data.totalUnionScore // 联查分总分
      this.dataInfo.totalSuperiorScore = data.totalSuperiorScore // 上级分总分
      this.dataInfo.totalLastScore = Math.floor(data.totalLastScore) // 最终总分
      this.dataInfo.detailReqs = data.detailDtos
      this.dataInfo.realName = data.realName
      this.dataInfo.organizationName = data.organizationName
      this.dataInfo.jobName = data.jobName
      this.dataInfo.headurl = data.headurl
      this.logList = data.logDtos // 修订记录
      this.dataInfo.detailReqs.forEach((item) => {
        if (item.type === 1) {
          this.kpiList.push({
            ...item,
            fileReqs: item.files,
            weight: parseInt(item.weight),
            seniorScore: parseInt(item.seniorScore),
            togetherScore: parseInt(item.togetherScore),
            lastScore: Math.floor(item.lastScore)
          })
        } else if (item.type === 2) {
          this.teamList.push({
            ...item,
            fileReqs: item.files,
            weight: parseInt(item.weight),
            seniorScore: parseInt(item.seniorScore),
            togetherScore: parseInt(item.togetherScore),
            lastScore: Math.floor(item.lastScore)
          })
        } else if (item.type === 3) {
          this.jobMannerList.push({
            ...item,
            fileReqs: item.files,
            weight: parseInt(item.weight),
            seniorScore: parseInt(item.seniorScore),
            togetherScore: parseInt(item.togetherScore),
            lastScore: Math.floor(item.lastScore)
          })
        } else {
          this.specialList.push({
            ...item,
            fileReqs: item.files,
            weight: parseInt(item.weight),
            seniorScore: parseInt(item.seniorScore),
            togetherScore: parseInt(item.togetherScore),
            lastScore: Math.floor(item.lastScore)
          })
        }
      })
      this.computeSumWeight()
      this.computeTotalSelfScore()
    },
    // 评分
    score(row, index, type) {
      this.openIndex = index
      this.$refs['scoreDialog'].scoreInfo = { ...row }
      this.$refs['scoreDialog'].type = type
      if (row.seniorScore) {
        this.$refs['scoreDialog'].seniorScore = row.seniorScore
      }
      if (row.togetherScore) {
        this.$refs['scoreDialog'].togetherScore = row.togetherScore
      }
      this.scoreDialog = true
    },
    // 退回
    sendBack() {
      this.sendBackDialog = true
      this.$nextTick(() => {
        this.$refs['sendBack'].achievementId = this.$route.params.id
      })
    },
    // 确定评分
    confirmScore() {
      this.dataInfo.detailReqs = [...this.kpiList, ...this.teamList, ...this.jobMannerList, ...this.specialList]
      // 判断是否有未评分的
      const noScore = this.dataInfo.detailReqs.some((item) => {
        if (!item.lastScore) {
          return true
        }
      })
      if (noScore) return this.$message.warning('请先将所有绩效进行打分')
      this.dataInfo.state = 2
      tAchievementUpdate(this.dataInfo)
        .then((res) => {
          this.loading = false
          this.$router.push('/performance')
          this.$message.success('保存成功')
        })
        .catch(() => {
          this.loading = false
          this.$message.error('保存失败，请重试')
        })
    },
    scoreOver(data, type) {
      if (type === '上级评分') {
        if (data.type === 1) {
          this.computeSuperiorScore(this.kpiList, data)
        } else if (data.type === 2) {
          this.computeSuperiorScore(this.teamList, data)
        } else if (data.type === 3) {
          this.computeSuperiorScore(this.jobMannerList, data)
        } else if (data.type === 4) {
          this.computeSuperiorScore(this.specialList, data)
        }
        this.computeSeniorScore()
      } else {
        if (data.type === 1) {
          this.computeUnionScore(this.kpiList, data)
        } else if (data.type === 2) {
          this.computeUnionScore(this.teamList, data)
        } else if (data.type === 3) {
          this.computeUnionScore(this.jobMannerList, data)
        } else if (data.type === 4) {
          this.computeUnionScore(this.specialList, data)
        }
        this.computeTogetherScore() // 计算联查分数
      }
    },
    // 给列表赋值上级分数
    computeSuperiorScore(list, data) {
      list.forEach((item, index) => {
        if (this.openIndex === index) {
          item['seniorScore'] = data.seniorScore
          if (item.togetherScore) {
            item['lastScore'] = Math.floor((data.selfScore / 100) * 20 + (data.seniorScore / 100) * 50 + (data.togetherScore / 100) * 30)
          }
        }
      })
      this.computeTotalLastScore() // 计算最终总分
    },
    // 给列表赋值联查分数和总分
    computeUnionScore(list, data) {
      list.forEach((item, index) => {
        if (this.openIndex === index) {
          item['togetherScore'] = data.togetherScore
          item['lastScore'] = parseFloat((data.selfScore / 100) * 20 + (data.seniorScore / 100) * 50 + (data.togetherScore / 100) * (30).toFixed(2))
        }
      })
      this.computeTotalLastScore() // 计算最终总分
    },
    printStart() {
      this.showKpi = true
      this.showTeam = true
      this.showSpecial = true
      this.showJobManner = true
    }
  }
}
</script>
<style lang="scss">
.stateTooltip {
  border-radius: 12px;
  max-width: 302px;
  background: #0d1c46 !important;
  font-size: 12px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #f5f5f5 !important;
  line-height: 20px;
}
</style>

<style scoped lang="scss">
.app-container {
  position: relative;
  padding: 0;
  background-color: #e8eaed;
  min-height: 100%;
  padding-top: 58px;
  padding-bottom: 10px;
  .top {
    position: absolute;
    top: 0px;
    width: 100%;
    background-color: #e8eaed;
    padding: 24px 0 16px 0;
    z-index: 999;
    .el-col {
      i {
        font-size: 14px;
        color: #657081;
      }
      span {
        &:first-of-type {
          margin: 0 5px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #657081;
        }
        &:last-of-type {
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
      }
    }
  }
  .box {
    position: relative;
    width: 1754px;
    min-height: 791px;
    padding-bottom: 30px;
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    background: url('~@/assets/performance/add_bg.png') no-repeat;
    background-size: cover;
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-top: 48px;
      padding-left: 94px;
      padding-right: 40px;
      .left {
        display: flex;
        align-items: center;
        .avatar {
          width: 108px;
          height: 108px;
          margin-right: 29px;
          border-radius: 50%;
        }
        .userInfo {
          & > div:first-of-type {
            display: flex;
            align-items: center;
            .realName {
              font-size: 20px;
              font-family: Microsoft YaHei-Bold, Microsoft YaHei;
              font-weight: bold;
              color: #0b1a44;
            }
            img {
              margin-left: 4px;
              width: 14px;
              height: 20px;
            }
            i {
              margin: 0 12px;
            }
            .jobName {
              font-size: 16px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #0b1a44;
            }
          }
          .organizationName {
            margin-top: 17px;
            width: 56px;
            height: 24px;
            line-height: 22px;
            background: #3464e0;
            border-radius: 4px 4px 4px 4px;
            font-size: 13px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #ffffff;
            text-align: center;
          }
        }
      }
      .center {
        display: flex;
        align-items: center;
        & > div {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 120px;
          height: 58px;
          border-right: 1px dashed #dcdcdc;
          &:last-of-type {
            border: none;
          }
          span {
            &:first-of-type {
              font-size: 14px;
              font-family: Microsoft YaHei-Bold, Microsoft YaHei;
              font-weight: bold;
              color: #0b1a44;
            }
            &:last-of-type {
              margin-top: 12px;
              font-size: 24px;
              font-family: Microsoft YaHei-Bold, Microsoft YaHei;
              font-weight: bold;
              color: #0b1a44;
            }
          }
        }
      }
      .right {
        position: relative;
        top: -45px;
        display: flex;
        div {
          width: 118px;
          height: 44px;
          line-height: 44px;
          border-radius: 6px;
          font-size: 18px;
          text-align: center;
          font-weight: 400;
          cursor: pointer;
        }
        .print {
          margin-right: 24px;
          background: #fdfdff;
          color: #0b1a44;
        }
        .submit {
          background: #3465df;
          color: #fff;
        }
        .sendBack {
          margin-right: 24px;
          background: #eb6557;
          color: #fff;
        }
        .confirmScore {
          background: #03b47d;
          color: #fff;
        }
      }
      .statistics {
        & > div:first-of-type {
          display: flex;
          align-items: center;
          section {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 124px;
            height: 80px;
            margin-right: 12px;
            border-radius: 13px;
            & > span:first-of-type {
              font-size: 14px;
              font-weight: bold;
              color: #0b1a44;
            }
            & > span:last-of-type {
              margin-top: 5px;
              font-size: 24px;
              font-weight: bold;
              color: #0b1a44;
            }
            &:first-of-type {
              background: #dff2eb;
              border: 1px dashed rgba(166, 223, 203, 0.86);
            }
            &:nth-of-type(2) {
              background: #f2e9df;
              border: 1px dashed #f8d4ac;
            }
            &:nth-of-type(3) {
              background: #dfeaf2;
              border: 1px dashed #b8daf3;
            }
            &:nth-of-type(4) {
              background: #f0f2df;
              border: 1px dashed #dce39c;
            }
            &:last-of-type {
              margin-right: 0;
              background: #f2e1df;
              border: 1px dashed #f3beb8;
            }
          }
        }
        & > div:last-of-type {
          position: absolute;
          right: 24px;
          top: 24px;
          display: flex;
          align-items: center;
          span {
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #657081;
            cursor: pointer;
          }
          i {
            margin: 0 12px;
            font-size: 14px;
            font-style: normal;
            color: #b1bac7;
          }
        }
      }
    }
    .content {
      margin-top: 42px;

      .title {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        &::before {
          content: '';
          position: absolute;
          right: 55px;
          top: 50%;
          transform: translateY(-50%);
          width: 646px;
          height: 1px;
          border-top: 1px dashed #b1bac7;
        }
        &::after {
          content: '';
          position: absolute;
          left: 40px;
          top: 50%;
          transform: translateY(-50%);
          width: 646px;
          height: 1px;
          border-top: 1px dashed #b1bac7;
        }
        & > span {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
        }
        ::v-deep {
          .el-date-editor {
            position: relative;
            &::after {
              content: '';
              position: absolute;
              left: -28px;
              bottom: 0;
              width: 228px;
              height: 1px;
              background: #d8dbe1;
            }
          }
          .el-input__inner {
            border: none;
            background: none;
            padding: 0;
            font-size: 24px;
            font-family: Source Han Serif CN-Bold, Source Han Serif CN;
            font-weight: bold;
            color: #3464e0;
            &::placeholder {
              font-size: 24px;
            }
          }
          .el-input__prefix,
          .el-input__suffix {
            display: none;
          }
        }
        .detailsTime {
          font-size: 24px;
          font-weight: bold;
          color: #0b1a44;
        }
      }
      .detailsTitle {
        &::before {
          width: 710px;
        }
        &::after {
          width: 710px;
        }
      }
      .table {
        width: 1674px;
        // height: 300px;
        margin: 0 auto;
        margin-top: 30px;
        .table_header {
          display: flex;
          width: 100%;
          height: 80px;
          line-height: 80px;
          background: #3268b5;
          border-radius: 8px 8px 0px 0px;
          text-align: center;
          & > div {
            height: 100%;
            border-right: 1px solid #3d74c1;
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #eeeeef;
          }
          .sort {
            width: 78px;
          }
          & > div:nth-of-type(2) {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 280px;
            img {
              margin-left: 8px;
            }
          }

          & > div:nth-of-type(3),
          & > div:nth-of-type(4),
          & > div:nth-of-type(5),
          & > div:nth-of-type(7),
          & > div:nth-of-type(8),
          & > div:nth-of-type(9) {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            line-height: 25px;
            & > div:first-of-type {
              display: flex;
              align-items: center;
              img {
                margin-left: 8px;
              }
            }
            & > div:last-of-type {
              font-size: 12px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #b1bac7;
            }
          }
          & > div:nth-of-type(3) {
            width: 162px;
          }
          & > div:nth-of-type(4) {
            width: 338px;
          }
          & > div:nth-of-type(5) {
            width: 134px;
          }
          & > div:nth-of-type(6) {
            width: 88px;
          }
          & > div:nth-of-type(7) {
            width: 68px;
          }
          & > div:nth-of-type(8) {
            width: 108px;
          }
          & > div:nth-of-type(9) {
            width: 213px;
            & > div:first-of-type {
              width: 126px;
              line-height: 18px;
            }
          }
          & > div:nth-of-type(10) {
            width: 71px;
          }
          & > div:last-of-type {
            width: 130px;
            border: none;
          }
        }
        .kpi,
        .team,
        .jobManner,
        .special {
          align-items: center;
          width: 100%;
          height: 48px;
          background: #fbe8af;
          padding-left: 32px;
          padding-right: 40px;
          & > div {
            display: flex;
            align-items: center;
            font-size: 13px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #604f2d;
          }
          .arrow {
            width: 16px;
            height: 16px;
            cursor: pointer;
          }
          .add {
            margin-left: 32px;
            color: #3464e0;
            font-size: 20px;
            cursor: pointer;
            &:hover {
              color: #355fce;
            }
          }
        }
        .team {
          background: #cce8cf;
        }
        .jobManner {
          background: #bad7ee;
        }
        .special {
          background: #e0e1e5;
        }
        .kpiTable {
          width: 100%;
          background: #fff;
          .el-row {
            display: flex;
            height: 68px;
            border-bottom: 1px solid #eeeeef;
            &:hover {
              background: #f6f6f6;
            }
            & > div {
              display: flex;
              justify-content: center;
              align-items: center;
              border-right: 1px solid #eeeeef;
              font-size: 14px;
              font-family: Microsoft YaHei-Bold, Microsoft YaHei;
              font-weight: bold;
              color: #0b1a44;

              &:first-of-type {
                width: 78px;
              }
              &:nth-of-type(2) {
                width: 280px;
              }
              &:nth-of-type(3) {
                width: 162px;
              }
              &:nth-of-type(4) {
                width: 338px;
              }
              &:nth-of-type(5) {
                width: 134px;
              }
              &:nth-of-type(6) {
                width: 88px;
              }
              &:nth-of-type(7) {
                width: 68px;
              }
              &:nth-of-type(8) {
                width: 108px;
              }
              &:nth-of-type(6) {
                img {
                  margin-right: 4px;
                }
              }
              &:nth-of-type(9) {
                width: 213px;
              }
              &:nth-of-type(10) {
                width: 71px;
              }
              &:last-of-type {
                width: 130px;
                border: none;
                padding: 0 12px;
                span {
                  display: inline-block;
                  width: 44px;
                  height: 44px;
                  cursor: pointer;
                  &:last-of-type {
                    margin-right: 0;
                  }
                }
                .scoreButton {
                  background: url('~@/assets/performance/score_round.png') no-repeat;
                  background-size: cover;
                  &:hover {
                    background: url('~@/assets/performance/score_hover.png') no-repeat;
                    background-size: cover;
                  }
                }
                .scoreButton2 {
                  background: url('~@/assets/performance/score2.png') no-repeat;
                  background-size: cover;
                }
                .detailsButton {
                  background: url('~@/assets/performance/details.png') no-repeat;
                  background-size: cover;
                  &:hover {
                    background: url('~@/assets/performance/details_hover.png') no-repeat;
                    background-size: cover;
                  }
                }
                .editButton {
                  background: url('~@/assets/performance/edit.png') no-repeat;
                  background-size: cover;
                  &:hover {
                    background: url('~@/assets/performance/edit_hover.png') no-repeat;
                    background-size: cover;
                  }
                }
                .delButton {
                  background: url('~@/assets/performance/del.png') no-repeat;
                  background-size: cover;
                  &:hover {
                    background: url('~@/assets/performance/del_hover.png') no-repeat;
                    background-size: cover;
                  }
                }
              }
            }
            .textHidden {
              margin-left: 5px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
          .substitution {
            position: relative;
            &::before {
              content: '';
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
              height: 1px;
              width: calc(100% - 135px);
              background: #d8dbe1;
            }
            & > div {
              color: #d8dbe1;
            }
          }
          .emphasis {
            & > div {
              color: #009d8d;
            }
          }
          .beReplaced {
            & > div {
              color: #ff6926;
            }
          }
        }
        .table_footer {
          display: flex;
          width: 100%;
          height: 80px;
          background: #f8cdad;
          border-radius: 0 0 8px 8px;
          div {
            width: 78px;
            border-color: #ffdfc8;
            font-size: 14px;
            font-weight: bold;
            color: #0b1a44;
            &:first-of-type {
              font-size: 18px;
              font-family: Microsoft YaHei-Bold, Microsoft YaHei;
              font-weight: bold;
            }
            &:nth-of-type(4) {
              width: 562px;
            }
            &:nth-of-type(5) {
              width: 68px;
            }
            &:nth-of-type(6) {
              width: 108px;
            }
            &:nth-of-type(7) {
              width: 213px;
            }
            &:nth-of-type(8) {
              width: 71px;
            }
          }
        }
      }
    }
  }
  .sendBackMessage {
    position: absolute;
    top: 0;
    left: 242px;
    z-index: 999;
    display: flex;
    align-items: center;
    width: 1184px;
    height: 58px;
    padding-left: 24px;
    background: #eb6557;
    border-radius: 4px 4px 4px 4px;
    img {
      width: 18px;
      height: 18px;
    }
    & > span:first-of-type {
      margin-left: 7px;
      margin-right: 24px;
      font-size: 16px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #ffffff;
    }
    & > span:last-of-type {
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #ffffff;
    }
  }
}
::v-deep {
  .addDialog {
    position: relative;
    width: 910px;
    height: 830px;
    border-radius: 10px;
    overflow: hidden;
    // height: 730px;
    background: #f5f5f5;
    .el-dialog__header {
      padding: 0;
      padding-left: 24px;
      background: #d8dbe1;
      height: 56px;
      .header {
        display: flex;
        align-items: center;
        width: 100%;
        height: 100%;
        & > span:first-of-type {
          font-size: 16px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
        & > span:last-of-type {
          margin-left: 4px;
          font-size: 13px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #604f2d;
        }
      }
      .el-dialog__headerbtn {
        .el-icon-close {
          color: #657081;
        }
      }
    }
    .el-dialog__body {
      padding-top: 20px;
      padding-bottom: 0;
      max-height: 703px;
      overflow: auto;

      .el-form {
        .el-form-item {
          .label1 {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            span {
              margin-left: 4px;
              font-size: 14px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #0b1a44;
            }
          }
          .select {
            .el-input__inner {
              width: 336px;
              height: 40px;
              background: #ffffff;
            }
            .el-input__suffix .el-select__caret {
              color: #0b1a44;
            }
          }
          .el-textarea {
            margin-top: 12px;
            .el-textarea__inner {
              width: 668px;
              height: 105px;
              background: #ffffff;
            }
            .el-textarea__inner:focus {
              border: 1px solid #3464e0;
            }
          }
          .label2 {
            display: flex;
            flex-direction: column;
            justify-content: center;
            & > div:first-of-type {
              display: flex;
              align-items: center;
              justify-content: flex-end;
              span {
                margin-left: 4px;
                font-size: 14px;
                font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                font-weight: 400;
                color: #0b1a44;
              }
            }
            & > div:last-of-type {
              line-height: 12px;
              font-size: 12px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #b1bac7;
            }
          }
          .el-input-number {
            margin-top: 10px;
            width: 180px;
            overflow: hidden;
            .el-input-number__decrease {
              border-radius: 10px 0 0 10px;
            }
            .el-input-number__increase {
              border-radius: 0 10px 10px 0px;
            }
            .el-input-number__decrease:hover,
            .el-input-number__increase:hover {
              color: #3464e0;
            }
          }
          .el-input-number__decrease:hover:not(.is-disabled) ~ .el-input .el-input__inner:not(.is-disabled),
          .el-input-number__increase:hover:not(.is-disabled) ~ .el-input .el-input__inner:not(.is-disabled) {
            border-color: #3465df;
          }
        }
        .label3,
        .label4 {
          .el-form-item__label {
            display: flex;
            flex-direction: column;
            justify-content: center;
            & > div:first-of-type {
              display: flex;
              align-items: center;
              justify-content: flex-end;
              span {
                margin-left: 4px;
                font-size: 14px;
                font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                font-weight: 400;
                color: #0b1a44;
              }
            }
            & > div:last-of-type {
              line-height: 12px;
              font-size: 12px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #b1bac7;
            }
          }
          .el-textarea {
            margin-top: 0;
            .el-textarea__inner {
              width: 668px;
              height: 78px;
              background: #ffffff;
            }
          }
        }
        .state {
          .el-input__prefix {
            padding-top: 3px;
            left: 16px;
          }
          .el-select {
            width: 129px;
            .el-input__inner {
              padding-left: 40px;
            }
          }
          .el-form-item__label {
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #0b1a44;
          }
        }
        .upload {
          .el-form-item__label {
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #0b1a44;
          }
          .upload-demo {
            width: 88px;
            height: 88px;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .uploadList {
            margin-top: 5px;
            line-height: 25px;
            & > div {
              width: 400px;
              span {
                display: inline-block;
                margin-right: 15px;
                height: 19px;
                font-size: 14px;
                font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                font-weight: 400;
                color: #3464e0;
              }
              .download,
              .remove {
                font-size: 14px;
                font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                color: #b1bac7;
                cursor: pointer;
              }
              .remove {
                color: red;
              }
            }
          }
        }
      }
    }
    .el-dialog__footer {
      position: absolute;
      bottom: 0;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0;
      padding-left: 100px;
      padding-right: 24px;
      width: 100%;
      height: 71px;
      background: rgba($color: #f3c057, $alpha: 0.24);
      .footer_left {
        display: flex;
        & > div:first-of-type {
          display: flex;
          flex-direction: column;
          align-items: center;
          span {
            font-family: Microsoft YaHei-Bold, Microsoft YaHei;
            color: #eb6557;
          }
          & > span:first-of-type {
            font-size: 16px;
            font-weight: bold;
          }
          & > span:last-of-type {
            margin-top: 5px;
            font-size: 12px;
            font-weight: 400;
          }
        }
        & > div:last-of-type {
          width: 100px;
          .el-input-number {
            width: 100px;
            .el-input__inner {
              padding-right: 0;
              padding-left: 25px;
              border-radius: 3px;
              text-align: left;
            }
          }
        }
      }
      .footer_right {
        display: flex;
        align-items: center;
        .confirm,
        .cancel {
          width: 118px;
          height: 44px;
          background: url('~@/assets/performance/confirm.png') no-repeat;
          background-size: cover;
          cursor: pointer;
          &:hover {
            background: url('~@/assets/performance/confirm_hover.png') no-repeat;
            background-size: cover;
          }
        }
        .cancel {
          background: url('~@/assets/performance/cancel.png') no-repeat;
          background-size: cover;
          &:hover {
            background: url('~@/assets/performance/cancel_hover.png') no-repeat;
            background-size: cover;
          }
        }
      }
    }
  }
  .detailsDialog {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    .el-dialog__header {
      padding: 0;
      padding-left: 24px;
      background: #d8dbe1;
      height: 56px;
      .header {
        display: flex;
        align-items: center;
        width: 100%;
        height: 100%;
        & > span:first-of-type {
          font-size: 16px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
        & > span:last-of-type {
          margin-left: 4px;
          font-size: 13px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #604f2d;
        }
      }
      .el-dialog__headerbtn {
        .el-icon-close {
          color: #657081;
        }
      }
    }
    .el-dialog__body {
      padding: 0;
      .scoreRow {
        align-items: center;
        padding-top: 32px;
        padding-left: 56px;
        margin-bottom: 32px;
        .score {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          width: 124px;
          height: 80px;
          border-radius: 13px;
          & > span:first-of-type {
            font-size: 14px;
            font-weight: bold;
            color: #0b1a44;
          }
          & > span:last-of-type {
            margin-top: 12px;
            font-size: 24px;
            font-weight: bold;
            color: #0b1a44;
          }
          &:first-of-type {
            background: #f2e9df;
          }
          &:nth-of-type(2) {
            background: #dfeaf2;
          }
          &:nth-of-type(3) {
            background: #f0f2df;
          }
          &:nth-of-type(4) {
            background: #f2e1df;
            & > span:last-of-type {
              color: #eb6557;
            }
          }
        }
        .describe {
          width: 227px;
          margin-left: 12px;
          font-size: 12px;
          font-weight: 400;
          color: #a3a8bb;
          line-height: 20px;
        }
      }
      .titleRow {
        display: flex;
        align-items: flex-start;
        margin-top: 32px;
        padding-left: 33px;
        padding-right: 43px;
        & > div:first-of-type {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          margin-right: 24px;
          width: 132px;
          font-size: 14px;
          font-weight: 400;
          color: #0b1a44;
          img {
            margin-right: 4px;
          }
        }
        & > div:last-of-type {
          width: 678px;
          font-size: 14px;
          font-weight: bold;
          color: #0b1a44;
          // line-height: 20px;
        }
      }
      .describeRow {
        display: flex;
        align-items: flex-start;
        margin-top: 32px;
        padding-left: 33px;
        padding-right: 43px;
        & > div:first-of-type {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          margin-right: 24px;
          width: 132px;
          font-size: 14px;
          font-weight: 400;
          color: #0b1a44;

          & > div:first-of-type {
            display: flex;
            align-items: center;
            img {
              margin-right: 4px;
            }
          }
          .titleDescribe {
            margin-top: 8px;
            font-size: 12px;
            font-weight: 400;
            color: #b1bac7;
          }
        }
        & > div:last-of-type {
          width: 678px;
          font-size: 14px;
          font-weight: bold;
          color: #0b1a44;
          line-height: 20px;
        }
      }
      .taskRow {
        display: flex;
        align-items: center;
        & > div:last-of-type {
          display: flex;
          align-items: center;
          img {
            margin-right: 4px;
          }
        }
      }
      .outputRow {
        padding-bottom: 55px;
        & > div:first-of-type {
          font-weight: bold;
        }
        & > div:last-of-type {
          .files {
            margin-bottom: 16px;
            // & > span:first-of-type {
            //   display: inline-block;
            //   width: 150px;
            // }
            & > span {
              margin-right: 12px;
              font-size: 14px;
              font-weight: 400;
              color: #3464e0;
            }
            & > span:last-of-type {
              margin-left: 8px;
              font-size: 14px;
              font-weight: 400;
              color: #b1bac7;
              cursor: pointer;
            }
          }
        }
      }
    }
  }
  .delDialog {
    height: 206px;
    border-radius: 8px !important;
    .el-dialog__header {
      display: flex;
      align-items: center;
      padding-top: 12px;
      padding-left: 24px;
      padding-right: 20px;
      padding-bottom: 8px;
      border-bottom: 1px solid #eeeeef;
      .hint {
        width: 30px;
        height: 30px;
      }
      .hintText {
        margin-left: 6px;
        font-size: 16px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #303a40;
      }
      .el-dialog__headerbtn {
        right: 20px;
        top: 20px;
        .el-dialog__close {
          font-weight: bold;
        }
      }
    }
    .delText {
      text-align: center;
      font-size: 16px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #303a40;
    }
    .operate {
      display: flex;
      justify-content: center;
      margin-top: 48px;
      .closeButton {
        width: 114px;
        height: 38px;
        line-height: 36px;
        background: #ffffff;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #d8dbe1;
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        color: #a3a8bb;
        text-align: center;
        cursor: pointer;
        &:hover {
          background: #f5f5f5;
        }
      }
      .confirmButton {
        margin-left: 32px;
        width: 114px;
        height: 38px;
        line-height: 36px;
        background: #3464e0;
        border-radius: 4px 4px 4px 4px;
        font-size: 14px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        font-weight: bold;
        color: #ffffff;
        text-align: center;
        cursor: pointer;
        &:hover {
          background: #355fce;
        }
      }
    }
  }
  .reviseRecordDialog {
    height: 386px;
    border-radius: 2px;
    overflow: hidden;
    box-shadow: none;
    .el-dialog__header {
      display: flex;
      align-items: center;
      padding: 0;
      padding-left: 24px;
      height: 40px;
      background: #3464e0;
      .el-dialog__title {
        font-size: 16px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        font-weight: bold;
        color: #ffffff;
      }
      .el-dialog__headerbtn {
        top: 12px;
        .el-dialog__close {
          color: #fff;
          font-weight: bold;
        }
      }
    }
    .el-dialog__body {
      position: relative;
      height: 346px;
      padding: 24px 0;
      overflow: auto;
      .record {
        padding: 0 40px;
        height: 40px;
        line-height: 40px;
        &:hover {
          background: #f5f5f5;
        }
        & > span:first-of-type {
          font-size: 14px;
          color: #0b1a44;
        }
        & > span:nth-of-type(2) {
          margin-left: 32px;
          font-size: 14px;
          color: #0b1a44;
        }
        & > span:last-of-type {
          font-size: 14px;
          color: #3464e0;
        }
      }
      .empty {
        position: absolute;
        left: 50%;
        top: 45%;
        transform: translate(-50%, -50%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        span {
          margin-left: -10px;
          margin-top: 19px;
          font-size: 16px;
          color: #b1bac7;
        }
      }
    }
  }
}
</style>
<style lang="scss" scoped>
::v-deep {
  .el-input__inner,
  .el-textarea__inner {
    border: 1px solid #d8dbe1;
    border-radius: 10px;
    font-size: 14px;
    font-family: '微软雅黑';
    font-weight: bold;
    color: #0b1a44;
    &::placeholder {
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #868b9f;
    }
  }
}
</style>
<style media="print" lang="scss">
@import '../../styles/test.scss';
@media print {
  @page {
    size: auto;
    margin: 30mm 0mm 0mm;
    zoom: 65%;
  }
  html {
    background-color: #ffffff;
    height: auto;
    margin: 0px;
  }
  .footer {
    page-break-after: always;
  }
}
</style>
