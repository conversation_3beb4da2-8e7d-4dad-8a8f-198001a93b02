<template>
  <div class="app-container">
    <el-row :gutter="10" style="margin-bottom: 20px">
      <el-col :span="6"><el-button type="primary" size="small" @click="addRole">添加角色</el-button></el-col>
    </el-row>
    <el-table v-loading="listLoading" :data="roleList" element-loading-text="Loading" border fit highlight-current-row>
      <el-table-column label="序号" align="center" width="120" type="index"> </el-table-column>
      <el-table-column label="角色名称" align="center" prop="roleName"> </el-table-column>
      <el-table-column label="角色标识" align="center" prop="roleKey"> </el-table-column>
      <el-table-column label="操作" align="center" width="400">
        <template v-slot="scope">
          <el-button size="small" type="warning   " @click="editRole(scope.row)">修改角色</el-button>
          <el-button size="small" type="danger" @click="delRole(scope.row)">删除角色</el-button>
          <el-button size="small" type="success" @click="RolePermission(scope.row)">角色授权</el-button>
        </template>
      </el-table-column>
    </el-table>
    <improvement ref="RoleDialog" :show-dialog.sync="showRoleDialog" @addRoleSuccess="getTabList" />
    <!-- 分页 -->
    <el-pagination layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 15, 20, 30]" :total="total" :page-size.sync="page.pageSize" :current-page.sync="page.pageNum" style="text-align: center; margin-top: 15px" @size-change="getTabList" @current-change="getTabList"> </el-pagination>

    <!-- 权限分配 -->
    <el-dialog title="角色授权" :visible.sync="AllotpermissionDialog" width="270px">
      <el-tree ref="Rolepermission" :data="permission" :props="props" default-expand-all node-key="id" show-checkbox :default-checked-keys="DefaulCheckedKeys" check-strictly> </el-tree>
      <div slot="footer">
        <el-button @click="AllotpermissionDialog = false">取 消</el-button>
        <el-button type="primary" @click="onClickPermission">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getroleList, roleRemove, saveRoleConfiguration } from '@/api/role'
import { getMenuTree } from '@/api/menu'
import improvement from './components/improvement'
export default {
  name: 'Role',
  components: {
    improvement
  },
  data() {
    return {
      roleList: null,
      listLoading: true,
      total: null,
      page: {
        pageIndex: 1,
        pageSize: 10
      },
      showRoleDialog: false,
      props: {
        label: 'menuName',
        children: 'children'
      },
      // 权限分配字段
      AllotpermissionDialog: false,
      permission: [],
      DefaulCheckedKeys: [],
      roleId: null
    }
  },
  created() {
    this.getTabList()
  },
  methods: {
    getTabList() {
      this.listLoading = true
      getroleList(this.page).then((response) => {
        console.log(response)
        this.roleList = response.data.list
        this.total = response.data.total
        this.listLoading = false
      })
    },
    addRole() {
      this.showRoleDialog = true
    },
    editRole(row) {
      this.$refs['RoleDialog'].showData(row)
      this.showRoleDialog = true
    },
    delRole(row) {
      this.$confirm('你确定要删除改角色吗', '删除角色', { type: 'warning' })
        .then(async (res) => {
          await roleRemove(row.roleId)
          this.$message.success('删除成功')
          this.getTabList()
        })
        .catch(() => {
          this.$message.info('已取消删除')
        })
    },
    RolePermission(row) {
      this.AllotpermissionDialog = true
      this.roleId = row.roleId
      getMenuTree(row.roleId)
        .then((res) => {
          console.log(res)
          this.permission = res.data
          const result = this.TreeToFlat(res.data)
          const keys = []
          result.forEach((item) => {
            if (item.checked === true) {
              keys.push(item.id)
            }
          })
          this.DefaulCheckedKeys = keys
          console.log(keys)
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 将树形结构扁平化
    TreeToFlat(data) {
      let formatData = []
      for (var i = 0; i < data.length; i++) {
        formatData.push({
          id: data[i].id,
          checked: data[i].checked
        })
        if (data[i].children) {
          formatData = formatData.concat(this.TreeToFlat(data[i].children))
        }
      }
      return formatData
    },
    async onClickPermission() {
      const keys = this.$refs['Rolepermission'].getCheckedKeys(false).concat(this.$refs['Rolepermission'].getHalfCheckedKeys(false))
      console.log(keys)
      await saveRoleConfiguration({
        roleId: this.roleId,
        menuIds: keys
      })
      this.$message.success('分配成功')
      this.AllotpermissionDialog = false
      this.$router.go(0)
    }
  }
}
</script>

<style scoped lang="sass"></style>
