<template>
  <div class="app-container">
    <el-button type="primary" style="margin: 0 0 10px 10px" icon="el-icon-circle-plus-outline" size="small" @click="openAddRouter">添加菜单</el-button>
    <div style="margin: 10px">
      <el-table :data="tableRouter" style="width: 100%" row-key="id" :tree-props="treeProps" default-expand-all>
        <el-table-column prop="menuName" label="菜单名称" />
        <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
        ><template v-slot="{ row }">{{ row.createTime | formatDate }}</template>
        </el-table-column>
        <el-table-column prop="permissionKey" label="权限标识" />
        <el-table-column label="操作" align="center">
          <template v-if="scope.row.id !== 1" slot-scope="scope">
            <!-- <el-button type="primary" size="small" @click="openAddRouter(scope.row.id)">添加下级</el-button> -->
            <el-button type="warning" size="small" @click="openEditRouter(scope.row)">编辑</el-button>
            <el-button type="danger" size="small" @click="removeRouter(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 添加菜单 -->
    <improvement ref="addMenuDialog" :add-menu-dialog.sync="addMenuDialog" @addSuccess="getRouterList" />
    <eidtDalog ref="editMenuDialog" :edit-menu-dialog.sync="editMenuDialog" @addSuccess="getRouterList" />
  </div>
</template>
<script>
import { getMenuList, deleteMenu } from '@/api/menu.js'
import improvement from './components/improvement.vue'
import eidtDalog from './components/eidtDalog.vue'
export default {
  name: 'Menu',
  components: {
    improvement,
    eidtDalog
  },
  data() {
    return {
      tableRouter: [],
      dataloading: false,
      addMenuDialog: false,
      editMenuDialog: false,
      treeProps: {
        children: 'children'
      },
      addForm: {
        parentId: '',
        name: '',
        perms: ''
      },
      editForm: {
        menuId: '',
        name: '',
        perms: ''
      },
      rules: {
        name: [
          {
            required: true,
            message: '请输入路由名称'
          }
        ],
        perms: [
          {
            required: true,
            message: '请输入权限标识'
          }
        ]
      }
    }
  },
  created() {
    this.getRouterList()
  },
  methods: {
    getRouterList() {
      getMenuList({}).then((res) => {
        console.log(res)
        this.tableRouter = res.data
      })
    },
    openAddRouter(parentId) {
      this.addMenuDialog = true
      this.$refs['addMenuDialog'].getMenuTreeOfParent()
      // this.addForm.parentId = parentId
    },
    openEditRouter(item) {
      this.$refs['editMenuDialog'].showData(item)
      this.editMenuDialog = true
    },

    removeRouter(mId) {
      this.$confirm('是否删除该路由及子路由?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          console.log(mId)
          deleteMenu(mId).then(async(res) => {
            if (res.code === 200) {
              this.$message({
                message: '删除成功',
                type: 'success'
              })
              this.getRouterList()
            } else {
              this.$message({
                message: res.msg,
                type: 'error'
              })
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.div-user-top {
  color: rgb(32, 29, 29);
  border-bottom: 1px solid rgba(32, 30, 30, 0.432);
  height: 70px;
  font-size: 20px;
  padding-top: 30px;
  padding-left: 50px;
  margin-bottom: 20px;
}
</style>
