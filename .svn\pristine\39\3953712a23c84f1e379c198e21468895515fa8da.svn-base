<template>
  <div v-loading="loading" element-loading-text="模型加载中" class=""></div>
</template>

<script>
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js'
export default {
  name: '',
  data() {
    return {
      loading: true,
      maincontWidth: null,
      maincontHeight: null,
      maincontSc: null,
      scene: null, // 场景
      camera: null, // 相机
      renderer: null, // 渲染器
      controls: null, // 控制器
      modelUrl: null, // 模型地址
      cameraOriginal: []
    }
  },
  methods: {
    init(url) {
      this.$nextTick(() => {
        this.modelUrl = url
        this.maincontWidth = this.$el.offsetWidth
        this.maincontHeight = this.$el.offsetHeight
        this.maincontSc = this.maincontWidth / this.maincontHeight
        // this.clock = new THREE.Clock()
        this.initScene() // 初始化场景
        // this.initCamera() // 初始化相机
        this.initRenderer() // 初始化渲染器
        this.initLight() // 灯光

        this.initFile() // 加载模型

        // // 增加坐标系红色代表 X 轴. 绿色代表 Y 轴. 蓝色代表 Z 轴.
        // // 显示三维坐标系，辅助找旋转角度
        // const axes = new THREE.AxesHelper(20)
        // // 添加坐标系到场景中
        // this.scene.add(axes)
      })
    },
    initScene() {
      this.loading = false
      var scene = new THREE.Scene()
      this.scene = scene
    },
    initCamera() {
      // 参数：视野角度、长宽比、近截面、远截面
      const camera = new THREE.PerspectiveCamera(30, this.maincontSc, 0.1, 10000)
      camera.position.set(this.cameraOriginal[0], this.cameraOriginal[1], this.cameraOriginal[2]) // 设置相机位置
      camera.lookAt(0, 0, 0)
      this.camera = camera
      this.initControls() // 控制器
      this.animate() // 渲染场景
    },
    initLight() {
      const light = new THREE.AmbientLight(0xffffff)
      this.scene.add(light)
      const pointLight = new THREE.PointLight(0xffffff, 1, 0)
      pointLight.position.set(200, 200, 200) // 设置点光源位置
      this.scene.add(pointLight)
    },
    initRenderer() {
      const renderer = new THREE.WebGLRenderer({ alpha: true, antialias: true })
      this.renderer = renderer
      this.renderer.setSize(this.maincontWidth, this.maincontHeight)
      this.$el.appendChild(this.renderer.domElement)
    },
    animate() {
      requestAnimationFrame(this.animate)
      // this.controls.update()
      this.renderer.render(this.scene, this.camera)
    },
    initFile() {
      var that = this
      var fbxLoader = new FBXLoader()
      // 本地加载：require("@/xxx/xxx_ys.glb")
      fbxLoader.load(
        that.modelUrl,
        function (obj) {
          that.loading = false
          // obj.traverse(function (item) {
          //   item.castShadow = true
          //   item.receiveShadow = true
          // })

          var box3 = new THREE.Box3()
          box3.expandByObject(obj)
          var center = new THREE.Vector3()
          box3.getCenter(center)
          that.cameraOriginal = [box3.max.x * 2, box3.max.y * 2, box3.max.z * 2]
          obj.position.x = obj.position.x - center.x
          obj.position.y = obj.position.y - center.y
          obj.position.z = obj.position.z - center.z
          that.scene.add(obj)
          that.initCamera() // 初始化相机
        },
        undefined,
        function (error) {
          console.error(error)
        }
      )
    },
    initControls() {
      const controls = new OrbitControls(this.camera, this.renderer.domElement)
      this.controls = controls
      // controls.update() must be called after any manual changes to the camera's transform
      // this.camera.position.set(0, 500, 0)
      // this.camera.position.set(0, 2000, 1200)
      // controls.update()
    }
  }
}
</script>

<style scoped lang="scss"></style>
