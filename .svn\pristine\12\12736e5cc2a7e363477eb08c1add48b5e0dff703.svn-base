// 菜单列表
import request from '@/utils/request'
export function getMenuList(params) {
  return request({
    url: '/system/menu/menuList',
    method: 'get',
    params
  })
}
// 添加菜单
export function addMenu(data) {
  return request({
    url: '/system/menu/addMenu',
    method: 'post',
    data
  })
}
// 获取菜单树
export function getMenuTree(roleId) {
  return request({
    url: '/system/menu/getMenuTree',
    method: 'get',
    params: {
      roleId
    }
  })
}
// 删除菜单
export function deleteMenu(id) {
  return request({
    url: '/system/menu/deleteMenu',
    method: 'DELETE',
    params: {
      menuId: id
    }
  })
}
// 更新菜单
export function updateMenu(data) {
  return request({
    url: '/system/menu/updateMenu',
    method: 'POST',
    data
  })
}
// 获取菜单树的父级
export function getMenuTreeOfParent() {
  return request({
    url: '/system/menu/getMenuTreeOfParent',
    method: 'get'
  })
}
// 获取菜单id
export function selectMenuById(id) {
  return request({
    url: '/system/menu/selectMenuById',
    method: 'get',
    params: {
      menuId: id
    }
  })
}
