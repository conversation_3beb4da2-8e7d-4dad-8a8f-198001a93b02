<!-- eslint-disable vue/require-v-for-key -->
<template>
  <div class="real_filecont_new">
    <div class="fils_main_scroll">
      <div class="files_btns_new clearfix">
        <div class="files_input_new clearfix">
          <div class="real_inputitem more">
            <div class="classroom_right_inputitem">
              <input v-model="searchForm.resourceName" placeholder="素材名称" @keyup.enter="currentChange(1)" />
              <img src="@/assets/resources/classroom_right_inputitem.png" class="classroom_right_inputicon like_btn" @click="currentChange(1)" />
            </div>
          </div>
        </div>
        <div class="files_add_btns l clearfix">
          <input v-show="false" ref="uploadFile_img" type="file" class="uploadFilebox" multiple accept=".jpg,.png" @change="beforeformRequest" />
          <input v-show="false" ref="uploadFile_video" type="file" class="uploadFilebox" multiple accept=".mp4" @change="beforeformRequest" />
          <input v-show="false" ref="uploadFile_pdf" type="file" class="uploadFilebox" multiple accept=".pdf" @change="beforeformRequest" />
          <div class="classroom_right_btn left like_btn" @click="openbeforeUpload"><i class="el-icon-plus"></i> 添加素材</div>
          <div class="classroom_right_btn colorful left like_btn" @click="openAdd"><i class="el-icon-folder-add"></i> 新建文件夹</div>
          <div class="files_add_types">
            <div class="files_add_type" @click="resourceTypeChange({ value: '' })"><i class="el-icon-s-unfold" /> 全部</div>
            <div
              v-for="item in resourceTypes"
              :key="item.value"
              class="files_add_type"
              :class="searchForm.resourceType == item.value ? 'active' : ''"
              :label="item.value"
              @click="resourceTypeChange(item)"
            >
              <i v-if="item.value == 'PICTURE'" class="el-icon-picture-outline" />
              <i v-if="item.value == 'VIDEO'" class="el-icon-video-camera" />
              <i v-if="item.value == 'LINK'" class="el-icon-link" />
              <i v-if="item.value == 'FILE'" class="el-icon-document" />
              <IconExperiment v-if="item.value == 'VR'" />
              <IconEeg v-if="item.value == 'DICOM'" /> {{ item.name }}
            </div>
          </div>
        </div>
      </div>
      <div class="fils_list initial_table fils_list_new beautifulScroll">
        <div class="real_label clearfix">
          <!--<div class="l real_label_text_new ">
						我的资源
					</div>-->
          <div class="fils_nav_new">
            <div class="fils_navarr_new">
              <div class="fils_navarritem" :class="navs.length > 0 && activeIndex > -1 ? 'active' : ''" @click="backInit()">
                <img src="@/assets/resources/fils_navarritem1.png" class="active" />
                <img src="@/assets/resources/fils_navarritem10.png" class="normal" />
              </div>
              <div class="fils_navarritem" :class="navs.length > 0 && activeIndex < navs.length - 1 ? 'active' : ''" @click="prueInit()">
                <img src="@/assets/resources/fils_navarritem2.png" class="active" />
                <img src="@/assets/resources/fils_navarritem20.png" class="normal" />
              </div>
            </div>
            <span class="pointer" @click="backAll">我的资源</span>
            <template v-for="(item, index) in navs">
              <span v-if="index <= activeIndex" :class="index > -1 && index < navs.length - 1 ? 'pointer' : ''" @click="backIndex(item, index)">
                <i class="el-icon-arrow-right"></i>{{ item.resourceName }}
              </span>
            </template>
          </div>
          <span v-if="!multipleType" class="real_controls_new like_btn unselect more" @click="openMultiple"> 批量操作<i class="el-icon-s-operation" /> </span>
          <div v-if="multipleType" class="real_selects_new r">
            <span class="real_selects_newnum">已选择{{ multipleSelects && multipleSelects.length }}个</span>
            <el-button :disabled="!multipleSelects || multipleSelects.length <= 0" @click="openDeleteMore">删除</el-button>
            <el-button type="success" :disabled="!multipleSelects || multipleSelects.length <= 0" @click="openShareMore">共享</el-button>
            <el-button type="primary" @click="changeMultiple">全选</el-button>
            <el-button type="primary" @click="closeMultiple">完成</el-button>
          </div>
        </div>
        <emptymain :emptytext="'暂无素材'" :emptyheight="'615px'" :emptylist="dataList">
          <img src="@/assets/resources/files_empty.png" />
        </emptymain>
        <div class="clearfix">
          <template v-for="(item, index) in dataList">
            <div class="fils_item_index" @click.stop>
              <div
                v-show="resourceId != item.resourceId && !multipleType"
                v-filecontextmenu="getMenus(item)"
                class="fils_itemcover_new"
                @click.stop="setItem(item)"
                @click.right.stop="showMenu(item)"
              ></div>
              <div v-show="multipleType" class="fils_itemselect_new" @click="chengSelect(item)">
                <div class="fils_item_newselect" :class="multipleSelects.includes(item.resourceId) ? 'active' : ''">
                  <i class="el-icon-check"></i>
                </div>
              </div>
              <div v-if="item.isPublic == 1" class="fils_item_newshare">已共享</div>
              <div class="fils_item_indexicon">
                <img v-if="item.fileType == 1" src="@/assets/resources/fils_nametype1new.png" />
                <img v-if="item.fileType == 2 && item.resourceType == 'VIDEO'" src="@/assets/resources/fils_nametype2new.png" />
                <img v-if="item.fileType == 2 && item.resourceType == 'PICTURE'" :src="item.url" />
                <img v-if="item.fileType == 2 && item.resourceType == 'LINK'" src="@/assets/resources/fils_nametype4new.png" />
                <img v-if="item.fileType == 2 && item.resourceType == 'VR'" src="@/assets/resources/fils_nametype5new.png" />
                <img v-if="item.fileType == 2 && item.resourceType == 'FILE'" src="@/assets/resources/fils_nametype6new.png" />
              </div>
              <div class="fils_item_newname text-ellipsis" @click.stop>
                <span v-if="resourceId != item.resourceId">
                  {{ getFileType(item) }}
                  <span>{{ item.resourceName }}</span>
                </span>
                <el-input v-if="resourceId == item.resourceId" ref="change_input" v-model="resourceName" class="change_input" @keyup.enter.native="checkEdit(index)"></el-input>
              </div>
              <div class="fils_item_indextime">
                <div class="fils_item_indexinfo_teacher">
                  <div class="fils_item_indexinfo_teachericon">
                    <img v-if="item.headPortraitUrl" :src="baseurl + item.headPortraitUrl" />
                    <img v-else src="@/assets/resources/user_infoicon.png" />
                  </div>
                  {{ item.teacherName }}
                </div>
                <div class="fils_item_index_time">
                  {{ item.updateTime }}
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div class="topic_pages">
        <el-pagination
          :hide-on-single-page="true"
          background
          :current-page="searchForm.pageNum"
          :page-size="searchForm.pageSize"
          layout="prev, pager, next, total, jumper"
          :total="searchForm.total"
          @current-change="currentChange"
        >
        </el-pagination>
      </div>
    </div>

    <div v-if="addShow" class="normal_dialog_back">
      <div class="normal_dialog reource_link">
        <div class="normal_dialog_title">
          <span>添加外链</span>
          <div class="normal_dialog_close" @click="clearCheck()">
            <i class="el-icon-close"></i>
          </div>
        </div>
        <div class="normal_dialog_body beautifulScroll topic_dialog_body">
          <el-form ref="addForm" :model="addForm" :rules="rules">
            <el-form-item label="名称" :label-width="'80px'" prop="resourceName">
              <el-col :span="22">
                <el-input v-model="addForm.resourceName" placeholder="请输入名称"></el-input>
              </el-col>
            </el-form-item>
            <el-form-item label="URL地址" :label-width="'80px'" prop="url">
              <el-col :span="22">
                <el-input v-model="addForm.url" placeholder="请输入URL地址"></el-input>
              </el-col>
            </el-form-item>
          </el-form>
        </div>
        <div class="normal_dialog_foot">
          <div class="normal_dialog_foot3 like_btn" @click="clearCheck()">取消</div>
          <div class="normal_dialog_foot1 like_btn" @click="beforeCheck">确定</div>
        </div>
      </div>
    </div>
    <div v-if="deleteShow" class="normal_dialog_back">
      <div class="normal_dialog reource_delete">
        <div class="normal_dialog_title">
          <span><i class="el-icon-warning"></i>提示</span>
          <div class="normal_dialog_close" @click="clearDelete()">
            <i class="el-icon-close"></i>
          </div>
        </div>
        <div class="normal_dialog_body beautifulScroll">
          <div class="reource_delete1">该资源删除后，所有人员都将无法查看到此资源信息</div>
          <div class="reource_delete2">确定删除该资源吗？</div>
          <!--<div class="reource_delete3">
							<span class="like_btn" @click="openAbout">查看关联记录</span>
						</div>-->
        </div>
        <div class="normal_dialog_foot">
          <div class="normal_dialog_foot3 like_btn" @click="clearDelete()">取消</div>
          <div class="normal_dialog_foot1 like_btn" @click="beforeDelete()">确定</div>
        </div>
      </div>
    </div>
    <div v-if="deleteShowAbout" class="normal_dialog_back">
      <div class="normal_dialog">
        <div class="normal_dialog_title">
          <span> <i class="el-icon-warning"></i> 素材关联记录</span>
          <div class="normal_dialog_close" @click="deleteShowAbout = false">
            <i class="el-icon-close"></i>
          </div>
        </div>
        <div class="normal_dialog_body beautifulScroll">
          <el-table :data="deleteShowList" border stripe height="100%" style="width: 750px; margin: 25px">
            <template #empty>
              <emptymain slot="empty" :emptytext="'暂无关联记录'" :emptyheight="'405px'" :emptylist="deleteShowList"> </emptymain>
            </template>
            <el-table-column prop="classroomName" label="课程名称" align="center"> </el-table-column>
            <el-table-column prop="classroomName" label="关联时间" align="center"> </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div v-if="moveShow" class="normal_dialog_back">
      <div class="normal_dialog reource_move">
        <div class="normal_dialog_title">
          <span>移动</span>
          <div class="normal_dialog_close" @click="clearMove()">
            <i class="el-icon-close"></i>
          </div>
        </div>
        <div class="normal_dialog_body beautifulScroll reource_move_body">
          <div class="reource_move_tree">
            <el-tree ref="reourceTree" :data="moveList" :props="defaultProps" node-key="id" :expand-on-click-node="false" @node-click="handleNodeClick">
              <template #default="{ node, data }">
                <div class="custom_tree_node"><img src="@/assets/resources/fils_nametype1.png" />{{ data.resourceName }}</div>
              </template>
            </el-tree>
          </div>
          <el-form ref="moveForm" :model="moveForm" :rules="rules">
            <el-form-item prop="moveSelectId">
              <el-input v-show="false" v-model="moveForm.moveSelectId"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div class="normal_dialog_foot">
          <div class="normal_dialog_foot3 like_btn" @click="clearMove()">取消</div>
          <div class="normal_dialog_foot1 like_btn" @click="beforeMove()">确定</div>
        </div>
      </div>
    </div>

    <div v-if="beforeuploadShow" class="normal_dialog_back">
      <div class="normal_dialog files_before_upload">
        <div class="normal_dialog_title">
          <span>上传附件</span>
          <div class="normal_dialog_close" @click="beforeuploadShow = false">
            <i class="el-icon-close"></i>
          </div>
        </div>
        <div class="normal_dialog_body beautifulScroll upload_dialog_body clearfix">
          <div class="upload_btn like_btn" @click.stop="openUploadFolder('uploadFile_img')">
            <div class="upload_btn_tip" @click.stop>
              <el-tooltip class="item" effect="dark" content="支持格式.jpg/.png" placement="top-start">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </div>
            <img src="@/assets/resources/upload_btn1.png" />图片素材
          </div>
          <div class="upload_btn like_btn" @click.stop="openUploadFolder('uploadFile_video')">
            <div class="upload_btn_tip" @click.stop>
              <el-tooltip class="item" effect="dark" content="支持格式.mp4" placement="top-start">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </div>
            <img src="@/assets/resources/upload_btn2.png" />视频素材
          </div>
          <div class="upload_btn like_btn" @click.stop="openUploadFolder('uploadFile_pdf')">
            <div class="upload_btn_tip" @click.stop>
              <el-tooltip class="item" effect="dark" content="支持格式.pdf" placement="top-start">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </div>
            <img src="@/assets/resources/upload_btn3.png" />文档素材
          </div>
          <!-- <div class="upload_btn like_btn" @click.stop="openAddLink"><img src="@/assets/resources/upload_btn4.png" />外部链接</div> -->
          <!-- <div class="upload_btn like_btn" @click.stop="openAddResource">
            <div class="upload_btn_tip" @click.stop>
              <el-tooltip class="item" effect="dark" content="从实验库选择实验" placement="top-start">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </div>
            <img src="@/assets/resources/upload_btn1.png" />实验资源选择
          </div> -->
          <!-- <div class="upload_btn like_btn" @click.stop=""><img src="@/assets/resources/upload_btn1.png" />医学影像片源</div> -->
        </div>
      </div>
    </div>

    <div v-if="uploadShow" class="normal_dialog_back">
      <div class="normal_dialog files_upload">
        <div class="normal_dialog_title">
          <span>上传附件</span>
        </div>
        <div class="normal_dialog_body beautifulScroll upload_dialog_body">
          <div v-for="(item, index) in viewFileUpList" class="upload_file upload_fileitem clearfix">
            <div class="upload_fileP1 text-ellipsis">
              {{ item.name }}
              <!--<div class="upload_fileP1del" @click.stop='cancelRequest(viewFileIndex)' v-if="viewFileIndex == index">取消</div>-->
              <div v-if="viewFileIndex > index" class="upload_fileP1suc">完成</div>
            </div>
            <div v-if="viewFileIndex == index" class="upload_fileP2">
              <div class="upload_fileP2info" :style="getWidth"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="viewShow" class="normal_dialog_back">
      <div class="normal_dialog files_view">
        <div class="normal_dialog_title">
          <span>文件预览</span>
          <div class="normal_dialog_close" @click="viewShow = false">
            <i class="el-icon-close"></i>
          </div>
        </div>
        <div v-if="viewShow" class="normal_dialog_body fileview_dialog_body">
          <video v-if="playType == 'VIDEO'" :src="playType == 'VIDEO' && playUrl ? playUrl : ''" autoplay controls="controls" controlsList="nodownload"></video>
          <iframe v-if="playType == 'FILE'" :src="'/pdfjs/web/viewer.html?file=' + playUrl"></iframe>
          <iframe v-if="playType == 'LINK' || playType == 'VR'" :src="playUrl"></iframe>
          <img v-if="playType == 'PICTURE'" :src="playUrl" />
        </div>
        <div class="normal_dialog_foot center">
          <div class="normal_dialog_foot1 like_btn" @click="viewShow = false">确定</div>
        </div>
      </div>
    </div>
    <div v-if="addShowResource" class="normal_dialog_back" @click.stop>
      <div class="normal_dialog topic_select">
        <div class="normal_dialog_title">
          <span>实验资源选择</span>
          <div class="normal_dialog_close" @click="clearSeCheck(false)">
            <i class="el-icon-close"></i>
          </div>
        </div>
        <div class="normal_dialog_body beautifulScroll topic_select_body">
          <div class="real_input clearfix">
            <div class="real_inputitem">
              <div class="real_inputlabel">实验名称：</div>
              <div class="real_inputit">
                <el-input v-model="searchResourceForm.name" placeholder="请输入实验名称" clearable></el-input>
              </div>
            </div>
            <div class="real_inputitem">
              <div class="real_inputlabel">所属专业：</div>
              <div class="real_inputit">
                <el-select v-model="searchResourceForm.majorId" placeholder="请选择专业" clearable @change="currentResourceChange(1)">
                  <el-option v-for="item in majors" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                </el-select>
              </div>
            </div>
            <div class="real_btn">
              <img src="@/assets/resources/real_btn1.png" class="like_btn" @click="currentResourceChange(1)" />
            </div>
            <div class="real_btn">
              <img src="@/assets/resources/real_btn2.png" class="like_btn" @click="reGetResource()" />
            </div>
          </div>
          <div class="task_selectlist">
            <el-table ref="dataResourceList" :data="dataResourceList" border stripe @selection-change="selectionChange">
              <el-table-column type="selection" width="55" />
              <el-table-column prop="vrResourceName" label="实验名称" align="center" />
              <el-table-column prop="majorName" label="所属专业" align="center" />
              <el-table-column prop="mode" label="实验模式" align="center">
                <template #default="scope">
                  {{ scope.row.mode == '1' ? '学习' : '考核' }}
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="topic_pages">
            <el-pagination
              :hide-on-single-page="true"
              background
              :current-page="searchResourceForm.pageNum"
              :page-size="searchResourceForm.pageSize"
              layout="prev, pager, next, total, jumper"
              :total="searchResourceForm.total"
              @current-change="currentResourceChange"
            >
            </el-pagination>
          </div>
        </div>
        <div class="normal_dialog_foot">
          <div class="normal_dialog_foot3 like_btn" @click="clearSeCheck(false)">取消</div>
          <div class="normal_dialog_foot1 like_btn" @click="beforeSeCheck">确定</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { putProgress } from '@/utils/resourceUpload.js'
import { allMajor } from '@/api/specialty.js'
// import { vrResourceInfoList } from '@/api/vrResource.js'
import {
  saveVrResources,
  selectResourceDetail,
  saveResourceFile,
  saveResource,
  saveLinkResource,
  updateState,
  removeResource,
  updateResourceSite,
  updateResourceName,
  resourceTree,
  resourceList,
  updateCollectState,
  updateBatchPublicState,
  resourceBatchRemove
} from '@/api/resource.js'
export default {
  data() {
    return {
      navs: [],
      activeIndex: -1,
      parentId: 0,
      dataList: [],
      viewShow: false,
      playType: '',
      playUrl: '',
      moveShow: false,
      moveList: [],
      moveForm: {
        moveId: '',
        moveSelectId: ''
      },
      defaultProps: {
        children: 'children',
        label: 'resourceName'
      },
      deleteShowAbout: false,
      deleteShowList: [],
      deleteShow: false,
      deleteId: '',
      addShowResource: false,
      addSelectResource: [],
      searchResourceForm: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        vrResourceName: '',
        majorId: ''
      },
      dataResourceList: [],
      resourceId: '',
      resourceName: '',
      addShow: false,
      addForm: {
        resourceName: '',
        url: ''
      },
      multipleType: false,
      multipleSelects: [],
      viewFileUpList: [],
      viewFileIndex: 0,
      viewFileMax: 0,
      uploadShow: false,
      beforeuploadShow: false,
      uploadForm: {
        uploadPress: 0,
        uploadFlag: false
      },
      rules: {
        resourceName: [
          {
            required: true,
            message: '请输入外链名称'
          }
        ],
        url: [
          {
            required: true,
            message: '请输入URL地址'
          }
        ],
        moveSelectId: [
          {
            required: true,
            message: '请选择移动到的文件夹'
          }
        ]
      },
      rightClickInfo: {},
      searchForm: {
        resourceName: '',
        resourceType: '',
        orderBy: 'updateTime',
        ascDesc: 'desc',
        pageNum: 1,
        pageSize: 18,
        total: 0
      },
      resourceTypes: [
        {
          name: '图片',
          value: 'PICTURE'
        },
        {
          name: '视频',
          value: 'VIDEO'
        },
        // {
        //   name: '外部链接',
        //   value: 'LINK'
        // },
        {
          name: '文档',
          value: 'FILE'
        }
        // {
        //   name: '仿真实验',
        //   value: 'VR'
        // },
        // {
        //   name: '影像片源',
        //   value: 'DICOM'
        // }
      ],
      majors: []
    }
  },
  computed: {
    getWidth() {
      return 'width:' + this.uploadForm.uploadPress + '%;'
    }
  },
  watch: {
    parentId(val) {
      this.currentChange(1)
    }
  },
  created() {
    this.getDataList()
    this.getMajors()
  },
  mounted() {},
  methods: {
    getFileType(item) {
      // eslint-disable-next-line eqeqeq
      if (item.fileType == 1) {
        return
      } else {
        switch (item.resourceType) {
          case 'VIDEO':
            return '【视频】'
          case 'PICTURE':
            return '【图片】'
          case 'LINK':
            return '【链接】'
          case 'FILE':
            return '【文档】'
          case 'VR':
            return '【实验】'
          case 'DICOM':
            return '【片源】'
          default:
            break
        }
      }
    },
    async getMajors() {
      const { data } = await allMajor()
      this.majors = data
    },
    changeMultiple() {
      if (this.multipleSelects && this.multipleSelects.length > 0) {
        this.multipleSelects = []
      } else {
        this.dataList.map((item) => {
          if (this.multipleSelects.indexOf(item.resourceId) <= -1) {
            this.multipleSelects.push(item.resourceId)
          }
        })
      }
    },
    openMultiple() {
      this.multipleType = true
      this.multipleSelects = []
    },
    closeMultiple() {
      this.multipleType = false
      this.multipleSelects = []
    },
    chengSelect(item) {
      var delIndex = this.multipleSelects.indexOf(item.resourceId)
      if (delIndex > -1) {
        this.multipleSelects.splice(delIndex, 1)
      } else {
        this.multipleSelects.push(item.resourceId)
      }
    },
    openfiles(activeTab) {
      this.$emit('openfiles', activeTab)
    },
    getMenus(item, type) {
      var contextmenus = []
      var contextmenusEdit = [
        {
          text: '编辑',
          handler: this.handleritEdit
        },
        {
          text: '删除',
          handler: this.handleritDelete
        },
        {
          text: '移动',
          handler: this.handleritMove
        }
      ]
      var sharemenus = [
        {
          text: '分享',
          handler: this.handleritShare
        }
      ]
      var unSharemenus = [
        {
          text: '取消分享',
          handler: this.handleritUnShare
        }
      ]
      var collectmenus = [
        {
          text: '收藏',
          handler: this.handleritCollect
        }
      ]
      var unCollectmenus = [
        {
          text: '取消收藏',
          handler: this.handleritUnCollect
        }
      ]
      contextmenus = contextmenus.concat(contextmenusEdit)
      // eslint-disable-next-line eqeqeq
      if (item.isPublic == 1) {
        contextmenus = contextmenus.concat(unSharemenus)
      } else {
        contextmenus = contextmenus.concat(sharemenus)
      }
      // eslint-disable-next-line eqeqeq
      if (item.isCollect == 1) {
        contextmenus = contextmenus.concat(unCollectmenus)
      } else {
        contextmenus = contextmenus.concat(collectmenus)
      }
      return contextmenus
    },
    handleritEdit() {
      this.openEdit(this.rightClickInfo)
    },
    handleritDelete() {
      this.openDelete(this.rightClickInfo)
    },
    handleritShare() {
      this.openShare(this.rightClickInfo)
    },
    handleritUnShare() {
      this.openShare(this.rightClickInfo)
    },
    handleritCollect() {
      this.openCollect(this.rightClickInfo)
    },
    handleritUnCollect() {
      this.openCollect(this.rightClickInfo)
    },
    handleritMove() {
      this.openMove(this.rightClickInfo)
    },
    showMenu(item, event) {
      this.rightClickInfo = item
    },
    openCollect(item) {
      // eslint-disable-next-line eqeqeq
      var message = item.isCollect == '1' ? '是否取消收藏该资源?' : '是否收藏该资源?'
      this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      })
        .then(() => {
          updateCollectState({
            resourceId: item.resourceId,
            // eslint-disable-next-line eqeqeq
            state: item.isCollect == '1' ? 2 : 1
          }).then((res) => {
            // eslint-disable-next-line eqeqeq
            if (res.code == '200') {
              this.$message({
                message: res.message,
                type: 'success'
              })
              this.currentChange(1)
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        })
        .catch(() => {})
    },
    openShareMore() {
      if (this.multipleSelects && this.multipleSelects.length > 0) {
        this.$confirm('是否分享所选资源？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        })
          .then(() => {
            updateBatchPublicState(this.multipleSelects).then((res) => {
              // eslint-disable-next-line eqeqeq
              if (res.code == '200') {
                this.$message({
                  message: res.message,
                  type: 'success'
                })
                this.multipleSelects = []
                this.currentChange(1)
              } else {
                this.$message({
                  message: res.message,
                  type: 'error'
                })
              }
            })
          })
          .catch(() => {})
      } else {
        this.closeMultiple()
      }
    },
    openShare(item) {
      // eslint-disable-next-line eqeqeq
      var message = item.isPublic == '1' ? '是否取消分享该资源?' : '是否分享该资源?'
      this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      })
        .then(() => {
          updateState({
            resourceId: item.resourceId,
            // eslint-disable-next-line eqeqeq
            state: item.isPublic == '1' ? 2 : 1
          }).then((res) => {
            // eslint-disable-next-line eqeqeq
            if (res.code == '200') {
              this.$message({
                message: res.message,
                type: 'success'
              })
              this.currentChange(1)
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        })
        .catch(() => {})
    },
    beforeMove() {
      if (this.postData) {
        return
      }
      this.postData = true
      setTimeout(() => {
        this.postData = false
      }, 1000)
      this.$refs.moveForm.validate((valid) => {
        if (valid) {
          var data = {
            resourceId: this.moveForm.moveId,
            parentId: this.moveForm.moveSelectId
          }
          updateResourceSite(data).then((res) => {
            // eslint-disable-next-line eqeqeq
            if (res.code == '200') {
              this.$message({
                type: 'success',
                message: res.message
              })
              this.currentChange(1)
              this.clearMove()
            } else {
              this.$message({
                type: 'error',
                message: res.message
              })
            }
          })
        }
      })
    },
    handleNodeClick(data) {
      this.moveForm.moveSelectId = data.id
    },
    clearMove() {
      this.moveForm.moveId = ''
      this.moveForm.moveSelectId = ''
      this.$nextTick(() => {
        this.$refs.moveForm.clearValidate()
        this.moveShow = false
      })
    },
    openMove(item) {
      resourceTree().then((res) => {
        this.moveList = [
          {
            resourceName: '我的资源',
            id: '0',
            children: res.data || []
          }
        ]
        this.moveForm.moveId = item.resourceId
        this.moveShow = true
      })
    },
    openDeleteMore() {
      if (this.multipleSelects && this.multipleSelects.length > 0) {
        this.$confirm('是否删除所选资源？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        })
          .then(() => {
            resourceBatchRemove(this.multipleSelects).then((res) => {
              // eslint-disable-next-line eqeqeq
              if (res.code == '200') {
                this.$message({
                  message: res.message,
                  type: 'success'
                })
                this.multipleSelects = []
                this.currentChange(1)
              } else {
                this.$message({
                  message: res.message,
                  type: 'error'
                })
              }
            })
          })
          .catch(() => {})
      } else {
        this.closeMultiple()
      }
    },
    openDelete(item) {
      this.deleteId = item.resourceId
      this.deleteShow = true
    },
    beforeDelete() {
      removeResource({
        resourceId: this.deleteId
      }).then((res) => {
        // eslint-disable-next-line eqeqeq
        if (res.code == '200') {
          this.$message({
            type: 'success',
            message: res.message
          })
          this.deleteShow = false
          this.deleteId = ''
          this.currentChange(1)
        } else {
          this.$message({
            type: 'error',
            message: res.message
          })
        }
      })
    },
    clearDelete() {
      this.deleteShow = false
      this.deleteId = ''
    },
    openAbout() {
      this.deleteShowAbout = true
    },
    clearSeCheck(flag) {
      this.$refs.dataResourceList.clearSelection()
      this.addSelectResource = []
      this.addShowResource = false
      if (flag) {
        this.beforeuploadShow = false
      }
    },
    beforeSeCheck() {
      if (!this.addSelectResource || this.addSelectResource.length <= 0) {
        this.$message({
          type: 'error',
          message: '请选择实验！'
        })
        return
      }
      var saveData = []
      this.addSelectResource.map((item) => {
        saveData.push({
          vrResourceId: item.vrResourceId,
          vrResourceDetailId: item.vrResourceDetailId,
          resourceName: item.vrResourceName,
          parentId: 0,
          url: item.url
        })
      })
      saveVrResources(saveData).then((res) => {
        // eslint-disable-next-line eqeqeq
        if (res.code == '200') {
          this.$message({
            type: 'success',
            message: res.message
          })
          this.clearSeCheck(true)
          this.currentChange(1)
        } else {
          this.$message({
            type: 'error',
            message: res.message
          })
        }
      })
    },
    selectionChange(val) {
      this.addSelectResource = val
    },
    reGetResource() {
      this.addSelectResource = []
      this.searchResourceForm.vrResourceName = ''
      this.searchResourceForm.majorId = ''
      this.currentResourceChange(1)
    },
    currentResourceChange(pageNum) {
      this.addSelectResource = []
      this.searchResourceForm.pageNum = pageNum
      this.getResourceDataList()
    },
    getResourceDataList() {
      // vrResourceInfoList(this.searchResourceForm).then(async (res) => {
      //   this.dataResourceList = res.data.list
      //   this.searchResourceForm.total = res.data.total
      // })
    },
    openAddResource() {
      this.reGetResource()
      this.addShowResource = true
    },
    beforeCheck() {
      if (this.postData) {
        return
      }
      this.postData = true
      setTimeout(() => {
        this.postData = false
      }, 1000)
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          var data = Object.assign({}, this.addForm, {
            parentId: this.parentId
          })
          saveLinkResource(data).then((res) => {
            // eslint-disable-next-line eqeqeq
            if (res.code == '200') {
              this.$message({
                type: 'success',
                message: res.message
              })
              this.clearCheck()
              this.currentChange(1)
            } else {
              this.$message({
                type: 'error',
                message: res.message
              })
            }
          })
        }
      })
    },
    openAddLink() {
      this.beforeuploadShow = false
      this.addShow = true
    },
    clearCheck() {
      this.addForm.resourceName = ''
      this.addForm.url = ''
      this.$nextTick(() => {
        this.$refs.addForm.clearValidate()
        this.addShow = false
      })
    },
    openAdd() {
      saveResourceFile({
        resourceName: '新建文件夹',
        parentId: this.parentId
      }).then((res) => {
        this.currentChange(1)
        // this.$nextTick(() => {
        // this.openEdit(this.dataList[0])
        // })
      })
    },
    checkEdit(index) {
      // eslint-disable-next-line eqeqeq
      if (this.resourceName != this.dataList[index].resourceName) {
        updateResourceName({
          resourceId: this.resourceId,
          resourceName: this.resourceName
        }).then((res) => {
          this.dataList[index].resourceName = this.resourceName
          this.closeEdit()
        })
      } else {
        this.closeEdit()
      }
    },
    closeEdit() {
      this.resourceId = ''
      this.resourceName = ''
    },
    openEdit(item) {
      this.resourceId = item.resourceId
      this.resourceName = item.resourceName
      this.$nextTick(() => {
        if (this.$refs.change_input.length) {
          this.$refs.change_input[0].focus()
        } else {
          this.$refs.change_input.focus()
        }
      })
    },
    backAll() {
      this.activeIndex = -1
      this.parentId = 0
    },
    prueInit() {
      if (this.navs.length > 0 && this.activeIndex < this.navs.length - 1) {
        this.activeIndex++
        this.parentId = this.navs[this.activeIndex].resourceId
      }
    },
    backIndex(item, index) {
      if (index > -1 && index < this.navs.length - 1) {
        this.navs.splice(index + 1, this.navs.length - 1 - index)
        this.activeIndex = this.navs.length - 1
        this.parentId = item.resourceId
      }
    },
    backInit() {
      if (this.navs.length > 0 && this.activeIndex > -1) {
        this.activeIndex--
        if (this.activeIndex > -1) {
          this.parentId = this.navs[this.activeIndex].resourceId
        } else {
          this.parentId = 0
        }
      }
    },
    setItem(item) {
      // eslint-disable-next-line eqeqeq
      if (item.fileType == 1) {
        if (this.activeIndex > -1 && this.activeIndex < this.navs.length - 1) {
          this.navs.splice(this.activeIndex + 1, this.navs.length - 1 - this.activeIndex)
          this.navs.push(item)
          this.activeIndex = this.navs.length - 1
          this.parentId = item.resourceId
        } else {
          // eslint-disable-next-line eqeqeq
          if (this.activeIndex == -1) {
            this.navs = []
          }
          this.navs.push(item)
          this.activeIndex = this.navs.length - 1
          this.parentId = item.resourceId
        }
      } else {
        selectResourceDetail({
          resourceId: item.resourceId
        }).then((res) => {
          if (item.resourceType === 'VIDEO' || item.resourceType === 'PICTURE') {
            this.playUrl = res.data.url
          }
          if (item.resourceType === 'LINK' || item.resourceType === 'VR') {
            this.playUrl = res.data.url
          }
          if (item.resourceType === 'FILE') {
            this.playUrl = encodeURIComponent(res.data.url)
          }
          this.playType = item.resourceType
          this.viewShow = true
        })
      }
    },
    openbeforeUpload() {
      this.beforeuploadShow = true
    },
    openUploadFolder(uploadFilebox) {
      if (this.uploadForm.uploadFlag) {
        this.$message({
          title: '提示',
          message: '正在上传,请稍后。。',
          type: 'info'
        })
      } else {
        this.$refs[uploadFilebox].click()
        this.beforeuploadShow = false
      }
    },
    beforeformRequest(event) {
      var files = event.target.files
      if (files.length > 0) {
        var viewFileUpList = []
        for (var i = 0; i < files.length; i++) {
          var item = files[i]
          var resourceType = item.name.substring(item.name.lastIndexOf('.') + 1)
          if (resourceType === 'pdf') {
            item.resourceType = 'FILE'
          }
          if (resourceType === 'jpg' || resourceType === 'png' || resourceType === 'gif') {
            item.resourceType = 'PICTURE'
          }
          if (resourceType === 'mp4') {
            item.resourceType = 'VIDEO'
          }
          if (item.resourceType) {
            item.resourceSize = (item.size / 1024 / 1024).toFixed(2)
            item.resourceName = item.name
            item.overState = false
            viewFileUpList.push(item)
          }
        }
        this.viewFileUpList = viewFileUpList
        this.viewFileIndex = 0
        this.viewFileMax = viewFileUpList.length - 1
        this.uploadShow = true
        this.formRequest()
      }
    },
    formRequest() {
      var file = this.viewFileUpList[this.viewFileIndex]
      const key = `resources/${file.name}`
      putProgress(
        key,
        file,
        (progress) => {
          this.uploadForm.uploadFlag = true
          this.uploadForm.uploadPress = parseInt(progress * 100)
        },
        (source) => {
          this.viewFileUpList[this.viewFileIndex].source = source
        }
      )
        .then((res) => {
          this.uploadForm.uploadPress = 0
          this.viewFileUpList[this.viewFileIndex].overState = true
          // this.addFile(key, file)
          console.log(res)

          this.addFile(res.data.data[0], file)
        })
        .catch((err) => {
          console.log(err)
        })
    },
    cancelRequest(index) {
      this.viewFileUpList[index].source.cancel('已取消上传')
    },
    addFile(key, file) {
      var data = Object.assign({}, file, {
        parentId: this.parentId,
        url: key
      })
      saveResource(data).then((res) => {
        // eslint-disable-next-line eqeqeq
        if (res.code == '200') {
          this.getDataList()
          this.nextUpload()
        }
      })
    },
    nextUpload() {
      var viewFileIndex = this.viewFileIndex
      var viewFileMax = this.viewFileMax
      if (viewFileIndex < viewFileMax) {
        this.viewFileIndex++
        this.formRequest()
      } else {
        this.clearSectionVideo()
      }
    },
    clearSectionVideo() {
      this.viewFileUpList = []
      this.viewFileIndex = 0
      this.viewFileMax = 0
      this.uploadForm.uploadFlag = false
      this.uploadForm.uploadPress = 0
      var array = ['uploadFile_img', 'uploadFile_video', 'uploadFile_pdf']
      array.map((uploadFilebox) => {
        this.$refs[uploadFilebox].value = ''
      })
      this.uploadShow = false
    },
    reGet() {
      this.searchForm.resourceName = ''
      this.searchForm.resourceType = ''
      this.searchForm.orderBy = 'updateTime'
      this.searchForm.ascDesc = 'desc'
      this.currentChange(1)
    },
    sortChange() {
      // eslint-disable-next-line eqeqeq
      if (this.searchForm.ascDesc == 'desc') {
        this.searchForm.ascDesc = 'asc'
        this.currentChange(1)
        return
      }
      // eslint-disable-next-line eqeqeq
      if (this.searchForm.ascDesc == 'asc') {
        this.searchForm.ascDesc = 'desc'
        this.currentChange(1)
        return
      }
    },
    resourceTypeChange(item) {
      // eslint-disable-next-line eqeqeq
      if (this.searchForm.resourceType == item.value) {
        this.searchForm.resourceType = ''
      } else {
        this.searchForm.resourceType = item.value
      }
      this.currentChange(1)
    },
    currentChange(pageNum) {
      this.searchForm.pageNum = pageNum
      this.getDataList()
    },
    getDataList() {
      console.log(this.searchForm)

      this.dataList = []
      this.$nextTick(() => {
        var data = Object.assign({}, this.searchForm, {
          parentId: this.parentId,
          type: 1
        })
        resourceList(data).then(async (res) => {
          this.dataList = res.data.list
          this.searchForm.total = res.data.total
        })
      })
    }
  }
}
</script>
