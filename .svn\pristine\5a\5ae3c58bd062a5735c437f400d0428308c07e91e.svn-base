<template>
  <div class="app-container">
    <el-form ref="searchForm" label-width="80px" inline>
      <el-form-item label="客户名称:">
        <el-input v-model="queryInfo.customerName" size="small" placeholder="请输入客户名称" clearable></el-input>
      </el-form-item>
      <el-form-item label="客户负责人:" label-width="90px">
        <el-input v-model="queryInfo.realName" size="small" placeholder="请输入客户负责人" clearable></el-input>
      </el-form-item>
      <el-form-item label="客户来源:">
        <el-select v-model="queryInfo.source" placeholder="请选择客户来源" size="small" clearable>
          <el-option :value="1" label="转介绍"> </el-option>
          <el-option :value="2" label="线上咨询"> </el-option>
          <el-option :value="3" label="电话咨询"> </el-option>
          <el-option :value="4" label="线下展会"> </el-option>
          <el-option :value="5" label="外出拜访"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="客户级别:">
        <el-select v-model="queryInfo.level" placeholder="请选择客户级别" size="small" clearable>
          <el-option :value="1" label="重点"> </el-option>
          <el-option :value="2" label="中等"> </el-option>
          <el-option :value="3" label="普通"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间:">
        <el-date-picker v-model="date" size="small" type="daterange" range-separator="→" start-placeholder="开始日期" end-placeholder="结束日期" clearable @change="datePickerChange"> </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="success" size="small" @click="getSecretCustomerCustomerList">查询</el-button>
        <el-button type="primary" size="small" plain @click="reset">重置</el-button>
        <el-button type="primary" size="small" @click="addDialog = true">新增客户</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" style="width: 100%" border>
      <el-table-column prop="customerName" label="客户名称" width="width" align="center"> </el-table-column>
      <el-table-column prop="source" label="客户来源" width="width" align="center">
        <template v-slot="{ row }">
          <span>{{ row.source | source }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="level" label="客户级别" width="width" align="center">
        <template v-slot="{ row }">
          <span>{{ row.level | source }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="regionName" label="所属地域" width="width" align="center"> </el-table-column>
      <el-table-column prop="phone" label="联系方式" width="width" align="center"> </el-table-column>
      <el-table-column prop="realName" label="客户负责人" width="width" align="center"> </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="width" align="center"> </el-table-column>
      <el-table-column prop="updateTime" label="更新时间" width="width" align="center"> </el-table-column>
      <el-table-column label="操作" width="width" align="center">
        <template v-slot="{ row }">
          <el-button type="warning" size="small" @click="edit(row)">修改</el-button>
          <el-button type="primary" size="small" @click="details(row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-if="list.length > 0" layout="total,prev, pager, next" style="margin-top: 15px; text-align: right" :page-sizes="[5, 10, 15, 20]" background :total="total" :page-size.sync="queryInfo.pageSize" :current-page.sync="queryInfo.pageNum" @size-change="getSecretCustomerCustomerList" @current-change="getSecretCustomerCustomerList" />

    <!-- 添加和修改的dialog -->
    <el-dialog :title="showTitle" :visible.sync="addDialog" width="550px" :close-on-click-modal="false" @close="close">
      <el-form ref="form" :model="formInfo" label-width="90px" :rules="rules">
        <el-form-item label="客户名称:" prop="customerName" class="name_input">
          <el-input v-model="formInfo.customerName" placeholder="请输入客户名称" size="small" maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="客户来源:" prop="source" class="level">
          <el-select v-model="formInfo.source" placeholder="请选择客户来源" size="small">
            <el-option :value="1" label="转介绍"> </el-option>
            <el-option :value="2" label="线上咨询"> </el-option>
            <el-option :value="3" label="电话咨询"> </el-option>
            <el-option :value="4" label="线下展会"> </el-option>
            <el-option :value="5" label="外出拜访"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="客户级别:" prop="level" class="level">
          <el-select v-model="formInfo.level" placeholder="请选择客户级别" size="small">
            <el-option :value="1" label="重点"> </el-option>
            <el-option :value="2" label="中等"> </el-option>
            <el-option :value="3" label="普通"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属区域:" prop="regionId" class="level">
          <el-select ref="selecteltree" v-model="area" placeholder="请选择区域" size="small" clearable @change="searchArea">
            <el-option v-for="item in secretTree" :key="item.id" :label="item.name" :value="item.id" />
            <!-- <el-tree :data="secretTree" :props="defaultProps" default-expand-all @node-click="handleNodeClick"> </el-tree> -->
          </el-select>
          <el-select v-model="formInfo.regionId" style="margin-left: 15px" placeholder="请选择片区" size="small" clearable>
            <el-option v-for="item in district" :key="item.regionId" :label="item.name" :value="item.regionId"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="联系方式:" prop="phone" class="name_input">
          <el-input v-model="formInfo.phone" placeholder="请输入联系方式" size="small" maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="联系地址:" class="name_input">
          <el-input v-model="formInfo.address" placeholder="请输入联系地址" size="small" maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="邮箱:" class="name_input" prop="email">
          <el-input v-model="formInfo.email" placeholder="请输入邮箱" size="small" maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="备注:" class="name_input">
          <el-input v-model="formInfo.remark" placeholder="请输入备注" type="textarea" size="small"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="addDialog = false">取 消</el-button>
        <el-button type="primary" @click="subData">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { secretCustomerCustomerList, secretCustomerGetArea, secretCustomerSaveCustomer, secretCustomerCustomerDetails, secretCustomerUpdateCustomer } from '@/api/clientele'
import { secretSecretTree } from '@/api/area'

import { formatDate, checkTel, checkEmail } from '@/filters'

export default {
  name: 'Clientele',
  data() {
    return {
      queryInfo: {
        customerName: null,
        source: null,
        level: null,
        realName: null,
        startTime: null,
        endTime: null,
        pageNum: 1,
        pageSize: 10
      },
      list: [],
      total: null,
      date: null,
      addDialog: false,
      formInfo: {
        customerName: null,
        source: null,
        level: null,
        regionId: null,
        phone: null,
        address: null,
        email: null,
        remark: null
      },
      rules: {
        customerName: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
        source: [{ required: true, message: '请选择客户来源', trigger: 'change' }],
        level: [{ required: true, message: '请选择客户级别', trigger: 'change' }],
        regionId: [{ required: true, message: '请选择区域片区', trigger: 'change' }],
        phone: [
          { required: true, message: '请输入联系方式', trigger: 'blur' },
          {
            validator: checkTel,
            trigger: 'blur'
          }
        ],
        email: [
          {
            validator: checkEmail,
            trigger: 'blur'
          }
        ]
      },
      secretTree: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      area: null,
      district: [],
      region: null
    }
  },
  computed: {
    showTitle() {
      return this.formInfo.customerId ? '修改客户' : '新增客户'
    }
  },
  created() {
    this.getSecretCustomerCustomerList()
    this.getSecretTree()
  },
  methods: {
    async getSecretCustomerCustomerList() {
      const { data } = await secretCustomerCustomerList(this.queryInfo)
      this.total = data.total
      this.list = data.list
      console.log(data)
    },
    // 时间搜索的格式化
    datePickerChange(val) {
      if (val) {
        this.queryInfo.startTime = formatDate(val[0])
        this.queryInfo.endTime = formatDate(val[1], 'yyyy-MM-dd') + ' 23:59:59'
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getSecretCustomerCustomerList()
    },
    async edit(row) {
      const { data } = await secretCustomerCustomerDetails({ customerId: row.customerId })
      this.formInfo = { ...data }
      this.area = data.name.split('-')[0]
      this.searchArea(parseInt(data.regionId.split(',')[0]))
      this.formInfo.regionId = parseInt(data.regionId.split(',')[1])

      this.addDialog = true
      console.log(this.formInfo)
    },
    details(row) {
      this.$router.push(`/clientele/details/${row.customerId}`)
    },
    // 重置搜索
    reset() {
      this.queryInfo = {
        customerName: null,
        source: null,
        level: null,
        realName: null,
        startTime: null,
        endTime: null,
        pageNum: 1,
        pageSize: 10
      }
      this.date = null
      this.getSecretCustomerCustomerList()
    },
    // 获取区域树
    async getSecretTree() {
      const { data } = await secretSecretTree()
      this.secretTree = data[0].children
    },
    //  查询区域
    async searchArea(id) {
      this.formInfo.regionId = null
      if (!id) {
        this.district = []
        return
      }
      const { data } = await secretCustomerGetArea({ regionId: id })
      this.district = data
    },
    handleNodeClick(node) {
      this.area = node.name
      this.$refs['selecteltree'].blur()
      this.searchArea(node.id)
    },
    async subData() {
      await this.$refs['form'].validate()
      if (this.formInfo.customerId) {
        await secretCustomerUpdateCustomer(this.formInfo)
        this.$message.success('修改客户成功!')
        this.getSecretCustomerCustomerList()
        this.addDialog = false
      } else {
        await secretCustomerSaveCustomer(this.formInfo)
        this.$message.success('新增客户成功!')
        this.getSecretCustomerCustomerList()
        this.addDialog = false
      }
    },
    close() {
      this.formInfo = {
        customerName: null,
        source: null,
        level: null,
        regionId: null,
        phone: null,
        address: null,
        email: null,
        remark: null
      }
      this.area = null
      this.$refs['form'].resetFields()
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep {
  .el-dialog__body {
    padding: 30px 50px;
  }
  .el-form-item__content {
    .el-date-editor--daterange.el-input,
    .el-date-editor--daterange.el-input__inner,
    .el-date-editor--timerange.el-input,
    .el-date-editor--timerange.el-input__inner {
      width: 274px;
      background: #ffffff;
      border-radius: 4px 4px 4px 4px;
    }
    // .el-range-input {
    //   background-color: #fff;
    // }
  }
  .name_input {
    .el-input__inner {
      width: 355px;
    }
  }
  .level {
    .el-input__inner {
      width: 170px;
    }
  }
}
</style>
