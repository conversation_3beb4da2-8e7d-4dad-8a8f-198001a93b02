<template>
  <div v-loading.fullscreen="fullscreenLoading" element-loading-text="数据保存中" element-loading-background="rgba(0, 0, 0, 0.8)" class="app-container">
    <el-row :gutter="10" class="top">
      <el-col :span="24">
        <i class="el-icon-location"></i>
        <span>当前位置：销售方案 /</span>
        <span>{{ pageType }}</span>
      </el-col>
    </el-row>
    <div class="box">
      <div class="header">
        {{ pageType }}
      </div>
      <el-form ref="form" class="addForm" :model="formInfo" :rules="rules" label-width="130px">
        <el-form-item label="方案名称:" prop="name" class="name">
          <el-input v-model="formInfo.name" placeholder="请输入方案名称" maxlength="40"></el-input>
        </el-form-item>
        <el-form-item label="客户名称:" prop="customerId" class="customerId">
          <el-select v-model="formInfo.customerId" placeholder="请选择客户" @focus="getCustomerList">
            <el-option v-for="item in customerList" :key="item.customerId" :label="item.customerName" :value="item.customerId"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="客户预算（万元）:" prop="budget" class="budget">
          <el-input v-model="formInfo.budget" placeholder="请输入客户预算（万元）" maxlength="40"></el-input>
        </el-form-item>
        <el-form-item label="选择产品:" class="product">
          <div class="addProduct" @click="addProductDialog = true">+添加产品</div>

          <div class="productList">
            <div class="total">合计（万元）: 410</div>
            <el-table :data="formInfo.productReqs" style="width: 864px" border header-cell-class-name="tableHeader">
              <el-table-column prop="prop" label="label" width="width"> </el-table-column>
            </el-table>
          </div>
        </el-form-item>
        <el-form-item label="备注:" class="remark">
          <el-input v-model="formInfo.remark" placeholder="请输入产品备注" type="textarea" resize="none" maxlength="500"></el-input>
        </el-form-item>
      </el-form>
      <div class="footer">
        <span @click="$router.push('/tSalesPlan')">取 消</span>
        <span @click="save">保 存</span>
      </div>
    </div>

    <!-- 选择产品的弹窗 -->
    <el-dialog title="添加产品" :visible.sync="addProductDialog" width="800px" @open="getSalesPlanProductList">
      <el-form ref="form" :model="productForm" label-width="100px" inline>
        <el-form-item label="产品名称:" prop="name" label-width="80px">
          <el-input v-model="productForm.name" placeholder="请输入产品名称" maxlength="40"></el-input>
        </el-form-item>
        <el-form-item label="产品类别:" prop="type" class="type">
          <el-radio-group v-model="productForm.type">
            <el-radio :label="1">软件</el-radio>
            <el-radio :label="2">硬件</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <span class="form_button search" @click="getSalesPlanProductList">查询</span>
          <span class="form_button reset" @click="reset">重置</span>
        </el-form-item>
      </el-form>
      <el-table :data="productList" style="width: 100%" border header-cell-class-name="tableHeader" @selection-change="handleSelectionChange">
        <el-table-column align="center" type="selection" width="55"> </el-table-column>
        <el-table-column align="center" prop="name" label="产品名称" width="width"> </el-table-column>
        <el-table-column align="center" label="产品类型" width="width">
          <template v-slot="{ row }">
            <span>{{ row.type === 1 ? '软件' : '硬件' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="offer" label="单价（万元）" width="width"> </el-table-column>
        <el-table-column align="center" prop="offer" label="单位" width="width">
          <template v-slot="{ row }">
            <span>{{ row.unit | softwareUnit }}</span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-if="productList.length > 0"
        style="margin-top: 32px; text-align: right"
        layout="total,  prev, pager, next"
        background
        :total="total"
        :page-size.sync="productList.pageSize"
        :current-page.sync="productList.pageNum"
        @size-change="getSalesPlanProductList"
        @current-change="getSalesPlanProductList"
      />
      <div slot="footer">
        <el-button @click="addProductDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { secretCustomeAllCustomer } from '@/api/clientele'
import { tSalesPlanProductList } from '@/api/tSalesPlan'
export default {
  name: '',
  data() {
    var checkNum = (rule, value, callback) => {
      if (!value) {
        callback()
      }
      if (!isNaN(parseInt(value))) {
        callback()
      } else {
        return callback(new Error('请输入数字'))
      }
    }
    return {
      fullscreenLoading: false,
      formInfo: {
        name: null, // 方案名称
        customerId: null, // 客户id
        budget: null, // 客户预算
        totalMoney: null, // 方案总价
        remark: null,
        productReqs: [] // 产品
      },
      rules: {
        name: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
        customerId: [{ required: true, message: '请选择客户', trigger: 'change' }],
        budget: [{ validator: checkNum, trigger: 'blur' }]
      },
      customerList: [],
      addProductDialog: false,
      productForm: {
        name: null, // 产品名称
        type: null, // 1 软件 2 硬件
        pageNum: 1,
        pageSize: 6
      },
      productList: [],
      total: 0,
      selectData: []
    }
  },
  computed: {
    pageType() {
      return '添加方案'
    }
  },
  methods: {
    async getCustomerList() {
      const { data } = await secretCustomeAllCustomer()
      this.customerList = data
      console.log(data)
    },
    save() {},
    // 选择产品 ----------------
    async getSalesPlanProductList() {
      const { data } = await tSalesPlanProductList(this.productForm)
      this.productList = data.list
      this.productList.forEach((item) => {
        this.$set(item, 'productId', (Math.random() + new Date().getTime()).toString(32).slice(0, 8))
      })
      this.total = data.total
    },
    reset() {
      this.productForm = {
        name: null, // 产品名称
        type: null, // 1 软件 2 硬件
        pageNum: 1,
        pageSize: 6
      }
      this.getSalesPlanProductList()
    },
    handleSelectionChange(val) {
      this.selectData = val
      console.log(val)
    },
    confirm() {
      if (!this.formInfo.productReqs.length) {
        this.formInfo.productReqs = [...this.selectData]
      } else {
        const productKeys = this.formInfo.productReqs.map((item) => item.productId)
        this.selectData.forEach((item) => {
          if (!productKeys.includes(item.productId)) {
            this.formInfo.productReqs.push(item)
          }
        })
      }
      this.addProductDialog = false
    }
    // 选择产品 ---------------- over
  }
}
</script>

<style scoped lang="scss">
.app-container {
  position: relative;
  padding: 0;
  background-color: #e8eaed;
  min-height: 100%;
  padding-top: 58px;
  padding-bottom: 10px;
  .top {
    position: absolute;
    top: 0px;
    width: 100%;
    background-color: #e8eaed;
    padding: 24px 0 16px 0;
    z-index: 999;
    .el-col {
      i {
        font-size: 14px;
        color: #657081;
      }
      span {
        &:first-of-type {
          margin: 0 5px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #657081;
        }
        &:last-of-type {
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
      }
    }
  }
  .box {
    width: 1754px;
    max-height: 791px;
    background: #ffffff;
    padding-bottom: 40px;
    border-radius: 8px 8px 8px 8px;
    .header {
      padding: 24px 0 11px 48px;
      border-bottom: 1px solid #eeeeef;
      font-size: 16px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #0b1a44;
    }
  }
  ::v-deep {
    .addForm {
      padding-left: 488px;
      padding-top: 24px;
      border-bottom: 1px solid #eeeeef;
      .el-form-item {
        margin-bottom: 28px;
      }
      .productList {
        .total {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
        }
      }
    }
    .el-form-item__label {
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #0b1a44;
    }
    .el-input__inner {
      height: 40px;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #d8dbe1;
      &::placeholder {
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #b1bac7;
      }
    }
    .name {
      .el-input__inner {
        width: 436px;
      }
    }
    .customerId,
    .budget {
      .el-input__inner {
        width: 318px;
      }
    }
    .remark {
      .el-textarea__inner {
        width: 682px;
        height: 138px;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #d8dbe1;
      }
    }
    .addProduct {
      width: 116px;
      height: 38px;
      line-height: 38px;
      background: #3464e0;
      font-size: 16px;
      color: #ffffff;
      text-align: center;
      border-radius: 4px;
      cursor: pointer;
      &:hover {
        background: #355fce;
      }
    }
    .el-radio-group {
      .el-radio__input.is-checked .el-radio__inner {
        // background: #3464e0;
        background: #fff;
        width: 18px;
        height: 18px;
        border-color: #3464e0;
        // border: none;
        &::after {
          background-color: #3464e0;
          width: 8px;
          height: 8px;
        }
      }
      .el-radio__inner {
        width: 18px;
        height: 18px;
      }
      .el-radio__input.is-checked + .el-radio__label {
        color: #0b1a44;
      }
      .el-radio__label {
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #0b1a44;
      }
    }
    .form_button {
      display: inline-block;
      margin-right: 16px;
      width: 68px;
      height: 28px;
      line-height: 28px;
      border-radius: 4px 4px 4px 4px;
      font-size: 14px;
      font-weight: 400;
      text-align: center;
      cursor: pointer;
    }
    .search {
      margin-left: 25px;
      background: #3464e0;
      color: #fff;
    }
    .reset {
      color: #b1bac7;
      border: 1px solid #b1bac7;
    }
    .tableHeader {
      background: #f0f0f0;
      font-size: 14px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #0b1a44;
    }
    .el-table td.el-table__cell,
    .el-table th.el-table__cell.is-leaf {
      border-color: #a3a8bb;
    }
    .el-table--border,
    .el-table--group {
      border-color: #a3a8bb;
    }
    .el-table--border::after,
    .el-table--group::after,
    .el-table::before {
      background-color: #a3a8bb;
    }
    .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
      background: #eff1f3;
    }
  }
  .footer {
    display: flex;
    justify-content: center;
    margin-top: 40px;
    span {
      display: inline-block;
      width: 260px;
      height: 46px;
      line-height: 46px;
      font-size: 14px;
      font-weight: 400;
      border-radius: 4px 4px 4px 4px;
      text-align: center;
      cursor: pointer;
    }
    & > span:first-of-type {
      background: #ffffff;
      border: 1px solid #d8dbe1;
      color: #a3a8bb;
    }
    & > span:last-of-type {
      margin-left: 32px;
      background: #3464e0;
      font-weight: bold;
      color: #ffffff;
    }
  }
}
</style>
