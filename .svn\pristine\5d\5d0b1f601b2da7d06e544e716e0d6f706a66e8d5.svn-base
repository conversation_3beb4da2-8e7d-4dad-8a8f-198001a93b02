<template>
  <div class="app-container">
    <el-row :gutter="10" class="top">
      <el-col :span="24">
        <i class="el-icon-location"></i>
        <span>当前位置：合同管理 /</span>
        <span>合同详情/合同货款信息管理</span>
      </el-col>
    </el-row>
    <div class="bodyBox">
      <section class="leftBox">
        <h2>合同货款信息管理</h2>
        <el-form ref="form" :model="formInfo" label-width="150px" :disabled="contractType == 2">
          <el-form-item label="质保金(元):">
            <el-input v-model.number="formInfo.qualityMoney" type="number" placeholder="请输入质保金" maxlength="10"></el-input>
          </el-form-item>
          <el-form-item label="质保金应收日期:" class="dateType">
            <el-date-picker v-model="formInfo.qualityTime" type="date" value-format="yyyy-MM-dd" placeholder="质保金应收日期"> </el-date-picker>
          </el-form-item>
          <el-form-item label="投标保证金(元):">
            <el-input v-model.number="formInfo.bidMoney" type="number" placeholder="请输入质保金" maxlength="10"></el-input>
          </el-form-item>
          <el-form-item label="投标保证金应收日期:" class="dateType">
            <el-date-picker v-model="formInfo.bidTime" type="date" value-format="yyyy-MM-dd" placeholder="质保金应收日期"> </el-date-picker>
          </el-form-item>
          <el-form-item label="履约保证金(元):">
            <el-input v-model.number="formInfo.performanceMoney" type="number" placeholder="请输入质保金" maxlength="10"></el-input>
          </el-form-item>
          <el-form-item label="履约保证金应收日期:" class="dateType">
            <el-date-picker v-model="formInfo.performanceTime" type="date" value-format="yyyy-MM-dd" placeholder="质保金应收日期"> </el-date-picker>
          </el-form-item>
          <el-form-item label="货款(元):">
            <el-input v-model.number="formInfo.money" :disabled="isDisabledMoney" type="number" placeholder="请输入货款" maxlength="10"></el-input>
          </el-form-item>
        </el-form>
        <el-alert title="注意:货款保存后无法修改，请谨慎操作" type="warning" :closable="false"> </el-alert>
        <div v-if="contractType != 2" class="subButton">
          <span @click="submit"><i class="el-icon-success"></i> 提交</span>
        </div>
      </section>
      <section class="rightBox">
        <header>
          <span @click="goBack">返回</span>
        </header>
        <div class="main">
          <el-form ref="form" :model="queryInfo" label-width="90px" inline>
            <el-form-item label="收款类型:">
              <el-select v-model="queryInfo.type" placeholder="请选择收款类型">
                <el-option label="货款" :value="1"> </el-option>
                <el-option label="质保金" :value="2"> </el-option>
                <el-option label="投标保证金" :value="3"> </el-option>
                <el-option label="履约保证金" :value="4"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="收款金额:">
              <el-input v-model.number="queryInfo.money" type="number" placeholder="请输入收款金额" maxlength="10"></el-input>
            </el-form-item>
            <el-form-item label="时间:">
              <el-date-picker v-model="time" type="daterange" value-format="yyyy-MM-dd" range-separator="→" start-placeholder="开始日期" end-placeholder="结束日期" @change="datePickerChange">
              </el-date-picker>
            </el-form-item>
          </el-form>
          <el-row type="flex" justify="end">
            <el-button type="primary" size="small" @click="getList">查询</el-button>
            <el-button type="primary" size="small" plain @click="reset">重置</el-button>
          </el-row>
          <section class="tableBox">
            <el-row v-if="contractType != 2" type="flex" justify="end" style="margin-bottom: 30px">
              <el-button type="primary" icon="el-icon-plus" @click="addReceipt">新增收款</el-button>
            </el-row>
            <el-table :data="list" style="width: 100%" border header-cell-class-name="headerCell" cell-class-name="allCell">
              <el-table-column label="序号" width="80" type="index" align="center"> </el-table-column>
              <el-table-column prop="createTime" label="收款时间" width="width" align="center"> </el-table-column>
              <el-table-column label="收款类型" width="width" align="center">
                <template v-slot="{ row }">
                  <span>{{ row.type | collectionType }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="money" label="收款金额(元)" width="width" align="center"> </el-table-column>
              <el-table-column prop="remainMoney" label="剩余金额(元)" width="width" align="center" class-name="remainMoneyColumn"> </el-table-column>
            </el-table>

            <el-pagination
              v-if="list.length > 0"
              layout="total,prev, pager, next"
              style="margin-top: 40px; text-align: center"
              :page-sizes="[5, 10, 15, 20]"
              background
              :total="total"
              :page-size.sync="queryInfo.pageSize"
              :current-page.sync="queryInfo.pageNum"
              @size-change="getList"
              @current-change="getList"
            />
          </section>
        </div>
      </section>
    </div>

    <!-- 新增收款弹窗 -->
    <el-dialog title="新增收款" custom-class="collectionDialog" :visible.sync="collectionDialog" width="580px">
      <div>
        <el-form ref="collectionForm" :model="collectionInfo" label-width="110px" :rules="rules">
          <el-form-item label="收款类型:" prop="type">
            <el-select v-model="collectionInfo.type" placeholder="请选择收款类型" @change="collectionType">
              <el-option label="货款" :value="1"> </el-option>
              <el-option label="质保金" :value="2"> </el-option>
              <el-option label="投标保证金" :value="3"> </el-option>
              <el-option label="履约保证金" :value="4"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="收款金额(元):" prop="money">
            <el-input v-model.number="collectionInfo.money" type="number" placeholder="请输入收款金额" maxlength="10"></el-input>
          </el-form-item>
          <!-- <el-form-item label="剩余金额(元):">
            <el-input v-model.number="collectionInfo.remainMoney" type="number" placeholder="请输入收款金额" maxlength="10"></el-input>
          </el-form-item>
          <el-form-item label="备注:">
            <el-input v-model="collectionInfo.remark" type="textarea" placeholder="请输入备注" maxlength="10" resize="none"></el-input>
          </el-form-item> -->
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="dialogClose">取 消</el-button>
        <el-button type="primary" @click="dialogSubmit">提 交</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { contractMoneyUpdate, contractReceiveList, contractReceiveAdd, contractDetail, contractReceiveJudgeAdd } from '@/api/contractNew'
import { collectionType } from '@/filters'
export default {
  name: 'MoneyInfo',
  data() {
    return {
      formInfo: {
        qualityMoney: null, // 质保金(元)
        qualityTime: null, // 质保金应收日期
        bidMoney: null, // 投标保证金(元)
        bidTime: null, // 投标保证金应收日期
        performanceMoney: null, // 履约保证金(元)
        performanceTime: null, // 履约保证金应收日期
        money: null, //  货款(元)
        remark: null // 备注
      },
      isDisabledMoney: false,
      queryInfo: {
        pageNum: 1,
        pageSize: 8,
        moneyId: null,
        type: null,
        money: null,
        startTime: null,
        endTime: null
      },
      time: null,
      list: [],
      total: 0,
      collectionDialog: false,
      collectionInfo: {
        contractId: null,
        moneyId: null,
        type: null,
        money: null
        // remark: null,
        // remainMoney: null
      },
      rules: {
        type: [{ required: true, message: '请选择类型', trigger: 'change' }],
        money: [{ required: true, message: '请输入收款金额', trigger: 'blur', type: 'number' }]
      },
      contractType: 1
    }
  },
  computed: {
    // 合同id
    contractId() {
      return this.$route.params.contractId
    },
    // 施工id
    moneyId() {
      return this.$route.params.moneyId
    }
  },
  created() {
    this.getList()
    this.getDetails()
  },
  methods: {
    goBack() {
      this.$router.push(`/newContract/details/${this.contractId}`)
    },
    async getDetails() {
      const { data } = await contractDetail({ id: this.contractId })
      this.contractType = data.type
      const moneyDetail = data.moneyDetailDto
      this.formInfo.qualityMoney = moneyDetail.qualityMoney ? parseInt(moneyDetail.qualityMoney) : null
      this.formInfo.qualityTime = moneyDetail.qualityTime
      this.formInfo.bidMoney = moneyDetail.bidMoney ? parseInt(moneyDetail.bidMoney) : null
      this.formInfo.bidTime = moneyDetail.bidTime
      this.formInfo.performanceMoney = moneyDetail.performanceMoney ? parseInt(moneyDetail.performanceMoney) : null
      this.formInfo.performanceTime = moneyDetail.performanceTime
      this.formInfo.money = parseInt(moneyDetail.money)
      if (moneyDetail.money) {
        this.isDisabledMoney = true
      }
      this.formInfo.remark = moneyDetail.remark
      console.log(data)
    },
    submit() {
      this.formInfo.contractId = this.contractId
      this.formInfo.moneyId = this.moneyId
      const loading = this.$loading({
        text: '数据保存中，请稍后...',
        background: 'rgba(0,0,0,0.7)'
      })
      contractMoneyUpdate(this.formInfo)
        .then(() => {
          loading.close()
          this.$message.success('数据保存成功!')
          this.close()
        })
        .catch(() => {
          loading.close()
        })
    },
    datePickerChange(val) {
      if (val) {
        this.queryInfo.startTime = val[0]
        this.queryInfo.endTime = val[1]
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getList()
    },
    reset() {
      this.queryInfo = {
        pageNum: 1,
        pageSize: 8,
        moneyId: null,
        type: null,
        money: null,
        startTime: null,
        endTime: null
      }
      this.time = null
      this.getList()
    },

    async getList() {
      this.queryInfo.moneyId = this.moneyId
      const { data } = await contractReceiveList(this.queryInfo)
      this.list = data.list
      this.total = data.total
    },
    addReceipt() {
      this.collectionDialog = true
    },
    collectionType(val) {
      contractReceiveJudgeAdd({ moneyId: this.moneyId, type: val }).then((res) => {
        if (res.data === 0) {
          const typeText = collectionType(val)
          this.$message.error(`货款信息中的${typeText}为空，不可选择`)
          this.$nextTick(() => {
            this.collectionInfo.type = null
          })
        }
        console.log(res.data)
      })
    },
    dialogClose() {
      this.collectionDialog = false
      this.collectionInfo = {
        contractId: null,
        moneyId: null,
        type: null,
        money: null,
        remark: null,
        remainMoney: null
      }
      this.$refs['collectionForm'].resetFields()
    },
    dialogSubmit() {
      this.$refs['collectionForm'].validate((val) => {
        if (val) {
          this.collectionInfo.contractId = this.contractId
          this.collectionInfo.moneyId = this.moneyId
          const loading = this.$loading({
            text: '数据保存中，请稍后...',
            background: 'rgba(0,0,0,0.7)'
          })
          contractReceiveAdd(this.collectionInfo)
            .then(() => {
              loading.close()
              this.$message.success('新增收款成功！')
              this.dialogClose()
              this.getList()
            })
            .catch(() => {
              loading.close()
            })
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.app-container {
  position: relative;
  padding: 0;
  background-color: #e8eaed;
  min-height: 100%;
  height: 100%;
  padding-top: 58px;
  padding-right: 40px;
  padding-bottom: 14px;
  .top {
    position: absolute;
    top: 0px;
    width: 100%;
    background-color: #e8eaed;
    padding: 24px 0 16px 0;
    z-index: 999;
    .el-col {
      i {
        font-size: 14px;
        color: #657081;
      }
      span {
        &:first-of-type {
          margin: 0 5px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #657081;
        }
        &:last-of-type {
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
      }
    }
  }
  .bodyBox {
    display: flex;
    height: 100%;
    background: #fbfbfb;
    border-radius: 8px;
    overflow: hidden;

    .leftBox {
      position: relative;
      width: 656px;
      background: #ffffff;
      padding-left: 85px;
      padding-top: 30px;
      padding-right: 50px;
      h2 {
        position: relative;
        padding-left: 16px;
        margin-bottom: 25px;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: bold;
        font-size: 16px;
        color: #0b1a44;
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 2px;
          height: 40px;
          background: #3464e0;
        }
      }
      ::v-deep {
        .el-form-item__label {
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: bold;
          font-size: 14px;
          color: #0b1a44;
        }
        .el-input__inner {
          border-color: #e9ebef;
        }
        .dateType {
          .el-date-editor {
            width: 100%;
            .el-input__inner {
              background: #f5f5f5;
              // color: #0b1a44;
            }
          }
        }
      }
      .subButton {
        position: absolute;
        left: 50%;
        bottom: 40px;
        transform: translateX(-50%);
        display: flex;
        justify-content: center;
        align-items: center;
        span {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 114px;
          height: 38px;
          font-size: 14px;
          border-radius: 4px;
          cursor: pointer;
          i {
            margin-right: 7px;
          }
        }
        & > span:first-of-type {
          background: #f2f4ff;
          color: #868b9f;
          i {
            color: #868b9f;
          }
          &:hover {
            background: #e8ebf8;
            i {
              color: #888da1;
            }
          }
        }
        & > span:last-of-type {
          margin-left: 32px;
          background: #3464e0;
          color: #ffffff;
          i {
            color: #ffffff;
          }
          &:hover {
            background: #355fce;
          }
        }
      }
    }
    .rightBox {
      flex: 1;
      border-left: 1px solid #e4e4e4;
      header {
        display: flex;
        justify-content: flex-end;
        padding-right: 52px;
        padding-top: 20px;
        padding-bottom: 20px;
        border-bottom: 1px solid #e5e5e5;
        span {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 62px;
          height: 30px;
          border: 1px solid #d8dbe1;
          border-radius: 4px;
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: 400;
          font-size: 14px;
          color: #868b9f;
          cursor: pointer;
        }
      }
      .main {
        padding: 40px 50px;
        .tableBox {
          width: 100%;
          height: 539px;
          margin-top: 20px;
          padding: 33px 48px;
          background: #ffffff;
          border-radius: 4px;
          border: 1px solid #d8dbe1;
        }
        ::v-deep {
          .el-form-item__label {
            font-family: Microsoft YaHei, Microsoft YaHei;
            font-weight: 400;
            font-size: 14px;
            color: #0b1a44;
          }
          .el-input__inner {
            height: 36px;
            line-height: 36px;
          }
          .el-date-editor {
            width: 300px;
          }
          .remainMoneyColumn {
            .cell {
              color: #3464e0;
            }
          }
          .headerCell {
            height: 42px;
            padding: 0;
            background: #f8f8f8;
            .cell {
              font-family: Source Han Sans CN;
              font-weight: 400;
              font-size: 14px;
              color: #657081;
            }
          }
          .allCell {
            padding: 0;
            height: 42px;
            font-family: Microsoft YaHei, Microsoft YaHei;
            font-weight: bold;
            font-size: 14px;
            color: #0b1a44;
          }
        }
      }
    }
  }

  ::v-deep {
    .collectionDialog {
      .el-dialog__header {
        border-bottom: 1px solid #eeeeef;
        .el-dialog__title {
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: bold;
          font-size: 18px;
          color: #0b1a44;
        }
      }
      .el-dialog__body {
        padding: 24px 0;
        padding-bottom: 0;
        .el-form {
          padding-left: 60px;
          padding-right: 60px;
          .el-form-item__label {
            font-family: Microsoft YaHei, Microsoft YaHei;
            font-weight: 400;
            font-size: 14px;
            color: #0b1a44;
          }
          .el-select {
            width: 100%;
            .el-input__inner {
              width: 100%;
              background: #f5f5f5;
              color: #0b1a44;
            }
          }
        }
      }
      .el-dialog__footer {
        & > div {
          display: flex;
          justify-content: center;
          .el-button {
            padding: 0;
            width: 114px;
            height: 38px;
            border-radius: 4px;
            &:first-of-type {
              margin-right: 10px;
              background: #f2f4ff;
              color: #868b9f;
              &:hover {
                color: #868b9f;
                border-color: #f2f4ff;
              }
            }
          }
        }
      }
    }
  }
}
</style>
