// 小微秘 客户管理
import request from '@/utils/request'
/** 新增客户管理 */
export function secretCustomerSaveCustomer(data) {
  return request({
    url: '/secret/customer/saveCustomer',
    method: 'POST',
    data
  })
}
/** 修改客户管理 */
export function secretCustomerUpdateCustomer(data) {
  return request({
    url: '/secret/customer/updateCustomer',
    method: 'POST',
    data
  })
}

/** 客户管理列表 */
export function secretCustomerCustomerList(params) {
  return request({
    url: '/secret/customer/customerList',
    method: 'GET',
    params
  })
}

/** 客户管理详情 - 客户信息*/
export function secretCustomerCustomerDetails(params) {
  return request({
    url: '/secret/customer/customerDetails',
    method: 'GET',
    params
  })
}

/** 保存客户联系人 */
export function secretCustomerSaveContacts(data) {
  return request({
    url: '/secret/customer/saveContacts',
    method: 'POST',
    data
  })
}

/** 客户联系人修改 */
export function secretCustomerUpdateContacts(data) {
  return request({
    url: '/secret/customer/updateContacts',
    method: 'POST',
    data
  })
}

/** 查询区域*/
export function secretCustomerGetArea(params) {
  return request({
    url: '/secret/customer/getArea',
    method: 'GET',
    params
  })
}
/** 客户管理详情 - 客户联系人列表*/
export function secretCustomerDetailsContactsList(params) {
  return request({
    url: '/secret/customer/detailsContactsList',
    method: 'GET',
    params
  })
}

/** 移除客户联系人*/
export function secretCustomeRemoveContacts(params) {
  return request({
    url: '/secret/customer/removeContacts',
    method: 'GET',
    params
  })
}
/** 客户管理详情 - 商机信息*/
export function secretCustomeDetailsChanceList(params) {
  return request({
    url: '/secret/customer/detailsChanceList',
    method: 'GET',
    params
  })
}
/** 客户管理详情 - 日志信息*/
export function secretCustomeDetailsLogList(params) {
  return request({
    url: '/secret/customer/detailsLogList',
    method: 'GET',
    params
  })
}
/** 查询所有客户*/
export function secretCustomeAllCustomer(params) {
  return request({
    url: '/secret/customer/allCustomer',
    method: 'GET',
    params
  })
}

/** 查询所有客户联系人*/
export function secretCustomeAllContacts(params) {
  return request({
    url: '/secret/customer/allContacts',
    method: 'GET',
    params
  })
}
