import request from '@/utils/request'

// 获取用户列表
export function getList(params) {
  return request({
    url: '/system/sysUser/userList',
    method: 'get',
    params
  })
}
// 添加用户
export function addSysUser(data) {
  return request({
    url: '/system/sysUser/addSysUser',
    method: 'post',
    data
  })
}
// 删除用户
export function delUser(id) {
  return request({
    url: '/system/sysUser/deleteSysUser',
    method: 'delete',
    params: {
      userId: id
    }
  })
}
// 修改用户
export function updateSysUser(data) {
  return request({
    url: '/system/sysUser/updateSysUser',
    method: 'post',
    data
  })
}
// 重置密码
export function passwordReset(userId) {
  return request({
    url: '/system/sysUser/passwordReset',
    method: 'put',
    params: {
      userId
    }
  })
}
// 修改密码
export function changePassword(data) {
  return request({
    url: '/system/sysUser/changePassword',
    method: 'post',
    data
  })
}
// 上传文件
export function uploadFile(data) {
  return request({
    url: '/system/upload/file',
    method: 'POST',
    data
  })
}
// 根据id查询用户信息
export function selectSysUser(userId) {
  return request({
    url: '/system/sysUser/selectSysUser',
    method: 'get',
    params: {
      userId
    }
  })
}
