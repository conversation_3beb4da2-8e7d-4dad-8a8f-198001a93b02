<template>
  <div class="Home">
    <div class="top">
      <div class="left">
        <img src="@/assets/login/logo.png" alt="" />
        <img src="@/assets/Home/text.png" alt="" />
      </div>
      <div class="right">
        <iframe scrolling="no" src="https://tianqiapi.com/api.php?style=ty&skin=durian&color=fff" frameborder="0" width="200" height="75" allowtransparency="true" class="weather"></iframe>
        <span
          class="time"
        >{{ year }} <span style="margin: 0 5px 0 5px">{{ Day }}</span> {{ time }}</span>
        <span class="realName">欢迎您,{{ realName }}</span>
        <el-dropdown class="avatar-container" trigger="click">
          <div class="avatar-wrapper">
            <el-avatar shape="circle" :size="48" fit="cover" :src="avatar" @error="true">
              <img src="@/assets/login/logo.png" />
            </el-avatar>
            <i class="el-icon-caret-bottom" style="color: #fff" />
          </div>
          <el-dropdown-menu slot="dropdown" class="user-dropdown">
            <el-dropdown-item @click.native="changePassword"> 修改密码 </el-dropdown-item>
            <el-dropdown-item divided @click.native="logout"> 退出系统 </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <div class="center">
      <div class="title">系统入口</div>
      <div class="content">
        <img v-if="keyList.includes('basicInfo')" src="@/assets/Home/basicInfo.png" alt="" @click="checkedMode(1)" />
        <img v-if="keyList.includes('project_outer')" src="@/assets/Home/project_outer.png" alt="" @click="checkedMode(4)" />
        <img src="@/assets/Home/task.png" alt="" />
        <img v-if="keyList.includes('process')" src="@/assets/Home/process.png" alt="" @click="checkedMode(2)" />
        <img src="@/assets/Home/xiaoWeiMi.png" alt="" />
        <img v-if="keyList.includes('meeting')" src="@/assets/Home/meeting.png" alt="" @click="checkedMode(3)" />
        <img v-if="keyList.includes('training_repository')" src="@/assets/Home/training_repository.png" alt="" @click="checkedMode(5)" />
        <img v-if="keyList.includes('institution')" src="@/assets/Home/system.png" alt="" @click="checkedMode(6)" />
      </div>
    </div>
    <el-dialog title="修改密码" :visible.sync="changePasswordDialog" width="420px" append-to-body>
      <div>
        <el-form ref="passwordForm" :model="changePasswordInfo" label-width="80px" :rules="passwordRules">
          <el-form-item label="原密码" prop="oldPassword">
            <el-input v-model="changePasswordInfo.oldPassword" type="password"></el-input>
          </el-form-item>
          <el-form-item label="新密码" prop="newPassword">
            <el-input v-model="changePasswordInfo.newPassword" type="password"></el-input>
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input v-model="changePasswordInfo.confirmPassword" type="password"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="changePasswordDialog = false">取 消</el-button>
        <el-button type="primary" @click="onClickPassword">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { formatDate } from '@/filters'
import { changePassword } from '@/api/systemUser'
import { getPassword } from '@/utils/auth'

export default {
  name: '',
  data() {
    return {
      year: null,
      time: null,
      Day: null,
      times: null,
      changePasswordDialog: false,
      changePasswordInfo: {
        oldPassword: null,
        newPassword: null,
        confirmPassword: null
      },
      passwordRules: {
        oldPassword: [{ required: true, message: '请输入原密码', trigger: 'blur' }],
        newPassword: [{ required: true, message: '请输入新密码', trigger: 'blur' }],
        confirmPassword: [{ required: true, message: '请确认新密码', trigger: 'blur' }]
      }
    }
  },
  computed: {
    ...mapGetters(['keyList', 'realName', 'avatar', 'userId'])
  },
  mounted() {
    this.getTime()
  },
  beforeDestroy() {
    clearInterval(this.times)
    this.times = null
  },
  methods: {
    checkedMode(type) {
      if (type === 1) {
        this.$store.commit('checkedData/set_data_type', type)
        this.$nextTick(() => {
          this.$router.push('/basicInfo/home')
        })
      } else if (type === 2) {
        this.$store.commit('checkedData/set_data_type', type)
        this.$nextTick(() => {
          this.$router.push('/process/management')
        })
      } else if (type === 3) {
        this.$store.commit('checkedData/set_data_type', type)
        this.$nextTick(() => {
          this.$router.push('/meeting')
        })
      } else if (type === 4) {
        this.$store.commit('checkedData/set_data_type', type)
        this.$nextTick(() => {
          if (this.keyList.includes('project')) {
            this.$router.push('/project')
          } else {
            this.$router.push('/aftermarket')
          }
        })
      } else if (type === 5) {
        this.$store.commit('checkedData/set_data_type', type)
        this.$nextTick(() => {
          if (this.keyList.includes('training')) {
            this.$router.push('/training')
          } else {
            this.$router.push('/repository')
          }
        })
      } else if (type === 6) {
        this.$store.commit('checkedData/set_data_type', type)
        this.$nextTick(() => {
          this.$router.push('/institution')
        })
      }
    },
    getTime() {
      this.year = formatDate(new Date(), 'yyyy/MM/dd')
      const wk = new Date().getDay()
      const weeks = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
      this.Day = weeks[wk]
      this.times = setInterval(() => {
        this.time = formatDate(new Date(), 'hh:mm:ss')
      }, 1000)
    },
    changePassword() {
      this.changePasswordDialog = true
    },
    onClickPassword() {
      console.log(this.name)
      this.$refs['passwordForm'].validate(async (val) => {
        if (val) {
          if (this.changePasswordInfo.oldPassword === getPassword()) {
            if (this.changePasswordInfo.confirmPassword === this.changePasswordInfo.newPassword) {
              // 通过
              const res = await changePassword({
                userId: this.userId,
                newPassword: this.changePasswordInfo.newPassword,
                oldPassword: this.changePasswordInfo.oldPassword
              })
              this.$message.success('修改密码成功')
              this.changePasswordDialog = false
              // 修改密码后退出登录
              this.$nextTick(() => {
                this.logout()

                this.$message.warning('请重新登录')
              })
              console.log(res)
            } else {
              this.$message.warning('两次密码不一致')
            }
          } else {
            this.$message.warning('原密码不正确')
          }
        }
      })
    },
    async logout() {
      await this.$store.dispatch('user/logout')
      this.$router.push(`/login`)
    }
  }
}
</script>

<style scoped lang="scss">
.Home {
  width: 100%;
  height: 100%;
  padding-top: 65px;
  padding-left: 72px;
  padding-right: 72px;
  background: url('../assets/Home/bg.png') no-repeat;
  background-size: cover;
  overflow: hidden;
  .top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      img {
        &:first-of-type {
          width: 50px;
          height: 50px;
          margin-right: 15px;
        }
      }
    }
    .right {
      display: flex;
      align-items: center;
      .time {
        margin-right: 30px;
        font-size: 18px;
        font-family: D-DIN Exp-DINExp-Bold, D-DIN Exp-DINExp;
        // font-weight: bold;
        color: #ffffff;
      }
      .realName {
        font-size: 16px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #ffffff;
        margin-right: 16px;
      }
    }
  }
  .center {
    max-width: calc(1452px - 28px);
    min-width: 1240px;
    margin: 0 auto;
    margin-top: 166px;
    .title {
      margin-bottom: 27px;
      font-size: 22px;
      font-weight: bold;
      background: linear-gradient(180deg, #e5e5e5 0%, #ffffff 41%, #d2d2d2 100%);
      -webkit-background-clip: text;
      color: transparent;
    }
    .content {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      width: 100%;
      img {
        max-width: 332px;
        max-height: 212px;
        margin-bottom: 56px;
        margin-right: 32px;
        cursor: pointer;
      }
      // max-width: 1600px;
      // margin: 0 auto;
      // margin-top: 300px;
    }
  }
}
.weather {
  ::v-deep {
    color: #fff !important;
  }
}
@media screen and (min-width: 1240px) and (max-width: 1585px) {
  .Home {
    padding-top: 30px;
    .center {
      margin-top: 50px !important;
      .content {
        img {
          margin-bottom: 30px;
          &:nth-of-type(3n) {
            margin-right: 0;
          }
        }
      }
    }
  }
}
@media screen and (min-width: 1585px) {
  .center {
    .content {
      img {
        &:nth-of-type(4n) {
          margin-right: 0;
        }
        &:nth-of-type(3n) {
          margin-right: 32px;
        }
      }
    }
  }
}
</style>
