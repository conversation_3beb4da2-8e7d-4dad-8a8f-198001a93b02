<template>
  <div class="app-container">
    <el-form ref="searchForm" label-width="80px" inline>
      <el-form-item v-if="activeName === '日志记录'" label="客户名称:">
        <el-input v-model="queryInfo.customerName" size="small" placeholder="请输入客户名称" clearable></el-input>
      </el-form-item>
      <el-form-item v-if="activeName === '日志记录'" label="创建时间:">
        <el-date-picker v-model="date" size="small" type="daterange" range-separator="→" start-placeholder="开始日期" end-placeholder="结束日期" clearable @change="datePickerChange"> </el-date-picker>
      </el-form-item>
      <el-form-item v-if="activeName === '日志统计'" label="时间:" label-width="50px">
        <el-date-picker v-model="logStatisInfo.time" type="date" placeholder="选择日期" size="small" value-format="yyyy-MM-dd"> </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button v-if="activeName === '日志记录'" type="success" size="small" @click="getSecretLog_logList">查询</el-button>
        <el-button v-else type="success" size="small" @click="getlogStatisList">查询</el-button>
        <el-button type="primary" size="small" plain @click="reset">重置</el-button>
        <el-button type="primary" size="small" @click="addDialog = true">写日志</el-button>
      </el-form-item>
    </el-form>

    <el-tabs v-model="activeName" type="card">
      <el-tab-pane label="日志记录" name="日志记录">
        <el-table :data="list" style="width: 100%" border @row-click="goDetails">
          <el-table-column prop="name" label="标题" width="width" align="center"> </el-table-column>
          <el-table-column prop="customerNames" label="客户名称" width="width" align="center"> </el-table-column>
          <el-table-column prop="contactsNames" label="客户联系人" width="width" align="center"> </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="width" align="center"> </el-table-column>
        </el-table>
        <el-pagination v-if="list.length > 0" layout="total,prev, pager, next" style="margin-top: 15px; text-align: right" :page-sizes="[5, 10, 15, 20]" background :total="total" :page-size.sync="queryInfo.pageSize" :current-page.sync="queryInfo.pageNum" @size-change="getSecretLog_logList" @current-change="getSecretLog_logList" />
      </el-tab-pane>
      <el-tab-pane label="日志统计" name="日志统计">
        <el-table :data="logStatisList" style="width: 100%" border>
          <el-table-column prop="realName" label="姓名" width="width" align="center"> </el-table-column>
          <el-table-column v-for="item in headerList" :key="item.label" align="center" :prop="item.value" :label="item.label" width="width">
            <template v-slot="{ row }">
              <span v-if="row[item.value] === '0'">未提交</span>
              <span v-else>已提交</span>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination v-if="logStatisList.length > 0" layout="total,prev, pager, next" style="margin-top: 15px; text-align: right" :page-sizes="[5, 10, 15, 20]" background :total="logStatisTotal" :page-size.sync="logStatisInfo.pageSize" :current-page.sync="logStatisInfo.pageNum" @size-change="getlogStatisList" @current-change="getlogStatisList" />
      </el-tab-pane>
    </el-tabs>
    <!-- 写日志dialog -->
    <el-dialog title="写日志" :visible.sync="addDialog" width="1000px" @close="addDialogClose">
      <el-form ref="form" :model="formInfo" label-width="80px" :rules="rules">
        <el-form-item label="时间:" prop="time">
          <el-select v-model="time" placeholder="请选择时间" size="small" @change="selectChange">
            <el-option label="今天" value="今天"></el-option>
            <el-option label="昨天" value="昨天"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="拜访内容:">
          <el-input v-model="formInfo.visitContent" type="textarea" placeholder="请输入拜访内容"></el-input>
        </el-form-item>
        <el-form-item label="拜访结果:">
          <el-input v-model="formInfo.visitResult" type="textarea" placeholder="请输入拜访结果"></el-input>
        </el-form-item>
        <el-form-item label="跟踪客户:">
          <el-row type="flex" justify="end" style="margin-bottom: 15px">
            <el-button type="primary" size="small" @click="addCustomer">添加客户</el-button>
          </el-row>
          <el-table :data="trackCustomer" style="width: 100%" border>
            <el-table-column align="center" prop="prop" label="客户名称" width="width">
              <template v-slot="{ row }">
                <el-select v-model="row.customerId" placeholder="请选择客户" size="small" @focus="getSecretCustomerCustomerList">
                  <el-option v-for="item in allCustomer" :key="item.customerId" :value="item.customerId" :label="item.customerName"> </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="prop" label="客户联系人" width="width">
              <template v-slot="{ row }">
                <el-select v-model="row.contactsName" placeholder="请选择联系人" size="small" value-key="contactsId" @focus="getUserList(row)" @change="contactsChange($event, row)">
                  <el-option v-for="item in userList" :key="item.contactsId" :label="item.contactsName" :value="item"> </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="majorName" label="所属专业" width="width"> </el-table-column>
            <el-table-column align="center" prop="phone" label="联系方式" width="width"> </el-table-column>
            <el-table-column align="center" prop="prop" label="操作" width="width">
              <template v-slot="{ $index }">
                <el-button type="danger" size="small" @click="delCustomer($index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-form-item label="明日计划:">
          <el-input v-model="formInfo.planContent" type="textarea" placeholder="请输入明日计划"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="addDialog = false">取 消</el-button>
        <el-button type="primary" @click="submitLog">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 查看详情 -->
    <el-drawer :title="detailsInfo.name" :visible.sync="drawer" direction="rtl">
      <el-descriptions class="margin-top" :column="1" direction="vertical">
        <el-descriptions-item label="拜访内容">{{ detailsInfo.visitContent }}</el-descriptions-item>
        <el-descriptions-item label="拜访结果">{{ detailsInfo.visitResult }}</el-descriptions-item>
        <el-descriptions-item label="跟踪客户">
          <el-table :data="detailsInfo.customers" style="width: 100%" border>
            <el-table-column align="center" prop="customerName" label="客户名称" width="width"> </el-table-column>
            <el-table-column align="center" prop="contactsName" label="客户联系人" width="width"> </el-table-column>
            <el-table-column align="center" prop="majorName" label="所属专业" width="width"> </el-table-column>
            <el-table-column align="center" prop="majorName" label="联系方式" width="width"> </el-table-column>
          </el-table>
        </el-descriptions-item>
        <el-descriptions-item label="明日计划">
          {{ detailsInfo.planContent }}
        </el-descriptions-item>
      </el-descriptions>
    </el-drawer>
  </div>
</template>

<script>
import { secretCustomeAllCustomer, secretCustomeAllContacts } from '@/api/clientele'
import { secretLog_logList, secretLog_saveLog, secretLog_logStatis } from '@/api/secretLog'
import { formatDate } from '@/filters'
import { mapGetters } from 'vuex'
export default {
  name: 'SecretLog',
  data() {
    return {
      activeName: '日志记录',
      // 日志记录
      queryInfo: {
        customerName: null,
        startTime: null,
        endTime: null,
        createUser: null,
        pageNum: 1,
        pageSize: 10
      },
      date: null,
      list: [],
      total: 0,
      addDialog: false,
      formInfo: {
        name: null,
        time: null,
        visitContent: null,
        visitResult: null,
        planContent: null,
        remark: null,
        contactsIds: []
      },
      time: null,
      trackCustomer: [],
      allCustomer: [],
      userList: [],
      rules: {
        time: [{ required: true, message: '请选择时间', trigger: 'change' }]
      },
      // 日志统计
      logStatisInfo: {
        time: null,
        pageNum: 1,
        pageSize: 10
      },
      logStatisList: [],
      headerList: [],
      logStatisTotal: 0,
      drawer: false,
      detailsInfo: {}
    }
  },
  computed: {
    ...mapGetters(['realName'])
  },
  created() {
    this.getSecretLog_logList()
    this.getlogStatisList()
  },
  methods: {
    //   获取日志记录
    async getSecretLog_logList() {
      const { data } = await secretLog_logList(this.queryInfo)
      this.list = data.list
      this.total = data.total
      console.log(data)
    },
    // 获取日志统计
    async getlogStatisList() {
      const { data } = await secretLog_logStatis(this.logStatisInfo)
      this.logStatisList = data.list
      this.logStatisTotal = parseInt(data.total)
      const list = this.logStatisList[0]
      this.headerList = [
        {
          label: list.monKey,
          value: 'monValue'
        },
        {
          label: list.tueKey,
          value: 'tueValue'
        },
        {
          label: list.wedKey,
          value: 'wedValue'
        },
        {
          label: list.thuKey,
          value: 'thuValue'
        },
        {
          label: list.friKey,
          value: 'friValue'
        },
        {
          label: list.satKey,
          value: 'satValue'
        },
        {
          label: list.sunKey,
          value: 'sunValue'
        }
      ]
      console.log(this.headerList)
    },
    // 时间搜索的格式化
    datePickerChange(val) {
      if (val) {
        this.queryInfo.startTime = formatDate(val[0])
        this.queryInfo.endTime = formatDate(val[1], 'yyyy-MM-dd') + ' 23:59:59'
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getSecretLog_logList()
    },
    // 重置搜索
    reset() {
      this.queryInfo = {
        customerName: null,
        startTime: null,
        endTime: null,
        createUser: null,
        pageNum: 1,
        pageSize: 10
      }
      this.date = null
      this.logStatisInfo = {
        time: null,
        pageNum: 1,
        pageSize: 10
      }
      this.getSecretLog_logList()
      this.getlogStatisList()
    },
    selectChange(val) {
      let time = null
      if (val === '今天') {
        time = formatDate(new Date(), 'yyyy-MM-dd')
      } else {
        const dateTime = new Date()
        dateTime.setDate(dateTime.getDate() - 1)
        time = formatDate(dateTime, 'yyyy-MM-dd')
      }
      this.formInfo.time = time
      this.formInfo.name = `${this.realName}的工作日志(${time})`
    },
    //  写日志dialog - 点击添加客户按钮
    addCustomer() {
      this.trackCustomer.push({ customerId: null, contactsId: null, contactsName: null, majorName: null, phone: null })
    },
    //  写日志dialog -  获取客户
    async getSecretCustomerCustomerList() {
      const { data } = await secretCustomeAllCustomer()
      this.allCustomer = data
    },
    //  写日志dialog -  获取全部销售人员
    async getUserList(row) {
      if (!row.customerId) return
      const { data } = await secretCustomeAllContacts({ customerId: row.customerId })
      this.userList = data
      console.log(data)
    },
    //  写日志dialog -  选择联系人后赋值
    contactsChange(e, row) {
      row.contactsId = e.contactsId
      row.majorName = e.majorName
      row.phone = e.phone
    },
    // 写日志dialog - 删除客户
    delCustomer(index) {
      this.trackCustomer.splice(index, 1)
      console.log(index)
    },
    // 提交日志
    submitLog() {
      this.formInfo.contactsIds = this.trackCustomer.map((item) => item.contactsId)
      this.$refs['form'].validate(async (val) => {
        if (!val) return
        await secretLog_saveLog(this.formInfo)
        this.$message.success('日志提交成功!')
        this.getSecretLog_logList()
        this.addDialog = false
      })
      console.log(this.formInfo)
    },
    // 写日志dialog -  关闭事件
    addDialogClose() {
      this.formInfo = {
        name: null,
        time: null,
        visitContent: null,
        visitResult: null,
        planContent: null,
        remark: null,
        contactsIds: []
      }
      this.time = null
      this.trackCustomer = []
      this.$refs['form'].resetFields()
    },
    goDetails(val) {
      this.detailsInfo = { ...val }
      this.drawer = true
      console.log(val)
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep {
  .el-dialog__body {
    padding: 20px 50px;
  }
  .el-drawer__header {
    margin-bottom: 0;
    padding-bottom: 20px;
    border-bottom: 1px solid #e6e6e6;
  }
  .el-drawer__header > :first-child {
    color: #000;
    font-weight: bold;
  }
  .el-drawer__body{
    padding: 0 15px;
    padding-top: 15px;
    .tbody{
      margin-top: 25px;
    }
    .el-descriptions-item__label{
      font-size: 16px;
      font-weight: bold;
      color: #000;
    }
  }
}
</style>
