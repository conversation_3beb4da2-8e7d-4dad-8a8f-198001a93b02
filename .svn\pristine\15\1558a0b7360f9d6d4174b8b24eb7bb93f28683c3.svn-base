<template>
  <div class="app-container">
    <div class="box">
      <div class="header">
        <el-form ref="form" :model="queryInfo" label-width="80px" inline>
          <el-form-item label="产品名称:">
            <el-input v-model="queryInfo.name" placeholder="请输入产品名称" maxlength="40" @keydown.enter.native="getList('search')"></el-input>
          </el-form-item>
          <el-form-item label="品牌:" label-width="50px">
            <el-input v-model="queryInfo.brand" placeholder="请输入品牌" maxlength="40" @keydown.enter.native="getList('search')"></el-input>
          </el-form-item>
          <el-form-item label="参数:" label-width="50px">
            <el-input v-model="queryInfo.param" placeholder="请输入参数" maxlength="40" @keydown.enter.native="getList('search')"></el-input>
          </el-form-item>
          <el-form-item label="资质:" label-width="50px">
            <el-input v-model="queryInfo.flair" placeholder="请输入资质 " maxlength="40" @keydown.enter.native="getList('search')"></el-input>
          </el-form-item>
          <el-form-item>
            <span class="form_button search" @click="getList('search')">查询</span>
            <span class="form_button reset" @click="reset">重置</span>
            <span class="form_button search" @click="upload">导入</span>
            <span class="form_button export" @click="exportList">导出</span>
          </el-form-item>
        </el-form>
      </div>
      <div class="body">
        <div v-if="$store.getters.keyList.includes('addHardware')" class="addButton" @click="add">
          <img src="@/assets/software/addButton_icon.png" alt="" />
          添加产品
        </div>
        <el-table :data="list" style="width: 100%" border header-cell-class-name="tableHeader" @sort-change="sortChange">
          <el-table-column align="center" prop="name" label="产品名称" width="width">
            <template v-slot="{ row }">
              <span class="hardwareName" @click="details(row)">{{ row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="type" label="类型" width="width">
            <template v-slot="{ row }">
              <span>{{ row.type === 1 ? '自研' : '外购' }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="brand" label="产品品牌" width="width"> </el-table-column>
          <el-table-column align="center" prop="model" label="型号" width="width"> </el-table-column>
          <el-table-column align="center" prop="specs" label="规格" width="width"> </el-table-column>
          <el-table-column align="center" prop="offer" label="单价(万元)" width="width" sortable="custom"> </el-table-column>
          <el-table-column align="center" label="单位" width="width">
            <template v-slot="{ row }">
              <span>{{ row.unit | softwareUnit }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="$store.getters.keyList.includes('addHardware')" align="center" label="操作" width="220">
            <template v-slot="{ row }">
              <el-tooltip v-if="row.isOneself === 1" effect="dark" content="修改" placement="top">
                <img class="operateButton" src="@/assets/meeting/edit.png" alt="" @click="edit(row)" />
              </el-tooltip>
              <el-tooltip v-if="row.isOneself === 1" effect="dark" content="删除" placement="top">
                <img class="operateButton" src="@/assets/meeting/del.png" alt="" @click="del(row)" />
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          v-if="list.length > 0"
          style="margin-top: 32px; text-align: right"
          layout="total,  prev, pager, next"
          background
          :total="total"
          :page-size.sync="queryInfo.pageSize"
          :current-page.sync="queryInfo.pageNum"
          @size-change="getList"
          @current-change="getList"
        />
      </div>
    </div>
    <!-- 删除弹窗 -->
    <el-dialog custom-class="removeDialog" :visible.sync="delDialog" width="413px" top="35vh">
      <template v-slot:title>
        <img src="@/assets/library/hint.png" alt="" class="hint" />
        <span class="hintText">提示</span>
      </template>
      <div class="delText">确定删除该产品吗？</div>
      <div class="operate">
        <span class="closeButton" @click="delDialog = false">取 消</span>
        <span class="confirmButton" @click="confirmDel">确 定</span>
      </div>
    </el-dialog>
    <!-- 上传excel -->
    <upload :show-dialog.sync="uploadDialog" @success="getList" />
  </div>
</template>

<script>
import { tHardProductList, tHardProductRemove, tHardProductListExport } from '@/api/hardware'
import upload from './upload'
export default {
  name: 'Hardware',
  components: {
    upload
  },
  beforeRouteLeave(to, from, next) {
    if (to.name === 'addHardware' && from.name === 'Hardware') {
      from.meta.keepAlive = false
    } else {
      from.meta.keepAlive = true
    }
    next()
  },
  data() {
    return {
      queryInfo: {
        name: null, // 硬件名称
        brand: null, // 品牌
        param: null, // 参数
        flair: null, // 资质
        orderField: 1,
        order: null,
        pageNum: 1,
        pageSize: 6
      },
      list: [],
      total: 0,
      majorList: [], // 专业列表
      delDialog: false,
      hardProductId: null,
      uploadDialog: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    async getList(type) {
      if (type === 'search') {
        this.queryInfo.pageNum = 1
      }
      const { data } = await tHardProductList(this.queryInfo)
      this.list = data.list
      this.total = data.total
    },
    reset() {
      this.queryInfo = {
        name: null, // 硬件名称
        brand: null, // 品牌
        param: null, // 参数
        flair: null, // 资质
        orderField: 1,
        order: null,
        pageNum: 1,
        pageSize: 6
      }
      this.getList()
    },
    sortChange(val) {
      this.queryInfo.orderField = 1
      this.queryInfo.order = val.order === 'descending' ? 2 : 1
      this.getList()
    },
    add() {
      this.$router.push('/hardware/add/0')
    },
    details(row) {
      this.$router.push(`/hardware/details/${row.hardProductId}`)
    },
    edit(row) {
      this.$router.push(`/hardware/add/${row.hardProductId}`)
    },
    del(row) {
      this.hardProductId = row.hardProductId
      this.delDialog = true
    },
    async confirmDel() {
      await tHardProductRemove({ id: this.hardProductId })
      this.$message.success('删除成功')
      this.delDialog = false
      this.getList()
    },
    async exportList() {
      const { data } = await tHardProductListExport(this.queryInfo)
      const headers = {
        产品名称: 'name',
        产品品牌: 'brand',
        型号: 'model',
        规格: 'specs',
        '单价(万元)': 'offer',
        单位: 'unit'
      }
      const res = this.formatJson(headers, data)
      import('@/vendor/Export2Excel').then((excel) => {
        excel.export_json_to_excel({
          header: Object.keys(headers), // 表头 必填
          data: res, // 具体数据 必填
          filename: '硬件产品库列表' // 非必填
        })
      })
    },
    formatJson(headers, rows) {
      return rows.map((item) => {
        return Object.keys(headers).map((key) => {
          if (headers[key] === 'unit') {
            if (item[headers[key]] === 1) {
              return '套'
            } else if (item[headers[key]] === 2) {
              return '台'
            } else if (item[headers[key]] === 3) {
              return '个'
            } else if (item[headers[key]] === 4) {
              return '端'
            } else if (item[headers[key]] === 5) {
              return '把'
            }
          } else {
            return item[headers[key]]
          }
        })
      })
    },
    // 导入
    upload() {
      this.uploadDialog = true
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  width: 100%;
  height: 100%;
  background: #e8eaed;
  .box {
    width: 1754px;
    height: 826px;
    background: #ffffff;
    border-radius: 4px 4px 4px 4px;
    overflow: hidden;
    .header {
      display: flex;
      align-items: center;
      padding-left: 50px;
      height: 96px;
      background: url('~@/assets/hardware/header.png') no-repeat;
      background-size: cover;
      .form_button {
        display: inline-block;
        margin-right: 16px;
        width: 68px;
        height: 28px;
        line-height: 28px;
        border-radius: 4px 4px 4px 4px;
        font-size: 14px;
        font-weight: 400;
        text-align: center;
        cursor: pointer;
      }
      .search {
        margin-left: 25px;
        background: #fff;
        color: #0b1a44;
      }
      .reset,
      .export {
        color: #b1bac7;
        border: 1px solid #b1bac7;
      }
    }
    .body {
      padding-left: 40px;
      padding-right: 40px;
      padding-top: 24px;
      .addButton {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 24px;
        width: 116px;
        height: 42px;
        background: #3464e0;
        border-radius: 4px 4px 4px 4px;
        font-size: 16px;
        font-weight: 400;
        color: #ffffff;
        cursor: pointer;
        img {
          margin-right: 8px;
        }
      }

      .operateButton {
        width: 44px;
        height: 44px;
        cursor: pointer;
      }
    }
  }
  ::v-deep {
    .el-form {
      .el-form-item {
        margin-bottom: 0;
        .el-form-item__label {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #ffffff;
        }
        .el-form-item__content {
          .el-input {
            .el-input__inner {
              width: 254px;
              height: 36px;
              background: #e9ebef;
              &::placeholder {
                font-size: 14px;
                font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                font-weight: 400;
                color: #868b9f;
              }
            }
          }
        }
      }
    }
    .tableHeader {
      background: #f0f0f0;
      font-size: 14px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #0b1a44;
    }
    .el-table td.el-table__cell,
    .el-table th.el-table__cell.is-leaf {
      border-color: #a3a8bb;
    }
    .el-table--border,
    .el-table--group {
      border-color: #a3a8bb;
    }
    .el-table--border::after,
    .el-table--group::after,
    .el-table::before {
      background-color: #a3a8bb;
    }
    .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
      background: #eff1f3;
    }
    .hardwareName {
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #3464e0;
      cursor: pointer;
    }
    .removeDialog {
      height: 206px;
      border-radius: 8px !important;
      .el-dialog__header {
        display: flex;
        align-items: center;
        padding-top: 12px;
        padding-left: 24px;
        padding-right: 20px;
        padding-bottom: 8px;
        border-bottom: 1px solid #eeeeef;
        .hint {
          width: 30px;
          height: 30px;
        }
        .hintText {
          margin-left: 6px;
          font-size: 16px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #303a40;
        }
        .el-dialog__headerbtn {
          right: 20px;
          top: 20px;
          .el-dialog__close {
            font-weight: bold;
          }
        }
      }
      .delText {
        margin-top: 10px;
        margin-bottom: 30px;
        text-align: center;
        font-size: 16px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #303a40;
      }
      .operate {
        display: flex;
        justify-content: center;
        .closeButton {
          width: 114px;
          height: 38px;
          line-height: 36px;
          background: #ffffff;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #d8dbe1;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          color: #a3a8bb;
          text-align: center;
          cursor: pointer;
          &:hover {
            background: #f5f5f5;
          }
        }
        .confirmButton {
          margin-left: 32px;
          width: 114px;
          height: 38px;
          line-height: 36px;
          background: #3464e0;
          border-radius: 4px 4px 4px 4px;
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #ffffff;
          text-align: center;
          cursor: pointer;
          &:hover {
            background: #355fce;
          }
        }
      }
    }
  }
}
</style>
