<template>
  <div class="app-container">
    <el-row :gutter="50" class="top" type="flex" justify="space-between" align="middle" style="margin-bottom: 16px">
      <el-col :span="6">
        <div class="top_left">
          <span class="meeting_icon">
            <img src="@/assets/repository/repository_icon.png" alt="" />
            培训管理
          </span>
          <span class="add"> <i class="el-icon-circle-plus-outline"></i> 分享知识</span>
        </div>
      </el-col>
    </el-row>
    <el-card style="border-radius: 8px; box-shadow: none">
      <div class="repository_content">
        <div class="content_left">
          <el-form ref="form" label-width="80px">
            <el-form-item label="知识类型:">
              <el-radio-group v-model="queryInfo.type" class="fileType" @change="getKnowledgeBaseList">
                <!-- <el-radio :label="null">全部</el-radio> -->
                <el-radio :label="1">技术类</el-radio>
                <el-radio :label="2">专业类 </el-radio>
                <el-radio :label="3">商务类</el-radio>
                <el-radio :label="4">其他</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="分享时间:" class="time">
              <el-date-picker v-model="queryInfo.startTime" type="date" size="small" prefix-icon="" placeholder="选择开始时间" @change="getKnowledgeBaseList"> </el-date-picker>
              至
              <el-date-picker v-model="queryInfo.endTime" type="date" size="small" prefix-icon="" placeholder="选择结束时间" @change="getKnowledgeBaseList"> </el-date-picker>
            </el-form-item>
            <el-form-item class="name">
              <el-input v-model="queryInfo.name" size="small" placeholder="查询文件名称/上传人" maxlength="50" clearable>
                <el-button slot="append" type="primary" icon="el-icon-search" size="small" @click="getKnowledgeBaseList"></el-button>
              </el-input>
            </el-form-item>
          </el-form>
          <!-- 列表展示区域 -->
          <div class="showRegion">
            <div v-for="item in list" :key="item.knowledgeBaseId">
              <div class="showRegion_left">
                <img src="@/assets/repository/list_icon.png" alt="" />
              </div>
              <div class="showRegion_center">
                <div>
                  <span>{{ item.name }}</span>
                  <span class="type">{{ item.type | repository_type }}</span>
                </div>
                <div>
                  <span><i class="el-icon-user-solid"></i> {{ item.realName }}</span>
                  <span><i class="iconfont zf-shijianxuanzhong"></i> 分享时间: {{ item.createTime }}</span>
                  <span><i class="iconfont zf-yanjing"></i>浏览次数: {{ item.browseCount }}</span>
                  <span><i class="iconfont zf-dianzan_kuai"></i>点赞数: {{ item.likeCount }}</span>
                </div>
              </div>
              <div class="showRegion_right" :style="{ marginRight: item.isBuild !== 1 ? '70px' : '0' }">
                <router-link :to="`/repository/details/${item.knowledgeBaseId}`" style="display: flex; align-items: center">
                  <img class="details" src="@/assets/repository/details_icon.png" alt="" />
                  <span v-if="item.isBuild !== 1" class="details_text">详情</span>
                </router-link>
                <img v-if="item.isBuild === 1" src="@/assets/meeting/edit.png" alt="" @click="edit(item)" />
                <img v-if="item.isBuild === 1" src="@/assets/meeting/del.png" alt="" @click="del(item)" />
              </div>
            </div>
          </div>
          <el-pagination v-if="list.length > 0" style="text-align: center" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[5, 10, 15, 20]" background :total="total" :page-size.sync="queryInfo.pageSize" :current-page.sync="queryInfo.pageNum" @size-change="getKnowledgeBaseList" @current-change="getKnowledgeBaseList" />
          <el-empty v-if="list.length <= 0" :image="noDataImg">
            <template v-slot:description>
              <img src="@/assets/repository/noData_text.png" alt="" />
            </template>
          </el-empty>
        </div>
        <div class="content_right">
          <div>
            <img src="@/assets/repository/add_bg.png" alt="" />
            <div style="margin: 38px 0 28px 0">走过知识的路程，成就未来的前程</div>
            <div class="addButton" @click="$router.push('/repository/add/0')">给同事分享你的知识</div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { knowledgeBaseList, knowledgeBaseDelete } from '@/api/repository'
import noDataImg from '@/assets/repository/noData_bg.png'
export default {
  name: '',
  data() {
    return {
      queryInfo: {
        name: null,
        type: null,
        realName: null,
        startTime: null,
        endTime: null,
        pageNum: 1,
        pageSize: 10
      },
      list: [],
      total: null,
      noDataImg
    }
  },
  created() {
    this.getKnowledgeBaseList()
  },
  methods: {
    async getKnowledgeBaseList() {
      const { data } = await knowledgeBaseList(this.queryInfo)
      this.list = data.list
      // this.list = []
      this.total = data.total

      console.log(data)
    },
    edit(row) {
      this.$router.push(`/repository/add/${row.knowledgeBaseId}`)
    },
    del(row) {
      this.$confirm('您确认要删除吗, 是否继续?', '删除知识库', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await knowledgeBaseDelete({ knowledgeBaseId: row.knowledgeBaseId })
          this.$message.success('删除成功')
          this.getKnowledgeBaseList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  background-color: #e8eaed;
  min-height: 100%;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
}
.top {
  .top_left {
    display: flex;
    align-items: center;
    .meeting_icon {
      display: flex;
      align-items: center;
      margin-right: 10px;
      font-size: 16px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #0b1a44;
      img {
        margin-right: 8px;
      }
    }
    .add {
      display: none;
      width: 104px;
      height: 34px;
      line-height: 34px;
      font-size: 16px;
      background-color: #3464e0;
      font-family: Microsoft YaHei;
      color: #fff;
      text-align: center;
      border-radius: 8px;
      cursor: pointer;
    }
  }
}

.repository_content {
  position: relative;
  display: flex;
  padding-left: 44px;
  min-height: 500px;
  .content_left {
    width: 1194px;
    .fileType {
      ::v-deep {
        .el-radio__input.is-checked .el-radio__inner {
          // background: #3464e0;
          background: #fff;
          width: 18px;
          height: 18px;
          border-color: #3464e0;
          // border: none;
          &::after {
            background-color: #3464e0;
            width: 8px;
            height: 8px;
          }
        }
        .el-radio__inner {
          width: 18px;
          height: 18px;
        }
        .el-radio__input.is-checked + .el-radio__label {
          color: #0b1a44;
        }
        .el-radio__label {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
        }
      }
    }
    .time {
      color: #0b1a44;
      ::v-deep {
        .el-input {
          width: 150px;
          border-bottom: 1px solid #b8c0cc;
        }
        .el-input__prefix {
          display: none;
        }
        .el-input__inner {
          width: 100%;
          background: none;
          border: none;
          // border-bottom: 1px solid #b8c0cc;
          opacity: 1 !important;
          color: #0b1a44;
        }
      }
    }
    .name {
      width: 256px;
      ::v-deep {
        .el-form-item__content {
          line-height: initial;
          margin-left: 0 !important;
        }
        .el-input {
          width: 256px;
          height: 30px;
          .el-input__prefix,
          .el-input__suffix {
            top: 1px !important;
          }
        }
        .el-input__inner {
          background-color: #fff;
          height: 30px;
          border-radius: 4px 0 0px 4px;
          margin-right: 10px;
          &::placeholder {
            font-size: 14px;
            font-weight: 400;
            color: #a3a8bb;
          }
        }
        .el-input__inner::placeholder {
          color: #a3a8bb;
        }
        .el-button {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          padding: 0;
        }
        .el-input-group__append .el-button,
        .el-input-group__append .el-select,
        .el-input-group__prepend .el-button,
        .el-input-group__prepend .el-select {
          margin: 0;
        }
        .el-input-group__append,
        .el-input-group__prepend {
          position: relative;
          background: #3464e0;
          height: 30px;
          width: 34px;
          padding: 0;

          border-radius: 0;
          border: none;
          .el-icon-search {
            color: #fff;
            font-size: 18px;
          }
        }
      }
    }
    ::v-deep {
      .el-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        // border-bottom: 1px solid #EEEEEF;
      }
      .el-form-item__label {
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        color: #868b9f;
        font-weight: 400;
      }
    }
    // 列表展示区
    .showRegion {
      & > div {
        display: flex;
        // justify-content: space-between;
        height: 101px;
        // padding-right: 93px;
        // margin-bottom: 20px;
        background: #fff;
        border-top: 1px solid #eee;
        &:last-of-type {
          border-bottom: 1px solid #eee;
        }
        &:hover {
          background-color: #f5f5f5;
        }
        .showRegion_left {
          display: flex;
          align-items: center;
          padding-left: 43px;
        }
        .showRegion_center {
          padding-left: 40px;
          padding-top: 31px;
          padding-bottom: 31px;
          div {
            &:first-of-type {
              display: flex;
              align-items: center;
              font-size: 16px;
              font-weight: bold;
              color: #0b1a44;
              .type {
                margin-left: 12px;
                font-size: 14px;
                color: #ff7e26;
                font-weight: 400;
              }
            }
            &:last-of-type {
              display: flex;
              flex-wrap: wrap;
              padding-top: 15px;
              span {
                display: flex;
                align-items: center;
                margin-right: 25px;
                font-size: 14px;
                color: #868b9f;
                line-height: 25px;
                i {
                  margin-right: 5px;
                }
              }
            }
          }
        }
        .showRegion_right {
          position: relative;
          display: flex;
          align-items: center;
          margin-left: auto;
          img {
            cursor: pointer;
          }
          .details {
            &:hover + .details_text {
              color: #3464e0;
            }
          }
          .details_text {
            font-size: 14px;
            font-weight: 400;
            color: #657081;
            cursor: pointer;
            &:hover {
              color: #3464e0;
            }
          }
        }
      }
    }
    .el-pagination {
      margin-top: 35px;
      // position: absolute;
      // bottom: 20px;
      // left: 26%;
    }
  }
  .content_right {
    position: relative;
    flex: 1;
    & > div {
      position: absolute;
      top: 93px;
      right: 53px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .addButton {
        width: 166px;
        height: 45px;
        line-height: 45px;
        background: #ff7e26;
        border-radius: 4px;
        font-size: 14px;
        font-family: HelloFont WenYiHei-Regular, HelloFont WenYiHei;
        font-weight: 400;
        color: #ffffff;
        text-align: center;
        cursor: pointer;
      }
    }
  }
}
::v-deep {
  .el-empty {
    background: #f5f5f5;
    height: 400px;
    .el-empty__image {
      width: 176px;
      height: 116px;
    }
  }
}

// @media screen and (max-width: 1750px) {
//   .add{
//     display: block !important;
//   }
//   .content_right{
//     display: none;
//   }
// }
</style>
