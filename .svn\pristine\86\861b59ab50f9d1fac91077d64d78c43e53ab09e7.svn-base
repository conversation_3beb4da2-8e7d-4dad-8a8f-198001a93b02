<template>
  <div class="ContractInfo">
    <aside>
      <section :class="{ checkedInfo: current === 0 }" @click="current = 0">
        <img src="@/assets/contractNew/icon1.png" alt="" />
        <span>合同基本信息</span>
      </section>
      <section :class="{ checkedInfo: current === 1 }" @click="current = 1">
        <img src="@/assets/contractNew/icon2.png" alt="" />
        <span>合同供货信息</span>
      </section>
      <section :class="{ checkedInfo: current === 2 }" @click="current = 2">
        <img src="@/assets/contractNew/icon3.png" alt="" />
        <span>合同施工信息</span>
      </section>
      <section :class="{ checkedInfo: current === 3 }" @click="current = 3">
        <img src="@/assets/contractNew/icon4.png" alt="" />
        <span>合同货款信息</span>
      </section>
      <section v-if="keyList.includes('maintenanceInformat')" :class="{ checkedInfo: current === 4 }" @click="$router.push(`/newContract/aftersales/${contractId}`)">
        <img src="@/assets/contractNew/icon5.png" alt="" />
        <span>售后维护信息</span>
      </section>
      <el-button v-if="!disabledOperate && isFinance" type="primary" class="finishButton" @click="finish">完成合同</el-button>
    </aside>
    <section class="infoBox">
      <div v-show="current === 0" class="baseInfo">
        <section>
          <label>合同名称: </label>
          <span>{{ info.contractName }}</span>
        </section>
        <section>
          <label>合同编号: </label>
          <span>{{ info.contractCode }}</span>
        </section>
        <section>
          <label>客户名称: </label>
          <span>{{ info.customerName }}</span>
        </section>
        <section>
          <label>签订时间: </label>
          <span>{{ info.signingTime }}</span>
        </section>
        <section>
          <label>销售员: </label>
          <span>{{ info.realName }}</span>
        </section>
        <section>
          <label>质保日期: </label>
          <span>{{ info.expireTime }}</span>
        </section>
        <section class="finalize">
          <label>定稿合同: </label>
          <el-button v-if="info.isUpload === 1" type="primary" size="small">已完成</el-button>
          <span v-else-if="info.isUpload !== 1 && disabledOperate" style="color: #000000">未上传</span>
          <span v-else-if="isFinance" @click="uploadFile(19)"> <i class="el-icon-upload2"></i>上传</span>
        </section>
      </div>
      <!-- 供货信息 -->
      <div v-show="current === 1" class="supplyInfo">
        <div>
          <section>
            <label>软件内容: </label>
            <span>{{ info.supplySoft ? info.supplySoft : '暂无' }}</span>
          </section>
          <section>
            <label>软件交付时间: </label>
            <span>{{ info.confirmPaySoftTime ? info.confirmPaySoftTime : '暂无' }}</span>
          </section>
          <section>
            <label>硬件内容: </label>
            <span>{{ info.supplyHard ? info.supplyHard : '暂无' }}</span>
          </section>
          <section>
            <label>硬件交付时间: </label>
            <span>{{ info.confirmPayHardTime ? info.confirmPayHardTime : '暂无' }}</span>
          </section>
          <section>
            <label>硬件资金预算: </label>
            <span>{{ info.hardMoney ? info.hardMoney : '暂无' }}</span>
          </section>
        </div>
        <span v-if="keyList.includes('contractSupply')" class="lookAll" @click="look(1)">查看更多 <i class="el-icon-arrow-right"></i></span>
      </div>
      <!-- 合同施工信息 -->
      <div v-show="current === 2" class="constructionInfo">
        <template v-if="constructionInfo">
          <section>
            <label>客户联系人: </label>
            <span>{{ constructionInfo.contactsName ? constructionInfo.contactsName : '暂无' }}</span>
          </section>
          <section>
            <label>联系方式: </label>
            <span>{{ constructionInfo.phone ? constructionInfo.phone : '暂无' }}</span>
          </section>
          <section>
            <label>施工地址: </label>
            <span>{{ constructionInfo.address ? constructionInfo.address : '暂无' }}</span>
          </section>
          <section>
            <label>验收日期: </label>
            <span>{{ constructionInfo.acceptanceTime ? constructionInfo.acceptanceTime : '暂无' }}</span>
          </section>
          <section class="finalize">
            <label>现场勘测文件: </label>
            <span v-if="constructionInfo.surveyFile" @click="lookSurveyFile"> <i class="el-icon-view"></i> 查看</span>
            <span v-else style="color: #000">暂无</span>
          </section>
          <section class="finalize">
            <label>服务器信息: </label>
            <span v-if="constructionInfo.serverExplain" @click="serverExplainDialog = true"> <i class="el-icon-view"></i> 查看</span>
            <span v-else style="color: #000">暂无</span>
          </section>
          <section class="finalize">
            <label>现场装修效果图: </label>
            <span v-if="constructionInfo.renovationFiles && constructionInfo.renovationFiles.length" @click="lookRenovationFiles"> <i class="el-icon-view"></i> 查看</span>
            <span v-else style="color: #000">暂无</span>
          </section>
          <!-- <section class="finalize">
            <label>培训现场图: </label>
            <span v-if="!disabledOperate && keyList.includes('contractConstruction')" @click="uploadFile(21)"> <i class="el-icon-upload2"></i> 上传</span>
            <span v-else style="color: #000">在合同附件信息中查看</span>
          </section>
          <section class="finalize">
            <label>培训签字单: </label>
            <span v-if="!disabledOperate && keyList.includes('contractConstruction')" @click="uploadFile(17)"> <i class="el-icon-upload2"></i> 上传</span>
            <span v-else style="color: #000">在合同附件信息中查看</span>
          </section> -->
        </template>
        <span v-if="keyList.includes('contractConstruction')" class="lookAll" @click="look(2)">查看更多 <i class="el-icon-arrow-right"></i></span>
      </div>
      <!-- 合同货款信息 -->
      <div v-show="current === 3" class="loansInfo">
        <template v-if="moneyDetailDto">
          <section>
            <label>投标保证金: </label>
            <span>{{ moneyDetailDto.bidMoney ? moneyDetailDto.bidMoney : 0 }}</span>
          </section>
          <section>
            <label>质保金: </label>
            <span>{{ moneyDetailDto.qualityMoney ? moneyDetailDto.qualityMoney : 0 }}</span>
          </section>
          <section>
            <label>履约保证金:</label>
            <span>{{ moneyDetailDto.performanceMoney ? moneyDetailDto.performanceMoney : 0 }}</span>
          </section>
          <section>
            <label>剩余货款:</label>
            <span>{{ moneyDetailDto.remainMoney ? moneyDetailDto.remainMoney : 0 }}</span>
          </section>
          <section>
            <label>最近更新时间:</label>
            <span>{{ moneyDetailDto.updateTime ? moneyDetailDto.updateTime : '暂无' }}</span>
          </section>
        </template>
        <span v-if="isFinance" class="lookAll" @click="look(3)">查看更多 <i class="el-icon-arrow-right"></i></span>
      </div>
    </section>

    <!-- 通用的文件上传弹窗 -->
    <FileUploadDialog ref="FileUploadDialogRef" @refreshData="$emit('refreshData')" />
    <!-- 服务器信息富文本 -->
    <el-dialog title="服务器信息" custom-class="serverExplainDialog" :visible.sync="serverExplainDialog" top="10vh" width="width">
      <div class="h5Box" v-html="constructionInfo.serverExplain"></div>
      <div slot="footer" style="text-align: center">
        <el-button type="primary" @click="serverExplainDialog = false">关 闭</el-button>
      </div>
    </el-dialog>
    <!-- 图片预览 -->
    <ElImageViewer v-if="isViewerVisible" :url-list="urlList" :on-close="viewerClose" :z-index="999999" />
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import { contractReceiveJudgeOver, contractReceiveOver } from '@/api/contractNew'
import FileUploadDialog from './FileUploadDialog.vue'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'

export default {
  name: '',
  components: {
    FileUploadDialog,
    ElImageViewer
  },
  props: {
    info: {
      type: Object,
      default: () => {
        return {}
      }
    },
    contractId: {
      type: String,
      default: '0'
    },
    type: {
      type: Number,
      default: 1
    }
  },

  data() {
    return {
      current: 0,
      serverExplainDialog: false,
      isViewerVisible: false,
      urlList: []
    }
  },
  computed: {
    ...mapGetters(['token', 'keyList', 'organizationId']),
    constructionInfo() {
      return this.info.constructionDetailDto
    },
    moneyDetailDto() {
      return this.info.moneyDetailDto
    },
    disabledOperate() {
      return this.type === 2
    },
    // 当前用户是否是财务部门
    isFinance() {
      return this.organizationId === 5973411
    }
  },
  created() {},
  methods: {
    uploadFile(type) {
      this.$refs['FileUploadDialogRef'].uploadFile(type, this.constructionInfo, this.contractId)
    },

    look(type) {
      if (type === 1) {
        this.$router.push(`/newContract/supplyInfo/${this.contractId}`)
      } else if (type === 2) {
        this.$router.push(`/newContract/constructionInfo/${this.contractId}`)
      } else if (type === 3) {
        this.$router.push(`/newContract/constructionInfo/${this.contractId}/${this.moneyDetailDto.moneyId}`)
      }
    },
    // 查看现场勘测文件
    lookSurveyFile() {
      const fileType = this._.last(this.constructionInfo.surveyFile.split('.'))
      if (fileType === 'jpg' || fileType === 'png') {
        this.lookFile({ fileUrl: this.constructionInfo.surveyFile })
      } else {
        const downloadLink = document.createElement('a')
        downloadLink.href = this.constructionInfo.surveyFile
        downloadLink.setAttribute('target', '_blank') // 可选，设置下载文件的名称
        document.body.appendChild(downloadLink)
        downloadLink.click()
        document.body.removeChild(downloadLink)
      }
    },
    lookRenovationFiles() {
      this.urlList = this.constructionInfo.renovationFiles.map((item) => item.fileUrl)

      this.isViewerVisible = true
    },
    viewerClose() {
      this.isViewerVisible = false
      this.urlList = []
    },
    // 完成合同
    finish() {
      contractReceiveJudgeOver({ contractId: this.contractId }).then((res) => {
        if (res.data === 1) {
          this.$confirm('确定完成吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(() => {
              const loading = this.$loading({
                text: '数据保存中，请稍后...',
                background: 'rgba(0,0,0,0.7)'
              })
              contractReceiveOver({ contractId: this.contractId })
                .then((res) => {
                  loading.close()
                  this.$message.success('数据保存成功！')
                  this.$router.push('/newContract')
                })
                .catch(() => {
                  loading.close()
                })
            })
            .catch(() => {
              this.$message({
                type: 'info',
                message: '已取消'
              })
            })
        } else {
          this.$message.error('当前无法结束合同，请检查合同未完成内容！')
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.ContractInfo {
  display: flex;
  aside {
    position: relative;
    section {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 171px;
      height: 50px;
      border-radius: 4px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #666666;
      cursor: pointer;
      img {
        width: 40px;
        height: 40px;
        margin-right: 5px;
        object-fit: scale-down;
      }
    }
    .checkedInfo {
      background: #3465df;
      color: #ffffff;
    }
    .finishButton {
      position: absolute;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
    }
  }
  .infoBox {
    flex: 1;
    height: 453px;
    margin-left: 10px;
    background: #f0f3fc;
    border-radius: 4px;
    border: 1px solid #3465df;
    .baseInfo,
    .supplyInfo,
    .constructionInfo,
    .loansInfo {
      position: relative;
      width: 100%;
      height: 100%;
      padding: 30px 20px;
      overflow: auto;
      &::-webkit-scrollbar {
        width: 3px;
      }
      &::-webkit-scrollbar-thumb {
        width: 3px;
        border-radius: 3px;
        background: #3a6ff4;
      }

      section {
        display: flex;
        margin-top: 25px;
        &:first-of-type {
          margin-top: 0;
        }
        label {
          min-width: 95px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 18px;
          color: #656565;
          text-align: right;
        }
        span {
          margin-left: 15px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 18px;
          color: #000000;
        }
      }
      .finalize {
        label {
          margin-right: 15px;
        }
        span {
          margin-left: 0;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 18px;
          color: #3a6ff4;
          cursor: pointer;
        }
      }
    }
    .supplyInfo {
      padding-right: 0;
      padding-top: 0;
      padding-bottom: 40px;
      & > div {
        width: 100%;
        height: 100%;
        padding-top: 30px;
        overflow: auto;
        &::-webkit-scrollbar {
          width: 3px;
        }
        &::-webkit-scrollbar-thumb {
          width: 3px;
          border-radius: 3px;
          background: #3a6ff4;
        }
      }
      section {
        label {
          width: 115px;
        }
      }
    }
    .constructionInfo {
      .half {
        display: flex;
        align-items: center;
        section {
          margin-top: 25px;
          &:first-of-type {
            margin-right: 15px;
          }
        }
      }
    }
    .loansInfo {
      section {
        label {
          min-width: 110px;
        }
      }
    }
    .lookAll {
      position: absolute;
      left: 50%;
      bottom: 10px;
      transform: translateX(-50%);
      display: flex;
      align-items: center;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #666666;
      cursor: pointer;
    }
  }
}
::v-deep {
  .el-dialog {
    .el-dialog__header {
      font-family: Microsoft YaHei;
      font-weight: bold;
      font-size: 18px;
      color: #0b1a44;
    }
    .el-upload,
    .el-upload-dragger {
      width: 100%;
    }
  }
  .serverExplainDialog {
    max-height: 800px;
    overflow: auto;
    &::-webkit-scrollbar {
      height: 5px;
    }
    .el-dialog__body {
      padding-top: 10px;
    }
    .h5Box {
      img {
        max-width: 100%;
      }
    }
  }
}
</style>
