<template>
  <div class="Home">
    <div class="top">
      <div class="left">
        <img src="@/assets/login/logo.png" alt="" />
        <img src="@/assets/Home/text.png" alt="" />
      </div>
      <div class="right">
        <!-- <iframe scrolling="no" src="https://tianqiapi.com/api.php?style=ty&skin=durian&color=fff" frameborder="0" width="200" height="100" allowtransparency="true" class="weather"></iframe> -->
        <iframe scrolling="no" src="https://widget.tianqiapi.com/?style=ty&skin=durian&color=fff" frameborder="0" width="200" height="60" allowtransparency="true" class="weather"></iframe>
        <div class="time">
          {{ year }} <span style="margin: 0 5px 0 5px">{{ Day }}</span> {{ time }}
        </div>
        <el-popover placement="top" trigger="click" popper-class="meesage_popover">
          <template v-slot:reference>
            <div class="messageHint">
              <el-badge value="new" class="item" :hidden="!messageList.length">
                <i class="el-icon-message-solid"></i>
                <i class="el-icon-caret-bottom"></i>
              </el-badge>
            </div>
          </template>
          <template>
            <el-row class="meesage_top" type="flex" justify="space-between" align="middle">
              <span>最新通知({{ messageList.length }})</span>
            </el-row>
            <el-row style="margin-top: 14px">
              <img src="@/assets/personCenter/message_bg.png" alt="" />
            </el-row>
            <template v-if="messageList.length">
              <el-row v-for="item in messageList" :key="item.messageId" class="meesage_info" @click.native="goJump(item)">
                <span></span>
                <span>{{ item.description }}</span>
              </el-row>
            </template>

            <div v-else class="no_message">
              <img src="@/assets/personCenter/no_message.png" alt="" />
              <div>暂无最新消息</div>
            </div>

            <div class="lookAll" @click="lookAll">查看全部</div>
          </template>
        </el-popover>
        <span class="realName">{{ realName }}</span>
        <el-dropdown class="avatar-container" trigger="click">
          <div class="avatar-wrapper">
            <el-avatar shape="circle" :size="48" fit="cover" :src="avatar" @error="true">
              <img src="@/assets/login/logo.png" />
            </el-avatar>
            <i class="el-icon-caret-bottom" style="color: #fff" />
          </div>
          <el-dropdown-menu slot="dropdown" class="user-dropdown">
            <el-dropdown-item @click.native="changePassword"> 修改密码 </el-dropdown-item>
            <el-dropdown-item divided @click.native="logout"> 退出系统 </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <div class="center">
      <div class="title">系统入口</div>
      <div class="contentBox">
        <div ref="contentRef" class="content">
          <img v-for="item in moduleList.slice((pageNum - 1) * pageSize, pageSize * pageNum)" :key="item.moduleType" :src="item.url" alt="" @click="checkedMode(item)" />
        </div>
      </div>
      <el-pagination v-if="moduleList.length > 0" layout=" prev, pager, next" background :total="total" :page-size.sync="pageSize" :current-page.sync="pageNum" @current-change="cutPage" />
    </div>
    <div class="download" @click="downloadClient">下载客户端</div>
    <!-- 修改密码 -->
    <el-dialog title="修改密码" :visible.sync="changePasswordDialog" width="420px" append-to-body>
      <div>
        <el-form ref="passwordForm" :model="changePasswordInfo" label-width="80px" :rules="passwordRules">
          <el-form-item label="原密码" prop="oldPassword">
            <el-input v-model="changePasswordInfo.oldPassword" type="password"></el-input>
          </el-form-item>
          <el-form-item label="新密码" prop="newPassword">
            <el-input v-model="changePasswordInfo.newPassword" type="password"></el-input>
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input v-model="changePasswordInfo.confirmPassword" type="password"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="changePasswordDialog = false">取 消</el-button>
        <el-button type="primary" @click="onClickPassword">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { formatDate } from '@/filters'
import { changePassword } from '@/api/systemUser'
import { tMessageList } from '@/api/tMessage'
import { getPassword } from '@/utils/auth'
import { gsap } from 'gsap'

import modules from './menuModule'
// import { resetRouter } from '@/router'
export default {
  name: '',
  data() {
    return {
      year: null,
      time: null,
      Day: null,
      times: null,
      changePasswordDialog: false,
      changePasswordInfo: {
        oldPassword: null,
        newPassword: null,
        confirmPassword: null
      },
      passwordRules: {
        oldPassword: [{ required: true, message: '请输入原密码', trigger: 'blur' }],
        newPassword: [{ required: true, message: '请输入新密码', trigger: 'blur' }],
        confirmPassword: [{ required: true, message: '请确认新密码', trigger: 'blur' }]
      },
      messageList: [],
      moduleList: [],
      total: 0,
      pageNum: 1,
      pageSize: 8
    }
  },
  computed: {
    ...mapGetters(['keyList', 'realName', 'avatar', 'routes', 'userId', 'message'])
  },
  watch: {
    message: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val && val !== 'success') {
          const data = JSON.parse(val)
          const isCurrentUser = data.userIds.find((item) => item === this.userId)
          if (isCurrentUser) {
            this.gettMessageList()
          }
        }
      }
    }
  },
  mounted() {
    this.getTime()
    this.gettMessageList()
    this.getMyModuleList()
  },
  beforeDestroy() {
    clearInterval(this.times)
    this.times = null
  },
  methods: {
    getMyModuleList() {
      const myList = modules.filter((item) => {
        return this.keyList.includes(item.permission)
      })
      this.moduleList = myList
      this.total = this.moduleList.length
    },
    cutPage(val) {
      gsap.fromTo(this.$refs['contentRef'], { duration: 0.2, opacity: 0, scale: 0.2, ease: 'power1.inOut' }, { duration: 0.5, opacity: 1, scale: 1, ease: 'sine.out' })
    },
    checkedMode(data) {
      const type = data.moduleType
      if (type === 'task') {
        window.open('http://192.168.1.103:2222/manage/dashboard', 'blank')
      } else {
        this.$store.commit('checkedData/set_data_type', type)
        if (type === 5) {
          this.$nextTick(() => {
            if (this.keyList.includes('training')) {
              this.$router.push('/training')
            } else {
              this.$router.push('/repository')
            }
          })
        } else if (type === 7) {
          this.$nextTick(() => {
            const route = this.routes.filter((item) => {
              if (item.meta && item.meta.type === 7) {
                return item
              }
            })
            this.$router.push(route[0].path)
          })
        } else if (type === 12) {
          this.$nextTick(() => {
            if (this.keyList.includes('software')) {
              this.$router.push('/software')
            } else {
              this.$router.push('/hardware')
            }
          })
        } else {
          this.$nextTick(() => {
            this.$router.push(data.routePath)
          })
        }
      }
    },

    getTime() {
      this.year = formatDate(new Date(), 'yyyy/MM/dd')
      const wk = new Date().getDay()
      const weeks = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
      this.Day = weeks[wk]
      this.times = setInterval(() => {
        this.time = formatDate(new Date(), 'hh:mm:ss')
      }, 1000)
    },
    changePassword() {
      this.changePasswordDialog = true
    },
    onClickPassword() {
      this.$refs['passwordForm'].validate(async (val) => {
        if (val) {
          if (this.changePasswordInfo.oldPassword === getPassword()) {
            if (this.changePasswordInfo.confirmPassword === this.changePasswordInfo.newPassword) {
              // 通过
              await changePassword({
                userId: this.userId,
                newPassword: this.changePasswordInfo.newPassword,
                oldPassword: this.changePasswordInfo.oldPassword
              })
              this.changePasswordDialog = false
              // 修改密码后退出登录
              this.$nextTick(() => {
                this.logout()
                this.$message.warning('请重新登录')
              })
            } else {
              this.$message.warning('两次密码不一致')
            }
          } else {
            this.$message.warning('原密码不正确')
          }
        }
      })
    },
    async logout() {
      const loading = this.$loading({
        text: '正在退出，请稍后',
        background: 'rgba(0,0,0,0.8)'
      })
      await this.$store.dispatch('user/logout')
      loading.close()
      this.$router.push(`/login`)
    },

    // 获取消息接口
    async gettMessageList() {
      const { data } = await tMessageList({ pageNum: 1, pageSize: 5 })
      this.messageList = data.list
    },
    // 消息的跳转
    goJump(item) {
      const type = parseInt(item.type)
      const dataType = type === 1 ? 9 : type === 2 ? 6 : type === 3 ? 3 : type === 4 ? 7 : type === 5 ? 12 : 15
      this.$store.commit('checkedData/set_data_type', dataType)
      const jumpModule = type === 1 ? 'Bug' : type === 2 ? 'Institution' : type === 3 ? 'Meeting' : type === 4 ? 'SecretLog' : type === 5 ? 'Software' : 'newContract'
      this.$nextTick(() => {
        this.$router.push({
          name: jumpModule
        })
      })
    },
    // 查看全部消息
    lookAll() {
      window.sessionStorage.setItem('zf_admin_userCenter_activeTab', 2)
      this.$router.push('/personal/center')
    },
    // 下载客户端
    downloadClient() {
      const url = 'https://work.sdzft.com/cloudFile/workmanage/downloadexe/%E5%85%AC%E5%8F%B8%E5%86%85%E9%83%A8%E7%AE%A1%E7%90%86%E7%B3%BB%E7%BB%9FSetup1.2.13.exe'
      if (window.electronDownload) {
        window.electronDownload.downloadFile(url)
      } else {
        const downloadLink = document.createElement('a')
        downloadLink.href = url
        downloadLink.setAttribute('download', 'filename.ext') // 可选，设置下载文件的名称
        document.body.appendChild(downloadLink)
        downloadLink.click()
        document.body.removeChild(downloadLink)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.Home {
  width: 100%;
  height: 100%;
  padding-top: 65px;
  padding-left: 72px;
  padding-right: 72px;
  background: url('../assets/Home/bg.png') no-repeat;
  background-size: cover;
  overflow: hidden;
  .top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      img {
        &:first-of-type {
          width: 50px;
          height: 50px;
          margin-right: 15px;
        }
      }
    }
    .right {
      display: flex;
      align-items: center;
      .time {
        margin-right: 30px;
        font-size: 18px;
        font-family: D-DIN Exp-DINExp-Bold, D-DIN Exp-DINExp;
        // font-weight: bold;
        color: #ffffff;
      }
      .realName {
        font-size: 16px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #ffffff;
        margin-right: 16px;
      }
    }
  }
  .center {
    position: relative;
    max-width: calc(1452px - 28px);
    min-width: 1240px;
    height: 590px;
    margin: 0 auto;
    margin-top: 120px;
    .title {
      margin-bottom: 27px;
      font-size: 22px;
      font-weight: bold;
      background: linear-gradient(180deg, #e5e5e5 0%, #ffffff 41%, #d2d2d2 100%);
      -webkit-background-clip: text;
      color: transparent;
    }
    .contentBox {
      display: flex;
      width: 100%;
      .content {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        width: 1600px;
        height: 536px;
        overflow: hidden;

        img {
          flex: 0 0 calc(25% - 24px); /* 每个元素占据四分之一宽度，减去间隔 */
          margin-right: 32px; /* 设置间隔 */
          max-width: 332px;
          max-height: 212px;
          margin-bottom: 56px;
          cursor: pointer;
          &:nth-of-type(4n) {
            margin-right: 0;
          }
        }

        // max-width: 1600px;
        // margin: 0 auto;
        // margin-top: 300px;
      }
    }

    .goPage {
      position: absolute;
      left: 50%;
      bottom: 0;
      transform: translateX(-50%);
      width: 70px;
      height: 30px;
      line-height: 30px;
      background: #fff;
      color: #4783ed;
      text-align: center;
      border-radius: 15px;
      cursor: pointer;
    }
  }
  .download {
    position: absolute;
    right: 30px;
    bottom: 10px;
    width: 90px;
    height: 30px;
    line-height: 30px;
    background: #fff;
    color: #4783ed;
    border-radius: 15px;
    text-align: center;
    cursor: pointer;
  }
}
.messageHint {
  display: flex;
  align-items: center;
  margin-right: 35px;
  color: #fff;
  cursor: pointer;

  .el-icon-message-solid {
    position: relative;
    font-size: 21px;
    &::after {
      display: none;
      position: absolute;
      top: 0;
      right: 3px;
      z-index: 2;
      width: 5px;
      height: 5px;
      border-radius: 50%;
      background: #eb6557;
      content: '';
    }
  }
  .el-icon-caret-bottom {
    color: #fff;
    font-size: 12px;
  }
}
.weather {
  ::v-deep {
    color: #fff !important;
  }
}
</style>
