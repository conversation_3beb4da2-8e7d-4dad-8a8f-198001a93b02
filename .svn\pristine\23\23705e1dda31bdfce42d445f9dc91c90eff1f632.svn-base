<template>
  <div class="app-container">
    <!-- <el-tabs v-model="selectedName" type="border-card">
      <el-tab-pane label="我审批的">用户管理</el-tab-pane>
      <el-tab-pane label="我发起的">配置管理</el-tab-pane>
      <el-tab-pane label="抄送我的">角色管理</el-tab-pane>
    </el-tabs> -->
    <el-card>
      <div slot="header">
        <el-radio-group v-model="searchInfo.type" @change="getInstanceList">
          <el-radio-button :label="3">我审批的</el-radio-button>
          <el-radio-button :label="1">我发起的</el-radio-button>
          <el-radio-button :label="2">抄送我的</el-radio-button>
        </el-radio-group>
      </div>
      <div>
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form ref="form" label-width="80px" inline>
              <el-form-item label="事项名称:">
                <el-input v-model="searchInfo.title" size="small" placeholder="请输入事项名称" maxlength="50" clearable></el-input>
              </el-form-item>
              <el-form-item label="事项类型:">
                <el-select v-model="searchInfo.processName" placeholder="请选择事项类型" size="small" clearable>
                  <el-option v-for="item in typeList" :key="item.processCode" :label="item.processName" :value="item.processName"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="发起人:">
                <el-input v-model="searchInfo.realName" size="small" placeholder="请输入发起人名称" maxlength="50" clearable></el-input>
              </el-form-item>
              <el-form-item label="发起时间:">
                <el-date-picker v-model="searchInfo.startTime" type="date" size="small" placeholder="选择发起时间"> </el-date-picker>
              </el-form-item>
              <el-form-item label="结束时间:">
                <el-date-picker v-model="searchInfo.endTime" type="date" size="small" placeholder="选择结束时间"> </el-date-picker>
              </el-form-item>
              <el-form-item label="审核状态:">
                <el-select v-model="searchInfo.status" placeholder="请选择审核状态" size="small" clearable>
                  <el-option label="新创建" value="NEW"> </el-option>
                  <el-option label="审批中" value="RUNNING"> </el-option>
                  <el-option label="被终止" value="TERMINATED"> </el-option>
                  <el-option label="完成" value="COMPLETED"> </el-option>
                  <el-option label="取消" value="CANCELED"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="success" size="small" @click="getInstanceList"> 查询 </el-button>
                <el-button type="primary" size="small" @click="reset">重置 </el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-table :data="list" style="width: 100%" border>
          <el-table-column prop="title" label="事项名称" width="width" align="center"> </el-table-column>
          <el-table-column prop="processName" label="事项类型" width="width" align="center"> </el-table-column>
          <el-table-column label="审核状态" width="width" align="center">
            <template v-slot="{ row }">
              <span>{{ row.status | processStatus }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="发起时间" width="width" align="center"> </el-table-column>
          <el-table-column prop="realName" label="发起人" width="width" align="center"> </el-table-column>
          <el-table-column label="操作" width="width" align="center">
            <template v-slot="{ row }">
              <el-button type="primary" size="small" @click="seeDetails(row)">查看详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination style="text-align: center; margin-top: 15px" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 15, 20, 30]" :total="total" :page-size.sync="searchInfo.pageSize" :current-page.sync="searchInfo.pageNum" @size-change="getInstanceList" @current-change="getInstanceList" />
      </div>
    </el-card>
  </div>
</template>

<script>
import { instanceList, getAllProcess } from '@/api/process'
export default {
  name: '',
  data() {
    return {
      list: [], // 审批列表
      typeList: [], // 事项类型
      searchInfo: {
        title: null,
        processName: null,
        realName: null,
        startTime: null,
        endTime: null,
        status: null,
        type: 3,
        pageSize: 10,
        pageNum: 1
      },
      total: 0
    }
  },
  created() {
    this.getInstanceList()
    this.getAllProcessType()
  },
  methods: {
    // 获取审批列表
    async getInstanceList() {
      const { data } = await instanceList(this.searchInfo)
      this.list = data.list
      this.total = data.total
      console.log(data)
    },

    // 获取所有事项类型
    async getAllProcessType() {
      const { data } = await getAllProcess()
      this.typeList = data
      console.log(data)
    },
    // 查看详情
    seeDetails(row) {
      this.$router.push(`/process/management/details/${row.processInstanceId}`)
    },
    // 重置搜索表单
    reset() {
      this.searchInfo = {
        title: null,
        processName: null,
        realName: null,
        startTime: null,
        endTime: null,
        status: null
      }
      this.getInstanceList()
    }
  }
}
</script>

<style scoped lang="scss"></style>
