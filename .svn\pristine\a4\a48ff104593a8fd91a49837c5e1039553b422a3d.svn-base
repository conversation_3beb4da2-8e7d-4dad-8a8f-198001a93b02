function UnityProgress(gameInstance, progress) {
  // if (!gameInstance.Module) return
  if (!gameInstance.loading) {
    gameInstance.loading = document.querySelector('.webgl_virtual')
  }
  if (!gameInstance.progress) {
    gameInstance.progress = document.querySelector('.progress_bar')
  }
  gameInstance.progress.style.width = 94 * progress + '%'
  if (progress == 1) gameInstance.loading.style.display = 'none'
}
