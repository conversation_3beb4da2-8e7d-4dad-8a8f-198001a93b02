<template>
  <div class="app-container">
    <el-form ref="searchForm" label-width="80px" inline>
      <el-form-item label="负责人:" label-width="90px">
        <el-input v-model="queryInfo.realName" size="small" placeholder="请输入负责人" clearable></el-input>
      </el-form-item>

      <el-form-item label="商机等级:">
        <el-select v-model="queryInfo.rank" placeholder="请选择商机等级" size="small" clearable>
          <el-option :value="'A'" label="A"> </el-option>
          <el-option :value="'B'" label="B"> </el-option>
          <el-option :value="'C'" label="C"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="success" size="small" @click="getChanceList">查询</el-button>
        <el-button type="primary" size="small" plain @click="reset">重置</el-button>
        <el-button type="primary" size="small" @click="add">新增商机</el-button>
        <el-button v-if="keyList.includes('chance-export')" type="primary" size="small" @click="exportChance">导出商机</el-button>
        <el-button v-if="keyList.includes('chance-remove')" type="danger" size="small" @click="removeApproval">删除审批</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" style="width: 100%" border :cell-class-name="alarmsClass" @row-click="goDetails" @sort-change="sortChange">
      <el-table-column type="index" width="50" align="center" label="序号"> </el-table-column>
      <el-table-column prop="userRealName" label="商机负责人" width="150" align="center" sortable="custom"> </el-table-column>
      <el-table-column prop="customerName" label="客户" width="150" align="center"> </el-table-column>
      <el-table-column prop="content" label="项目内容" width="width" align="center"> </el-table-column>
      <el-table-column prop="rank" label="项目等级" width="120" align="center" sortable="custom">
        <template v-slot="{ row }">
          <el-tag v-if="row.rank" :type="row.rank === 'A' ? 'success' : row.rank === 'B' ? 'warning' : ''">{{ row.rank }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="money" label="项目金额(万元)" width="width" align="center"> </el-table-column>
      <el-table-column prop="current" label="项目当前情况" width="width" align="center"> </el-table-column>
      <el-table-column label="操作" width="150" fixed="right" align="center">
        <template v-slot="{ row }">
          <el-button v-if="keyList.includes('chance-remove')" type="danger" size="small" @click.stop="deleteChance(row)">删除</el-button>
          <el-button v-else type="danger" size="small" @click.stop="applyForRemove(row)">申请删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-if="list.length > 0"
      layout="total,prev, pager, next"
      style="margin-top: 15px; text-align: right"
      :page-sizes="[5, 10, 15, 20]"
      background
      :total="total"
      :page-size.sync="queryInfo.pageSize"
      :current-page.sync="queryInfo.pageNum"
      @size-change="getChanceList"
      @current-change="getChanceList"
    />
    <Add ref="addChance" @refresh="getChanceList" />
    <RemoveList ref="removeList" />
  </div>
</template>

<script>
import { chanceChanceList, chanceChanceListExport, chanceApplyForRemove, chanceRemove } from '@/api/chance'

import { formatDate } from '@/filters'
import Add from './components/Add'
import { mapGetters } from 'vuex'
import RemoveList from './components/RemoveList'
export default {
  name: 'Clientele',
  components: {
    Add,
    RemoveList
  },
  filters: {
    collectType(type) {
      // 1 校内招标 2 公开招标 3 直采
      return type === 1 ? '校内招标' : type === 2 ? '公开招标' : type === 3 ? '直采' : ''
    }
  },

  data() {
    return {
      queryInfo: {
        rank: null, // 等级 A B C
        realName: null, // 负责人
        pageNum: 1,
        pageSize: 10,
        sort: null,
        orderby: null
      },
      list: [],
      total: null,
      date: null,
      addDialog: false
    }
  },
  computed: {
    ...mapGetters(['keyList'])
  },

  created() {
    this.getChanceList()
  },

  methods: {
    async getChanceList() {
      const { data } = await chanceChanceList(this.queryInfo)
      this.total = data.total
      this.list = data.list
      console.log(data)
    },
    alarmsClass({ row }) {
      return row.isRed === 1 ? 'alarms-class' : ''
    },
    sortChange({ prop, order }) {
      console.log(prop, order)
      // 排序:  1 等级 2 负责人姓名
      this.queryInfo.sort = prop === 'rank' ? 1 : prop === 'userRealName' ? 2 : null
      this.queryInfo.orderby = order === 'ascending' ? 2 : order === 'descending' ? 1 : null
      this.getChanceList()
    },
    // 时间搜索的格式化
    datePickerChange(val) {
      if (val) {
        this.queryInfo.startTime = formatDate(val[0])
        this.queryInfo.endTime = formatDate(val[1], 'yyyy-MM-dd') + ' 23:59:59'
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getChanceList()
    },
    goDetails(row) {
      console.log(row)
      this.$router.push(`/chance/details/${row.chanceId}`)
    },
    add() {
      this.$refs.addChance.open()
    },

    // 重置搜索
    reset() {
      this.queryInfo = {
        customerName: null,
        source: null,
        level: null,
        realName: null,
        startTime: null,
        endTime: null,
        pageNum: 1,
        pageSize: 10,
        sort: null,
        orderby: null
      }
      this.date = null
      this.getChanceList()
    },
    // #region 导出
    exportChance() {
      chanceChanceListExport(this.queryInfo).then(({ data }) => {
        const headers = {
          code: '商机编号',
          createUserName: '商机创建人',
          customerName: '客户',
          majorName: '专业',
          contactsName: '联系人',
          name: '项目名称',
          content: '项目内容',
          rank: '项目等级',
          money: '项目金额(万元)',
          competitorInfo: '存在竞争对手情况',
          collectTypeName: '采购形式',
          inviteTime: '招标时间',
          current: '项目当前情况',
          remark: '备注',
          createTime: '创建时间',
          updateTime: '更新时间'
        }

        const res = this.formatJson(headers, data)
        import('@/vendor/Export2Excel').then((excel) => {
          excel.export_json_to_excel({
            header: Object.values(headers), // 表头 必填
            data: res, // 具体数据 必填
            filename: '商机列表' // 非必填
          })
        })
      })
    },
    formatJson(headers, rows) {
      return rows.map((item) => {
        return Object.keys(headers).map((key) => {
          return item[key]
        })
      })
    },
    // #endregion
    // #region 删除
    applyForRemove(row) {
      this.$confirm('确定申请删除该商机吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        chanceApplyForRemove({ id: row.chanceId }).then(({ data }) => {
          console.log(data)
          this.$message.success('申请成功')
          this.getChanceList()
        })
      })
    },
    deleteChance(row) {
      this.$confirm('确定删除该商机吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        chanceRemove({ id: row.chanceId }).then(({ data }) => {
          console.log(data)
          this.$message.success('删除成功')
          this.getChanceList()
        })
      })
    },
    removeApproval() {
      this.$refs.removeList.open()
    }
    // #endregion
  }
}
</script>

<style scoped lang="scss">
::v-deep {
  .alarms-class {
    background-color: #fff4f4;
  }
}
</style>
