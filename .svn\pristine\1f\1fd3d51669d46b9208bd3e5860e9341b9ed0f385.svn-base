<template>
  <div class="versionNumber-addOrEdit">
    <el-dialog
      v-loading.fullscreen.lock="loading"
      element-loading-text="数据保存中,请稍后"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.7)"
      :title="dialogTitle"
      :visible="showDialog"
      width="700px"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-form ref="formRef" :model="formInfo" :rules="rules" label-width="100px">
        <el-form-item label="产品编号:" prop="productCode">
          <el-input v-model="formInfo.productCode" placeholder="请输入产品编号" maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="产品名称:" prop="productName">
          <el-input v-model="formInfo.productName" placeholder="请输入产品名称" maxlength="50"></el-input>
        </el-form-item>
        <el-form-item v-if="!formInfo.versionNumberId" label="版本号:" prop="number">
          <el-input v-model="formInfo.number" placeholder="请输入版本号" maxlength="50"></el-input>
        </el-form-item>
        <el-form-item v-if="!formInfo.versionNumberId" label="svn地址:" prop="svnAddress">
          <el-input v-model="formInfo.svnAddress" placeholder="请输入svn地址" maxlength="200"></el-input>
        </el-form-item>
        <el-form-item v-if="!formInfo.versionNumberId" label="jenkins地址:">
          <el-input v-model="formInfo.jenkinsAddress" placeholder="请输入jenkins地址" maxlength="200"></el-input>
        </el-form-item>
        <el-form-item label="备注:">
          <el-input v-model="formInfo.remark" placeholder="请输入备注" maxlength="50"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="save">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { versionNumberAdd, versionNumberUpdate } from '@/api/versionNumber'
export default {
  name: 'VersionNumberAddOrEdit',
  props: {
    showDialog: {
      type: Boolean,
      require: true
    }
  },
  data() {
    return {
      loading: false,
      formInfo: {
        productCode: null, // 产品编号
        productName: null, // 产品名称
        number: null, // 版本号
        svnAddress: null, // svn地址
        jenkinsAddress: null, //  jenkins地址
        remark: null // 备注
      },
      rules: {
        productCode: [{ required: true, message: '请输入产品编号', trigger: 'blur' }],
        productName: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
        number: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
        svnAddress: [{ required: true, message: '请输入svn地址', trigger: 'blur' }]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.formInfo.versionNumberId ? '修改产品' : '新增产品'
    }
  },
  created() {},

  methods: {
    showDetails(row) {
      this.formInfo = this._.cloneDeep(row)
    },
    save() {
      this.$refs['formRef'].validate((val) => {
        if (val) {
          this.loading = true
          if (this.formInfo.versionNumberId) {
            versionNumberUpdate(this.formInfo)
              .then((res) => {
                this.$message.success('修改成功!')
                this.$emit('refreshList')
                this.close()
              })
              .finally(() => {
                this.loading = false
              })
          } else {
            versionNumberAdd(this.formInfo)
              .then((res) => {
                this.$message.success('新增成功!')
                this.$emit('refreshList')
                this.close()
              })
              .finally(() => {
                this.loading = false
              })
          }
        }
      })
    },
    close() {
      this.$emit('update:showDialog', false)
      this.$refs['formRef'].resetFields()
      setTimeout(() => {
        this.formInfo = {
          productCode: null, // 产品编号
          productName: null, // 产品名称
          number: null, // 版本号
          svnAddress: null, // svn地址
          remark: null // 备注
        }
      }, 600)
    }
  }
}
</script>
<style scoped lang="scss"></style>
