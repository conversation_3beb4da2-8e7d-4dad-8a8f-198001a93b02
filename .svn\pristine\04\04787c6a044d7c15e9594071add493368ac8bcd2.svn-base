import request from '@/utils/request'
/** 会议列表 */
export function meetingList(params) {
  return request({
    url: '/process/meeting/meetingList',
    method: 'get',
    params
  })
}
/** 会议室预约时间判断 */
export function meetingJudgeTime(data) {
  return request({
    url: '/process/meeting/judgeTime',
    method: 'POST',
    data
  })
}
/** 添加会议 */
export function meetingSave(data) {
  return request({
    url: '/process/meeting/save',
    method: 'POST',
    data
  })
}
/** 修改会议 */
export function meetingUpdate(data) {
  return request({
    url: '/process/meeting/update',
    method: 'POST',
    data
  })
}
/** 详情 */
export function meetingDetails(params) {
  return request({
    url: '/process/meeting/details',
    method: 'GET',
    params
  })
}
/** 删除文件 */
export function meetingDeleteFile(params) {
  return request({
    url: '/process/meeting/deleteFile',
    method: 'GET',
    params
  })
}
/** 修改文件名称 */
export function meetingUpdateFileName(params) {
  return request({
    url: '/process/meeting/updateFileName',
    method: 'GET',
    params
  })
}
/** 签到 */
export function meetingSign(params) {
  return request({
    url: '/process/meeting/sign',
    method: 'GET',
    params
  })
}
/** 人员确定 */
export function meetingDefine(params) {
  return request({
    url: '/process/meeting/define',
    method: 'GET',
    params
  })
}
/** 更新状态 */
export function meetingUpdateStatus(params) {
  return request({
    url: '/process/meeting/updateStatus',
    method: 'GET',
    params
  })
}
/** 补充会议纪要 */
export function meetingSummary(data) {
  return request({
    url: '/process/meeting/summary',
    method: 'POST',
    data
  })
}
