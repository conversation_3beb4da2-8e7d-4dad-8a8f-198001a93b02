import request from '@/utils/request'

/** 添加知识库 */
export function knowledgeBaseSave(data) {
  return request({
    url: '/knowledgeBase/save',
    method: 'POST',
    data
  })
}

/** 修改知识库 */
export function knowledgeBaseUdpate(data) {
  return request({
    url: '/knowledgeBase/udpate',
    method: 'POST',
    data
  })
}

/** 添加浏览次数 */
export function knowledgeBaseAddBrowse(params) {
  return request({
    url: '/knowledgeBase/addBrowse',
    method: 'GET',
    params
  })
}

/** 知识库管理列表 */
export function knowledgeBaseList(params) {
  return request({
    url: '/knowledgeBase/list',
    method: 'GET',
    params
  })
}

/** 删除知识库 */
export function knowledgeBaseDelete(params) {
  return request({
    url: '/knowledgeBase/delete',
    method: 'DELETE',
    params
  })
}

/** 添加评论 */
export function knowledgeBaseSaveComment(data) {
  return request({
    url: '/knowledgeBase/saveComment',
    method: 'POST',
    data
  })
}
/** 删除评论 */
export function knowledgeDeleteComment(params) {
  return request({
    url: '/knowledgeBase/deleteComment',
    method: 'DELETE',
    params
  })
}
/** 点赞及取消点赞 */
export function knowledgeLike(params) {
  return request({
    url: '/knowledgeBase/like',
    method: 'GET',
    params
  })
}
/** 添加回复 */
export function knowledgeBaseSaveReply(data) {
  return request({
    url: '/knowledgeBase/saveReply',
    method: 'POST',
    data
  })
}
/** 详情 */
export function knowledgeBaseDetails(params) {
  return request({
    url: '/knowledgeBase/details',
    method: 'GET',
    params
  })
}
