<template>
  <div class="versionNumber-DetailsDialog">
    <el-dialog title="版本号详情" :visible="showDialog" width="1500px" custom-class="addVersionNumberDialog" top="10vh" @open="openDialog" @close="close">
      <el-row style="margin-bottom: 20px">
        <el-button type="primary" size="small" @click="addDialog = true">添加版本号</el-button>
      </el-row>
      <el-table :data="list" style="width: 100%" border>
        <el-table-column prop="number" label="版本号" align="center" width="120"> </el-table-column>
        <el-table-column prop="description" label="说明" align="center" width="width">
          <template v-slot="{ row }">
            <!-- <div style="white-space: pre-wrap">{{ row.description }}</div> -->
            <el-button type="primary" size="small" @click="lookDetails(row)">查看详细说明</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="releaseTime" label="发布时间" align="center" width="120"> </el-table-column>
        <el-table-column prop="svnAddress" label="svn地址" align="center" width="width"> </el-table-column>
        <el-table-column prop="jenkinsAddress" label="jenkins地址" align="center" width="width"> </el-table-column>
        <el-table-column prop="prop" label="启用状态" align="center" width="120">
          <template v-slot="{ row }">
            <el-tag :type="row.state === 1 ? 'success' : 'danger'" effect="dark">{{ row.state === 1 ? '启用' : '禁用' }}</el-tag>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="remark" label="备注" align="center" width="width"> </el-table-column> -->
        <el-table-column prop="realName" label="创建人" align="center" width="120"> </el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center" width="width"> </el-table-column>
        <el-table-column label="操作" align="center" width="250">
          <template v-slot="{ row }">
            <el-button :type="row.state === 1 ? 'info' : 'success'" size="small" @click="updateState(row)">{{ row.state === 1 ? '禁用' : '启用' }}</el-button>
            <el-button type="warning" size="small" @click="edit(row)">修改</el-button>
            <el-button type="danger" size="small" @click="del(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-if="list.length > 0"
        background
        style="text-align: center; margin-top: 15px"
        layout="total, sizes, prev, pager, next"
        :page-sizes="[5, 10, 15, 30]"
        :total="total"
        :page-size.sync="queryInfo.pageSize"
        :current-page.sync="queryInfo.pageNum"
        @size-change="getList"
        @current-change="getList"
      />
      <div slot="footer" style="text-align: center">
        <el-button type="primary" @click="close">关 闭</el-button>
      </div>
    </el-dialog>
    <!-- 新增修改版本号弹窗 -->
    <el-dialog
      v-loading.fullscreen.lock="loading"
      element-loading-text="数据保存中,请稍后"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.7)"
      :title="dialogTitle"
      :visible.sync="addDialog"
      width="1000px"
      :close-on-click-modal="false"
    >
      <el-form ref="formRef" :model="formInfo" :rules="rules" label-width="120px">
        <el-form-item label="版本号:" prop="number">
          <el-input v-model="formInfo.number" placeholder="请输入版本号" maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="说明:">
          <el-input v-model="formInfo.description" type="textarea" placeholder="请输入说明" maxlength="500" :autosize="{ minRows: 5 }"></el-input>
        </el-form-item>
        <el-form-item label="发布时间:" prop="releaseTime">
          <el-date-picker v-model="formInfo.releaseTime" type="date" value-format="yyyy-MM-dd" placeholder="选择日期"> </el-date-picker>
        </el-form-item>
        <el-form-item label="svn地址:" prop="svnAddress">
          <el-input v-model="formInfo.svnAddress" placeholder="请输入svn地址" maxlength="200"></el-input>
        </el-form-item>
        <el-form-item label="jenkins地址:" prop="jenkinsAddress" :required="isNewData">
          <el-input v-model="formInfo.jenkinsAddress" placeholder="请输入jenkins地址" maxlength="200"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="addClose">取 消</el-button>
        <el-button type="primary" @click="save">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 详细说明 -->
    <el-dialog :title="descriptionDialogTitle" :visible.sync="descriptionDialog" custom-class="descriptionDialog" width="width">
      <el-descriptions class="margin-top" :column="1" border>
        <el-descriptions-item label="版本说明">
          <div style="white-space: pre-wrap">{{ descriptionDialogText }}</div>
        </el-descriptions-item>
      </el-descriptions>
      <div slot="footer">
        <el-button type="primary" @click="descriptionDialog = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  versionNumberDetailsList,
  versionNumberDetailAdd,
  versionNumberDetailUpdate,
  versionNumberDetailDelete,
  versionNumberDetailJudgeUpdate,
  versionNumberDetailJudgeUpdateState
} from '@/api/versionNumber'
import { isExternal } from '@/utils/validate'
export default {
  name: '',
  props: {
    showDialog: {
      type: Boolean,
      require: true
    },
    id: {
      type: Number || String,
      default: null
    }
  },
  data() {
    var validAddress = (rule, value, callback) => {
      console.log(rule)
      if (rule.field === 'jenkinsAddress' && this.isNewData) {
        if (!value) {
          callback(new Error('jenkins地址不能为空'))
        }
      }
      if (!value) {
        callback()
      } else {
        if (isExternal(value)) {
          callback()
        } else {
          callback(new Error('输入内容不符合规则'))
        }
      }
    }
    return {
      queryInfo: {
        pageNum: 1,
        pageSize: 5,
        versionNumberId: null
      },
      list: [],
      total: 0,
      addDialog: false,
      loading: false,
      formInfo: {
        detailId: null, // 版本号详情id
        versionNumberId: null, // 版本号id
        number: null, // 版本号
        description: null, // 说明
        releaseTime: null, // 发布时间
        svnAddress: null, // svn地址
        jenkinsAddress: null, // jenkins地址
        remark: null // 备注
      },
      rules: {
        number: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
        svnAddress: [{ validator: validAddress, trigger: 'blur' }],
        jenkinsAddress: [{ validator: validAddress, trigger: 'blur' }],
        releaseTime: [{ required: true, message: '请选择发布时间', trigger: 'change' }]
      },
      isNewData: false,
      descriptionDialog: false,
      descriptionDialogTitle: '',
      descriptionDialogText: ''
    }
  },
  computed: {
    dialogTitle() {
      return this.formInfo.detailId ? '修改版本' : '新增版本'
    }
  },
  created() {},
  methods: {
    openDialog() {
      this.queryInfo.versionNumberId = this.id
      this.getList()
    },
    async getList() {
      const { data } = await versionNumberDetailsList(this.queryInfo)
      this.list = data.list
      this.total = data.total
    },
    del(row) {
      if (row.state === 1) return this.$message.warning('启用状态下不可进行删除操作')
      this.$confirm('确定要删除该版本吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await versionNumberDetailDelete({ id: row.detailId })
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    close() {
      this.$emit('update:showDialog', false)
    },
    // #regioned 新增修改代码区域
    async edit(row) {
      if (row.state === 1) return this.$message.warning('启用状态下不可进行修改操作')
      this.formInfo = this._.cloneDeep(row)
      this.addDialog = true
      const { data } = await versionNumberDetailJudgeUpdate({ versionNumberId: this.id, detailId: row.detailId })
      this.isNewData = data === 1
    },
    addClose() {
      this.addDialog = false
      this.isNewData = false
      this.formInfo = {
        detailId: null, // 版本号详情id
        versionNumberId: null, // 版本号id
        number: null, // 版本号
        description: null, // 说明
        releaseTime: null, // 发布时间
        svnAddress: null, // svn地址
        jenkinsAddress: null, // jenkins地址
        remark: null // 备注
      }
      this.$refs['formRef'].resetFields()
    },
    save() {
      this.$refs['formRef'].validate((val) => {
        if (val) {
          this.loading = true
          this.formInfo.versionNumberId = this.id
          if (this.formInfo.detailId) {
            versionNumberDetailUpdate(this.formInfo)
              .then((res) => {
                this.$message.success('修改版本号成功!')
                this.addClose()
                this.getList()
              })
              .finally(() => {
                this.loading = false
              })
          } else {
            versionNumberDetailAdd(this.formInfo)
              .then((res) => {
                this.$message.success('新增版本号成功!')
                this.addClose()
                this.getList()
              })
              .finally(() => {
                this.loading = false
              })
          }
        }
      })
    },
    // #endregioned
    async updateState(row) {
      const { data } = await versionNumberDetailJudgeUpdate({ versionNumberId: this.id, detailId: row.detailId })
      if (data === 1) return this.$message.warning('最新版本不可禁用')
      const stateText = row.state === 1 ? '禁用' : '启用'
      this.$confirm(`确定要${stateText}该版本吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await versionNumberDetailJudgeUpdateState({ state: row.state === 1 ? 2 : 1, detailId: row.detailId })

          this.$message({
            type: 'success',
            message: '状态修改成功!'
          })
          this.getList()
        })
        .catch(() => {})
    },
    lookDetails(row) {
      this.descriptionDialogTitle = row.number + '的版本说明'
      this.descriptionDialogText = row.description
      this.descriptionDialog = true
      console.log(row)
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .addVersionNumberDialog {
    max-height: 750px;
    overflow: auto;
    .el-dialog__header {
      padding-bottom: 25px;
    }
    .el-dialog__body {
      padding-top: 0;
    }
  }
  .descriptionDialog {
    .el-dialog__body {
      padding-top: 10px;
    }
    .el-descriptions-item__label {
      width: 120px;
      text-align: center !important;
      font-size: 18px;
    }
    .el-descriptions-item__content {
      font-size: 16px;
      color: #333;
    }
  }
}
</style>
