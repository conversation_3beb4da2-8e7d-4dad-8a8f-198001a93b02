// 项目管理
import request from '@/utils/request'

/** 项目及售后记录列表 */
export function projectList(params) {
  return request({
    url: '/process/project/list',
    method: 'GET',
    params
  })
}

/** 添加项目或 售后记录 */
export function projectSaveProject(data) {
  return request({
    url: '/process/project/saveProject',
    method: 'POST',
    data
  })
}
/** 删除项目或售后记录 */
export function projectDelete(params) {
  return request({
    url: '/process/project/delete',
    method: 'DELETE',
    params
  })
}
/** 项目管理或售后记录详情 - 人员列表 */
export function projectUsersDetails(params) {
  return request({
    url: '/process/project/usersDetails',
    method: 'GET',
    params
  })
}

/** 添加人员 */
export function projectSaveUser(data) {
  return request({
    url: '/process/project/saveUser',
    method: 'POST',
    data
  })
}
/** 移除人员 */
export function projectRemoveUser(data) {
  return request({
    url: '/process/project/removeUser',
    method: 'POST',
    data
  })
}

/** 项目管理或售后记录详情 - 文件列表 */
export function projectFilesDetails(params) {
  return request({
    url: '/process/project/filesDetails',
    method: 'GET',
    params
  })
}

/** 添加附件(项目管理或售后记录) */
export function projectSaveFile(data) {
  return request({
    url: '/process/project/saveFile',
    method: 'POST',
    data
  })
}
/** 项目管理或售后记录详情 - 版本列表 */
export function projectVersionDetails(params) {
  return request({
    url: '/process/project/versionDetails',
    method: 'GET',
    params
  })
}
/** 保存版本记录 */
export function projectSaveVersion(data) {
  return request({
    url: '/process/project/saveVersion',
    method: 'POST',
    data
  })
}
/** 更新版本记录 */
export function projectUpdateVersion(data) {
  return request({
    url: '/process/project/updateVersion',
    method: 'POST',
    data
  })
}
/** 删除版本记录 */
export function projectDeleteVersion(params) {
  return request({
    url: '/process/project/deleteVersion',
    method: 'GET',
    params
  })
}
/** 项目管理或售后记录详情 - 施工列表 */
export function projectBuildDetails(params) {
  return request({
    url: '/process/project/buildDetails',
    method: 'GET',
    params
  })
}
/** 保存施工记录 */
export function projectBuildSaveBuild(data) {
  return request({
    url: '/process/project/saveBuild',
    method: 'POST',
    data
  })
}
/** 更新施工记录 */
export function projectBuildUpdateBuild(data) {
  return request({
    url: '/process/project/updateBuild',
    method: 'POST',
    data
  })
}
/** 删除施工记录 */
export function projectBuildDeleteBuild(params) {
  return request({
    url: '/process/project/deleteBuild',
    method: 'GET',
    params
  })
}
/** 修改 项目管理 或 售后记录 */
export function projectUpdateProject(data) {
  return request({
    url: '/process/project/updateProject',
    method: 'POST',
    data
  })
}

/** 项目及售后记录详情 */
export function projectDetails(params) {
  return request({
    url: '/process/project/details',
    method: 'GET',
    params
  })
}

