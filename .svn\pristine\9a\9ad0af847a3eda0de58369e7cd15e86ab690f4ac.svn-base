<template>
  <div class="addressBook-container">
    <div class="addressBook_header">
      <el-input v-model="userInfo.realName" placeholder="请输入姓名" clearable>
        <template v-slot:append>
          <img src="@/assets/personCenter/search_icon.png" alt="" @click="getUserList" />
        </template>
      </el-input>
      <span class="organization_chart" @click="organizationImg = true">组织架构图</span>
    </div>
    <div class="addressBook_body">
      <div class="addressBook_body_left">
        <el-tree ref="Tree" :data="OrganizationTree" :props="defaultProps" node-key="id" default-expand-all highlight-current :current-node-key="currentKey" @node-click="handleNodeClick">
          <template v-slot="{ data }">
            <span>
              <img v-if="data.parentId === 0" src="@/assets/personCenter/organization_icon.png" alt="" /> {{ data.organizationName }}
              <span v-if="data.parentId !== 0">({{ data.count ? data.count : 0 }})</span>
            </span>
          </template>
        </el-tree>
      </div>
      <div class="addressBook_body_right">
        <div :style="{ width: showDetails === true ? '922px' : '100%' }">
          <el-table :data="userList" style="width: 100%" :header-cell-style="{ color: '#0B1A44' }" @row-click="lookDetails">
            <el-table-column prop="realName" label="姓名" width="width" align="center" class-name="realName">
              <template v-slot="{ row }">
                <div>
                  <img :src="row.headurl" alt="" />
                  <span>{{ row.realName }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="username" label="账号" width="width"> </el-table-column>
            <el-table-column align="center" prop="sexText" label="性别" width="width"> </el-table-column>
            <el-table-column align="center" prop="mobile" label="手机号码" width="width"> </el-table-column>
            <el-table-column align="center" prop="organizationName" label="部门" width="width"> </el-table-column>
            <el-table-column align="center" prop="jobName" label="岗位" width="width"> </el-table-column>
          </el-table>
          <el-pagination
            style="text-align: center; margin-top: 38px"
            background
            layout="prev, pager, next"
            :total="total"
            :page-size.sync="userInfo.pageSize"
            :current-page.sync="userInfo.pageNum"
            @size-change="getUserList"
            @current-change="getUserList"
          />
        </div>

        <div v-if="showDetails" class="details">
          <el-row type="flex" justify="end">
            <i class="el-icon-close" @click="close"></i>
          </el-row>
          <el-row class="avatar" type="flex" align="middle">
            <el-avatar :size="56" :src="detailsInfo.headurl"></el-avatar>
            <span class="realName">{{ detailsInfo.realName }}</span>
            <img v-if="detailsInfo.sex === 'F'" class="sexImg" src="@/assets/meeting/wuman.png" alt="" />
            <img v-else class="sexImg" src="@/assets/meeting/man.png" alt="" />
          </el-row>
          <el-row type="flex" class="phone">
            <span> 手机号码：</span>
            <span> {{ detailsInfo.mobile }} </span>
          </el-row>
          <el-row type="flex" class="organizationName">
            <span> 部门：</span>
            <span> {{ detailsInfo.organizationName }} </span>
          </el-row>
          <el-row type="flex" class="jobName">
            <span> 岗位：</span>
            <span> {{ detailsInfo.jobName }} </span>
          </el-row>
        </div>
      </div>
    </div>

    <!-- 组织架构图 -->
    <el-dialog title="组织架构图" :visible.sync="organizationImg" width="916px">
      <div class="title">
        <img src="@/assets/personCenter/organizationImg_title.png" alt="" />
      </div>
      <div>
        <img src="@/assets/personCenter/organizationImg.png" alt="" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getOrganizationTree } from '@/api/organization'
import { getList } from '@/api/systemUser'
export default {
  name: '',
  data() {
    return {
      OrganizationTree: [],
      organizationImg: false,
      defaultProps: {
        children: 'children',
        label: 'organizationName'
      },
      currentKey: null,
      userInfo: {
        organizationId: null,
        realName: null,
        pageNum: 1,
        pageSize: 10
      },
      userList: [],
      total: null,
      showDetails: false,
      detailsInfo: {}
    }
  },
  created() {
    this.getOrganizationTree()
  },
  methods: {
    async getOrganizationTree() {
      const { data } = await getOrganizationTree()
      this.OrganizationTree = data
      this.currentKey = data[0].children[0].id
      this.$nextTick(function () {
        this.$refs.Tree.setCurrentKey(this.currentKey)
        this.userInfo.organizationId = this.currentKey
        this.getUserList()
      })
    },
    async getUserList() {
      const { data } = await getList(this.userInfo)
      this.userList = data.list
      this.total = data.total
    },
    handleNodeClick(node) {
      this.userInfo.organizationId = node.id
      this.getUserList()
    },
    lookDetails(val) {
      this.detailsInfo = { ...val }
      this.showDetails = true
      console.log(this.detailsInfo)
    },
    close() {
      this.showDetails = false
    }
  }
}
</script>

<style scoped lang="scss">
.addressBook-container {
  width: 1581px;
  height: 826px;
  margin-left: 41px;
  margin-top: 26px;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  overflow: hidden;
  .addressBook_header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 81px;
    padding-left: 54px;
    padding-right: 32px;
    background: #f6f9ff;
    border-radius: 8px 8px;
    ::v-deep {
      .el-input {
        position: relative;
        width: 244px;
        height: 34px;
        .el-input__inner {
          width: 244px;
          height: 34px;
          border-radius: 17px;
          border-color: #d8dbe1;
        }
        .el-input-group__append {
          background: none;
          width: initial;
          padding: 0;
          border: none;
          position: absolute;
          right: 4px;
          top: 3px;
          cursor: pointer;
        }
        .el-input__icon {
          line-height: 35px !important;
        }
      }
    }
    .organization_chart {
      display: inline-block;
      width: 92px;
      height: 32px;
      line-height: 32px;
      border: 1px solid #3464e0;
      font-size: 14px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #3464e0;
      text-align: center;
      border-radius: 6px;
      cursor: pointer;
      &:hover {
        border: 1px solid #355fce;
        color: #355fce;
      }
    }
  }
  .addressBook_body {
    display: flex;
    height: 100%;
    .addressBook_body_left {
      width: 162px;
      height: 100%;
      border-right: 1px solid #eeeeef;
      padding-top: 20px;
      ::v-deep {
        .el-tree-node__content {
          height: 36px !important;
        }
        .el-tree-node__content {
          height: 100%;
          padding-left: 10px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #4a4a4a;
        }
        .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
          background: #eff3fd;
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #3464e0;
        }
      }
    }
    .addressBook_body_right {
      display: flex;
      justify-content: space-between;
      flex: 1;
      padding-left: 75px;
      .details {
        width: 370px;
        height: 720px;
        padding: 24px 24px 24px 32px;
        background: #f6f9ff;
        border: 1px solid #e6e6e6;
        border-right: none;
        border-radius: 6px 0 0 6px;
        .el-icon-close {
          font-size: 16px;
          font-weight: bold;
          color: #b1bac7;
          cursor: pointer;
        }
        .avatar {
          margin-bottom: 24px;
          .realName {
            margin: 0 8px 0 16px;
            font-size: 16px;
            font-family: Microsoft YaHei-Bold, Microsoft YaHei;
            font-weight: bold;
            color: #4a4a4a;
          }
          .sexImg {
            width: 10px;
            height: 14px;
          }
        }
        .phone,
        .organizationName,
        .jobName {
          margin-bottom: 30px;
          & > span:first-of-type {
            color: #868b9f;
          }
          & > span:last-of-type {
            color: #4a4a4a;
          }
        }
      }
      ::v-deep {
        .el-table {
          height: 584px;
          padding-top: 35px;

          &::before {
            display: none;
          }
          .realName > .cell > div {
            // display: flex;
            // justify-content: center;
            // align-items: center;
            // padding-left: 55px;
            min-width: 72px;
            margin: 0 auto;
            text-align: center;
            span {
              font-size: 14px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #4a4a4a;
            }
            img {
              display: inline-block;
              margin-right: 5px;
              width: 24px;
              height: 24px;
              object-fit: cover;
              border-radius: 50%;
              vertical-align: top;
            }
          }
        }
      }
    }
  }
  // 组织架构图
  ::v-deep {
    .el-dialog {
      height: 516px;
      border-radius: 8px 8px 8px 8px;
      background: url('../../assets/personCenter/organizationImg_bg.png') no-repeat;
      background-size: cover;
      .el-dialog__header {
        padding: 24px;
        padding-bottom: 0;
        .el-dialog__title {
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
        .el-dialog__headerbtn {
          .el-icon-close {
            font-size: 16px !important;
            color: #657081;
            font-weight: bold;
          }
        }
      }
      .el-dialog__body {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding-top: 42px;
        .title {
          margin-bottom: 30px;
        }
      }
    }
  }
}
</style>
