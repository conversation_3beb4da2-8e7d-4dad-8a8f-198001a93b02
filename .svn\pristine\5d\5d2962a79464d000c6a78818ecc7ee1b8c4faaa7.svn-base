import request from '@/utils/request'
/** 添加Bug */
export function tBugAdd(data) {
  return request({
    url: '/tBug/add',
    method: 'POST',
    data
  })
}
/** 修改Bug */
export function tBugUpdate(data) {
  return request({
    url: '/tBug/update',
    method: 'POST',
    data
  })
}
/** 删除Bug */
export function tBugRemove(params) {
  return request({
    url: '/tBug/remove',
    method: 'DELETE',
    params
  })
}
/** Bug列表 */
export function tBugList(data) {
  return request({
    url: '/tBug/list',
    method: 'POST',
    data
  })
}
/** Bug确认*/
export function tBugConfirm(params) {
  return request({
    url: '/tBug/confirm',
    method: 'GET',
    params
  })
}
/** Bug历史记录*/
export function tBugBugLogRecord(params) {
  return request({
    url: '/tBug/bugLogRecord',
    method: 'GET',
    params
  })
}
/** Bug详情*/
export function tBugDetail(params) {
  return request({
    url: '/tBug/detail',
    method: 'GET',
    params
  })
}
/** 列表导出 */
export function tBugListExport(data) {
  return request({
    url: '/tBug/listExport',
    method: 'POST',
    data
  })
}
/** 回退Bug */
export function tBugRollback(params) {
  return request({
    url: '/tBug/back',
    method: 'get',
    params: {
      bugId: params.bugId,
      remark: params.remark // 原因
    }
  })
}
