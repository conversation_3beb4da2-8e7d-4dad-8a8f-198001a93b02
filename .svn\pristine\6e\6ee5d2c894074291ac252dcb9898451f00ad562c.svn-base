import { login, logout, findUserRoleConfiguration } from '@/api/user'
import { getToken, setToken, removeToken, setPassword, removePassword } from '@/utils/auth'
import { resetRouter } from '@/router'

const getDefaultState = () => {
  return {
    token: getToken(),
    name: '',
    avatar: '',
    realName: '',
    userId: '',
    jobName: '', // 岗位
    organizationId: null,
    organizationName: '', // 部门名称
    keyList: []
  }
}

const state = getDefaultState()

const mutations = {
  RESET_STATE: (state) => {
    Object.assign(state, getDefaultState())
  },
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_REALNAME: (state, realName) => {
    state.realName = realName
  },
  SET_userId: (state, userId) => {
    state.userId = userId
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_KEYLIST: (state, list) => {
    state.keyList = list
  },
  SET_ORGANIZATIONID: (state, organizationId) => {
    state.organizationId = organizationId
  },
  // 设置部门名称
  SET_ORGANIZATIONNAME: (state, organizationName) => {
    state.organizationName = organizationName
  },
  // 设置岗位
  SET_JOBNAME: (state, jobName) => {
    state.jobName = jobName
  }
}

const actions = {
  // user login
  login({ commit }, userInfo) {
    const { username, password } = userInfo
    return new Promise((resolve, reject) => {
      login({ username: username.trim(), password: password })
        .then((response) => {
          const { data } = response
          commit('SET_TOKEN', data)
          setPassword(password)
          setToken(data)
          resolve()
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  // get user info
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      findUserRoleConfiguration()
        .then((res) => {
          const { data } = res
          if (!data) {
            return reject('验证失败请重新登录')
          }
          commit('SET_KEYLIST', data.keyList)
          commit('SET_NAME', data.user.username)
          commit('SET_REALNAME', data.user.realName)
          commit('SET_JOBNAME', data.user.jobName)
          commit('SET_ORGANIZATIONID', data.user.organizationId)
          commit('SET_ORGANIZATIONNAME', data.user.organizationName)
          commit('SET_userId', data.user.userId)
          commit('SET_AVATAR', data.user.headurl)
          resolve(data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  // user logout
  logout({ commit, state }) {
    return new Promise((resolve, reject) => {
      logout(state.token)
        .then(() => {
          removeToken() // must remove  token  first
          resetRouter()
          removePassword()
          commit('RESET_STATE')
          resolve()
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise((resolve) => {
      removeToken() // must remove  token  first
      commit('RESET_STATE')
      resolve()
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
