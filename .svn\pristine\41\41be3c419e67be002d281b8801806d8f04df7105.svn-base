<template>
  <div class="app-container">
    <!-- 添加的dialog -->
    <el-dialog title="编辑商机" :visible.sync="editDialog" width="1150px" :close-on-click-modal="false" @close="close">
      <el-form ref="form" :model="formInfo" label-width="130px" inline>
        <el-form-item label="商机编号:" prop="code" class="level">
          <el-input v-model="formInfo.code" disabled></el-input>
        </el-form-item>
        <el-form-item label="客户名称:" prop="customerName" class="level">
          <el-input v-model="formInfo.customerName" disabled></el-input>
        </el-form-item>
        <el-form-item label="客户联系人:" prop="contactsName" class="level">
          <el-input v-model="formInfo.contactsName" disabled></el-input>
        </el-form-item>
        <el-form-item label="项目名称:" prop="name" class="level">
          <el-input v-model="formInfo.name" disabled></el-input>
        </el-form-item>
        <el-form-item label="项目内容:" prop="content" class="name_input">
          <el-input v-model="formInfo.content" placeholder="请输入项目内容" type="textarea" maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="项目等级:" prop="rank" class="level">
          <el-select v-model="formInfo.rank" placeholder="请选择项目等级">
            <el-option :value="'A'" label="A"> </el-option>
            <el-option :value="'B'" label="B"> </el-option>
            <el-option :value="'C'" label="C"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="项目金额(万元):" prop="money" class="name_input">
          <el-input v-model="formInfo.money" placeholder="请输入项目金额" type="number"></el-input>
        </el-form-item>
        <el-form-item label="存在竞争对手情况:" prop="competitorInfo" class="name_input">
          <el-input v-model="formInfo.competitorInfo" placeholder="请输入竞争对手情况" type="textarea" maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="采购形式:" prop="collectType" class="level">
          <el-select v-model="formInfo.collectType" placeholder="请选择采购形式">
            <el-option :value="1" label="校内招标"> </el-option>
            <el-option :value="2" label="公开招标"> </el-option>
            <el-option :value="3" label="直采"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="项目当前情况:" prop="current" class="name_input">
          <el-input v-model="formInfo.current" placeholder="请输入项目当前情况" type="textarea" maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="招标时间:" prop="inviteTime" class="name_input">
          <el-date-picker v-model="formInfo.inviteTime" type="date" placeholder="选择日期" :format="'yyyy-MM-dd'" value-format="yyyy-MM-dd"></el-date-picker>
        </el-form-item>
        <el-form-item label="备注:" prop="remark" class="name_input">
          <el-input v-model="formInfo.remark" placeholder="请输入备注" type="textarea" maxlength="100"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="editDialog = false">取 消</el-button>
        <el-button type="primary" @click="subData">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { chanceUpdateChance, chanceSaveRecord } from '@/api/chance'
import { formatDate } from '@/filters'
export default {
  name: '',
  data() {
    return {
      editDialog: false,
      formInfo: {
        chanceId: null,
        content: null, // 项目内容
        rank: null, // 等级 A B C
        money: null, // 项目金额(万元)
        competitorInfo: null, // 存在竞争对手情况
        collectType: null, // 采购形式 1 校内招标 2 公开招标 3 直采
        inviteTime: null, // 招标时间
        current: null, // 项目当前情况
        remark: null, // 备注
        code: null,
        customerName: null,
        contactsName: null,
        name: null
      },
      oldInfo: {}
    }
  },

  created() {},
  methods: {
    open(info) {
      this.editDialog = true
      for (const key in this.formInfo) {
        if (key === 'customerName') {
          this.formInfo[key] = info.customerName ? info.customerName : ''
        } else if (key === 'contactsName') {
          this.formInfo[key] = info.contactsName ? info.contactsName : ''
        } else {
          this.formInfo[key] = info[key]
        }
      }
      if (this.formInfo.collectType) {
        this.formInfo.collectType = parseInt(this.formInfo.collectType)
      }
      this.oldInfo = JSON.parse(JSON.stringify(this.formInfo))
      console.log(this.formInfo)
    },
    close() {
      this.formInfo = {
        chanceId: null,
        content: null,
        rank: null,
        money: null,
        competitorInfo: null,
        collectType: null,
        inviteTime: null,
        current: null,
        remark: null,
        code: null,
        customerName: null,
        contactsName: null,
        name: null
      }
    },
    subData() {
      this.$refs['form'].validate(async (val) => {
        if (!val) return
        const loading = this.$loading({
          lock: true,
          text: '数据保存中...'
        })
        try {
          await chanceUpdateChance(this.formInfo)
          this.$message.success('更新商机成功!')
          this.$emit('refresh')
          this.editDialog = false
          this.addRecord()
        } catch (error) {
          console.log(error)
          // this.$message.error('更新商机失败!')
        } finally {
          loading.close()
        }
      })
    },
    async addRecord() {
      // 比对oldInfo和formInfo，两种情况，拿取变化的字段，变化分为两种变化
      // 1.原来字段为空变成有数据。 2.原来的数据有变化。type：1，2
      const changes = []
      for (const key in this.formInfo) {
        // 去除空格变化
        const oldKey = this.oldInfo[key]
        const newKey = this.formInfo[key]
        if (oldKey !== newKey) {
          changes.push({
            type: oldKey ? 2 : 1,
            key: key,
            value: this.needValue(key, newKey)
          })
        }
      }
      if (changes.length > 0) {
        const data = {
          chanceId: this.formInfo.chanceId,
          operType: 1,
          content: JSON.stringify(changes)
        }
        await chanceSaveRecord(data)
      }
      console.log(changes)
    },
    needValue(key, value) {
      if (key === 'inviteTime') {
        return formatDate(value, 'yyyy-MM-dd hh:mm:ss')
      }
      if (key === 'collectType') {
        return value === 1 ? '校内招标' : value === 2 ? '公开招标' : '直采'
      }
      return value
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .el-dialog__body {
    padding: 10px 30px;
    max-height: 600px;
    overflow: auto;
  }
  .el-form-item__content {
    .el-date-editor.el-input,
    .el-date-editor.el-input__inner {
      width: 400px !important;

      background: #ffffff;
      border-radius: 4px 4px 4px 4px;
    }
  }
  .name_input {
    .el-input__inner,
    .el-textarea__inner {
      width: 400px !important;
    }
  }
  .level {
    .el-input__inner {
      width: 400px;
    }
  }
}
</style>
