<template>
  <div class="app-container">
    <el-row :gutter="50" class="top" type="flex" justify="space-between" align="middle" style="margin-bottom: 16px">
      <el-col :span="6">
        <div class="top_left">
          <span class="meeting_icon">
            <img src="@/assets/training/training_icon.png" alt="" />
            培训管理
          </span>
        </div>
      </el-col>
    </el-row>
    <el-card style="border-radius: 8px">
      <div slot="header">
        <img src="@/assets/training/add.png" alt="" />
        <el-radio-group v-model="queryInfo.type" style="padding-left: 40px" @change="geTrainList">
          <el-radio-button :label="1">
            <span>我接收的</span>
          </el-radio-button>
          <el-radio-button :label="2">
            <span>我发起的</span>
          </el-radio-button>
        </el-radio-group>
      </div>
      <div style="background: #f5f5f5; border-radius: 8px; padding: 25px 73px 32px 57px">
        <el-row :gutter="10" class="searchForm">
          <el-col :span="24">
            <el-form ref="form" label-width="90px" inline>
              <el-form-item label="培训状态:" class="trainStatus">
                <!-- <el-select v-model="queryInfo.trainStatus" placeholder="请选择培训状态" size="small" clearable>
                <el-option label="未开始" :value="1"> </el-option>
                <el-option label="进行中" :value="2"> </el-option>
                <el-option label="已结束" :value="3"> </el-option>
                <el-option label="已终止" :value="4"> </el-option>
              </el-select> -->
                <el-radio-group v-model="queryInfo.trainStatus">
                  <el-radio :label="null">全部</el-radio>

                  <el-radio :label="1">未开始</el-radio>
                  <el-radio :label="2">进行中</el-radio>
                  <el-radio :label="3">已结束</el-radio>
                  <el-radio :label="4">已终止</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="培训时间:" class="trainTime">
                <el-date-picker v-model="queryInfo.startTime" type="date" size="small" prefix-icon="" placeholder="选择开始时间" @change="geTrainList"> </el-date-picker>
                至
                <el-date-picker v-model="queryInfo.endTime" type="date" size="small" prefix-icon="" placeholder="选择结束时间" @change="geTrainList"> </el-date-picker>
              </el-form-item>
              <el-form-item class="trainName">
                <el-input v-model="queryInfo.trainName" size="small" placeholder="请输入培训标题" maxlength="50" clearable>
                  <el-button slot="append" type="primary" icon="el-icon-search" size="small"></el-button>
                </el-input>
              </el-form-item>

              <!-- <el-form-item label="主讲人:">
                <el-input v-model="queryInfo.trainUserName" size="small" placeholder="请输入主讲人名称" maxlength="50" clearable></el-input>
              </el-form-item> -->

              <!-- <el-form-item>
                <el-button type="success" size="small" @click="geTrainList"> 查询 </el-button>
                <el-button type="primary" size="small" @click="reset">重置 </el-button>
                <el-button v-if="queryInfo.type === 2" type="primary" size="small" @click="showDialog = true">添加培训 </el-button>
              </el-form-item> -->
            </el-form>
          </el-col>
        </el-row>
        <div>
          <el-table :data="list" style="width: 100%" border>
            <!-- <el-table-column prop="meetingCode" label="培训编号" width="width" align="cent er"> </el-table-column> -->
            <el-table-column prop="trainName" label="培训标题" width="width" align="center"> </el-table-column>
            <!-- <el-table-column prop="meetingName" label="培训标题" width="width" align="center"> </el-table-column> -->
            <el-table-column prop="trainStatus" label="培训状态" width="width" align="center">
              <template v-slot="{ row }">
                <span>{{ row.trainStatus | trainStatus }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="trainUserName" label="主讲人" width="width" align="center"> </el-table-column>
            <el-table-column prop="startTime" label="培训时间" width="width" align="center"> </el-table-column>
            <el-table-column prop="meetingRoomName" label="培训地点" width="width" align="center"> </el-table-column>
            <el-table-column prop="powerType" label="培训范围" width="width" align="center">
              <template v-slot="{ row }">
                <span>{{ row.powerType | powerType }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="score" label="培训得分" width="width" align="center"> </el-table-column>
            <el-table-column prop="distanceTime" label="距离培训开始时间" width="width" align="center">
              <template v-slot="{ row }">
                <span>{{ row.distanceTime }}分钟</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="width" align="center">
              <template v-slot="{ row }">
                <template v-if="queryInfo.type === 1">
                  <el-button v-if="row.trainStatus === 1 && row.isSignUp === 0" type="primary" size="small" @click="comfirmJoin(row)">报名</el-button>
                  <el-tag v-if="row.trainStatus === 1 && row.isSignUp === 1">已报名</el-tag>
                  <el-button v-if="row.trainStatus === 2 && row.isSign === 0" type="primary" size="small" @click="sign(row)">签到</el-button>
                  <el-tag v-if="row.trainStatus === 2 && row.isSign === 1">已签到</el-tag>
                  <el-popover ref="popoverRef" placement="top" width="167" trigger="click">
                    <el-rate v-model="grade" show-score></el-rate>
                    <el-row :gutter="0" type="flex" justify="center" style="margin-top: 10px">
                      <el-col :span="14">
                        <el-button type="primary" size="mini" @click="score">确定评分</el-button>
                      </el-col>
                    </el-row>
                    <el-button v-if="row.trainStatus === 3 && row.isGrade === 0" slot="reference" type="warning" size="small" @click="trainId = row.trainId">评分</el-button>
                  </el-popover>
                  <el-button v-if="row.trainStatus === 3" type="primary" size="small" style="margin-left: 15px" @click="check(row)">查看</el-button>
                </template>
                <template v-if="queryInfo.type === 2">
                  <el-button v-if="row.trainStatus === 1" type="warning" size="small" @click="edit(row)">编辑</el-button>
                  <el-button v-if="row.trainStatus === 1" type="danger" size="small" @click="del(row)">删除</el-button>
                  <!-- <el-button v-if="row.trainStatus === 2 && row.isSign === 0" type="primary" size="small" @click="sign(row)">签到</el-button>
                <el-tag v-if="row.trainStatus === 2 && row.isSign === 1" style="margin-right: 10px">已签到</el-tag> -->
                  <el-button v-if="row.trainStatus === 2" type="primary" size="small" @click="overMeeting(row)">结束培训</el-button>

                  <el-button v-if="row.trainStatus === 3" type="primary" size="small" @click="check(row)">查看</el-button>
                </template>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination style="text-align: center; margin-top: 15px" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 15, 20, 30]" :total="total" :page-size.sync="queryInfo.pageSize" :current-page.sync="queryInfo.pageNum" @size-change="geTrainList" @current-change="geTrainList" />
        </div>
      </div>
    </el-card>
    <addOrModify ref="addOrModify" :show-dialog.sync="showDialog" @success="geTrainList" />
    <el-dialog title="培训查看" :visible.sync="meetingCheckDialog" width="800px">
      <div>
        <el-tabs v-model="activeName" type="card">
          <el-tab-pane label="培训信息" name="first">
            <el-descriptions v-if="JSON.stringify(trainDetails) !== `{}`" :column="3" border style="margin: 0 auto">
              <el-descriptions-item label="预定开始时间">{{ trainDetails.startTime }} </el-descriptions-item>
              <el-descriptions-item label="预定结束时间">{{ trainDetails.endTime }} </el-descriptions-item>
              <el-descriptions-item label="培训">{{ trainDetails.trainName }}</el-descriptions-item>
              <el-descriptions-item label="培训标题">{{ trainDetails.meetingName }}</el-descriptions-item>
              <el-descriptions-item label="培训状态">
                {{ trainDetails.trainStatus | trainStatus }}
              </el-descriptions-item>
              <el-descriptions-item label="预定人">{{ trainDetails.trainUserName }}</el-descriptions-item>
              <el-descriptions-item label="培训内容">{{ trainDetails.remark }}</el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>
          <el-tab-pane label="参会人员" name="second">
            <el-table :data="trainDetails.userDtos" style="width: 100%" border>
              <el-table-column align="center" label="序号" type="index" width="width"> </el-table-column>
              <el-table-column align="center" prop="trainUserName" label="姓名" width="width"> </el-table-column>
              <el-table-column align="center" prop="organizationName" label="部门" width="width"> </el-table-column>
              <el-table-column align="center" prop="jobName" label="岗位" width="width"> </el-table-column>
              <el-table-column align="center" prop="signTime" label="签到时间" width="width"> </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="相关附件" name="third">
            <el-table :data="trainDetails.fileDtos" style="width: 100%" border>
              <el-table-column align="center" label="序号" type="index" width="width"> </el-table-column>
              <el-table-column align="center" prop="fileName" label="文件名称" width="width"> </el-table-column>
              <el-table-column align="center" prop="fileSize" label="文件大小" width="width">
                <template v-slot="{ row }">
                  <span>{{ row.fileSize }}KB</span>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="trainUserName" label="上传人" width="width"> </el-table-column>
              <el-table-column align="center" prop="createTime" label="上传时间" width="width"> </el-table-column>
              <el-table-column align="center" label="操作" width="width">
                <template v-slot="{ row }">
                  <el-button type="primary" size="mini" @click="download(row)">下载</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div slot="footer">
        <el-button type="primary" @click="meetingCheckDialog = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { trainList, trainDetails, trainSign, trainGrade, trainSignUp, trainpdateStatus } from '@/api/training.js'
import { mapGetters } from 'vuex'

import Add_or_modify from './components/Add_or_modify.vue'
export default {
  name: 'Meeting',
  components: {
    addOrModify: Add_or_modify
  },
  data() {
    return {
      queryInfo: {
        trainName: null,
        trainStatus: null,
        trainUserName: null,
        startTime: null,
        endTime: null,
        userId: null,
        organizationId: null,
        type: 1,
        pageNum: 1,
        pageSize: 10
      },
      list: [],
      total: 0,
      showDialog: false,
      replenishSummaryDialog: false,
      fileList: [],
      header: {
        Authorization: null
      },
      trainId: null,
      meetingCheckDialog: false, // 培训查看dialog
      trainDetails: {},
      activeName: 'first',
      grade: 0 // 分数
    }
  },
  computed: {
    ...mapGetters(['token', 'organizationId'])
  },
  created() {
    this.geTrainList()
  },

  methods: {
    async geTrainList() {
      this.queryInfo.organizationId = this.organizationId
      const { data } = await trainList(this.queryInfo)
      this.total = data.total
      this.list = data.list
    },
    reset() {
      const type = this.queryInfo.type
      this.queryInfo = {
        trainName: null,
        trainStatus: null,
        trainUserName: null,
        userId: null,
        startTime: null,
        endTime: null,
        type,
        pageNum: 1,
        pageSize: 10
      }
      this.geTrainList()
    },
    async edit(row) {
      const { data } = await trainDetails({ trainId: row.trainId, belongType: 2, isReserve: 1 })
      this.$refs['addOrModify'].edit(data)
      this.showDialog = true
      console.log(data)
    },
    // 参加
    comfirmJoin(row) {
      this.$confirm('您确认要参加该培训吗, 是否继续?', '参加培训', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await trainSignUp({ trainId: row.trainId })
          this.$message.success('参加成功')
          this.geTrainList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
    },
    // 签到
    async sign(row) {
      await trainSign({ trainId: row.trainId })
      this.$message.success('签到成功')
      this.geTrainList()
    },
    // 结束培训
    overMeeting(row) {
      this.$confirm('您确认要结束该培训吗, 是否继续?', '结束培训', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await trainpdateStatus({ status: 2, trainId: row.trainId })
          this.$message.success('结束培训成功')
          this.geTrainList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
    },
    // 删除
    del(row) {
      this.$confirm('您确认要删除该培训吗, 是否继续?', '删除培训', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await trainpdateStatus({ status: 1, trainId: row.trainId })
          this.$message.success('删除培训成功')
          this.geTrainList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
    },
    // 评分
    async score() {
      console.log(this.trainId)
      await trainGrade({ trainId: this.trainId, grade: this.grade })
      document.body.click()
      this.$message.success('评分成功')
      this.geTrainList()
    },
    // 查看
    async check(row) {
      const { data } = await trainDetails({ trainId: row.trainId, belongType: 2 })
      this.trainDetails = data
      console.log(data)
      this.meetingCheckDialog = true
    },
    // 查看中附件下载
    download(row) {
      window.open(row.fileUrl, '_blank')
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  background-color: #e8eaed;
  min-height: 100%;
}
.top {
  .top_left {
    display: flex;
    align-items: center;
    .meeting_icon {
      display: flex;
      align-items: center;
      margin-right: 10px;
      font-size: 16px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #0b1a44;
      img {
        margin-right: 8px;
      }
    }
  }
}
.searchForm {
  .trainStatus {
    ::v-deep {
      .el-radio__input.is-checked .el-radio__inner {
        // background: #3464e0;
        background: #fff;
        width: 18px;
        height: 18px;
        border-color: #3464e0;
        // border: none;
        &::after {
          background-color: #3464e0;
          width: 8px;
          height: 8px;
        }
      }
      .el-radio__inner {
        width: 18px;
        height: 18px;
      }
      .el-radio__input.is-checked + .el-radio__label {
        color: #0b1a44;
      }
      .el-radio__label {
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #0b1a44;
      }
    }
  }
  .trainTime {
    width: 500px;
    color: #0b1a44;
    ::v-deep {
      .el-form-item__content {
        width: 400px;
      }
      .el-input {
        width: 150px;
        border-bottom: 1px solid #b8c0cc;
      }
      .el-input__prefix {
        display: none;
      }
      .el-input__inner {
        width: 100%;
        background: none;
        border: none;
        // border-bottom: 1px solid #b8c0cc;
        opacity: 1 !important;
        color: #0b1a44;
      }
    }
  }
  .trainName{
    ::v-deep{
      .el-input{
        width: 340px;
        height: 30px;
      }
      .el-input__inner{
        background-color:  #f5f5f5;
        height: 30px;
        border-radius: 0;

      }
      .el-input__inner::placeholder{
        color: #A3A8BB;
      }
      .el-button{
        width: 34px;
        height: 30px;
        border-radius: 0;
      }
    }
  }
  ::v-deep {
    .el-form-item__label {
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      color: #868b9f;
      font-weight: 400;
    }
  }
}

::v-deep {
  .el-card__header {
    padding: 0;
    border: none;
  }
  .el-card__body {
    padding-left: 56px;
    padding-right: 56px;
  }
  .el-radio-button {
    .el-radio-button__inner {
      border: none;
      background: none;
      font-size: 18px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      span {
        position: relative;
        z-index: 999;
      }
    }
    .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      font-weight: bold;
      color: #0b1a44;
      -webkit-box-shadow: none;
      &::after {
        content: '';
        position: absolute;
        left: 50%;
        bottom: 10px;
        z-index: 1;
        width: 72px;
        height: 5px;
        background-color: #f3c057;
        transform: translateX(-50%);
      }
    }
  }
  .el-radio-button__inner:hover {
    color: #0b1a44;
  }
}
</style>
