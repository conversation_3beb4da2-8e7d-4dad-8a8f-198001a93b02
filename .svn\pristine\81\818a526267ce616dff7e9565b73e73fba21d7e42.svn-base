<template>
  <div class="message-container">
    <div class="message-content">
      <div class="table-list">
        <el-table :data="messageList" style="width: 100%" header-cell-class-name="message-tableHeader" cell-class-name="message-tableCell">
          <el-table-column align="center" prop="createTime" label="通知时间" width="250"> </el-table-column>
          <el-table-column align="center" prop="description" label="通知详情" width="width"> </el-table-column>
          <el-table-column align="center" label="操作" width="200">
            <template v-slot="{ row }">
              <span class="detail" @click="goJump(row)">了解详情</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="paging-content">
        <el-pagination
          v-if="messageList.length > 0"
          style="margin-top: 20px; text-align: center"
          layout="total,  prev, pager, next"
          background
          :total="total"
          :page-size.sync="queryInfo.pageSize"
          :current-page.sync="queryInfo.pageNum"
          @size-change="gettMessageList"
          @current-change="gettMessageList"
        />
      </div>
    </div>
    <el-empty v-if="!messageList.length">
      <img slot="image" src="@/assets/dashboard/noData_bg.png" alt="" />
      <img slot="description" src="@/assets/dashboard/noData_text.png" alt="" />
    </el-empty>
  </div>
</template>

<script>
import { tMessageList } from '@/api/tMessage'

export default {
  name: '',
  data() {
    return {
      queryInfo: {
        pageNum: 1,
        pageSize: 12
      },
      messageList: [],
      total: 0
    }
  },
  mounted() {
    this.gettMessageList()
  },
  methods: {
    async gettMessageList() {
      const { data } = await tMessageList(this.queryInfo)
      this.messageList = data.list
      this.total = data.total
    },
    goJump(item) {
      const type = parseInt(item.type)
      const dataType = type === 1 ? 9 : type === 2 ? 6 : type === 3 ? 3 : type === 4 ? 7 : 12
      this.$store.commit('checkedData/set_data_type', dataType)
      const jumpModule = type === 1 ? 'Bug' : type === 2 ? 'Institution' : type === 3 ? 'Meeting' : type === 4 ? 'SecretLog' : 'Software'
      this.$router.push({
        name: jumpModule
      })
    }
  }
}
</script>

<style scoped lang="scss">
.message-container {
  position: relative;
  width: 1656px;
  height: 818px;
  margin-left: 24px;
  margin-top: 24px;
  padding-top: 40px;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  .message-content {
    width: 1206px;
    height: 726px;
    margin: 0 auto;
    background: #fff;
    box-shadow: 0px 1px 2px 1px rgba(0, 0, 0, 0.07);
    border-radius: 4px;
    border: 1px solid #eeeeef;
    .table-list {
      padding: 20px 60px;
    }
    .paging-content {
      border-top: 1px solid #eeeeef;
    }
  }

  ::v-deep {
    .el-empty {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      .el-empty__image {
        width: 414px;
        height: 192px;
      }
    }

    .message-tableHeader {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #a3a8bb;
    }
    .message-tableCell {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #0b1a44;
    }
    .el-table__row:hover {
      .el-table__cell {
        background: #f6f9ff;
      }
    }
    .detail {
      color: #3464e0;
      cursor: pointer;
    }

    .el-table {
      // 去掉边框
      &::before {
        display: none;
      }
    }
    // 去掉边框
    .el-table td.el-table__cell,
    .el-table th.el-table__cell.is-leaf {
      border: none;
    }
  }
}
</style>
