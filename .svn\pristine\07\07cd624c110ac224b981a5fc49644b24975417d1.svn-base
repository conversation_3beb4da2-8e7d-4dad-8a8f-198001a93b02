<template>
  <div class="app-container">
    <el-row :gutter="10" class="top">
      <el-col :span="24">
        <i class="el-icon-location"></i>
        <span>当前位置：资源库管理 /</span>
        <span>{{ pageType }}</span>
      </el-col>
    </el-row>
    <div class="box">
      <div class="header">{{ pageType }}</div>
      <el-form ref="form" :model="formInfo" label-width="90px" :rules="rules">
        <el-form-item label="资源编号:" prop="code" class="code">
          <el-input v-model="formInfo.code" maxlength="40" placeholder="请输入资源编号"></el-input>
        </el-form-item>
        <el-form-item label="资源名称:" prop="resourceName" class="resourceName">
          <el-input v-model="formInfo.resourceName" maxlength="40" placeholder="请输入资源名称"></el-input>
        </el-form-item>
        <el-form-item label="专业:" prop="majorId" class="majorId">
          <el-select v-model="formInfo.majorId" placeholder="请选择专业" @focus="getMajorList">
            <el-option v-for="item in majorList" :key="item.majorId" :label="item.majorName" :value="item.majorId"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="项目:" prop="projectName" class="projectId">
          <el-input v-model="formInfo.projectName" placeholder="请输入项目名称"></el-input>
        </el-form-item>
        <el-form-item label="资源属性:" prop="type" class="type">
          <el-radio-group v-model="formInfo.type">
            <el-radio :label="1">模型</el-radio>
            <el-radio :label="2">场景</el-radio>
            <el-radio :label="3">动画</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="文件格式:" prop="modelType" class="modelType">
          <el-radio-group v-model="formInfo.modelType">
            <el-radio :label="1">FBX</el-radio>
            <el-radio :label="2">GLB</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="画质:" prop="quality" class="quality">
          <el-radio-group v-model="formInfo.quality">
            <el-radio :label="1">低画质</el-radio>
            <el-radio :label="2">中画质</el-radio>
            <el-radio :label="3">高画质</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="上传资源:" prop="fileUrl" class="fileUrl">
          <el-upload
            ref="upload"
            class="upload-demo"
            :action="uploadUrl"
            :file-list="fileList"
            :show-file-list="false"
            :headers="header"
            :on-change="uploadChange"
            :before-upload="beforeUpload"
            :on-success="uploadSuccess"
            :on-error="uploadError"
          >
            <div v-if="!fileList.length" class="uploadBox"></div>
            <!-- 上传列表 -->
            <ul class="fileList">
              <li v-for="item in fileList" :key="item.name" @click.stop>
                <div>
                  <div class="fileImg">
                    <img src="@/assets/meeting/file.png" alt="" />
                    <span v-if="item.fileSize">{{ item.fileSize }}KB</span>
                    <span v-else-if="item.size">{{ item.size | formattingFileSize }}</span>
                  </div>
                  <div>
                    <el-tooltip class="item" effect="dark" :content="item.name" placement="top">
                      <span>{{ item.name }}</span>
                    </el-tooltip>
                  </div>
                </div>
                <div v-if="item.percentage && item.percentage !== 100" class="progress">
                  <el-progress :percentage="item.percentage" color="#f3c057" :show-text="false" define-back-color="#FFFFFF" stroke-linecap="square"></el-progress>
                </div>
                <div :style="{ marginTop: item.percentage && item.percentage !== 100 ? '7px' : item.size ? '18px' : '35px' }">
                  <span v-if="item.name && item.createTime">{{ item.realName }}上传于{{ item.createTime | formatDate }}</span>
                  <span v-else>{{ realName }}上传于{{ new Date() | formatDate }}</span>
                  <i class="el-icon-delete" @click="delFile(item)"></i>
                </div>
              </li>
            </ul>
          </el-upload>
        </el-form-item>
        <el-form-item label="上传封面:" prop="coverReqs" class="coverReqs">
          <el-upload ref="uploadCover" :action="uploadUrl" multiple :show-file-list="false" :headers="header" :before-upload="beforeUpload_cover" :on-success="uploadSuccess_cover">
            <div class="uploadCover"></div>
          </el-upload>
          <div class="coverPreview" @click.stop>
            <div v-for="(item, index) in formInfo.coverReqs" :key="item.fileUrl">
              <img :src="item.fileUrl" alt="" />
              <div class="close" @click="delCover(item, index)"></div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="其他资源:" class="fileUrl">
          <el-upload
            ref="upload"
            class="upload-demo"
            :action="uploadUrl"
            :file-list="restsFileList"
            :show-file-list="false"
            :headers="header"
            :on-change="restsUploadChange"
            :before-upload="restsBeforeUpload"
            :on-success="restsUploadSuccess"
            :on-error="restsUploadError"
          >
            <div v-if="!restsFileList.length" class="uploadBox"></div>
            <!-- 上传列表 -->
            <ul class="fileList">
              <li v-for="item in restsFileList" :key="item.name" @click.stop>
                <div>
                  <div class="fileImg">
                    <img src="@/assets/meeting/file.png" alt="" />
                    <span v-if="item.fileSize">{{ item.fileSize }}KB</span>
                    <span v-else-if="item.size">{{ item.size | formattingFileSize }}</span>
                  </div>
                  <div>
                    <el-tooltip class="item" effect="dark" :content="item.name" placement="top">
                      <span>{{ item.name }}</span>
                    </el-tooltip>
                  </div>
                </div>
                <div v-if="item.percentage && item.percentage !== 100" class="progress">
                  <el-progress :percentage="item.percentage" color="#f3c057" :show-text="false" define-back-color="#FFFFFF" stroke-linecap="square"></el-progress>
                </div>
                <div :style="{ marginTop: item.percentage && item.percentage !== 100 ? '7px' : item.size ? '18px' : '35px' }">
                  <span v-if="item.name && item.createTime">{{ item.realName }}上传于{{ item.createTime | formatDate }}</span>
                  <span v-else>{{ realName }}上传于{{ new Date() | formatDate }}</span>
                  <i class="el-icon-delete" @click="delFile(item, 'rests')"></i>
                </div>
              </li>
            </ul>
          </el-upload>
        </el-form-item>
        <el-form-item label="介绍说明:" class="description">
          <el-input v-model="formInfo.description" type="textarea" placeholder="请输入资源介绍说明" maxlength="150"></el-input>
        </el-form-item>
      </el-form>
      <div class="bottom">
        <div class="closeButton" @click="closeDialog = true">取 消</div>
        <div class="confirmButton" @click="save">确 定</div>
      </div>
    </div>
    <!-- 退出弹窗 -->
    <el-dialog :visible.sync="closeDialog" width="413px" top="35vh">
      <template v-slot:title>
        <img src="@/assets/library/hint.png" alt="" class="hint" />
        <span class="hintText">提示</span>
      </template>
      <div class="delText">您还未保存该资源，确认退出该页面吗？</div>
      <div class="operate">
        <span class="closeButton" @click="closeDialog = false">取 消</span>
        <span class="confirmButton" @click="close">确 定</span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { allMajor } from '@/api/specialty'
import { resourceAdd, resourceUpdate, resourceDetail } from '@/api/Library'
import { mapGetters } from 'vuex'
export default {
  name: '',
  data() {
    return {
      pageType: '新增资源',
      formInfo: {
        code: null, // 资源编号
        resourceName: null, // 资源名称
        type: null, // 资源属性 1 模型 2 场景 3 动画
        modelType: null, // 文件格式 1 FBX 2 GLB
        quality: null, // 画质 1 低 2 中 3 高
        majorId: null, // 专业id
        description: null, // 资源描述
        projectName: null,
        fileUrl: null, // 资源地址
        otherFileUrl: null, // 其他资源地址
        coverReqs: []
      },
      rules: {
        code: [{ required: true, message: '请输入资源编号', trigger: 'blur' }],
        resourceName: [{ required: true, message: '请输入资源名称', trigger: 'blur' }],
        majorId: [{ required: true, message: '请选择专业', trigger: 'change', type: 'number' }],
        projectName: [{ required: true, message: '请输入专业名称', trigger: 'blur' }],
        type: [{ required: true, message: '请选择资源属性', trigger: 'change', type: 'number' }],
        modelType: [{ required: true, message: '请选择文件格式', trigger: 'change', type: 'number' }],
        quality: [{ required: true, message: '请选择画质', trigger: 'change', type: 'number' }],
        fileUrl: [{ required: true, message: '请上传资源', trigger: 'blur' }],
        coverReqs: [{ type: 'array', required: true, message: '请上传封面', trigger: 'change' }]
      },
      majorList: [],
      projectList: [],
      // 上传资源
      uploadUrl: window.config.VUE_APP_BASE_API + '/system/upload/file',
      header: {
        Authorization: null
      },
      fileList: [],
      // 上传封面
      closeDialog: false,
      restsFileList: []
    }
  },
  computed: {
    ...mapGetters(['token', 'realName'])
  },
  created() {
    if (parseInt(this.$route.params.id) !== 0) {
      this.pageType = '修改资源'
      this.edit()
    }
  },
  methods: {
    // 获取专业列表
    async getMajorList() {
      const { data } = await allMajor()
      this.majorList = data
    },
    uploadChange(file, fileList) {
      if (!file.response) {
        this.fileList = fileList
      }
    },
    beforeUpload(file) {
      this.header.Authorization = `Bearer ${this.token}`
      const postfix = file.name.split('.')[file.name.split('.').length - 1]
      if (this.formInfo.modelType === 2) {
        return postfix === 'glb'
      } else if (this.formInfo.modelType === 1) {
        return postfix === 'fbx'
      }
    },
    // 上传成功回调
    uploadSuccess(response, file, fileList) {
      if (response.code !== 200) {
        this.$message.error(response.message)
        this.fileList = []
      } else {
        this.formInfo.fileUrl = response.data[0]
        this.$refs['form'].validateField('fileUrl')
        this.fileList = fileList
      }
    },
    // 上传失败回调
    uploadError() {
      this.$message.error('上传失败')
      this.fileList = []
    },
    // 删除文件
    delFile(row, type) {
      if (row.percentage && row.percentage !== 100) {
        this.$refs['upload'].abort(row.raw)
      }
      console.log(row)
      if (type === 'rests') {
        this.restsFileList = this.restsFileList.filter((item) => item.uid !== row.uid)
        this.formInfo.otherFileUrl = null
      } else {
        this.fileList = this.fileList.filter((item) => item.uid !== row.uid)
        this.formInfo.fileUrl = null
      }
    },

    // 上传封面的事件
    beforeUpload_cover(file) {
      this.header.Authorization = `Bearer ${this.token}`
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      if (!isJPG && !isPNG) {
        this.$message.warning('只能上传.jpg .png 格式的附件')
      }
      return isJPG || isPNG
    },
    uploadSuccess_cover(res, file, fileList) {
      if (res.code !== 200) {
        this.$message.error(res.message)
      } else {
        this.setCoverReqs(file)
      }
    },

    setCoverReqs(item) {
      // const arr = []
      // list.forEach((item) => {
      //   arr.push({
      //     fileName: item.name || item.fileName,
      //     fileSize: item.size / 1024 || item.fileSize,
      //     fileUrl: item.response.data[0] || item.fileUrl,
      //     belongType: 11
      //   })
      // })
      // this.formInfo.coverReqs.push(...arr)

      this.formInfo.coverReqs.push({
        fileName: item.name || item.fileName,
        fileSize: item.size / 1024 || item.fileSize,
        fileUrl: item.response.data[0] || item.fileUrl,
        belongType: 11
      })

      this.$refs['form'].validateField('coverReqs')
    },
    delCover(data) {
      this.formInfo.coverReqs = this.formInfo.coverReqs.filter((item, Index) => {
        if (item.fileName !== data.fileName) {
          return item
        }
      })
    },
    // 其他资源上传
    restsUploadChange(file, fileList) {
      if (!file.response) {
        this.restsFileList = fileList
      }
    },
    restsBeforeUpload(file) {
      this.header.Authorization = `Bearer ${this.token}`
      const postfix = file.name.split('.')[file.name.split('.').length - 1]
      const zip = file.type === 'application/x-zip-compressed'
      const tar = file.type === 'application/x-tar'
      const sevenZ = postfix === '7z'
      const zipx = postfix === 'zipx'
      if (!zip && !tar && !sevenZ && !zipx) {
        this.$message.error('只能上传：zip、tar、sevenZ、zipx')
      }
      return zip || tar || sevenZ || zipx
    },
    restsUploadSuccess(res, file, fileList) {
      if (res.code !== 200) {
        this.$message.error(res.message)
        this.restsFileList = []
      } else {
        this.formInfo.otherFileUrl = res.data[0]
        this.restsFileList = fileList
      }
    },
    restsUploadError() {
      this.$message.error('上传失败')
      this.restsFileList = []
    },
    save() {
      this.$refs['form'].validate(async (val) => {
        if (val) {
          const loading = this.$loading({
            text: '正在保存，请稍后',
            background: 'rgba(0, 0, 0, 0.8)'
          })
          if (this.pageType === '修改资源') {
            await resourceUpdate(this.formInfo)
              .then(() => {
                loading.close()
                this.$message.success('修改成功')
                this.$router.push('/library')
              })
              .catch(() => {
                loading.close()
                this.$message.error('修改失败，请重试')
              })
          } else {
            await resourceAdd(this.formInfo)
              .then(() => {
                loading.close()
                this.$message.success('保存成功')
                this.$router.push('/library')
              })
              .catch(() => {
                loading.close()
                this.$message.error('保存失败，请重试')
              })
          }
        }
      })
    },
    async edit() {
      const { data } = await resourceDetail({ id: this.$route.params.id })
      this.formInfo = {
        resourceId: data.resourceId,
        code: data.code, // 资源编号
        resourceName: data.resourceName, // 资源名称
        type: data.type, // 资源属性 1 模型 2 场景 3 动画
        modelType: data.modelType, // 文件格式 1 FBX 2 GLB
        quality: data.quality, // 画质 1 低 2 中 3 高
        majorId: data.majorId, // 专业id
        description: data.description, // 资源描述
        projectName: data.projectName, // 项目名称
        fileUrl: data.fileUrl, // 资源地址
        otherFileUrl: data.otherFileUrl, // 其他资源地址
        coverReqs: data.coverFiles
      }
      this.fileList = [
        {
          name: data.fileUrl.split('/')[data.fileUrl.split('/').length - 1],
          realName: data.realName,
          createTime: data.createTime
        }
      ]
      this.restsFileList = [
        {
          name: data.otherFileUrl.split('/')[data.otherFileUrl.split('/').length - 1],
          realName: data.realName,
          createTime: data.createTime
        }
      ]
      this.getMajorList()
    },
    close() {
      this.closeDialog = false
      this.$router.push('/library')
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  position: relative;
  padding: 0;
  background-color: #e8eaed;
  min-height: 100%;
  padding-top: 58px;
  padding-bottom: 10px;
  .top {
    position: absolute;
    top: 0px;
    width: 100%;
    background-color: #e8eaed;
    padding: 24px 0 16px 0;
    z-index: 999;
    .el-col {
      i {
        font-size: 14px;
        color: #657081;
      }
      span {
        &:first-of-type {
          margin: 0 5px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #657081;
        }
        &:last-of-type {
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
      }
    }
  }
  .box {
    position: relative;
    width: 1754px;
    height: 800px;
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    // padding-left: 40px;
    padding-top: 24px;
    overflow: auto;
    &::-webkit-scrollbar {
      width: 5px;
      background: #e8eaed;
    }
    &::-webkit-scrollbar-thumb {
      background: #3465df;
      border-radius: 5px;
    }
    .header {
      margin-bottom: 24px;
      padding-left: 48px;
      padding-bottom: 11px;
      font-size: 16px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #0b1a44;
      border-bottom: 1px solid #eeeeef;
    }
    ::v-deep {
      .el-form {
        padding-left: 498px;
        .el-form-item {
          margin-bottom: 28px;
        }
      }
      .el-form-item__label {
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #0b1a44;
      }
      .el-input__inner {
        height: 40px;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #d8dbe1;
        &::placeholder {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #b1bac7;
        }
      }
      .code,
      .resourceName {
        width: 436px;
      }
      .majorId,
      .projectId {
        .el-input__inner {
          width: 235px;
          height: 38px;
          &::placeholder {
            color: #0b1a44;
          }
        }
        .el-input__icon {
          line-height: 38px;
        }
      }
      .type,
      .modelType,
      .quality {
        .el-radio__input.is-checked .el-radio__inner {
          // background: #3464e0;
          background: #fff;
          width: 18px;
          height: 18px;
          border-color: #3464e0;
          // border: none;
          &::after {
            background-color: #3464e0;
            width: 8px;
            height: 8px;
          }
        }
        .el-radio__inner {
          width: 18px;
          height: 18px;
        }
        .el-radio__input.is-checked + .el-radio__label {
          color: #0b1a44;
        }
        .el-radio__label {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
        }
      }
      .type,
      .modelType {
        display: inline-block;
      }
      .modelType {
        margin-left: 72px;
      }
      // 上传资源
      .upload-demo {
        width: 268px;
        height: 124px;
        .el-upload {
          display: flex;
        }
        .uploadBox {
          width: 268px;
          height: 124px;
          background: url('~@/assets/library/upload_bg.png') no-repeat;
          background-size: cover;
          cursor: pointer;
        }
        .fileList {
          display: flex;
          li {
            padding: 14px 10px 20px 10px;
            width: 268px;
            height: 124px;
            background: #f9f9f9;
            border-radius: 4px 4px 4px 4px;
            border: 1px solid #eeeeef;
            box-sizing: border-box;
            & > div {
              &:first-of-type {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                .fileImg {
                  display: flex;
                  flex-direction: column;
                  justify-content: flex-start;
                  img {
                    width: 46px;
                    height: 38px;
                  }
                  span {
                    font-size: 12px;
                    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                    font-weight: 400;
                    color: #868b9f;
                    line-height: initial;
                  }
                }
                & > div {
                  margin-left: 3px;
                  line-height: initial;
                  .el-textarea {
                    width: initial;
                    .el-textarea__inner {
                      width: 100%;
                    }
                  }

                  &:last-of-type {
                    span {
                      display: inline-block;
                      max-width: 160px;
                      font-size: 14px;
                      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                      font-weight: 400;
                      color: #0b1a44;
                      white-space: nowrap;
                      overflow: hidden;
                      text-overflow: ellipsis;
                    }
                    i {
                      margin-left: 5px;
                      color: #ff7e26;
                      font-size: 18px;
                      cursor: pointer;
                    }
                  }
                }
              }

              &:last-of-type {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-top: 18px;
                font-size: 12px;
                font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                font-weight: 400;
                color: #a3a8bb;
                line-height: initial;
                text-align: left;
                i {
                  font-size: 18px;
                  float: right;
                  margin-left: 15px;
                  cursor: pointer;
                }
                .el-icon-download:hover {
                  color: #3464e0;
                }
                .el-icon-delete:hover {
                  color: #eb6557;
                }
              }
            }
            .progress {
              margin-top: 9px;
              ::v-deep {
                .el-progress {
                  width: 244px;
                  .el-progress-bar__outer {
                    height: 2px !important;
                  }
                }
              }
            }
          }
        }
      }

      // 上传封面
      .coverReqs {
        .el-form-item__content {
          display: flex;
        }
        .el-upload {
          margin-right: 24px;
          .uploadCover {
            width: 120px;
            height: 88px;
            background: url('~@/assets/library/upload_cover_bg.png') no-repeat;
            background-size: cover;
            cursor: pointer;
          }
        }
        .coverPreview {
          display: flex;
          & > div {
            position: relative;
            width: 120px;
            height: 88px;
            margin-right: 16px;
            img {
              width: 100%;
              height: 100%;
              border-radius: 6px;
            }
            .close {
              position: absolute;
              right: -9px;
              top: -9px;
              width: 18px;
              height: 18px;
              background: url('~@/assets/library/close.png') no-repeat;
              background-size: cover;
              cursor: pointer;
              &:hover {
                background: url('~@/assets/library/close_hover.png') no-repeat;
                background-size: cover;
              }
            }
          }
        }
      }
      //介绍说明
      .description {
        .el-textarea__inner {
          width: 682px;
          height: 138px;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #d8dbe1;
        }
      }
    }
    .bottom {
      display: flex;
      justify-content: center;
      padding-top: 40px;
      padding-bottom: 40px;
      border-top: 1px solid #eeeeef;
      .closeButton {
        width: 260px;
        height: 46px;
        line-height: 46px;
        background: #ffffff;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #d8dbe1;
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        color: #a3a8bb;
        text-align: center;
        cursor: pointer;
        &:hover {
          background: #f5f5f5;
        }
      }
      .confirmButton {
        margin-left: 32px;
        width: 260px;
        height: 46px;
        line-height: 46px;
        background: #3464e0;
        border-radius: 4px 4px 4px 4px;
        font-size: 14px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        font-weight: bold;
        color: #ffffff;
        text-align: center;
        cursor: pointer;
        &:hover {
          background: #355fce;
        }
      }
    }
  }
  ::v-deep {
    .el-dialog {
      height: 206px;
      border-radius: 8px !important;
      .el-dialog__header {
        display: flex;
        align-items: center;
        padding-top: 12px;
        padding-left: 24px;
        padding-right: 20px;
        padding-bottom: 8px;
        border-bottom: 1px solid #eeeeef;
        .hint {
          width: 30px;
          height: 30px;
        }
        .hintText {
          margin-left: 6px;
          font-size: 16px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #303a40;
        }
        .el-dialog__headerbtn {
          right: 20px;
          top: 20px;
          .el-dialog__close {
            font-weight: bold;
          }
        }
      }
      .delText {
        text-align: center;
        font-size: 16px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #303a40;
      }
      .operate {
        display: flex;
        justify-content: center;
        margin-top: 48px;
        .closeButton {
          width: 114px;
          height: 38px;
          line-height: 36px;
          background: #ffffff;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #d8dbe1;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          color: #a3a8bb;
          text-align: center;
          cursor: pointer;
          &:hover {
            background: #f5f5f5;
          }
        }
        .confirmButton {
          margin-left: 32px;
          width: 114px;
          height: 38px;
          line-height: 36px;
          background: #3464e0;
          border-radius: 4px 4px 4px 4px;
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #ffffff;
          text-align: center;
          cursor: pointer;
          &:hover {
            background: #355fce;
          }
        }
      }
    }
  }
}
</style>
