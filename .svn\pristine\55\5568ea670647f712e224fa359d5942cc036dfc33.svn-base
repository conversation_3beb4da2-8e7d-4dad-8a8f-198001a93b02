<template>
  <div class="app-container">
    <div class="box">
      <img src="@/assets/tSalesPlan/header.png" alt="" class="header" />
      <div class="form">
        <div class="add" @click="add"></div>
        <el-form ref="form" label-width="100px" inline>
          <el-form-item label="客户名称:">
            <el-input v-model="queryInfo.customerName" placeholder="请输入客户名称" maxlength="40"></el-input>
          </el-form-item>
          <el-form-item label="方案名称:">
            <el-input v-model="queryInfo.name" placeholder="请输入方案名称" maxlength="40"></el-input>
          </el-form-item>
          <el-form-item label="创建人:">
            <el-input v-model="queryInfo.realName" placeholder="请输入创建人" maxlength="40"></el-input>
          </el-form-item>
          <el-form-item>
            <span class="searchButton search">查询</span>
            <span class="searchButton reset">重置</span>
            <span class="searchButton export">导出</span>
          </el-form-item>
        </el-form>
      </div>
      <el-table :data="list" style="width: 100%">
        <el-table-column prop="prop" label="label" width="width"> </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { tSalesPlanList } from '@/api/tSalesPlan'
export default {
  name: '',
  data() {
    return {
      queryInfo: {
        name: null, // 方案名称
        customerName: null, // 客户名称
        realName: null,
        pageNum: 1,
        pageSize: 10
      },
      list: [],
      total: 0
    }
  },
  created() {
    this.getList()
  },
  methods: {
    async getList() {
      const { data } = await tSalesPlanList(this.queryInfo)
      this.list = data.list
      this.total = data.total
    },
    add() {
      this.$router.push(`/tSalesPlan/add/0`)
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  padding: 0;
  padding-top: 24px;
  background: #e8eaed;
  height: 100%;
  .box {
    width: 1754px;
    height: 826px;
    background: #f5f5f5;
    border-radius: 4px 4px 4px 4px;
    overflow: hidden;
    .header {
      width: 100%;
      height: 82px;
    }
    .form {
      display: flex;
      align-items: center;
      width: 100%;
      height: 96px;
      background: #fff;
      padding-left: 40px;
      .add {
        margin-right: 15px;
        width: 116px;
        height: 42px;
        background: url('~@/assets/tSalesPlan/add.png') no-repeat;
        background-size: cover;
        cursor: pointer;
        &:hover {
          background: url('~@/assets/tSalesPlan/add_hover.png') no-repeat;
          background-size: cover;
        }
      }
    }
  }
  ::v-deep {
    .el-form {
      .el-form-item {
        margin-bottom: 0;
        .el-form-item__label {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
        }
        .el-form-item__content {
          .el-input {
            .el-input__inner {
              width: 284px;
              height: 36px;
              background: #ffffff;
              border-color: #d8dbe1;
              &::placeholder {
                font-size: 14px;
                font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                font-weight: 400;
                color: #868b9f;
              }
            }
          }
        }
        .searchButton {
          display: inline-block;
          margin-top: 6px;
          width: 68px;
          height: 28px;
          line-height: 26px;
          font-size: 14px;
          text-align: center;
          border-radius: 4px;
          cursor: pointer;
          border: 1px solid transparent;
        }
        .search {
          margin-left: 25px;
          background: #3464e0;
          border-color: #3464e0;
          color: #fff;
          &:hover {
            background: #355fce;
            border-color: #355fce;
          }
        }
        .reset {
          margin: 0 16px;
          background: #fff;
          border-color: #3464e0;
          color: #3464e0;
          &:hover {
            background: #fff;
            border-color: #355fce;
            color: #355fce;
          }
        }
        .export {
          border-color: #b1bac7;
          background: #fff;
          color: #a3a8bb;
          &:hover {
            background: #f5f5f5;
            color: #a3a8bb;
            border-color: #b1bac7;
          }
        }
      }
    }
    // .tableHeader {
    //   background: #f0f0f0;
    //   font-size: 14px;
    //   font-family: Microsoft YaHei-Bold, Microsoft YaHei;
    //   font-weight: bold;
    //   color: #0b1a44;
    // }
    // .el-table td.el-table__cell,
    // .el-table th.el-table__cell.is-leaf {
    //   border-color: #a3a8bb;
    // }
    // .el-table--border,
    // .el-table--group {
    //   border-color: #a3a8bb;
    // }
    // .el-table--border::after,
    // .el-table--group::after,
    // .el-table::before {
    //   background-color: #a3a8bb;
    // }
    // .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
    //   background: #eff1f3;
    // }
    // .softwareName {
    //   font-size: 14px;
    //   font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    //   font-weight: 400;
    //   color: #3464e0;
    //   cursor: pointer;
    // }
  }
}
</style>
